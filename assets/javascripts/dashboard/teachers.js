var instituteName,userName,contactName,contactNo;
// var emailPattern=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
instituteName=document.getElementById('institutename');
userName=document.getElementById('username');
contactName=document.getElementById('contactName');
contactNo=document.getElementById('contactNo');
var errorMsg=document.getElementById('error');
var successMsg=document.getElementById('success');
var table='';
function addTeachers() {
    if ((instituteName.value && userName.value && contactName.value && contactNo.value) != "") {
        $.ajax({
            type: "POST",
            url: 'dashboard/addInstitute',
            data: {
                userName: userName.value,
                contactName: contactName.value,
                contactNumber: contactNo.value,
                instituteName: instituteName.value,
                mode: 'add'
            },
            success: function (data, status,xhr) {


                if (data.status == "OK") {
                    successMsg.innerText = 'Submitted Successfully'
                    errorMsg.innerText ='';
                    techersData();

                } else {
                    successMsg.innerText = '';
                    errorMsg.innerText = 'User is not registered in WS';

                }

            }
        });
    }
}
function editInstitute() {
    if ((instituteName.value && userName.value && contactName.value && contactNo.value) != "") {
        $.ajax({
            type: "POST",
            url: 'addInstitute',
            data: {
                userName: userName.value,
                contactName: contactName.value,
                contactNumber: contactNo.value,
                instituteName: instituteName.value,
                mode: 'edit'
            },
            success: function (data, status,xhr) {


                if (data.status == "OK") {
                    successMsg.innerText = 'Submitted Successfully';
                    errorMsg.innerText ='';
                    instituteData();

                } else {
                    successMsg.innerText = '';
                    errorMsg.innerText = 'User is not registered in WS';

                }

            }
        });
    }
}

function deleteInstitute() {

}

//Data tables

function techersData () {

    $('#teachersTable').DataTable( {
        "responsive": true,
        "columnDefs": [
            { responsivePriority: 1, targets: 4},

        ],
        "processing": true,
        "serverSide": true,
        "bRetrieve": true,
        "searching": true,
        "ordering":  false,
        "ajax": {
            "url": "/dashboard/getInstitutesDetails",
            "type": "GET",
            "data": function ( outData ) {
                return outData;
            },
            dataFilter:function(inData){
                // what is being sent back from the server (if no error)
                console.log("Data received:", inData);
                return inData;
            },
            error:function(err, status){
                // what error is seen(it could be either server side or client side.
                console.error("Error loading teacher data:", err);
            },
        },
        "columns": [{
            "data": "instituteName",
            "defaultContent": "<i>Not set</i>"
        },
        {
            "data": "userName",
            "defaultContent": "<i>Not set</i>"
        },
        {
            "data": "contactName",
            "defaultContent": "<i>Not set</i>"
        },
        {
            "data":"id",
            "render": function (data, type, row) {
                if (row.id != null) {
                    return "<a href='javascript:editInstitute(\"" + row.contactName + "\"," + row.id + ",\""+row.contactNumber+"\",\""+row.userName+"\",\""+row.instituteName+"\")'><i class='material-icons'>edit</i></a>";
                }
                return "";
            }
        },
        {
            "data":"id",
            "render": function (data, type, row) {
                if (row.id != null) {
                    return "<a href='javascript:deleteInstitute(\"" + row.id +")'><i class=\"material-icons delete\">delete</i></a>";
                }
                return "";
            }
        }]
    });

}


techersData();




$("#addteacher").on('show.bs.modal', function(){
    var data = '{"className":"class1","id":1},{"className":"class2","id":2}';
    var optionData = JSON.parse('[' + data + ']');
    var select=document.getElementById('getClassName');
    var options="<option value='' selected>Select class</option>";

    for(var i=0;i<optionData.length;i++){
        options += "<option id="+optionData[i].id+">"+ optionData[i].className +"</option>";
    }
    select.innerHTML=options;
});

function getSection(selectOption) {

    var value=selectOption.value;



}
