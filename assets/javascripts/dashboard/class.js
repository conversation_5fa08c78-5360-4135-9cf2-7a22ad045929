var className,section;
className=document.getElementById('classname');
section=document.getElementById('section')
function addClass() {
    if ((className.value && section.value) != "") {
        $.ajax({
            type: "POST",
            url: 'dashboard/addClass',
            data: {
                className: className.value,
                section: section.value,
                mode: 'add'
            },
            success: function (data, status,xhr) {
                if (data.status == "OK") {
                    successMsg.innerText = 'Submitted Successfully'
                    errorMsg.innerText ='';
                    classData();

                } else {
                    successMsg.innerText = '';
                    errorMsg.innerText = 'User is not registered in WS';

                }

            }
        });
    }
}
function editClass() {
    
}
function classData () {

    $('#classTable').DataTable( {
        "responsive": true,
        "columnDefs": [
            { responsivePriority: 1, targets: 4},

        ],
        // "processing": true,
        "serverSide": true,
        "bRetrieve": true,
        // "searching": false,
        "ordering":  false,
        "ajax": {
            "url": "dashboard/getInstitutesDetails",
            "type": "GET",
            "data": function ( outData ) {

                return outData;
            },
            dataFilter:function(inData){
                // what is being sent back from the server (if no error)

                return inData;
            },
            error:function(err, status){
                // what error is seen(it could be either server side or client side.

            },
        },
        "columns": [{
            "data": "instituteName",
        },

            {
                "data": "userName",

            },

            {
                "data":"id",
                "render": function (data, type, row) {

                    if ( row.id !=null) {
                        // var  tests =row.userName;
                        //   //var newStr = myStr.replace(/,/g, '-');
                        //   tests =tests.replace(/./g,"ans")
                        //   console.log("usrssrss==="+tests);

                        return "<a href='javascript:editInstitute(\"" + row.contactName + "\"," + row.id + ",\""+row.contactNumber+"\",\""+row.userName+"\",\""+row.instituteName+"\")'><i class='material-icons'>edit</i></a>";
                    }

                }
            },
            {
                "data":"id",
                "render": function (data, type, row) {

                    if ( row.id !=null) {
                        // var  tests =row.userName;
                        //   //var newStr = myStr.replace(/,/g, '-');
                        //   tests =tests.replace(/./g,"ans")
                        //   console.log("usrssrss==="+tests);

                        return "<a href='javascript:deleteInstitute(\"" + row.id +")'><i class=\"material-icons delete\">delete</i></a>";
                    }

                }
            }



        ]
    });

}


classData();
