var chapterDetails={

    chapterDetails:[
        {
            "quizId": "1804",
            "id": 2740,
            "name": "Biology 2017",
            "subject": "Practice- KCET Biology Previous Year Question Papers"
        },
        {
            "quizId": "1876",
            "id": 2810,
            "name": "Chemistry 2010",
            "subject": "Practice- IIT JEE (Mains) Chemistry Previous Year Question Papers"
        }
    ]};

/*For web and android below variable will be true and only for IOS variable will be false*/
var android=true;
/*------------------------------------------------*/
var wonderslate=true;
var userAnswers=[];
var qa;
var startTime;
var endTime;
var score={};
var ansOptKeys = {"ans1":"op1","ans2":"op2","ans3":"op3","ans4":"op4","ans5":"op5"};
var subjects=[];
var subjectDetails=[];
var subjectKey;
var userDataTable;
var userSuggestions;
var userDataTable = "";
var userSuggestions = "";
var nameoftheuser="";
var chapatersList;
var sectionsPresent=false;
var previousSubject=null;
var examDtl=null;
var sections={};
var testSeriesQuiz=false;
var testInOtherLang=false;
var testSubmitted=false;
var examMst=null;
var scoredTest=false;
var scoreHolder=[];
var quizSource="app";
var quizActivityType="";
var resultsMode=false;
var testEndDate="";
var testResultDate="";
var percent = 0;
var accuracy = 0;
var timeTaken = 0;
var noOfQuestionsAttempted = 0;
var isSecondLanguageAvailable=false;
var noOfQuestionsPerHour = 0;
var optionLabel=["A","B","C","D","E"];
var totalTestTime;

function showSecondLanguage(){
    $("#changeLanguage").show();
    $("#changeLanguage1").show();
}

function setResultsMode(userAnswersInput,scoreInput,examMaster,examDetail,testSeriesInput){
    resultsMode=true;
    userAnswers=userAnswersInput;
    examMst = examMaster;
    if(examDetail!=null&&!examDetail==""&&examDetail.length>0) {
        examDtl = examDetail;

        sectionsPresent=true;
        for (i = 0; i < examDtl.length; i++) {
            //add the correct thingy also
            examDtl[i].skipped = 0;
            examDtl[i].correctAnswers = 0;
            examDtl[i].wrongAnswers = 0;
        }
    }
}


function changeLanguage(){
    $('.language').toggleClass('rotate');
    if(testInOtherLang) testInOtherLang=false;
    else testInOtherLang=true;
    for (var i = 0; i < qa.length; ++i){
        if(qa[i].directions){
            dirStr = qa[i].directions;
            if(testInOtherLang&&dirStr.indexOf("~~")!=-1){
                //changing the language
                dirStr = dirStr.substr(dirStr.indexOf("~~")+2);
            }else if(dirStr.indexOf("~~")!=-1){
                //getting the first  language
                dirStr = dirStr.substr(0,dirStr.indexOf("~~"));
            }
            if(elementExists("directions-"+(i+1))) {
                var allDirection= document.querySelectorAll(".directions-"+(i+1));
                for (var a = 0; a < allDirection.length; a++) {
                    allDirection[a].innerHTML ="<p class='total-direction-que'>" + dirStr + "</p>";

                }
            }
            if(elementExists("directionss-"+(i+1))) {
                var allDirections= document.querySelectorAll("#directionss-"+(i+1));
                for (var b = 0; b < allDirections.length; b++) {
                    allDirections[b].innerHTML ="<p class='total-direction-que'>" + dirStr + "</p>";

                }
            }



            // document.getElementById("directionss-"+(i+1)).innerHTML=  "<p class='total-direction-que'>"+dirStr+ "</p>";
            // document.getElementById("directionsss-"+(i+1)).innerHTML=  "<p class='total-direction-que'>"+dirStr+ "</p>";
        }

        questionString = qa[i].ps;
        if(testInOtherLang&&questionString.indexOf("~~")!=-1){
            //changing the language
            questionString = questionString.substr(questionString.indexOf("~~")+2);
        }else if(questionString.indexOf("~~")!=-1){
            //getting the first  language
            questionString = questionString.substr(0,questionString.indexOf("~~"));
        }
        questionString = questionString.replace('<p>','');
        questionString = questionString.replace('</p>','');
        if(elementExists("question-"+(i+1))){
            var allQuestion= document.querySelectorAll(".question-"+(i+1));
            for (var w = 0; w < allQuestion.length; w++) {
                allQuestion[w].innerHTML =questionString;

            }
        }
        if(elementExists("questions-"+(i+1))){
            var allQuestions=document.querySelectorAll(".questions-"+(i+1));
            for (var x = 0; x < allQuestions.length; x++) {
                allQuestions[x].innerHTML = questionString;
            }
        }

        if(elementExists("list-question-"+(i+1))) {
            var listQuestion=document.querySelectorAll(".list-question-"+(i+1));
            for (var k = 0; k < listQuestion.length; k++) {
                listQuestion[k].innerHTML = questionString;
            }
        }


        for(var j=0;j< 5;++j){
            answerString = eval("qa[" + i + "].op" + (j+1));
            if(answerString){
                if(testInOtherLang&&answerString.indexOf("~~")!=-1){
                    //changing the language
                    answerString = answerString.substr(answerString.indexOf("~~")+2);
                }else if(answerString.indexOf("~~")!=-1){
                    //getting the first  language
                    answerString = answerString.substr(0,answerString.indexOf("~~"));
                }
                if(answerString.indexOf("<p")==0){
                    answerString = answerString.replace('<p>','');
                    answerString = answerString.replace('</p>','');
                }

                if(elementExists("answerString-"+(i+1)+"-"+(j+1))){
                    var allAnswer=document.querySelectorAll(".answerString-"+(i+1)+"-"+(j+1));
                    for (var l = 0; l < allAnswer.length; l++) {
                        allAnswer[l].innerHTML = answerString;
                    }
                }

                if(elementExists("answerStrings-"+(i+1)+"-"+(j+1))){
                    var allAnswers= document.querySelectorAll(".answerStrings-"+(i+1)+"-"+(j+1));
                    for (var m = 0; m < allAnswers.length; m++) {
                        allAnswers[m].innerHTML = answerString;
                    }
                }
            }
        }
        if (qa[i].answerDescription) {
            qa[i].answerDescription = qa[i].answerDescription.replace('<p>', '');
            qa[i].answerDescription = qa[i].answerDescription.replace('</p>', '');
            var descriptionString = qa[i].answerDescription;
            if(testInOtherLang&&descriptionString.indexOf("~~")!=-1){
                //changing the language
                descriptionString = descriptionString.substr(descriptionString.indexOf("~~")+2);
            }else if(descriptionString.indexOf("~~")!=-1){
                //getting the first  language
                descriptionString = descriptionString.substr(0,descriptionString.indexOf("~~"));
            }
            if(elementExists("demo-" + (i + 1) )) {
                var description= document.querySelectorAll(".demo-" + (i + 1));
                for (var n = 0; n < description.length; n++) {
                    description[n].innerHTML=descriptionString;
                }
            }
            if(elementExists("demos-" + (i + 1) )) {
                var descriptions=document.querySelectorAll(".demos-" + (i + 1));
                for (var p = 0; p < descriptions.length; p++) {
                    descriptions[p].innerHTML = descriptionString;
                }
            }
        }



    }

    renderMathInElement(document.body, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true },
            { left: "$$", right: "$$", display: true },
            { left: "$", right: "$", display: false },
        ],
        ignoredTags: [],
    });

}



function displayQuestions(){
    var htmlStr="";
    var currentSubject="";
    var fhtmlStr ="";
    var listhtmlStr='';
    var dirStr="";

    if(testInOtherLang) {
        $('.language').toggleClass('rotate');
    }
    for (var i = 0; i < qa.length; ++i) {
        if(qa[i].directions){
            dirStr = qa[i].directions;
            if(testInOtherLang&&dirStr.indexOf("~~")!=-1){
                //changing the language
                dirStr = dirStr.substr(dirStr.indexOf("~~")+2);
            }else if(dirStr.indexOf("~~")!=-1){
                //getting the first  language
                dirStr = dirStr.substr(0,dirStr.indexOf("~~"));
            }
            htmlStr +=  "<div class='container directions-"+(i+1)+"' id='directions-"+(i+1)+"'>\n" +"<p class='total-direction-que'>"+dirStr+ "</p>"+"</div>";
        }

        questionString = qa[i].ps;
        if(testInOtherLang&&questionString.indexOf("~~")!=-1){
            //changing the language
            questionString = questionString.substr(questionString.indexOf("~~")+2);
        }else if(questionString.indexOf("~~")!=-1){
            //getting the first  language
            questionString = questionString.substr(0,questionString.indexOf("~~"));
        }
        questionString = questionString.replace('<p>','');
        questionString = questionString.replace('</p>','');

        htmlStr += "<div class='question-wrapper'>"+
            "<div class='que-menu container'>"+
            "<span>"+"Q"+(i+1)+".</span>"+
            "<div class='btn-wrapper' id='markbtn-"+(i+1)+"'><a class='error-btn' href='javascript:openIssue("+i+")'></a>" +
            "<input type='checkbox' class='markbutn' name='bookmark' value='valuable' id='bookmarkbtn-"+(i+1)+"' onchange='reviewChanged("+i+")'/>"+
            "<label class='custom-mark' for='bookmarkbtn-"+(i+1)+"'></label> "+
            "</div>"+
            "</div>"+
            "<pre class='container question question-"+(i+1)+"' id='question-"+(i+1)+"'>"+questionString+"</pre>"+"<div class='container border-start'><p></p></div>";


        for(var j=0;j< 5;++j){
            answerString = eval("qa[" + i + "].op" + (j+1));
            if(answerString){
                if(testInOtherLang&&answerString.indexOf("~~")!=-1){
                    //changing the language
                    answerString = answerString.substr(answerString.indexOf("~~")+2);
                }else if(answerString.indexOf("~~")!=-1){
                    //getting the first  language
                    answerString = answerString.substr(0,answerString.indexOf("~~"));
                }
                if(answerString.indexOf("<p")==0){
                    answerString = answerString.replace('<p>','');
                    answerString = answerString.replace('</p>','');
                }
                htmlStr +=
                    "<div class='container options-string opt-str-"+(i)+"' onclick='javascript:answerSelected("+i+","+j+");'>" +
                    "<div class='radio user-input'>" +
                    "<label>" +
                    "<div>"+"<span class='choice'>"+" "+optionLabel[j]+"</span>"+"</div>" +"<input type='radio' class='answerChk' name='quiz.rad.ans"+(i+1)+"' value='ans"+(j+1)+"' id='ans"+(j+1)+"_"+(i+1)+"'>"+
                    "<p class='answer answerStrings-"+(i+1)+"-"+(j+1)+"' id='answerStrings-"+(i+1)+"-"+(j+1)+"'>"+answerString+"</p>"+
                    "</label>" +
                    "</div>" +
                    "</div>";

            }
        }

        htmlStr +="</div>";

        if(sectionsPresent){
            // if the question doesn't have section, put it to first section

            if(qa[i].subject==null||qa[i].subject==''){
                qa[i].subject=document.getElementById("sectionSelection")[0].value
            }
            if(!sections[qa[i].subject.replace(/[^a-zA-Z0-9]/g, '')]) {
                sections[qa[i].subject.replace(/[^a-zA-Z0-9]/g, '')]=1;

            }
            else {
                sections[qa[i].subject.replace(/[^a-zA-Z0-9]/g, '')] +=1;
            }

            //removing the condition which was present till version 1.93 as it will not work for newly created divs. And also the situation for that shouldn't arise
            document.getElementById("question-" + qa[i].subject.replace(/[^a-zA-Z0-9]/g, '')).innerHTML += htmlStr;
            document.getElementById("grid-que-wrapper-" + qa[i].subject.replace(/[^a-zA-Z0-9]/g, '')).innerHTML += "<div class='que-no' id='grid_" + i + "'><a href='#question-" + (i + 1) + "' class='onclickScrolls'>" + (i + 1) + "</a></div>";

            document.getElementById("list-que-wrapper-" + qa[i].subject.replace(/[^a-zA-Z0-9]/g, '')).innerHTML += "<div class='onclickScrollsList d-flex align-items-center' id='#question-" + (i + 1) + "'>" + "<div class='d-flex align-self-start'>" + "<div class='que-no-list' id='list_" + i + "'>" +
                "<a>"
                + (i + 1) + "</a>" + "</div>" + "</div>" +
                "<div class='question'>" +
                "<p class='list-question-" + (i + 1) + "' id='list-question-" + (i + 1) + "'>" + questionString + "</p>" +
                "</div>" +
                "</div>";

            htmlStr="";
        }else{
            fhtmlStr += "<div class='que-no' id='grid_"+i+"'> <a class='onclickScrolls' href='#question-"+(i+1)+"'>"+ (i+1) +"</div>";
            listhtmlStr += "<div class='d-flex align-items-center mt-2 onclickScrollsList' id='#question-"+(i+1)+"'>"+
                "<div class='d-flex align-self-start mt-2'>" +"<div class='que-no-list' id='list_"+i+"'><a class=''>"+ (i+1) + "</a></div>"+"</div>"+
                "<div class='question'>"+
                "<p class='list-question-"+(i+1)+"' id='list-question-"+(i+1)+"'>"+questionString+"</p>"+
                "</div>"+
                "</div>";
        }


    }

    if(quizSource !='app') {
        htmlStr += "<div class='mt-2 pb-2 text-center'><button type='button' class='btn btn-starts' onclick='javascript:submitTest()'>Submit</button></div>";

    }

    if(sectionsPresent){
        if(previousSubject!=null) $("#question-"+previousSubject).hide();
        previousSubject = document.getElementById("sectionSelection")[document.getElementById("sectionSelection").selectedIndex].value.replace(/[^a-zA-Z0-9]/g, '');


        //closing the grid and list
        for (i = 0; i < examDtl.length; i++) {

            document.getElementById("grid-"+examDtl[i].subject.replace(/[^a-zA-Z0-9]/g, '')).innerHTML+= "</div>"+"</div>"+"</div>";
            document.getElementById("list-"+examDtl[i].subject.replace(/[^a-zA-Z0-9]/g, '')).innerHTML+="</div>";
        }
        $("#question-"+previousSubject).show();
        $("#grid-"+previousSubject).show();
        $("#list-"+previousSubject).show();
        document.getElementById("totalQuestions").innerHTML=sections[previousSubject];

    }else{
        document.getElementById("question-block").innerHTML=htmlStr;
        fhtmlStr +="</div>"+"</div>"+"</div>";
        document.getElementById("grid").innerHTML="<div id='accordion'>"+
            "<a class='card-link d-none' data-toggle='collapse' href='#collapseOne'>All Questions</a>"+
            "<div class='progress d-none'>"+
            "<div class='progress-bar' role='progressbar' aria-valuenow='70' aria-valuemin='0' aria-valuemax='100' style='width:70%'>"+
            "<span class='sr-only'>70% Complete</span>"+
            "</div>"+
            "</div>"+
            "<div id='collapseOne' class='collapse show' data-parent='#accordion'>"+
            "<div class='que-wrapper'>"+fhtmlStr;
        document.getElementById("list").innerHTML= "<div class='que-wrapper'>"+listhtmlStr+ "</div>";
        document.getElementById("totalQuestions").innerHTML=qa.length;
    }

}
$(document).delegate('.onclickScrolls','click',  function(e){
    e.preventDefault();
    var id = $(this).attr('href');
    $('html, body').animate({
        scrollTop: $(id).offset().top - 250
    }, 0);

    if($(window).width() < 768) {
        closesideNav();
    }
    return false;
});



$(document).delegate('.onclickScrollsList','click',  function(e){
    e.preventDefault();
    var id = $(this).attr('id');
    $('html, body').animate({
        scrollTop: $(id).offset().top - 250
    }, 0);

    if($(window).width() < 768) {
        closesideNav();
    }
    return false;
});

if((quizSource !='app')) {
    $(document).delegate('.viewsolution', 'click', function (e) {
        $('html, body').animate({
            scrollTop: ($('#scrolltoTab').offset().top) - 250
        }, 0);
        return false;
    });
}
function createMCQ(data,isPassage,passage,cList,name,examMaster,examDetail,testSeriesInput,useOtherLang,source,activityType){
    testSeries = testSeriesInput;
    qa = data;
    var htmlStr="";
    var questionString="";
    var optionString="";
    chaptersList=cList;
    nameoftheuser=name;
    pauseBtn.disabled=false;
    if(useOtherLang) testInOtherLang=true;
    quizSource=source;
    quizActivityType=activityType;
    if(isPassage){

        htmlStr += "<div class='container'>\n" +
            "<p class='direction-passage'>\n" +
            "                "+passage+"\n" +
            "            </p>" +
            "</div>"


    }

    // check if sections are present
    if(examDetail!=null&&!examDetail==""&&examDetail.length>0) {
        examDtl = examDetail;
        var select = document.getElementById('sectionSelection');
        select.options.length = 0;
        for (i = 0; i < examDtl.length; i++) {
            //add the correct thingy also
            examDtl[i].skipped=0;
            examDtl[i].correctAnswers=0;
            examDtl[i].wrongAnswers=0;
            var el = document.createElement("option");
            el.textContent = examDtl[i].subject;
            el.value = examDtl[i].subject;
            select.appendChild(el);

            document.getElementById("question-block").innerHTML +="<div id='question-"+examDtl[i].subject.replace(/[^a-zA-Z0-9]/g, '')+"' style='display:none'></div>";
            document.getElementById("grid").innerHTML +="<div id='grid-"+examDtl[i].subject.replace(/[^a-zA-Z0-9]/g, '')+"' style='display:none'>"+
                "<div id='accordion'>"+
                "<a class='card-link' data-toggle='collapse' href='#collapseOne'>"+examDtl[i].subject+"</a>"+
                "<div class='progress'>"+
                "<div class='progress-bar' role='progressbar' aria-valuenow='70' aria-valuemin='0' aria-valuemax='100' style='width:70%'>"+
                "<span class='sr-only'>70% Complete</span>"+
                "</div>"+
                "</div>"+
                "<div id='collapseOne' class='collapse show' data-parent='#accordion'>"+
                "<div class='que-wrapper' id='grid-que-wrapper-"+examDtl[i].subject.replace(/[^a-zA-Z0-9]/g, '')+"'>";

            document.getElementById("list").innerHTML +="<div id='list-"+examDtl[i].subject.replace(/[^a-zA-Z0-9]/g, '')+"' style='display:none'>"+
                "<div class='que-wrapper' id='list-que-wrapper-"+examDtl[i].subject.replace(/[^a-zA-Z0-9]/g, '')+"'>"+"</div>";

        }
        $("#sectionSelectionDiv").show();
        sectionsPresent=true;
    }
    else{
        $("#sectionSelectionDiv").hide();
        sectionsPresent=false;
    }
    displayQuestions();
    if(examMaster!=null&&!examMaster==""){
        examMst=examMaster;
        if(testSeries=="true") {
            testSeriesQuiz = true;
            pauseBtn.disabled=true;
            if(quizSource=='web') {
                totalTime(totalTestTime);
            }
            if(sectionsPresent&&examDtl[0].totalTime!=null&&!examDtl[0].totalTime=="") document.getElementById("sectionSelection").disabled=true;
        }
        if(sectionsPresent&&examDtl[0].totalTime!=null&&!examDtl[0].totalTime=="" ) {
            changeWholeTime(examDtl[0].totalTime*60);
            startTimer();

        }else{
            if(examMaster.totalTime!=null&&!examMaster.totalTime==""){
                changeWholeTime(examMaster.totalTime*60);
                startTimer();
            }
        }

    }
    initializeForCalculation();
    startTime = new Date();
}
function totalTime(totalTime){
    $('.total-time-wrapper').show();
    $('.normal-time').hide();
    if((examMst.noOfSections>=1)){
        $('.sub-header').addClass('section-header');
    }
    if((examMst.noOfSections===null)||(examMst.noOfSections>=1)) {
        document.querySelector('.tot-time-text').textContent='Section Time Left';
        changeTotalTime(totalTime);
        startTotalTimer();
    }
    else{
        document.querySelector('.total-time-wrapper').remove();
        document.querySelector('.timeLeft').textContent='Total Time Left';
    }

}
function initializeForCalculation(){
    //stuff required for analysis
    subjects=[];
    subjectDetails=[];
    userDataTable="";
    userSuggestions="";

    //check if this test generator thingy
    if(chaptersList){

        for(var index = 0; index < qa.length; ++index) {
            for (l = 0; l < chaptersList.length; l++) {
                if(qa[index].quizId==chaptersList[l].quizId){
                    qa[index].subject = chaptersList[l].subject;
                    qa[index].chapterId = chaptersList[l].id;
                    break;

                }
            }
        }
    }else{

    }
    for(var index = 0; index < qa.length; ++index) {
        if(qa[index].subject) {
            subjectKey = qa[index].subject.replace(/\W+/g, '');

            if (subjects.indexOf(qa[index].subject) < 0) {
                subjects.push(qa[index].subject);
                var subjectDetail = {};
                subjectDetail.noOfQuestions = 1;
                subjectDetail.correctAnswers = 0;
                subjectDetail.chapters={};
                //add the chapter information
                var chapter = {};
                chapter.id=qa[index].chapterId;
                chapter.noOfQuestions = 1;
                chapter.correctAnswers = 0;
                chapter.wrongAnswers = 0;
                chapter.skipped=0;
                if(chaptersList){
                    chapter.name = getChapterName(chaptersList,qa[index].chapterId);
                    subjectDetail.chapters[''+qa[index].chapterId] = chapter;
                }else{
                }


                subjectDetails[subjectKey] = subjectDetail;
            }
            else {
                //already added
                subjectDetails[subjectKey].noOfQuestions++;
                if(subjectDetails[subjectKey].chapters[''+qa[index].chapterId]==undefined) {

                    //if the chapter doesnt exist
                    var chapter = {};
                    chapter.id = qa[index].chapterId;
                    chapter.noOfQuestions = 1;
                    chapter.correctAnswers = 0;
                    chapter.wrongAnswers = 0;
                    chapter.skipped=0;
                    if (chaptersList) {
                        chapter.name = getChapterName(chaptersList, qa[index].chapterId);
                        subjectDetails[subjectKey].chapters[''+qa[index].chapterId] = chapter;
                    }

                }else{
                    //if the chapter already exists
                    subjectDetails[subjectKey].chapters[''+qa[index].chapterId].noOfQuestions++;
                }

            }
        }
    }
}

function answerSelected(i,j) {
    if(document.getElementById('ans'+(j+1)+'_'+(i+1)).checked) {
        $('.opt-str-'+(i)).removeClass('active');
        $('#ans'+(j+1)+'_'+(i+1)).parents('.options-string').addClass('active');
        $("#list_"+i).addClass('answered');
        $("#grid_"+i).addClass('answered');

    }
    else{
        $('.opt-str-'+(i)).removeClass('active');
        $("#list_"+i).removeClass('answered');
        $("#grid_"+i).removeClass('answered');
    }

}

$(document).delegate('#question-block input[type="radio"]','click',  function(e) {
    var radio=$(this);
    uncheckRadio(radio);
});
function uncheckRadio(radio) {
    if(radio.prop('checked'))
        radio.one('click', function(){ radio.prop('checked', false); } );
}
function mcqLoaders(){

}

function submitForm(){
    mcqLoaders();
    if ((quizSource == 'app')) {
        if(android) {
            JSInterface.showSubmitLoader();
            JSInterface.quizAttempted(true);
        }
        else{
            webkit.messageHandlers.showLoader.postMessage('');
        }
    }

    testSubmitted=true;
    $('#force-submit-test').modal('hide');
    $('.btn.submit').attr("disabled", true);
    $('#submit-test').modal('hide');
    if(quizSource=="web"){
        getAnswers();
    }else {
        scoreAndShowAnswersMCQ(false);
    }
    $("#question-block").hide();
    $("#answer-block").show();
    $('.sub-header').hide();
    $('.result-menu').show();
    // $('#changeLanguage1').hide();
    $('.web-mcq .que-side-menu').hide();
    window.scrollTo(0, 0);
    // $("html, body").animate({ scrollTop: 0 }, "fast");
    if (quizActivityType === "Practice"&&quizSource=="app"){
        updateQuizAnswers();
    }
    else if (quizActivityType === "MCQ"&&quizSource=="app") {
        updateRevisionQuizAnswers();
    } else if(quizActivityType === "assignments"){
        updateAssignment();
    }
    else if (quizActivityType === "QA"&&quizSource=="app") {
        updateQA();
    }
    $('.quiz-related').show();
    if(siteId==3 || siteId==37){
        $("#resourceTitle").hide();
    }
    return false;

}

function updateQuizAnswers() {
    if(android) {
        JSInterface.sendToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped,
            score.totalQuesions, (endTime - startTime), score.marks);
    }
    else {
        sendToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped, score.totalQuesions, (endTime - startTime), score.marks);
    }
}

function updateRevisionQuizAnswers() {
    if(android) {
        JSInterface.updateRevisionMCQToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped,
            score.totalQuesions, (endTime - startTime), score.marks);
    }
    else {
        updateRevisionMCQToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped,
            score.totalQuesions, (endTime - startTime), score.marks);
    }
}

function updateAssignment() {
    if(android) {
        JSInterface.updateAssignmentToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped,
            score.totalQuesions, (endTime - startTime), score.marks);
    }
    else{
        updateAssignmentToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped,
            score.totalQuesions, (endTime - startTime),score.marks);
    }
}

function updateQA() {
    if (android) {
        JSInterface.updateQAToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped,
            score.totalQuesions, (endTime - startTime), score.marks);
    } else {
        updateQAToServer(JSON.stringify(userAnswers), score.correctAnswers, score.wrongAnswers, score.skipped,
            score.totalQuesions, (endTime - startTime), score.marks);
    }
}

function answeredUnanswered(){
    var skipped=0;
    var answered=0;
    var markedForReview=0;
    for (var index = 0; index < qa.length; ++index) {
        if(document.getElementById("bookmarkbtn-"+(index+1)).checked) markedForReview++;
        if ($('#ans1_' + (index + 1)).is(":checked") || $('#ans2_' + (index + 1)).is(":checked") || $('#ans3_' + (index + 1)).is(":checked") || $('#ans4_' + (index + 1)).is(":checked") || $('#ans5_' + (index + 1)).is(":checked")) {
            answered++;

        } else {
            skipped ++;
        }

    }
    document.getElementById("answered").innerHTML="<span>"+answered+"</span>";
    document.getElementById("unanswered").innerHTML="<span>"+(qa.length-answered)+"</span>";
    document.getElementById("review").innerHTML="<span>"+markedForReview+"</span>";
    document.getElementById("noOfQuestions").innerHTML="(Total Questions:"+qa.length+")";


}
function calculateScore(){
    var htmlStr="";
    var questionString="";
    var optionString="";
    var answers=qa;
    if(!resultsMode) {
        for (var index = 0; index < qa.length; ++index) {
            if ($('#ans1_' + (index + 1)).is(":checked") || $('#ans2_' + (index + 1)).is(":checked") || $('#ans3_' + (index + 1)).is(":checked") || $('#ans4_' + (index + 1)).is(":checked") || $('#ans5_' + (index + 1)).is(":checked")) {
                userAnswers[index] = {
                    'skipped': 'false',
                    'ans1': $('#ans1_' + (index + 1)).is(":checked") ? 'Yes' : null,
                    'ans2': $('#ans2_' + (index + 1)).is(":checked") ? 'Yes' : null,
                    'ans3': $('#ans3_' + (index + 1)).is(":checked") ? 'Yes' : null,
                    'ans4': $('#ans4_' + (index + 1)).is(":checked") ? 'Yes' : null,
                    'ans5': $('#ans5_' + (index + 1)).is(":checked") ? 'Yes' : null
                };

            } else {
                userAnswers[index] = {
                    'skipped': 'true',
                    'ans1': null,
                    'ans2': null,
                    'ans3': null,
                    'ans4': null,
                    'ans5': null
                }
            }

        }
    }

    var length = userAnswers.length;
    var isIncorrect = false;
    score.correctAnswers = 0;
    score.wrongAnswers=0;
    score.skipped=0;
    score.totalQuesions=qa.length;
    score.marks = 0;

    for(var i = 0; i < length; ++i){
        userAnswers[i]['correctAnswer']="false";
        userAnswers[i]['id']=qa[i]['id'];
        for(var key in userAnswers[i]){
            //removed the simple check and added a more complicated one, as in some browsers it was evaluating correctly
            // this current if condition is checking if both or yes or if both are not yes... if one of them of them is true.. then its not error.
            if(userAnswers[i].skipped=="true"){
                isIncorrect = true;
                score.skipped++;
                if(qa[i].subject) {
                    if(sectionsPresent){
                        for(l=0;l<examDtl.length;l++){
                            if(qa[i].subject==examDtl[l].subject){
                                examDtl[l].skipped ++;
                                break;
                            }
                        }
                    }
                    var subjectDetail = subjectDetails[qa[i].subject.replace(/\W+/g, '')];
                    var chapterKeys = Object.keys(subjectDetail.chapters);

                    if(qa[i].chapterId){
                        for(k=0;k<chapterKeys.length;k++){
                            var chapter = subjectDetail.chapters[''+chapterKeys[k]];
                            if(chapter.id==qa[i].chapterId){
                                chapter.skipped++;
                                break;
                            }
                        }
                    }
                }
                break;
            }
            else if(!((((""+userAnswers[i][key]) == 'Yes') &&((""+qa[i][key])=='Yes')) || (((""+qa[i][key]) != 'Yes') &&((""+qa[i][key])!='Yes')))){
                isIncorrect = true;
                score.wrongAnswers++;
                //logic to get the negative marks
                //first to check marks for individual questions
                if(answers[i].negativeMarks!=null){
                    score.marks = score.marks-answers[i].negativeMarks;
                    scoredTest=true;
                }else {
                    // second check at the subject/section level
                    var marksFound=false;
                    if(examDtl!=null){

                        for(l=0;l<examDtl.length;l++){
                            if(examDtl[l].subject==answers[i].subject&&examDtl[l].wrongAnswerMarks!=null){
                                score.marks = score.marks-examDtl[l].wrongAnswerMarks;
                                marksFound=true;
                                scoredTest=true;
                                break;
                            }
                        }
                    }
                    // check at the exam level
                    if(!marksFound&&examMst!=null){
                        if(examMst.wrongAnswerMarks!=null){
                            score.marks = score.marks-examMst.wrongAnswerMarks;
                            scoredTest=true;
                        }
                    }
                }
                if(qa[i].subject) {
                    if(sectionsPresent){
                        for(l=0;l<examDtl.length;l++){
                            if(qa[i].subject==examDtl[l].subject){
                                examDtl[l].wrongAnswers ++;
                                break;
                            }
                        }
                    }
                    var subjectDetail = subjectDetails[qa[i].subject.replace(/\W+/g, '')];
                    var chapterKeys = Object.keys(subjectDetail.chapters);

                    if(qa[i].chapterId){
                        for(k=0;k<chapterKeys.length;k++){
                            var chapter = subjectDetail.chapters[''+chapterKeys[k]];
                            if(chapter.id==qa[i].chapterId){
                                chapter.wrongAnswers++;
                                break;
                            }
                        }
                    }
                }
                break;
            }

        }
        if(!isIncorrect){
            score.correctAnswers++;
            //logic to get the correct marks
            //first to check marks for individual questions
            if(answers[i].marks!=null){
                score.marks = score.marks+answers[i].marks;
                scoredTest=true;
            }else {
                // second check at the subject/section level
                var marksFound=false;
                if(examDtl!=null){

                    for(l=0;l<examDtl.length;l++){
                        if(examDtl[l].subject==answers[i].subject&&examDtl[l].rightAnswerMarks!=null){
                            score.marks = score.marks+examDtl[l].rightAnswerMarks;
                            scoredTest=true;
                            marksFound=true;
                            break;
                        }
                    }
                }
                // check at the exam level
                if(!marksFound&&examMst!=null){
                    if(examMst.rightAnswerMarks!=null){
                        score.marks = score.marks+examMst.rightAnswerMarks;
                        scoredTest=true;

                    }
                }
            }
            userAnswers[i].correctAnswer="true";
            if(qa[i].subject) {
                if(sectionsPresent){
                    for(l=0;l<examDtl.length;l++){
                        if(qa[i].subject==examDtl[l].subject){
                            examDtl[l].correctAnswers ++;
                            break;
                        }
                    }
                }
                var subjectDetail = subjectDetails[qa[i].subject.replace(/\W+/g, '')];
                subjectDetail.correctAnswers++;
                var chapterKeys = Object.keys(subjectDetail.chapters);

                if(qa[i].chapterId){
                    for(k=0;k<chapterKeys.length;k++){
                        var chapter = subjectDetail.chapters[''+chapterKeys[k]];
                        if(chapter.id==qa[i].chapterId){
                            chapter.correctAnswers++;
                            break;
                        }
                    }
                }
            }
        }
        isIncorrect = false;
    }

    document.getElementById("question-block").innerHTML="";

}


function scoreMessage(score,length){

    var msgStr;
    var imgSrc = "";
    var titleSrc="";
    var comment="";

    endTime =  new Date();
    percent = (score.correctAnswers/length)*100;
    accuracy = 0;
    timeTaken = (endTime - startTime);
    noOfQuestionsAttempted = (score.correctAnswers+score.wrongAnswers);

    noOfQuestionsPerHour = 0;
    if(noOfQuestionsAttempted>0)   noOfQuestionsPerHour = 3600 / ((timeTaken/1000)/noOfQuestionsAttempted);

    if(score.correctAnswers > 0) {
        accuracy = (score.correctAnswers/(score.correctAnswers+score.wrongAnswers))*100;
    }

    if(percent > 90) {
        titleSrc="Bravo, well done";
        comment="Fancy another quiz?";
        imgClass="gold";

    }
    else if(percent > 70) {
        titleSrc="Good.";
        comment="Do you think you can do better? Let's try again!";
        imgClass="silver";
    }
    else if(percent > 40) {
        titleSrc="That's Ok.";
        comment="You can do better. Lets practice more.";
        imgClass="bronze";

    }
    else  {

        titleSrc="That's Ok.";
        comment="You need more practice.";
        imgClass="medalnone";
    }
    msgStr = "<div class='medal-picture'>"+
        "<div class='"+  imgClass +"'></div>" +
        "</div>"+
        "<div class='practice-score-string'>"+
        "<p>"+titleSrc+"</p>"+
        "<p>"+comment+"</p>"+
        "<div class='d-flex justify-content-center mt-4'>"+
        "<div class='d-flex board align-items-center'>";
    if(scoredTest) {
        msgStr += "<div class='score'><div class='notes-assignent'></div> </div>" +
            "<div>" +
            "<p>SCORE</p>" +
            "<p class='practice-score-string-score'>" + parseFloat(""+score.marks).toFixed(2) + ((examMst != null) ? ("/" + examMst.totalMarks) : "") + "</p>" +
            "</div>" +
            "</div>" +


            "</div>";
    }
    msgStr +=         "</div>"+
        "</div>"+
        "</div>";
    msgStr = "<div class='practice-score-container'>" +
        "<div class='practice-score'>"+msgStr+"</div>"+
        "</div>"+
        "<div class='answer-summary'>"+
        "<div class='d-flex justify-content-around'>"+
        "<div class='dummy'></div>"+
        "<div>"+
        "<p class='summary-heading'>"+"Summary"+"</p>"+
        "<p class='short-heading'>"+"(Total Questions: "+"<span class='summary-heading'>"+length+"</span>"+")"+"</p>"+
        "</div>"+
        "<div>"+
        "<a href='#' class='share'></a>"+
        "</div>"+
        "</div>"+

        "<div class='score-summary d-flex justify-content-around'>"+
        "<div class=''>"+
        "<p>Correct<p>"+
        "<div class='correct-answers'>"+score.correctAnswers+"</div>"+
        "</div>"+
        "<div class=''>"+
        "<p>Incorrect<p>"+
        "<div class='wrong-answers'>"+score.wrongAnswers+"</div>"+
        "</div>"+
        "<div class=''>"+
        "<p>Skipped<p>"+
        "<div class='skipped-answers'>"+score.skipped+"</div>"+
        "</div>"+
        "</div>";
    if(!resultsMode) {
        msgStr += "<div class='accuracy-summary d-flex justify-content-around'>" +
            "<div>" +
            "<p class='time-taken'>" + "</p>" +
            "<div>" +
            msToTime((endTime - startTime)) +
            "<p>Time</p>" +
            "</div>" +
            "</div>" +
            "<div>" +
            "<p class='answer-accuracy'>" + "</p>" +
            "<div>" +
            "<span class='time-span'>" + Math.round(accuracy) + "%" + "</span>" +
            "<p>Accuracy</p>" +
            "</div>" +
            "</div>" +
            "<div>" +
            "<p class='total-hours'>" + "</p>" +
            "<div>" +
            "<span class='time-span'>" + Math.round(noOfQuestionsPerHour) + "</span>" +
            "<p>Q/HR</p>" +
            "</div>" +
            "</div>" +
            "</div>";
    }

    msgStr +=               "</div>"+
        "</div>"+
        "</div>"+
        "</div>" ;


    if(sectionsPresent){
        msgStr += "<div class='analysis'>";
        msgStr +=  "<div>"+
            "<h2>Section-wise Analysis</h2>"+
            "<ul class='nav nav-tabs' role='tablist'>";
        for(l=0;l<examDtl.length;l++){
            msgStr +=
                "<li class='nav-item'>"+
                "<a class='nav-link"+((l==0)?" active":"")+"' data-toggle='tab' href='#tab"+l+"'>"+examDtl[l].subject+"</a>"+
                "</li>";

        }
        msgStr +="</ul>"+
            "</div>"+
            "<div class='tab-content'>";
        //tab content

        for(l=0;l<examDtl.length;l++){

            var  marksData = [];
            marksData.unshift(['Skipped', examDtl[l].skipped,'color: #F2C94C']);
            marksData.unshift(['Wrong', examDtl[l].wrongAnswers,'color: #B72319']);
            marksData.unshift(['Correct', examDtl[l].correctAnswers,'color: #46B520']);
            marksData.unshift(['', '', { role: 'style' }]);

            scoreHolder[l]=marksData;

            msgStr += "<div id='tab"+l+"' class='container tab-pane "+((l==0)?" active":"")+"' >"+
                " "+l+"</div>";
        }

        msgStr += "</div>"+
            "</div>";

    }else{
        msgStr += "<div class='analysis'>";
        msgStr +=  "<div>"+
            "<h2>Graphic Analysis</h2>"+
            "<div id='graphicAnalysis'></div>"+
            "</div></div>";

    }
    if(testSeriesQuiz&&!resultsMode) {
        if (testResultDate != null && testResultDate != '') {
            msgStr += "<div class='rank-wrapper button-wrapper'>" +
                "<div class='medal'></div>" +
                "<p class='rankText'>Results will be declared on</p>" +
                "<p class='dateText'>" + testResultDate + "</p>" +
                "</div>";
        } else if (testEndDate != null && testEndDate != '') {

            msgStr += "<div class='rank-wrapper button-wrapper'>" +
                "<div class='medal'></div>" +
                "<p class='rankText'>Test will end on</p>" +
                "<p class='dateText'>" + testEndDate + "</p>" +
                "</div>";
        }
    } else {
        if(quizSource !='app') {
            msgStr += "<div class='button-wrapper d-flex justify-content-between'>";
            if(siteId != 34 && siteId != 21 && siteId != 22){
              msgStr += "<a href=\"javascript:window.location.reload();\">Retry</a>";
            }

            if ("libwonder" != defaultSiteName && siteId != 34 && siteId != 21 && siteId != 22 && siteId != 3 && siteId != 37) {
                if(loggedInUser) {
                    msgStr += "<a href='/test-generator' id='resultsCreateTestBtn'>Create Test</a>";
                }
            }
            msgStr +="<a class='viewsolution' onclick='javascript:solution()'>Solutions</a>" +
                "</div>";
        }
        else if(quizSource =='app'){
            msgStr += "<div class='button-wrapper'>" +
                "" + "<a class='viewsolution'>Solutions</a>" +
                "</div>";
        }
    }

    google.charts.load('current', {'packages':['corechart']});
    google.charts.setOnLoadCallback(drawChart);

    var suggestion="";
    if(percent==100){
        suggestion+="You have aced it. Great job. Continue with the same focus to do well in your exams. ";
    }
    else if(percent>90){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Try little harder and you can get that 100. ";
    }
    else if(percent>80){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Little more effort and practice can definitely take you higher. ";
    }else if(percent>60){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Keep practicing more and you will definitely get better at this. ";
    }
    else if(percent>40){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Keep practicing more and you will definitely get better at this. Go through the concepts again and understand better. ";
    }else{
        suggestion+="You have got "+Math.round(percent)+"% right answers. No need to lose heart. Learn the concepts again, understand it better. By understanding the concepts and practicing you can definitely get better. ";
    }

    if(noOfQuestionsPerHour > 60){
        suggestion+="You are answering the quiz very fast. Pace your answering to improve your performance. Since you are able to answer the questions relatively fast, spend the remaining time " +
            "to review your answers. ";
    }else  if(noOfQuestionsPerHour < 40){
        suggestion+= "You are answering the quiz slowly. Pace your answering to improve your performance. Attempt the questions which you know the answer first and then try the ones which you " +
            "are not sure. ";
    }else{
        suggestion+= "You answering pace is good. " ;
    }

    if(accuracy<80){
        suggestion+="You accuracy is "+Math.round(accuracy)+" percent. Number of wrong answers will have reduce your overall score due to negative marking. Do not attempt the question if you are not sure of the answer. "
        ;
    }else if(accuracy>79&&accuracy<91){
        suggestion+= "You accuracy is "+Math.round(accuracy)+" percent.You are doing good. Number of wrong answers will have reduce your overall score due to negative marking. Do not " +
            "attempt the question if you are not sure of the answer. "
        ;
    }else if(accuracy>90&&accuracy<100){
        suggestion+= "You accuracy is "+Math.round(accuracy)+" percent.You are doing great.Keep going, you can get to 100 percent. "
        ;
    }else if(accuracy==100){
        suggestion+= "Well done in terms of accuracy. All your attempted answers are correct. "
        ;
    }




    if(nameoftheuser){
        suggestion = "Hey "+nameoftheuser+", "+suggestion;
    }
    userSuggestions = "<div class='suggestions-for-user'>" +
        "<p class='row-heading'>"+ "Suggestions" + "</p>" +
        "<hr class='collapse-hr'>" +
        "<p class='suggested-topics'>" + suggestion+ "</p>"+
        "</div>"+
        "<div class='div-separator'></div>";

    userDataTable = "<div class='col-xs-12 table-div' id='user-data-table' style='display: none;'>" +
        "<a href='javascript:hideUserDataTable();'>"+"<i class='icon-back'></i> Back"+"</a>"+
        "<table class='tabel table-responsive tabel-striped table-bordered detailed-table'>" +
        "<thead>"+
        "<tr>" +
        "<th class='table-chapters'>" + "Chapter" + "</th>"+
        "<th>" + "Total Questions" + "</th>"+
        "<th>" + "Skipped" + "</th>"+
        "<th class='correct'>" + "Correct" + "</th>"+
        "<th class='incorrect'>" + "Incorrect" + "</th>"+
        "</tr>" +
        "</thead>" +
        "<tbody>";
    for (i = 0; i < subjects.length; i++) {
        var subjectDetail = subjectDetails[subjects[i].replace(/\W+/g, '')];
        var chapterKeys = Object.keys(subjectDetail.chapters);
        for(j=0;j<chapterKeys.length;j++) {
            userDataTable += "<tr>" +
                "<td class='table-chapters-data'>" + subjectDetail.chapters[''+chapterKeys[j]].name + "</td>"+
                "<td>" +  subjectDetail.chapters[''+chapterKeys[j]].noOfQuestions + "</td>"+
                "<td>" +  subjectDetail.chapters[''+chapterKeys[j]].skipped + "</td>"+
                "<td class='correct'>" + subjectDetail.chapters[''+chapterKeys[j]].correctAnswers + "</td>"+
                "<td class='incorrect'>" +  subjectDetail.chapters[''+chapterKeys[j]].wrongAnswers + "</td>"+
                "</tr>";

        }
    }

    userDataTable +=    "</tbody>"+
        "</div>";

    return msgStr;



}

function drawChart(){
    var options = {
        vAxis: {format: 'decimal'},
        bar: {groupWidth: "61.8%"},
        legend: {position: "none"},
        height: 200,
        width: "90%",

    };

    if(sectionsPresent) {
        for (l = 0; l < examDtl.length; l++) {
            var data = google.visualization.arrayToDataTable(scoreHolder[l]);



            var chart = new google.visualization.ColumnChart(document.getElementById('tab' + l));
            chart.draw(data, options);

        }
    }else{
        var  marksData = [];
        marksData.unshift(['Skipped', score.skipped,'color: yellow']);
        marksData.unshift(['Wrong', score.wrongAnswers,'color: red']);
        marksData.unshift(['Correct', score.correctAnswers,'color: green']);
        marksData.unshift(['', '', { role: 'style' }]);

        var data = google.visualization.arrayToDataTable(marksData);
        var chart = new google.visualization.ColumnChart(document.getElementById('graphicAnalysis'));
        chart.draw(data, options);
    }
}
function msToTime(duration) {
    var milliseconds = parseInt((duration%1000)/100),
        seconds = parseInt((duration/1000)%60),
        minutes = parseInt((duration/(1000*60))%60),
        hours = parseInt((duration/(1000*60*60))%24);
    var timeStr="";


    if(hours>0) timeStr="<span class='time-span'>"+hours+"h"+"</span>";
    if(minutes>0) timeStr+="<span class='time-span'>"+minutes+"m"+"</span>";
    timeStr +="<span class='time-span'>"+seconds+"."+milliseconds+"s"+"</span>";

    return timeStr;
}

function scoreAndShowAnswersMCQ(readMode,data,isPassage,passage,source,useOtherLang){
    var msgStr ="";
    if(useOtherLang) {
        testInOtherLang=true;
        if(testInOtherLang) {
            $(".result-menu").show();
            window.scrollTo(0, 0);
            $('.language').toggleClass('rotate');
        }
    }

    if(!readMode||resultsMode) {
        if(resultsMode){
            qa=data;
            chaptersList="";
            quizSource=source;
            initializeForCalculation();

        }
        calculateScore();
        msgStr = scoreMessage(score, qa.length);

    }else{
        qa=data;
        chaptersList="";
        quizSource=source;
    }
    if(testSeriesQuiz) {

        document.getElementById("answer-block").innerHTML=msgStr;
    }
    else {
        var htmlStr = "";
        var skipStr="";
        var htmlStrAll = "";
        var questionString = "";
        var labelClass = "";
        var answerClass = "";
        var answer = "";
        var explainLink = "";
        var htmlStringArray = [];

        if (isPassage) {
            htmlStr += "<div class='row container'>\n";
            if (!"true" == appInApp) {
                htmlStr += "<pre class='col-xs-12 directions container'>\n" +
                    "            </pre>\n" +
                    "            </div>";

            } else {
                htmlStr += "<pre class='col-xs-12 directions container'>\n" + "<br>\n" +
                    "            </pre>\n" +
                    "            </div>";
            }
            htmlStr += "<div class='row container'>\n" +
                "                <div class='col-xs-12 passage container'>\n" +
                "                &nbsp;" + passage + "\n" +
                "            </div>\n" +
                "            </div>";
        }
        if (!readMode) {
            var correctStr='';
            var incorrectStr='';
            $('.ans-tab').show();
            $('.nav-tabs a[href="#alls"]').tab('show');
            document.getElementById("answer-block").innerHTML = msgStr + userSuggestions + htmlStr;
            var optionLabel = ["A", "B", "C", "D", "E"];
            for (var i = 0; i < qa.length; ++i) {
                htmlStrAll = "";
                labelClass = "correct-answer-by-user";
                answerClass = "correct-answer-by-user";
                answer = "";
                explainLink = "";
                if (!readMode) {
                    if ((userAnswers[i]['correctAnswer'] == "false" || userAnswers[i]['correctAnswer'] == "true") && userAnswers[i].skipped != "true") {
                        if (userAnswers[i]['correctAnswer'] == "false") {
                            labelClass = "wrong-answer-label";
                            answerClass = "wrong-answer-by-user";
                        }

                        //get the answers
                        for (var key in userAnswers[i]) {
                            if (("" + userAnswers[i][key]) == 'Yes') {
                                var answerString = eval("qa[" + i + "]." + eval("ansOptKeys." + key));
                                if (answerString) {
                                    if (answerString.indexOf("<p") == 0) {
                                        answerString = answerString.replace('<p>', '');
                                        answerString = answerString.replace('</p>', '');
                                        if (testInOtherLang && answerString.indexOf("~~") != -1) {
                                            //changing the language
                                            answerString = answerString.substr(answerString.indexOf("~~") + 2);
                                        } else if (answerString.indexOf("~~") != -1) {
                                            //getting the first  language
                                            answerString = answerString.substr(0, answerString.indexOf("~~"));
                                        }
                                    }
                                }
                                answer += answerString;
                            }
                        }

                    } else if (userAnswers[i].skipped == "true") {
                        labelClass = "wrong-answer-label";
                        answerClass = "wrong-answer-by-user";
                        answer = "Skipped";
                    }

                }
                explainLink = qa[i].explainLink;
                if((quizSource !='app')) {
                    if (("eutkarsh" == defaultSiteName)) {
                        if (explainLink) {
                            document.getElementById('explainLinks').style.display = 'block';
                        }
                    }
                }
                if((quizSource =='app')){
                    if(!wonderslate){
                        if (explainLink) {
                            document.getElementById('explainLinks').style.display = 'block';
                        }
                    }
                }
                var dirStr = '';


                questionString = qa[i].ps;
                if (testInOtherLang && questionString.indexOf("~~") != -1) {
                    //changing the language
                    questionString = questionString.substr(questionString.indexOf("~~") + 2);
                } else if (questionString.indexOf("~~") != -1) {
                    //getting the first  language
                    questionString = questionString.substr(0, questionString.indexOf("~~"));
                }
                questionString = questionString.replace("<p>", "");
                questionString = questionString.replace("</p>", "");
                if(qa[i].isValidAnswerKey && qa[i].isValidAnswerKey == "false"){
                    htmlStrAll += "<div class='question-box error-highlight with-less-with'>";
                }else{
                    htmlStrAll += "<div class='question-box  with-less-with'>"
                }

                htmlStrAll +="<div class='question container'>";
                if (qa[i].directions) {
                    dirStr = qa[i].directions;
                    if (testInOtherLang && dirStr.indexOf("~~") != -1) {
                        //changing the language
                        dirStr = dirStr.substr(dirStr.indexOf("~~") + 2);
                    } else if (dirStr.indexOf("~~") != -1) {
                        //getting the first  language
                        dirStr = dirStr.substr(0, dirStr.indexOf("~~"));
                    }
                }
                if (qa[i].directions) {
                    htmlStrAll += "<div class='row' style='margin:0;'>\n" +
                        "                <pre class='col-xs-12 directions container' id='directionss-"+(i+1)+"'>\n" +
                        "                &nbsp;" + dirStr + "\n" +
                        "            </pre>\n" +
                        "            </div>";
                }
                htmlStrAll +="<p class='que-no'>" + "Q" + (i + 1) + ".</p>" +
                    "<p class='question questions-"+(i+1)+"' id='questions-"+(i+1)+"'>" + questionString + "</p>" +
                    "</div>";


                //get right answers
                labelClass = "correct-answer-by-user";
                answerClass = "correct-answer";
                answer = "";
                var labelValue="";
                var containerClass="ans-neutral";
                var answerLetter="";

                for(var j=0;j< 5;++j) {
                    answerLetter=optionLabel[j];

                    var answerString = eval("qa[" + i + "]." + eval("ansOptKeys.ans" +(j+1) ));
                    if (answerString && (typeof answerString !== typeof undefined)) {
                        if (answerString.indexOf("<p") == 0) {
                            answerString = answerString.replace('<p>', '');
                            answerString = answerString.replace('</p>', '');
                        }
                        if (testInOtherLang && answerString.indexOf("~~") != -1) {
                            //changing the language
                            answerString = answerString.substr(answerString.indexOf("~~") + 2);
                        } else if (answerString.indexOf("~~") != -1) {
                            //getting the first  language
                            answerString = answerString.substr(0, answerString.indexOf("~~"));
                        }
                        if (("" + qa[i]["ans"+(j+1)]) == 'Yes') {
                            labelValue="Correct Answer";
                            labelClass = "correct-answer-by-user";
                            answerClass = "correct-answer";
                            if (!readMode) {
                                if (userAnswers[i]['correctAnswer'] == "true") {
                                    labelValue="Your Answer";
                                }
                                else if(userAnswers[i].skipped == "true"){
                                    labelValue="Skipped";
                                    labelClass="skipAnswer";
                                }
                            }

                            htmlStrAll +="<div class='your-answer ans-correct container'>" + "<div>" + "<p>" +
                                "  <span class=' " + labelClass + "'>" +labelValue+
                                "</span>" + "</p>";
                            htmlStrAll += "<p class='d-flex'>" +"<span class='choice'><span>"+answerLetter+"</span></span>"+
                                "  <span class='" + answerClass + "'>"+"<i class='material-icons'>check_circle</i>"+
                                "<span class='answerStrings-"+(i+1)+"-"+(j+1)+"' id='answerStrings-"+(i+1)+"-"+(j+1)+"'>"+ answerString +"</span>"+
                                "</span>" + "</p>" + "</div>" + "</div>";
                        }
                        else{
                            labelClass = "";
                            answerClass = "";
                            labelValue="";
                            containerClass="ans-neutral";
                            if (!readMode) {
                                if ((userAnswers[i]['correctAnswer'] == "false") && userAnswers[i].skipped != "true") {
                                    if (userAnswers[i]["ans"+(j+1)] == "Yes") {
                                        labelClass = "wrong-answer-label";
                                        answerClass = "wrong-answer-by-user";
                                        labelValue = "Your Answer";
                                        containerClass = "";
                                    }
                                }
                            }
                            htmlStrAll += "<div class='your-answer container "+containerClass+"'>" + "<div>" +

                                "<p>" +
                                "  <span class=' " + labelClass + "'>" +labelValue+

                                "</span>" + "</p>";
                            htmlStrAll += "<p class='d-flex'>" +"<span><span class='choice'>"+answerLetter+"</span></span>"+
                                "  <span class='" + answerClass + "'>"+"<i class='material-icons err'>error</i>"+
                                "<span class='answerStrings-"+(i+1)+"-"+(j+1)+"' id='answerStrings-"+(i+1)+"-"+(j+1)+"'>"+ answerString +"</span>"+
                                "</span>" + "</p>" + "</div>" + "</div>";
                        }
                        answer += answerString;
                    }



                }

                if (qa[i].answerDescription) {

                    qa[i].answerDescription = qa[i].answerDescription.replace('<p>', '');
                    qa[i].answerDescription = qa[i].answerDescription.replace('</p>', '');
                    var descriptionString=qa[i].answerDescription;
                    if (testInOtherLang && descriptionString.indexOf("~~") != -1) {
                        //changing the language
                        descriptionString = descriptionString.substr(descriptionString.indexOf("~~") + 2);
                    } else if (descriptionString.indexOf("~~") != -1) {
                        //getting the first  language
                        descriptionString = descriptionString.substr(0, descriptionString.indexOf("~~"));
                    }
                    var descriptionOfAnswer = "";
                    descriptionOfAnswer += "<div class='answer-explanation-row'><div class='container answer-description col-xs-12'>" +
                        "<p class='show-explanation'>" +
                        "<a class='show-explanation-btn' id='mys-" + (i + 1) + "' data-target='demos-" + (i + 1) + "'>" + "Explanation" + "</a>" +
                        "</p>" +
                        "<pre class='correct-answer-explanation demos-" + (i + 1) + "' id='demos-" + (i + 1) + "'>" +descriptionString + "</pre>" +

                        "</div>";
                } else {
                    descriptionOfAnswer = "";
                }

                htmlStrAll += descriptionOfAnswer + "</div></div>";
                if((quizSource !='app')) {
                    if (("eutkarsh" == defaultSiteName)) {
                        htmlStrAll += "</div>";
                    } else {
                        if (explainLink) {
                            if(siteId == 21)
                            {
                                htmlStrAll += "<div>"+"<p class='text-center pt-2' style='color:red'>Videos of this book are available in app only</p>"+"</div>";

                            }
                            else {
                                htmlStrAll += "<div>" + "<button onclick=\"javascript:showVideo('" + explainLink + "','" + qa[i].startTime + "','" + qa[i].endTime + "');\" class='showVideobtn'>" + "<i class='material-icons'>ondemand_video</i>" + "Video Explanation</button>" + "</div>"
                            }
                                +"</div>";

                        }
                    }
                }
                if((quizSource =='app')){
                    if (!wonderslate) {
                        htmlStrAll += "</div>";
                    } else if(wonderslate) {
                        if (explainLink) {
                            htmlStrAll += "<div>" + "<button onclick=\"javascript:showVideo('" + explainLink + "','" + qa[i].startTime + "','" + qa[i].endTime + "');\" class='showVideobtn'>" + "<i class='material-icons'>ondemand_video</i>" + "Video Explanation</button>" +

                                "</div></div>";

                        }
                    }
                }
                htmlStringArray.push(htmlStrAll);
            }
            //add all thingy
            document.getElementById('alls').innerHTML="";
            for(var i=0;i<htmlStringArray.length;i++){
                document.getElementById('alls').innerHTML +=htmlStringArray[i];
                if (userAnswers[i].skipped == 'true'){
                    skipStr +=htmlStringArray[i];
                }
                if (userAnswers[i]['correctAnswer'] == 'true') {
                    correctStr +=htmlStringArray[i];
                }
                if (userAnswers[i]['correctAnswer'] == 'false' && userAnswers[i].skipped != 'true') {
                    incorrectStr +=htmlStringArray[i];
                }


            }

            document.getElementById('incorrect').innerHTML = incorrectStr;
            document.getElementById('correct').innerHTML = correctStr;
            document.getElementById('skipped').innerHTML = skipStr;



            if($('#correct').is(':empty')){
                document.getElementById('correct').innerHTML='<p class="validateMsg">No Correct Answer</p>';
            }
            if($('#incorrect').is(':empty')){
                document.getElementById('incorrect').innerHTML='<p class="validateMsg">No Incorrect Answer</p>';
            }
            if($('#skipped').is(':empty')){
                document.getElementById('skipped').innerHTML='<p class="validateMsg">No Skipped Answer</p>';
            }
            htmlStr += "<div id='videoContent'>"+
                "<div class=\"modal fade\" id=\"videoModal\">\n" +
                "    <div class=\"modal-dialog  modal-dialog-centered\">\n" +
                "      <div class=\"modal-content\">\n" +
                "      \n" +
                "        <div class=\"modal-header\">\n" +

                "          <button type=\"button\" class=\"close\" data-dismiss=\"modal\">&times;</button>\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"modal-body\">\n" +
                "<div class='video-wrapper' id='videoUpdate'>" +
                "<iframe src='' frameborder='0' allow='accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture' allowfullscreen>" +
                "</iframe>" +
                "</div>" +
                "        </div>\n" +
                "        \n" +
                "      </div>\n" +
                "    </div>\n" +
                "  </div>" +
                "</div>";


        }

        if(readMode){
            var optionLabel=["A","B","C","D","E"];
            for (var i = 0; i < qa.length; ++i) {
                labelClass = "correct-answer-by-user";
                answerClass = "correct-answer-by-user";
                answer = "";
                explainLink = "";
                if (!readMode) {
                    if ((userAnswers[i]['correctAnswer'] == "false" || userAnswers[i]['correctAnswer'] == "true") && userAnswers[i].skipped != "true") {
                        if (userAnswers[i]['correctAnswer'] == "false") {
                            labelClass = "wrong-answer-label";
                            answerClass = "wrong-answer-by-user";
                        }

                        //get the answers
                        for (var key in userAnswers[i]) {
                            if (("" + userAnswers[i][key]) == 'Yes') {
                                var answerString = eval("qa[" + i + "]." + eval("ansOptKeys." + key));
                                if (answerString) {
                                    if (answerString.indexOf("<p") == 0) {
                                        answerString = answerString.replace('<p>', '');
                                        answerString = answerString.replace('</p>', '');
                                        if (testInOtherLang && answerString.indexOf("~~") != -1) {
                                            //changing the language
                                            answerString = answerString.substr(answerString.indexOf("~~") + 2);
                                        } else if (answerString.indexOf("~~") != -1) {
                                            //getting the first  language
                                            answerString = answerString.substr(0, answerString.indexOf("~~"));
                                        }
                                    }
                                }
                                answer += answerString;
                            }
                        }

                    } else if (userAnswers[i].skipped == "true") {
                        labelClass = "wrong-answer-label";
                        answerClass = "wrong-answer-by-user";
                        answer = "Skipped";
                    }

                }
                explainLink = qa[i].explainLink;
                if((quizSource !='app')) {
                    if (("eutkarsh" == defaultSiteName)) {
                        if (explainLink) {
                            document.getElementById('learn-link').style.display = 'block';
                        }
                    }
                }
                if((quizSource =='app')){
                    if(!wonderslate){
                        if (explainLink) {
                            document.getElementById('learn-link').style.display = 'block';
                        }
                    }
                }
                questionString = qa[i].ps;
                if (testInOtherLang && questionString.indexOf("~~") != -1) {
                    //changing the language
                    questionString = questionString.substr(questionString.indexOf("~~") + 2);
                } else if (questionString.indexOf("~~") != -1) {
                    //getting the first  language
                    questionString = questionString.substr(0, questionString.indexOf("~~"));
                }
                questionString = questionString.replace("<p>", "");
                questionString = questionString.replace("</p>", "");
                if(qa[i].isValidAnswerKey && qa[i].isValidAnswerKey == "false"){
                    htmlStr += "<div class='question-box error-highlight with-less-with' id='questionIndex_"+i+"'>&nbsp; <a href='javascript:editQuiz("+i+")'>Edit</a>";
                }else{
                    htmlStr += "<div class='question-box  with-less-with' id='questionIndex_"+i+"'>&nbsp; <a href='javascript:editQuiz("+i+")'>Edit</a>";
                }

                htmlStr +="<div class='question container'>";
                if (qa[i].directions) {
                    dirStr = qa[i].directions;
                    if (testInOtherLang && dirStr.indexOf("~~") != -1) {
                        //changing the language
                        dirStr = dirStr.substr(dirStr.indexOf("~~") + 2);
                    } else if (dirStr.indexOf("~~") != -1) {
                        //getting the first  language
                        dirStr = dirStr.substr(0, dirStr.indexOf("~~"));
                    }
                    htmlStr += "<div class='row' style='margin:0;'>\n" +
                        "                <div class='col-xs-12 directions container' id='directionss-"+(i+1)+"'>\n" +
                        "                &nbsp;" + dirStr + "\n" +
                        "            </div>\n" +
                        "            </div>";
                }

                htmlStr +="<p class='que-no'>" + "Q" + (i + 1) + ".  </p>" +
                    "<pre class='question question-"+(i+1)+"' id='question-"+(i+1)+"'>" + questionString + "</pre>" +
                    "</div>";


                //get right answers
                labelClass = "correct-answer-by-user";
                answerClass = "correct-answer";
                answer = "";
                var labelValue="";
                var containerClass="ans-neutral";
                var answerLetter="";

                for(var j=0;j< 5;++j) {
                    answerLetter=optionLabel[j];

                    var answerString = eval("qa[" + i + "]." + eval("ansOptKeys.ans" +(j+1) ));
                    if (answerString && (typeof answerString !== typeof undefined)) {
                        if (answerString.indexOf("<p") == 0) {
                            answerString = answerString.replace('<p>', '');
                            answerString = answerString.replace('</p>', '');
                        }
                        if (testInOtherLang && answerString.indexOf("~~") != -1) {
                            //changing the language
                            answerString = answerString.substr(answerString.indexOf("~~") + 2);
                        } else if (answerString.indexOf("~~") != -1) {
                            //getting the first  language
                            answerString = answerString.substr(0, answerString.indexOf("~~"));
                        }
                        if (("" + qa[i]["ans"+(j+1)]) == 'Yes') {
                            labelValue="Correct Answer";
                            labelClass = "correct-answer-by-user";
                            answerClass = "correct-answer";
                            if (!readMode) {
                                if (userAnswers[i]['correctAnswer'] == "true") {
                                    labelValue="Your Answer";
                                }
                                else if(userAnswers[i].skipped == "true"){
                                    labelValue="Skipped";
                                    labelClass="skipAnswer";
                                }
                            }

                            htmlStr +="<div class='your-answer ans-correct container' id='answerDiv-"+(i+1)+"-"+(j+1)+"'>" + "<div>" + "<p>" +
                                "  <span class=' " + labelClass + "'>" +labelValue+
                                "</span>" + "</p>";
                            htmlStr += "<p class='d-flex'>" +"<span class='choice'><span>"+answerLetter+"</span></span>"+
                                "  <pre class='" + answerClass + "' id='answerSpan-"+(i+1)+"-"+(j+1)+"'>"+"<i class='material-icons'>check_circle</i>"+
                                "<span class='answerStrings-"+(i+1)+"-"+(j+1)+"' id='answerStrings-"+(i+1)+"-"+(j+1)+"'>"+ answerString +"</span>"+
                                "</pre>" + "</p>" + "</div>" + "</div>";
                        }
                        else{
                            labelClass = "";
                            answerClass = "";
                            labelValue="";
                            containerClass="ans-neutral";
                            if (!readMode) {
                                if ((userAnswers[i]['correctAnswer'] == "false") && userAnswers[i].skipped != "true") {
                                    if (userAnswers[i]["ans"+(j+1)] == "Yes") {
                                        labelClass = "wrong-answer-label";
                                        answerClass = "wrong-answer-by-user";
                                        labelValue = "Your Answer";
                                        containerClass = "";
                                    }
                                }
                            }
                            htmlStr += "<div class='your-answer container "+containerClass+"' id='answerDiv-"+(i+1)+"-"+(j+1)+"'>" + "<div>" +

                                "<p>" +
                                "  <span class=' " + labelClass + "'>" +labelValue+

                                "</span>" + "</p>";
                            htmlStr += "<p class='d-flex'>" +"<span><span class='choice'>"+answerLetter+"</span></span>"+
                                "  <pre class='" + answerClass + "' id='answerSpan-"+(i+1)+"-"+(j+1)+"'>"+"<i class='material-icons err'>error</i>"+
                                "<span class='answerStrings-"+(i+1)+"-"+(j+1)+"' id='answerStrings-"+(i+1)+"-"+(j+1)+"'>"+ answerString +"</span>"+
                                "</pre>" + "</p>" + "</div>" + "</div>";
                        }
                        answer += answerString;
                    }



                }

                if (qa[i].answerDescription) {

                    qa[i].answerDescription = qa[i].answerDescription.replace('<p>', '');
                    qa[i].answerDescription = qa[i].answerDescription.replace('</p>', '');
                    var descriptionString=qa[i].answerDescription;
                    if (testInOtherLang && descriptionString.indexOf("~~") != -1) {
                        //changing the language
                        descriptionString = descriptionString.substr(descriptionString.indexOf("~~") + 2);
                    } else if (descriptionString.indexOf("~~") != -1) {
                        //getting the first  language
                        descriptionString = descriptionString.substr(0, descriptionString.indexOf("~~"));
                    }
                    var descriptionOfAnswer = "";
                    descriptionOfAnswer += "<div class='answer-explanation-row'><div class='container answer-description col-xs-12'>" +
                        "<p class='show-explanation'>" +
                        "<a class='show-explanation-btn' id='my-" + (i + 1) + "' data-target='demo-" + (i + 1) + "'>" + "Explanation" + "</a>" +
                        "</p>" +
                        "<pre class='correct-answer-explanation demo-" + (i + 1) + "' id='demo-" + (i + 1) + "'>" +descriptionString + "</pre>" +

                        "</div></div>";
                } else {
                    descriptionOfAnswer = "";
                }

                htmlStr += descriptionOfAnswer;
                if(qa[i].marks){
                    htmlStr += "<p class='marks positiveMrk container' id='positive-mark_"+i+"'>Marks: "+qa[i].marks+"</p>"
                }

                if(qa[i].negativeMarks){
                    htmlStr += "<p class='marks negativeMrk container' id='negative-mark_"+i+"'>Negative Marks: "+qa[i].negativeMarks+"</p>"
                }
                htmlStr += "</div>";
                if(quizSource != 'app') {
                    if (("eutkarsh" == defaultSiteName)) {

                        htmlStr += "</div>";
                    } else {
                        if (explainLink) {
                            if(siteId == 21)
                            {
                                htmlStr += "<div>"+"<p class='text-center pt-2' style='color:red'>Videos of this book are available in app only</p>"+"</div>";

                            }
                            else {
                                htmlStr += "<div class='mcqChatBtns d-flex align-items-center justify-content-center mt-3 w-100'>" +
                                    "<button onclick=\"javascript:showVideo('" + explainLink + "','" + qa[i].startTime + "','" + qa[i].endTime + "');\" class='showVideobtn' style='margin: 0!important;'>" + "<i class='material-icons'>ondemand_video</i>" + "Video Explanation</button>" + "</div>"
                            }
                            +"</div>";

                        }
                    }
                }
                if(quizSource=='app'){
                    if(!wonderslate){
                        htmlStr += "</div>";
                    }
                    else{
                        if (explainLink) {
                            htmlStr += "<div class='mcqChatBtns d-flex align-items-center justify-content-center mt-3 w-100'>" +
                                "" + "<button onclick=\"javascript:showVideo('" + explainLink + "','" + qa[i].startTime + "','" + qa[i].endTime + "');\" class='showVideobtn'>" + "<i class='material-icons'>ondemand_video</i>" + "Video Explanation</button>" +
                                "</div></div>";
                        }
                    }
                }
            }

            htmlStr += "<div id='videoContent'>"+
                "<div class=\"modal fade\" id=\"videoModal\">\n" +
                "    <div class=\"modal-dialog  modal-dialog-centered\">\n" +
                "      <div class=\"modal-content\">\n" +
                "      \n" +
                "        <div class=\"modal-header\">\n" +

                "          <button type=\"button\" class=\"close\" data-dismiss=\"modal\">&times;</button>\n" +
                "        </div>\n" +
                "        \n" +
                "        <div class=\"modal-body\">\n" +
                "<div class='video-wrapper' id='videoUpdate'>" +
                "<iframe src='' frameborder='0' allow='accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture' allowfullscreen>" +
                "</iframe>" +
                "</div>" +
                "        </div>\n" +
                "        \n" +
                "      </div>\n" +
                "    </div>\n" +
                "  </div>" +
                "</div>";
            document.getElementById("answer-block").innerHTML = msgStr + userSuggestions + htmlStr + userDataTable;
            renderMathInElement(document.getElementById("answer-block"), {
                delimiters: [
                    { left: "\\(", right: "\\)", display: false },
                    { left: "\\[", right: "\\]", display: true },
                    { left: "$$", right: "$$", display: true },
                    { left: "$", right: "$", display: false },
                ],
                ignoredTags: []
            });
        }

    }
    if (quizSource == "web") {
        $('.svg-timer').removeClass('d-flex').addClass('d-none');
        $('.sub-header').hide();
        $('.mt-fixed').css('margin-top','0');
        if (readMode || resultsMode) {
            $("#question-block").hide();
            $("#answer-block").show();
            $('.ans-tab').hide();
            $('.que-side-menu,.tim-wrapper,.submitWrapper').hide();
            $('.read-backarrow').show();
            // $('.mt-fixed').css('margin-top','7rem');
            $('.menu-wrapper > .menu').hide();
            $('.result-menu').show();
            window.scrollTo(0, 0);
            if(siteId==3 || siteId==37) {
                $('.result-menu h2').text(resourceTitle);
                $("#resourceTitle").hide();
            } else {
                $('.result-menu h2').text('');
            }


        } else {
            if (quizSource == "web") {
                updateWithQuizAnswers(userAnswers, score, "false");
            }
        }

        if(resultsMode){
            $('.ans-tab').show();
            $('.sub-header').hide();
            //$('#changeLanguage1').show();

        }

    }

    if (quizSource == "app") {
        if(readMode) {
            $("#answer-block").show();
            $('.sub-header').show();
            $('.menu-wrapper > .menu').hide();
            $('.sub-header,.option.container').css('height', '50px');
            $('.sub-header .submit').hide();
            $("#answer-block").css({'margin-top': '0', 'padding-bottom': '0'});
            $("#question-block").css({'margin-top': '0'});
        }
        if(resultsMode) {
            $("#answer-block").show();
            $('.result-menu').show();
            window.scrollTo(0, 0);
            $('.sub-header').hide();
            $(".sub-header").css({'height': '40px'});
            $('.read-backarrow').hide();
            $('.sub-header .submit').hide();
            $('.sub-header .options > p').hide();
            $('.sub-header .options .menu-wrapper').css({
                'justify-content': 'flex-end',
                'width': '100%',
                'margin-top': '10px'
            });
            $("#answer-block").css({'margin-top': '0', 'padding-bottom': '0'});

        }

    }

    renderMathInElement(document.body, {
        delimiters: [
            { left: "\\(", right: "\\)", display: false },
            { left: "\\[", right: "\\]", display: true },
            { left: "$$", right: "$$", display: true },
            { left: "$", right: "$", display: false },
        ],
        ignoredTags: [],
    });
}

function showUserDataTable() {
    $('#user-data-table').show();
    $('.practice-score-container').hide();
    $('.analysis-summary').hide();
    $('.question-box').hide();
    $('.answer-summary').hide();
    $('.directions').hide();
    $('.passage').hide();
    $('.collapse-hr').hide();
    $('.div-separator').hide();
    $('.suggestions-for-user').hide();
    $('br').hide();
}

function hideUserDataTable() {
    $('#user-data-table').hide();
    $('.practice-score-container').show();
    $('.analysis-summary').show();
    $('.question-box').show();
    $('.answer-summary').show();
    $('.directions').show();
    $('.passage').show();
    $('.collapse-hr').show();
    $('.div-separator').show();
    $('.suggestions-for-user').show();
    $('br').show();
}

function getChapterName(chaptersList,chapterId){
    var chapterName;
    for(j=0;j<chaptersList.length;j++){
        if(chaptersList[j].id==chapterId){
            chapterName = chaptersList[j].name;
            break;
        }

    }
    return chapterName;
}

function sectionChanged(){

    if(previousSubject!=null) {
        $("#question-"+previousSubject).hide();
        $("#grid-"+previousSubject).hide();
        $("#list-"+previousSubject).hide();
    }
    previousSubject = document.getElementById("sectionSelection")[document.getElementById("sectionSelection").selectedIndex].value.replace(/[^a-zA-Z0-9]/g, '');
    $("#question-"+previousSubject).show();
    $("#grid-"+previousSubject).show();
    $("#list-"+previousSubject).show();

    document.getElementById("totalQuestions").innerHTML=sections[previousSubject];
    $("html, body").animate({ scrollTop: 0 }, "fast");

}

function timeCompleted(){
    if(!testSubmitted) {
        if (testSeriesQuiz && sectionsPresent) {
            var sectionsList = document.getElementById("sectionSelection");
            if (sectionsList.selectedIndex < (sectionsList.length - 1)) {
                document.getElementById('comingup-subject').innerHTML = sectionsList[sectionsList.selectedIndex + 1].value;
                continueTest();
            } else {
                forceSubmitTest();
            }
        }else if(testSeriesQuiz){
            forceSubmitTest();
        }
    }
}

function nextSection() {
    document.getElementById("sectionSelection").selectedIndex += 1;
    var sectionTime = examDtl[document.getElementById("sectionSelection").selectedIndex].totalTime;
    if (sectionTime==undefined) {
        forceSubmitTest();
    } else {
        sectionChanged();
        changeWholeTime(sectionTime * 60);
        startTimer();
    }
}
function opensideNav() {
    $('.que-side-menu').css('width','75%');
    $('.que-side-menu').show();
    $('.que-side-menu .close-menu').show();
    $('.overlay-container').show();
    $('.que-side-menu .tab').show();
}

function closesideNav() {
    $('.que-side-menu').width(0);
    $('.que-side-menu').hide();
    $('.close-menu').hide();
    $('.overlay-container').hide();
    $('.que-side-menu .tab').hide();
}

function reviewChanged(index){
    if(document.getElementById("bookmarkbtn-"+(index+1)).checked){
        $("#list_"+index).addClass('marked');
        $("#grid_"+index).addClass('marked');

    }else{
        $("#list_"+index).removeClass('marked');
        $("#grid_"+index).removeClass('marked');
    }
}

var issueIndex;
function openIssue(index){
    issueIndex=index;
    document.getElementById("spellingMistake").checked=true;
    document.getElementById("directionNotGiven").checked=false;
    document.getElementById("imageNotVisible").checked=false;
    document.getElementById("incompleQuestion").checked=false;
    document.getElementById("otherIssues").checked=false;
    document.getElementById("moreInformation").value="";
    $('#report-que').modal('show');
}

function openIssueSubmit(){
    $('#report-que').modal('hide');
    var issuesSelected="";
    if(document.getElementById("spellingMistake").checked){
        issuesSelected="Spelling mistake";
    }
    if(document.getElementById("directionNotGiven").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Directions not given";
    }
    if(document.getElementById("imageNotVisible").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Image not visible";
    }
    if(document.getElementById("incompleQuestion").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Incomplete questions";
    }
    if(document.getElementById("otherIssues").checked){
        issuesSelected+=(issuesSelected==""?"":",")+"Other issues";
    }

    var moreInformation = document.getElementById("moreInformation").value;
    if(android) {
        JSInterface.sendToServer(qa[issueIndex].id, issuesSelected, moreInformation);
    }
    else {
        onIssueSubmit(qa[issueIndex].id, issuesSelected, moreInformation);
    }
}

function showVideo(link, startTimeLocal, endTimeLocal) {
    if(android) {
        if ((quizSource == 'app')) {
            JSInterface.videoExplanationClicked(link, startTime, endTime)
        }
        if(link.includes("/")){
            var yt_url=link;
        }else{
            var yt_url = "https://www.youtube.com/embed/" + link;
        }

        if (startTimeLocal) {
            if (startTimeLocal.includes(":")) startTimeLocal = toSeconds(startTimeLocal);
            yt_url += "?start=" + startTimeLocal;
            if (endTimeLocal) {
                if (endTimeLocal.includes(":")) endTimeLocal = toSeconds(endTimeLocal);
                yt_url += "&end=" + endTimeLocal;
            }

        }
        $('#videoModal').find('iframe').attr('src', yt_url);

        $('#videoModal').modal('show');
        $('#videoModal').on('hidden.bs.modal', function () {
            $("#videoUpdate" + ' iframe').attr('src', '');
        });
        if (quizSource != 'app') {
            if (loggedInUser) {
                updateUserView(resId, "explain", "videos");
            } else {
                updateView(resId, "explain", "videos");
            }
        }
    }
    else{

        var json = {'link':link,'startTime':startTimeLocal,'endTime':endTimeLocal};
        webkit.messageHandlers.videoPlayerHandler.postMessage(JSON.stringify(json));

    }

}


function toSeconds(str) {
    var pieces = str.split(":");
    var result = Number(pieces[0]) * 60 + Number(pieces[1]);
    return(parseInt(result));
}

function askDoubt(type,index) {
    ibookgpt.mcqChat(0, type, index)
}