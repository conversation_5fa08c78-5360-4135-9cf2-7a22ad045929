var data1 = {
    "results":[
        {
            "id": 35172,
            "ps": "<p>Ionisation energy of <span class=\"math-tex\">\\(He^+ \\ is \\ 19.6\\times10^{-18} \\ J \\ atom^{-1}.\\)</span> The energy of the first stationary state (n = 1) of <span class=\"math-tex\">\\(Li^{2+}\\)</span> is</p>\r\n",
            "op1": "<p><span class=\"math-tex\">\\(4.41\\times10^{-16}J \\ atom^{-1}\\)</span></p>\r\n",
            "op2": "<p><span class=\"math-tex\">\\(-4.41\\times10^{-17}J \\ atom^{-1}\\)</span></p>\r\n",
            "op3": "<p><span class=\"math-tex\">\\(-2.2\\times10^{-15}J \\ atom^{-1}\\)</span></p>\r\n",
            "op4": "<p><span class=\"math-tex\">\\(8.82\\times10^{-17}J \\ atom^{-1}\\)</span></p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "true",
            "ans2": "Yes",
            "ans3": null,
            "ans4": null,
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1876
        },
        {
            "id": 31889,
            "ps": "<p>The process of decomposition delays when,<br />\r\n&nbsp;</p>\r\n",
            "op1": "<p>the detritus is made up of sugars and nitrogen compounds.</p>\r\n",
            "op2": "<p>aeration is sufficient.</p>\r\n",
            "op3": "<p>warm and moist environment exists</p>\r\n",
            "op4": "<p>detritus is rich in lignin and chitin</p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "false",
            "ans2": null,
            "ans3": null,
            "ans4": "Yes",
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1804
        },
        {
            "id": 31862,
            "ps": "<p>Identify the plants that are dominant during Jurassic period<br />\r\n&nbsp;</p>\r\n",
            "op1": "<p>Fems, Conifers and Cycads<br />\r\n&nbsp;</p>\r\n",
            "op2": "<p>Angiosperms and Bryophytes<br />\r\n&nbsp;</p>\r\n",
            "op3": "<p>Monocotyledons and Arborescent lycopods<br />\r\n&nbsp;</p>\r\n",
            "op4": "<p>Sphenopsida and Ginkgos<br />\r\n<br />\r\n<br />\r\n&nbsp;</p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "false",
            "ans2": null,
            "ans3": null,
            "ans4": null,
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1804
        },
        {
            "id": 35183,
            "ps": "<p>The Gibbs energy for the decomposition of <span class=\"math-tex\">\\(Al_2O_3 \\ at \\ 500^oC\\)</span> is as follows :</p>\r\n\r\n<p><span class=\"math-tex\">\\(\\frac{2}{3}Al_2O_3\\rightarrow\\frac{4}{3}Al+O_2,\\Delta_rG=+966kJ \\ mol^{-1}\\)</span></p>\r\n\r\n<p>The potential difference needed for electrolytic reduction of <span class=\"math-tex\">\\(Al_2O_3 \\ at \\ 500^oC\\)</span> is at least :</p>\r\n",
            "op1": "<p>4.5 V</p>\r\n",
            "op2": "<p>3.0 V</p>\r\n",
            "op3": "<p>2.5 V</p>\r\n",
            "op4": "<p>5.0 V</p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "true",
            "ans2": null,
            "ans3": "Yes",
            "ans4": null,
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1876
        },
        {
            "id": 31891,
            "ps": "<p>Morphine is obtained from the:<br />\r\n&nbsp;</p>\r\n",
            "op1": "<p>Inflorescence of cannabis<br />\r\n&nbsp;</p>\r\n",
            "op2": "<p>Leaves of Erythroxylum</p>\r\n",
            "op3": "<p>Latex of Poppy plant<br />\r\n&nbsp;</p>\r\n",
            "op4": "<p>Root of Atropa<br />\r\n&nbsp;</p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "false",
            "ans2": null,
            "ans3": "Yes",
            "ans4": null,
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1804
        },
        {
            "id": 31910,
            "ps": "<p>Which class of Algae reproduces asexually by non-motile spores and sexually by non-motile gametes?</p>\r\n",
            "op1": "<p>Chlorophyceae</p>\r\n",
            "op2": "<p>Cyanophyceae</p>\r\n",
            "op3": "<p>Phaeophyceae</p>\r\n",
            "op4": "<p>Rhodophyceae</p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "true",
            "ans2": null,
            "ans3": null,
            "ans4": "Yes",
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1804
        },
        {
            "id": 31901,
            "ps": "<p>The type of epithelium found in the fallopian tube which functions to move particles or mucous in specific direction is:<br />\r\n&nbsp;</p>\r\n",
            "op1": "<p>Squamous epithelium<br />\r\n&nbsp;</p>\r\n",
            "op2": "<p>Cuboidal epithelium<br />\r\n&nbsp;</p>\r\n",
            "op3": "<p>Ciliated epithelium<br />\r\n&nbsp;</p>\r\n",
            "op4": "<p>Columnar epithelium<br />\r\n<br />\r\n<br />\r\n&nbsp;</p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "true",
            "ans2": null,
            "ans3": "Yes",
            "ans4": null,
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1804
        },
        {
            "id": 31875,
            "ps": "<p>Read the statements 1 and 2. Choose the correct option:</p>\r\n\r\n<p>&nbsp; &nbsp; &nbsp; Statement 1: RNAi take place in all prokaryotic and eukaryotic organisms as a method of cellular defence.</p>\r\n\r\n<p>&nbsp; &nbsp; &nbsp; Statement 2:RNA interferance is a pre-translational process.</p>\r\n\r\n<p><br />\r\n&nbsp;</p>\r\n",
            "op1": "<p>Statement-1 incorrect, Statement-2 correct</p>\r\n",
            "op2": "<p>Both statements are incorrect</p>\r\n",
            "op3": "<p>Both statements are correct</p>\r\n",
            "op4": "<p>Statement-1 correct, Statement-2 incorrect<br />\r\n<br />\r\n&nbsp;</p>\r\n",
            "op5": "",
            "resType": "True or False",
            "ans1": "false",
            "ans2": null,
            "ans3": null,
            "ans4": null,
            "ans5": null,
            "fileName": null,
            "fileoption1": null,
            "fileoption2": null,
            "fileoption3": null,
            "fileoption4": null,
            "fileoption5": null,
            "directions": null,
            "section": null,
            "answerDescription": "",
            "answer": "",
            "subject": null,
            "chapterId": null,
            "quizId": 1804
        }
    ]
};

var chapterDetails={

    chapterDetails:[
        {
            "quizId": "1804",
            "id": 2740,
            "name": "Biology 2017",
            "subject": "Practice- KCET Biology Previous Year Question Papers"
        },
        {
            "quizId": "1876",
            "id": 2810,
            "name": "Chemistry 2010",
            "subject": "Practice- IIT JEE (Mains) Chemistry Previous Year Question Papers"
        }
    ]};


var userAnswers=[];
var qa;
var startTime;
var endTime;
var score={};
var ansOptKeys = {"ans1":"op1","ans2":"op2","ans3":"op3","ans4":"op4","ans5":"op5"};
var subjects=[];
var subjectDetails=[];
var subjectKey;
var userDataTable;
var userSuggestions;
var userDataTable = "";
var userSuggestions = "";
var nameoftheuser="";
var chapatersList;

function createWebTOF(data,isPassage,passage,cList,name){
    qa = data;
    var htmlStr="";
    var questionString="";
    var optionString="";
    chaptersList=cList;
    nameoftheuser=name;
    if(isPassage){
        htmlStr += "<div class='row'>\n" +
            "                <div class='col-xs-12 directions'>\n" +
            "            </div>\n" +
            "            </div>" ;
        htmlStr += "<div class='row'>\n" +
            "                <div class='col-xs-12 passage'>\n" +
            "                "+passage+"\n" +
            "            </div>\n" +
            "            </div>" ;
    }
    for (var i = 0; i < qa.length; ++i) {
        if(qa[i].directions){
            htmlStr += "<div class='row'>\n" +
                "                <div class='col-xs-12 directions'>\n" +
                "                &nbsp;"+qa[i].directions+"\n" +
                "            </div>\n" +
                "            </div>" ;
        }
        questionString = qa[i].ps;
        questionString = questionString.replace('<p>','');
        questionString = questionString.replace('</p>','');
        htmlStr += "<div class='question-box'>"+
            "<div class='question' id='question-"+(i+1)+"'>"+(i+1)+"&nbsp;|&nbsp;"+questionString+"</div>"+
            "<div class='col-xs-12 options-string opt-str-"+(i)+"'>" +
            "<div class='radio user-input'>" +
            "<label style='width: 100%; padding-left: 30px; padding-top: 16px; padding-bottom: 16px;'>" +
            "<input type='radio' name='quiz.rad.ans"+(i+1)+"' id='tof_true"+i+"' value='true'>TRUE"+
            "<span class='checkmark checkmark-radio' style='border:0;'>"+" A</span>" +
            "</label>" +
            "</div>" +
            "</div>"+
            "<div class='col-xs-12 options-string opt-str-"+(i)+"' >" +
            "<div class='radio user-input'>" +
            "<label style='width: 100%; padding-left: 30px; padding-top: 16px; padding-bottom: 16px;'>" +
            "<input type='radio' name='quiz.rad.ans"+(i+1)+"' id='tof_false"+i+"' value='false'>FALSE"+
            "<span class='checkmark checkmark-radio' style='border:0;'>"+" B</span>" +
            "</label>" +
            "</div>" +
            "</div>";

        htmlStr +="</div>";

    }
    htmlStr += "            <div class='row submit-exercise'>" +
        "                <div class='col-xs-12'>" +
        "                    <input type='button' class='submit-exercise-btn' onclick='javascript:submitForm()' name='Submit' value='Submit'>" +
        "                </div>" +
        "            </div>";
    document.getElementById("question-block").innerHTML=htmlStr;


    //stuff required for analysis
    subjects=[];
    subjectDetails=[];
    userDataTable="";
    userSuggestions="";

    //check if this test generator thingy
    if(chaptersList){

        for(var index = 0; index < qa.length; ++index) {
            for (l = 0; l < chaptersList.length; l++) {
                if(qa[index].quizId==chaptersList[l].quizId){
                    qa[index].subject = chaptersList[l].subject;
                    qa[index].chapterId = chaptersList[l].id;
                    break;

                }
            }
        }
    }else{

    }

    for(var index = 0; index < qa.length; ++index) {
        if(qa[index].subject) {
            subjectKey = qa[index].subject.replace(/\W+/g, '');

            if (subjects.indexOf(qa[index].subject) < 0) {
                subjects.push(qa[index].subject);
                var subjectDetail = {};
                subjectDetail.noOfQuestions = 1;
                subjectDetail.correctAnswers = 0;
                subjectDetail.chapters={};
                //add the chapter information
                var chapter = {};
                chapter.id=qa[index].chapterId;
                chapter.noOfQuestions = 1;
                chapter.correctAnswers = 0;
                chapter.wrongAnswers = 0;
                chapter.skipped=0;
                if(chaptersList){
                    chapter.name = getChapterName(chaptersList,qa[index].chapterId);
                    subjectDetail.chapters[''+qa[index].chapterId] = chapter;
                }else{
                }


                subjectDetails[subjectKey] = subjectDetail;
            }
            else {
                //already added
                subjectDetails[subjectKey].noOfQuestions++;
                if(subjectDetails[subjectKey].chapters[''+qa[index].chapterId]==undefined) {

                    //if the chapter doesnt exist
                    var chapter = {};
                    chapter.id = qa[index].chapterId;
                    chapter.noOfQuestions = 1;
                    chapter.correctAnswers = 0;
                    chapter.wrongAnswers = 0;
                    chapter.skipped=0;
                    if (chaptersList) {
                        chapter.name = getChapterName(chaptersList, qa[index].chapterId);
                        subjectDetails[subjectKey].chapters[''+qa[index].chapterId] = chapter;
                    }

                }else{
                    //if the chapter already exists
                    subjectDetails[subjectKey].chapters[''+qa[index].chapterId].noOfQuestions++;
                }

            }
        }
    }

    startTime = new Date();
}




function submitForm(){

    scoreAndShowAnswersTOF(false);
    $("#question-block").hide();
    $("#answer-block").show();
    $("html, body").animate({ scrollTop: 0 }, "fast");
    return false;
}

function calculateScore(){
    var htmlStr="";
    var questionString="";
    var optionString="";
    score.correctAnswers = 0;
    score.wrongAnswers=0;
    score.skipped=0;

    for (var index = 0; index < qa.length; ++index) {
        if($('#tof_true'+index).is(':checked')){
            userAnswers[index] = "true";
            if(qa[index].ans1=="true");
            score.correctAnswers++;
         }else if($('#tof_false'+index).is(':checked')){
            userAnswers[index] = "false";
            if(qa[index].ans1=="false");
            score.wrongAnswers++;
        }else {
            userAnswers[index] = "skipped";
            score.skipped++;
        }

    }

}

function scoreMessage(score,length){
    var percent = (score.correctAnswers/length)*100;
    var accuracy = 0;
    if(score.correctAnswers > 0) {
        accuracy = (score.correctAnswers/(score.correctAnswers+score.wrongAnswers))*100;
    }
    var msgStr;
    var imgSrc = "";
    var titleSrc="";
    var comment="";

    endTime =  new Date();

    if(percent > 90) {
        imgSrc = "file:///android_asset/qaHtmlFiles/medal-gold.png";
        titleSrc="Bravo, well done";
        comment="Fancy another quiz?";

    }
    else if(percent > 70) {
        imgSrc = "file:///android_asset/qaHtmlFiles/medal-silver.png";
        titleSrc="Good.";
        comment="Do you think you can do better? Letâ€™s try again!";
    }
    else if(percent > 40) {
        titleSrc="Thatâ€™s Ok.";
        comment="You can do better. Lets practice more.";
        imgSrc = "file:///android_asset/qaHtmlFiles/medal-bronz.png";

    }
    else  {
        imgSrc = "file:///android_asset/qaHtmlFiles/medal-none.png";
        titleSrc="Thatâ€™s Ok.";
        comment="You need more practice.";
    }
    msgStr = "<div class='medal-picture'>"+
        "<img class='img-responsive' src='"+imgSrc+"' alt=''/>" +
        "</div>"+
        "<div class='practice-score-string'>"+
        "<p class='practice-score-string-score'> Your score is "+score.correctAnswers+"</p>"+
        "<p>"+titleSrc+"</p>"+
        "<p>"+comment+"</p>"+
        "</div>";
    msgStr = "<div class='practice-score-container'>" +
        "<div class='practice-score'>"+msgStr+"</div>"+
        "</div>"+
        "<div class='answer-summary'>"+
        "<p class='summary-heading'>"+"Summary"+"</p>"+
        "<p class='short-heading'>"+"(Total Questions: "+"<span class='summary-heading'>"+length+"</span>"+")"+"</p>"+
        "<div class='score-summary row'>"+
        "<div class='col-md-4 col-xs-4'>"+
        "<p>Correct"+"<p>"+
        "<p class='correct-answers'>"+score.correctAnswers+"</p>"+
        "</div>"+
        "<div class='col-md-4 col-xs-4'>"+
        "<p>Incorrect"+"<p>"+
        "<p class='wrong-answers'>"+score.wrongAnswers+"</p>"+
        "</div>"+
        "<div class='col-md-4 col-xs-4'>"+
        "<p>Skipped"+"<p>"+
        "<p class='skipped-answers'>"+score.skipped+"</p>"+
        "</div>"+
        "</div>"+
        "<div class='accuracy-summary row'>"+
        "<div class='col-xs-11 col-xs-offset-1' style='text-align:left;'>"+
        "<p class='time-taken'>"+"</p>"+
        msToTime((endTime - startTime))+
        "<p class='answer-accuracy' style='margin-left:8px;'>"+"</p>"+
        "<span class='time-span' style='border-right: none;'>"+Math.round(accuracy)+"%"+"</span>"+
        "</div>"+
        "</div>"+
        "</div>"+
        "</div>"+
        "</div>" ;

    if(subjects.length>0) {
        msgStr += "<div class='analysis-summary'>" +
            "<div class='col-md-12'>" +
            "<p class='row-heading'>" + "Analysis" + "</p>" +
            "<div class='analysis-by-book-detail'>";
        var subjectScoreClass;
        for (i = 0; i < subjects.length; i++) {
            var subjectDetail = subjectDetails[subjects[i].replace(/\W+/g, '')];
            var subjectScorePercent = (subjectDetail.correctAnswers / subjectDetail.noOfQuestions) * 100;
            if (subjectScorePercent < 40) subjectScoreClass = "badge-danger";
            else if (subjectScorePercent < 80) subjectScoreClass = "badge-warning";
            else subjectScoreClass = "badge-success";
            msgStr += "<a href='#" + subjects[i].replace(/\W+/g, '') + "' class='analysis-book-name' data-toggle='collapse' aria-expanded='false'>" + subjects[i] +
                "<span class='badge " + subjectScoreClass + "'>" + subjectDetail.correctAnswers + "/" + subjectDetail.noOfQuestions + "</span>" +
                "<i class='icon-chevron simple'></i>" +
                "</a>" +
                "<hr class='collapse-hr'>" +
                "<div class='collapse collapsed-detail-container' id='" + subjects[i].replace(/\W+/g, '') + "'>" +
                "<ul class='collapsed-detail-wrapper'>";
            var chapterKeys = Object.keys(subjectDetail.chapters);
            for (j = 0; j < chapterKeys.length; j++) {
                msgStr += "<li class='collapsed-detail-list-item'>" +
                    "<p class='collapsed-detail-list-item-chapter'>" + subjectDetail.chapters['' + chapterKeys[j]].name +
                    "<span class='collapsed-detail-list-item-chapter-score'>" + subjectDetail.chapters['' + chapterKeys[j]].correctAnswers + "/" + subjectDetail.chapters['' + chapterKeys[j]].noOfQuestions + "</span>" +
                    "</p>" +
                    "</li>";

            }
            msgStr += "</ul>" +
                "</div>";
        }
    }

    msgStr +=  "<a href='javascript:showUserDataTable();' class='expand-table-btn'>" + "View in depth details" +"</a>" +
        "</div>"+
        "</div>"+
        "</div>"+
        "<div class='div-separator'></div>";

    var percent = (score.correctAnswers/length)*100;
    var accuracy = 0;
    var timeTaken = (endTime - startTime);
    var noOfQuestionsAttempted = (score.correctAnswers+score.wrongAnswers);
    var noOfQuestionsPerHour = 3600 / ((timeTaken/1000)/noOfQuestionsAttempted);

    if(score.correctAnswers > 0) {
        accuracy = (score.correctAnswers/(score.correctAnswers+score.wrongAnswers))*100;
    }
    var suggestion="";
    if(percent==100){
        suggestion+="You have aced it. Great job. Continue with the same focus to do well in your exams. ";
    }
    else if(percent>90){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Try little harder and you can get that 100. ";
    }
    else if(percent>80){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Little more effort and practice can definitely take you higher. ";
    }else if(percent>60){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Keep practicing more and you will definitely get better at this. ";
    }
    else if(percent>40){
        suggestion+="You have got "+Math.round(percent)+"% right answers. Keep practicing more and you will definitely get better at this. Go through the concepts again and understand better. ";
    }else{
        suggestion+="You have got "+Math.round(percent)+"% right answers. No need to lose heart. Learn the concepts again, understand it better. By understanding the concepts and practicing you can definitely get better. ";
    }

    if(noOfQuestionsPerHour > 60){
        suggestion+="You are answering the quiz very fast. Pace your answering to improve your performance. Since you are able to answer the questions relatively fast, spend the remaining time " +
            "to review your answers. ";
    }else  if(noOfQuestionsPerHour < 40){
        suggestion+= "You are answering the quiz slowly. Pace your answering to improve your performance. Attempt the questions which you know the answer first and then try the ones which you " +
            "are not sure. ";
    }else{
        suggestion+= "You answering pace is good. " ;
    }

    if(accuracy<80){
        suggestion+="You accuracy is "+Math.round(accuracy)+" percent. Number of wrong answers will have reduce your overall score due to negative marking. Do not attempt the question if you are not sure of the answer. "
        ;
    }else if(accuracy>79&&accuracy<91){
        suggestion+= "You accuracy is "+Math.round(accuracy)+" percent.You are doing good. Number of wrong answers will have reduce your overall score due to negative marking. Do not " +
            "attempt the question if you are not sure of the answer. "
        ;
    }else if(accuracy>90&&accuracy<100){
        suggestion+= "You accuracy is "+Math.round(accuracy)+" percent.You are doing great.Keep going, you can get to 100 percent. "
        ;
    }else if(accuracy==100){
        suggestion+= "Well done in terms of accuracy. All your attempted answers are correct. "
        ;
    }




    if(nameoftheuser){
        suggestion = "Hey "+nameoftheuser+", "+suggestion;
    }
    userSuggestions = "<div class='suggestions-for-user'>" +
        "<p class='row-heading'>"+ "Suggestions" + "</p>" +
        "<hr class='collapse-hr'>" +
        "<p class='suggested-topics'>" + suggestion+ "</p>"+
        "</div>"+
        "<div class='div-separator'></div>";


    return msgStr;



}

function msToTime(duration) {
    var milliseconds = parseInt((duration%1000)/100),
        seconds = parseInt((duration/1000)%60),
        minutes = parseInt((duration/(1000*60))%60),
        hours = parseInt((duration/(1000*60*60))%24);
    var timeStr="";


    if(hours>0) timeStr="<span class='time-span'>"+hours+"h"+"</span>";
    if(minutes>0) timeStr+="<span class='time-span'>"+minutes+"m"+"</span>";
    timeStr +="<span class='time-span'>"+seconds+"."+milliseconds+"s"+"</span>";

    return timeStr;
}

function scoreAndShowAnswersTOF(readMode,data,isPassage,passage){
    var msgStr ="";
    if(!readMode) {
        calculateScore();
        msgStr = scoreMessage(score, qa.length);
    }else{
        qa=data;
        chaptersList="";
    }
    var htmlStr="";
    var questionString="";
    var labelClass="";
    var answerClass="";
    var answer="";

    if(isPassage){
        htmlStr += "<div class='row'>\n" +
            "                <div class='col-xs-12 directions'>\n" +
            "            </div>\n" +
            "            </div>" ;
        htmlStr += "<div class='row'>\n" +
            "                <div class='col-xs-12 passage'>\n" +
            "                &nbsp;"+passage+"\n" +
            "            </div>\n" +
            "            </div>" ;
    }
    for (var i = 0; i < qa.length; ++i) {
        labelClass="correct-answer-by-user";
        answerClass="correct-answer-by-user";
        answer="";
        if(!readMode) {
            console.log("i="+i+" and correct answer="+qa[i].ans1+" and user answer is "+userAnswers[i]);
            if (userAnswers[i]=="skipped") {

                    labelClass = "wrong-answer-label";
                    answerClass = "wrong-answer-by-user";
                     answer = "Skipped";




            } else if ((userAnswers[i] == "true"&&qa[i].ans1=="false")||(userAnswers[i] == "false"&&qa[i].ans1=="true")) {
                labelClass = "wrong-answer-label";
                answerClass = "wrong-answer-by-user";

            }

        }
        if(qa[i].directions){
            htmlStr += "<div class='row' style='margin:0;'>\n" +
                "                <div class='col-xs-12 directions'>\n" +
                "                &nbsp;"+qa[i].directions+"\n" +
                "            </div>\n" +
                "            </div>" ;
        }
        questionString = qa[i].ps;
        questionString = questionString.replace("<p>","");
        questionString = questionString.replace("</p>","");
        htmlStr += "<div class='question-box with-less-with'>"+
            "<div class='question'>"+
            (i+1)+"&nbsp;|&nbsp;"+questionString+
            "</div>";
        if(!readMode) {

            htmlStr += "<div class='row' style='margin:0;'>\n" +
                "                <div class='col-xs-12 " + labelClass + "'>\n" +
                "                 &nbsp;Your Answer\n" +
                "            </div></div>\n";
            htmlStr += "<div class='row' style='margin:0;'>\n" +
                "                <div class='col-xs-12 " + answerClass + "'>\n" +
                "                 &nbsp;" + userAnswers[i] +
                "            </div></div>";
        }
        //get right answers
        labelClass="correct-answer-by-user";
        answerClass="correct-answer";
        answer="";


                var answerString = qa[i].ans1;
                if(answerString) {
                    if (answerString.indexOf("<p") == 0) {
                        answerString = answerString.replace('<p>', '');
                        answerString = answerString.replace('</p>', '');
                    }
                }
                answer =answerString+"<br>";

        if(!readMode) {
            htmlStr += "<div class='row' style='margin:0;'><br>" +
                "                <div class='col-xs-12 " + labelClass + "'>\n" +
                "                 &nbsp;Correct Answer\n" +
                "            </div></div>\n";
            htmlStr += "<div class='row' style='margin:0;'>\n" +
                "                <div class='col-xs-12 " + answerClass + "'>\n" +
                "                 &nbsp;" + answer +
                "            </div></div>";
        }
        else {
            console.log("answer is "+answer);
            htmlStr += "<div class='row' style='margin:0;'><br>" +
                "                <div class='col-xs-12 " + labelClass + "'>\n" +
                "                 &nbsp;Correct Answer\n" +
                "            </div></div>\n";
            htmlStr += "<div class='row' style='margin:0;'>\n" +
                "                <div class='col-xs-12 " + answerClass + "'>\n" +
                "                 &nbsp;" + answer +
                "            </div></div>";
        }
        if (qa[i].answerDescription) {

            qa[i].answerDescription = qa[i].answerDescription.replace('<p>', '');
            qa[i].answerDescription = qa[i].answerDescription.replace('</p>', '');
            var descriptionOfAnswer = "";
            descriptionOfAnswer += "<div class='row answer-explanation-row'><div class='answer-description col-xs-12'>"+
                "<p class='show-explanation'>"+
                "<a href='#' class='show-explanation-btn'>"+"Show Explanation"+"</a>"+
                "</p>"+
                "<div class='correct-answer-explanation'>"+qa[i].answerDescription+"</div>"+
                "</div></div>";
        } else {
            descriptionOfAnswer = "";
        }
        htmlStr +=descriptionOfAnswer+"</div><br>";

    }


    document.getElementById("answer-block").innerHTML=msgStr+htmlStr;
    if(readMode){
        $("#question-block").hide();
        $("#answer-block").show();

    }
    renderMathInElement(document.body);
}

function showUserDataTable() {
    $('#user-data-table').show();
    $('.practice-score-container').hide();
    $('.analysis-summary').hide();
    $('.question-box').hide();
    $('.answer-summary').hide();
    $('.directions').hide();
    $('.passage').hide();
    $('.collapse-hr').hide();
    $('.div-separator').hide();
    $('.suggestions-for-user').hide();
    $('br').hide();
}

function hideUserDataTable() {
    $('#user-data-table').hide();
    $('.practice-score-container').show();
    $('.analysis-summary').show();
    $('.question-box').show();
    $('.answer-summary').show();
    $('.directions').show();
    $('.passage').show();
    $('.collapse-hr').show();
    $('.div-separator').show();
    $('.suggestions-for-user').show();
    $('br').show();
}

function getChapterName(chaptersList,chapterId){
    var chapterName;
    for(j=0;j<chaptersList.length;j++){
        if(chaptersList[j].id==chapterId){
            chapterName = chaptersList[j].name;
            break;
        }

    }
    return chapterName;
}