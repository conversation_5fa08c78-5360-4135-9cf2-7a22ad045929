/**
 * Created by achy<PERSON>nan<PERSON> on 26/11/15.
 */
var quiz = {};
var userAnswers=[];
var currentQuizIndex;
var currentQuizLink;
var modalName;
var skipable=true; // use this in future implementation to allow user to set this.
var currentQuizId;
var startTime;
var endTime;
var subjects=[];
var subjectDetails=[];
var subjectKey;
var qa;
var chapatersList;
var ansOptKeys = {"ans1":"op1","ans2":"op2","ans3":"op3","ans4":"op4","ans5":"op5"};
var optionsArray = ["A","B","C","D","E"];
var testGenId;
var immediateFeedback=false;
var flashQuiz=false;
var dataStorer;
var quizModalContent=document.getElementById("quizModal") ? document.getElementById("quizModal").innerHTML : '';
var assignmentQuiz=false;
var totalQuestions,noOfRightAnswers;


function hideAndShowDivInQuiz() {
  $('.next-btn').removeClass('close-modal');
  $('.next-btn').html('Next');
  $('.next-btn').show();
  $('.next-btn').attr('href', 'javascript:nextQ();');
  $('.previous-btn').show();
  $('.mcq-question-div').show();
  $('.questionumber-containte').show();
  $('#quiz-modal-footer').show();
  $('#quiz-modal-footer-mobile').show();
  $('.quiz-modal-body').show();
  $('.question-section-main').show();
  $('#quiz-Done').show();
  $('#quiz-Done').html('Submit Exercise');
  $('#quiz-Done').attr('href','javascript: done();');
  $('.mcq-learn').hide();
  $('.question-section-main #question').show();
  $('#question-option').show();
  $('#mcqquestion').show();
  $('#score-container-div').hide();
  $('#review-footer').hide();
}

function showQuizImmediate(immediate,quizLink,id){
  immediateFeedback = true;
    flashQuiz = false;
    quiz.generated="false";
  showQuiz(quizLink,id);

}

function quizFromFlash(id,quizType){
    quiz = {};
    quiz.title = revisionName;
    quiz.link = "";
    quiz.type="Multiple Choice Questions";
    quiz.mode="play";
    flashQuiz = true;
    immediateFeedback = true;
    startTime = new Date();
    $("#loading").show();
    hideAndShowDivInQuiz();
    getFlashQuiz(id,quizType);
}



function resetTestType() {
  immediateFeedback = false;
    flashQuiz = false;
    $('#quizModal').hide();
    $("#afterGenerateTest").hide();
    $('#beforeGenerateTest').show();
    document.getElementById("noofquestionslabel").innerHTML = "Number of questions";
}

function showQuiz(quizLink,id) {
    flashQuiz = false;
    assignmentQuiz=false;
  $('.loading-icon').removeClass('hidden');
  if ("Multiple Choice Questions" == quiz.type || "Fill in the blanks" == quiz.type ) {
    hideAndShowDivInQuiz();
      document.getElementById("quizModal").innerHTML = quizModalContent;
  }
  currentQuizLink = quizLink;
  currentQuizId = id;

  var quizNumber;
  var quizsData = formattedTopicData.quizs;
  for(var i = 0; i < quizsData.length; ++i) {
    if(quizsData[i].link==quizLink){
      quizNumber=i;
      break;
    }
  }

  var quizData = quizsData[quizNumber];
  currentQuizIndex=quizNumber;

  if("true"==quiz.generated || (quizData!=null&&quizData.link==quiz.link)){
    //same quiz called
    initializeAnswers();
    $('.loading-icon').addClass('hidden');

    if(quiz.currentIndex==0) {
      if ("Multiple Choice Questions" == quiz.type) {
        resetCheckBoxes(0); // for some reason the state of this is retained
      }
      
      showNextQuestion(quiz.currentIndex);
      $("#"+modalName).find('.unanswered').text("");
      $("#"+modalName).find('.questionsection').css("display","block");
      $("#"+modalName).find('.answersection').css("display","none");
      $("#"+modalName).find('.quizTitle').text(quiz.title);
    }


    if ("Multiple Choice Questions" == quiz.type||"Fill in the blanks" == quiz.type){
      if(siteId!=4 || siteId!=-6) {
        $("#mainbookcontent").fadeOut(1000);
        $("#quizModal").show();
        $("#mcqquestionsection").show();
          $("#afterGenerateTest").show();
          $('#beforeGenerateTest').hide();
      } else {
        $("#quizModal").modal('show');
          $("#afterGenerateTest").show();
          $('#beforeGenerateTest').hide();
      }
    } else {
      $("#"+modalName).modal('show');
    }
  } else {
      iniQuizFromServer(quizNumber);
  }
    if(immediateFeedback&&"Multiple Choice Questions" == quiz.type){
        $('#questionNumberContainer').removeClass('col-md-4');
        $('#questionNumberContainer').addClass('hidden');
        $('#individualQuestionContainer').addClass('col-md-12').removeClass('col-md-8');
    }else if("Multiple Choice Questions" == quiz.type){
        $('#questionNumberContainer').addClass('col-md-4');
        $('#questionNumberContainer').removeClass('hidden');
        $('#individualQuestionContainer').addClass('col-md-8');
    }

  startTime = new Date();
  if(siteId!==4 || siteId!==-6) {
    // $(quizModal).fadeIn();
    //$('#score-container').show();
    $('.book-details-container').css('min-height', '700px');
  }
    if(loggedInUser){
        if(allTabMode)
            updateUserView(id,"all","quiz");
        else
            updateUserView(id,"quiz","quiz");
    }
    else{
        if(allTabMode)
            updateView(id,"all","quiz");
        else
            updateView(id,"quiz","quiz");
    }
}

function hideAndShowDivInRead() {
  $('#quizModal #review-footer').hide();
  $('.mcq-learn').removeClass('col-md-8').addClass('col-md-12');
  $('#quizModal .quiz-modal-body').show();
  $('#quizModal #quiz-Done').show();
  $('#quizModal .close-modal').show();
  $('.next-btn').show();
  $('.next-btn').html("Close");
  $('.next-btn').attr('href', '#');
  $('#score-container').hide();
  $('.next-btn').addClass('close-modal');
  $('.mcq-learn').show();
  $('.quizTitle').html("Learn");
  $('#quiz-Done').html("Start Practicing");
  $('#quiz-Done').attr('href', 'javascript:playAgain();');
  $('.next-btn').hide();
  $('.previous-btn').hide();
  $('.questionumber-containte').hide();
  //$('.mcq-question-div').hide();
  $('#fibModal').removeClass('hidden');
  $('#question-option').hide();
  $('.question-section-main #question').hide();
  $('#mcqquestion').hide();
  $('#quiz-modal-footer').show();
  $('#quiz-modal-footer-mobile').show();
  $('#answer-holder').show();
}



function showQuizRead(quizLink,id) {
  $('#questionNumberContainer').addClass('hidden');
  immediateFeedback = false;
  $('.loading-icon').removeClass('hidden');
  hideAndShowDivInRead();
  currentQuizLink = quizLink;
  currentQuizId = id;
  var quizNumber;
  var quizsData = formattedTopicData.quizs;

  for(var i = 0; i < quizsData.length; ++i) {
    if(quizsData[i].link==quizLink){
      quizNumber=i;
      break;
    }
  }
  $('.quiz-modal-body').animate({ scrollTop: 0 });
  var quizData = quizsData[quizNumber];
  quiz = {};
  quiz.title = quizData.title;
  quiz.link = quizData.link;
  quiz.type=quizData.type;
  quiz.mode="read";
  currentQuizIndex=quizNumber;
  getQuestionAnswers(quizData.link);

    if(loggedInUser){
        updateUserView(id);
    }
    else{
        updateView(id);
    }
}

function iniQuizFromServer(quizNumber){
  var quizsData = formattedTopicData.quizs;
  var quizData = quizsData[quizNumber];
  quiz = {};

  quiz.title = quizData.title;
  quiz.link = quizData.link;
  quiz.type=quizData.type;

  quiz.mode="play";
  if(quiz.type=="Match the answers") getMTAQuestions(quizData.link);
  else{
    if(immediateFeedback) getQuestionAnswers(quizData.link);
    else getQuestions(quizData.link);
  }
}

function removeNullFromQuestions(){
  for(var index = 0; index < quiz.questions.length; ++index) {
    if(quiz.questions[index].op1 == null) quiz.questions[index].op1="";
    if(quiz.questions[index].op2 == null) quiz.questions[index].op2="";
    if(quiz.questions[index].op3 == null) quiz.questions[index].op3="";
    if(quiz.questions[index].op4 == null) quiz.questions[index].op4="";
    if(quiz.questions[index].op5 == null) quiz.questions[index].op5="";
  }
}
function initializeQuestionsTestGenerator(data) {
  immediateFeedback=false;
  document.getElementById("quizModal").innerHTML = quizModalContent;
  initializeQuestions(data);
}

function initializeQuestions(data) {

  quiz.questions = data.results;
  if(immediateFeedback) {
    quiz.answers=data.results;
  }
  quiz.isPassage=false;
  quiz.description=data.description;
  quiz.chaptersList = data.chaptersList
  if("true"==quiz.generated)  testGenId = data.testgenid;else testGenId=null;
  
  if("true"==data.isPassage){
    quiz.isPassage=true;
    quiz.passage=data.passage;
  }
  removeNullFromQuestions();


  resetUserAnswers();

 if ("Opposites" == quiz.type) {
    modalName = "oppModal";
    resetOppInput(0);
  }
  else if ("True or False" == quiz.type) {
    modalName = "tofModal";
    resetTOFInput(0);
  }
  else if ("Match the answers" == quiz.type) {
    modalName = "mtaModal";
  }else{
    modalName = "quizModal";
      if ("Fill in the blanks" == quiz.type) {
         quiz.originalQuestions = data.results;
         quiz.questions = formatQuestions(data.results);
         resetTextInput(0);
     }else {
         resetCheckBoxes(0);
     }
  }
  initializeAnswers();
  $("#"+modalName).find('.quizTitle').text(quiz.title);
  showNextQuestion(0);
  $("#"+modalName).find('.unanswered').text("");
  $("#"+modalName).find('.questionsection').css("display","block");
  $("#"+modalName).find('.answersection').css("display","none");

  if ("Multiple Choice Questions" == quiz.type){
    $("#mainbookcontent").fadeOut(1000);
    $("#quizModal").show();
      $("#afterGenerateTest").show();
      $('#beforeGenerateTest').hide();
  }else{
    $("#"+modalName).modal('show');
  }
  $('.loading-icon').addClass('hidden');
}

function showNextQuestion(index){
   if ("Opposites" == quiz.type) {
    showNextQuestionOpp(index);
  } else if ("True or False" == quiz.type) {
    showNextQuestionTOF(index);
  } else if ("Match the answers" == quiz.type) {
    showMTAQuestions();
  } else {
    showNextQuestionMCQ(index);
  }
  showNextPrevious(index);
}

function saveAnswer(){
  var answered;
  if ("Fill in the blanks" == quiz.type) {
    answered=saveAnswerFIB(quiz.currentIndex);
  } else if ("Opposites" == quiz.type) {
    answered=saveAnswerOPP(quiz.currentIndex);
  } else if ("True or False" == quiz.type) {
    answered=saveAnswerTOF(quiz.currentIndex);
  } else{
    answered=saveAnswerMCQ(quiz.currentIndex);
    resetCheckBoxes(1+quiz.currentIndex);
  }

  if(answered) quiz.questions[quiz.currentIndex].status='answered';
  else quiz.questions[quiz.currentIndex].status='skipped';

  return answered;
}

function goToQuestion(index){
  saveAnswer();
  showNextQuestion(index);
}

function markForReview(){
  var answered = saveAnswer();
  quiz.questions[quiz.currentIndex].status='review';
  if(quiz.currentIndex < (quiz.questions.length-1)) showNextQuestion(quiz.currentIndex+1);
}

function showNextPrevious(index) {

  var bottomButtons = document.createElement('div');
  var htmlStr="";
  var pageHtmlStr="";
  var prev="<button class='btn btn-primary'  onClick='javascript:prevQ();'>Previous</button>&nbsp;&nbsp;";
  var next="<button class='btn btn-primary'  onClick='javascript:nextQ();'>Next</button>&nbsp;&nbsp;";
  // var done="<button class='btn btn-primary'  onClick='javascript:done();'>Done</button>&nbsp;&nbsp;";
  // var review="<button class='btn btn-primary'  onClick='javascript:markForReview();'>Mark for review</button>&nbsp;&nbsp;";



  if ("Match the answers" == quiz.type){
    htmlStr = done;
  } else {
      if (index == (quiz.questions.length - 1)) {

          if (quiz.questions.length > 1) {
              htmlStr = prev + done;
              $('.next-btn').html('Submit');
              $('.next-btn').attr('href', 'javascript:done();');
              $('.previous-btn').removeClass('disabled');
              $('#quiz-Done').hide();
          }
          else if(quiz.questions.length === 1){
              htmlStr = prev + done;
              $('.next-btn').html('Submit');
              $('.next-btn').attr('href', 'javascript:done();');
              $('.next-btn').removeClass('disabled');
              $('#quiz-Done').hide();
          }
          else {
              htmlStr = done;
          }
      }


      else if (index < (quiz.questions.length - 1)) {
          if (index != 0) {
              //htmlStr = prev + review + next;
              $('.previous-btn').removeClass('disabled');
              $('.next-btn').html('Next');
              $('.next-btn').attr('href', 'javascript:nextQ();');
              $('#quiz-Done').show();
          }
          else {
              //htmlStr = review + next;
              $('.next-btn').removeClass('disabled');
              $('.previous-btn').addClass('disabled');
              $('.next-btn').html('Next');
              $('.next-btn').attr('href', 'javascript:nextQ();');
              $('#quiz-Done').show();
          }
      }
  }
    if(immediateFeedback){
        $('.next-btn').html('Check');
        $('.next-btn').attr('href', 'javascript:check();');
        $('.next-btn').addClass('disabled');
        $('.previous-btn').html('');
        $('.previous-btn').addClass('disabled');

    }
  if("Multiple Choice Questions" == quiz.type || "Fill in the blanks" == quiz.type) {

        htmlStr += "<BR><BR><BR>";
        if (siteId == 4 || siteId == -6) {
            var pageNoClass = "pagenumber-grey";
        } else {
            var pageNoClass = "grey-question";
        }
        if (!immediateFeedback){
            pageHtmlStr += "<ul class='question-number-help-wrapper'>" +
                "<li class='dropdown'>" +
                "<a class='dropdown-toggle' id='help-dropdown' data-toggle='dropdown' aria-haspopup='true' aria-expanded='true'>" + "<i class='material-icons'>help_outline</i>" + "</a>" +
                "<ul class='dropdown-menu question-number-help-dropdown' aria-labelledby='help-dropdown'>" +
                "<li class='question-number-help-dropdown-list-item'>" +
                "<div class='answered-indicator'></div>" +
                "<div class='question-number-help-label'>Answered</div>" +
                "</li>" +
                "<li class='question-number-help-dropdown-list-item'>" +
                "<div class='skipped-indicator'></div>" +
                "<div class='question-number-help-label'>Unanswered</div>" +
                "</li>" +
                // "<li class='question-number-help-dropdown-list-item'>" +
                // "<div class='review-marked-indicator'></div>" +
                // "<div class='question-number-help-label'>Marked for review</div>" +
                // "</li>" +
                "<li class='question-number-help-dropdown-list-item'>" +
                "<div class='not-seen-indicator'></div>" +
                "<div class='question-number-help-label'>Not yet seen</div>" +
                "</li>" +
                "<li class='question-number-help-dropdown-list-item'>" +
                "<div class='current-indicator'></div>" +
                "<div class='question-number-help-label'>Current question</div>" +
                "</li>" +
                "</ul>" +
                "</li>" +
                "</ul>";


        for (var index = 0; index < quiz.questions.length; ++index) {
            pageNoClass = "grey-question";
            if (quiz.questions[index].status == 'skipped') {
                pageNoClass = "skipped-question";
            }
            else if (quiz.questions[index].status == 'review') {
                pageNoClass = "review-question";
            }
            else if (quiz.questions[index].status == 'answered') {
                pageNoClass = "answered-question";
            }
            if (index == quiz.currentIndex) {
                pageNoClass = "current-question";
            }

            pageHtmlStr += "<a href='javascript:goToQuestion(" + index + ")' class='" + pageNoClass + "' >" + (index + 1) + "</a>";
        }
    }
    if ("Multiple Choice Questions" == quiz.type || "Fill in the blanks" == quiz.type){
      if(siteId!==4 || siteId!== -6) {
        pageHtmlStr += " ";
      } else {
        pageHtmlStr += "<div class='smallText'> <span class='pagenumber-current' style='height: 15px;width: 15px;'></span>&nbsp;Current Question&nbsp;&nbsp;&nbsp;<span class='pagenumber-green' style='height: 15px;width: 15px;'></span>&nbsp;Answered&nbsp;&nbsp;&nbsp;" +
          "<span class='pagenumber-red' style='height: 15px;width: 15px;'></span>&nbsp;Unanswered&nbsp;&nbsp;&nbsp;<span class='pagenumber-blue' style='height: 15px;width: 15px;'></span>&nbsp;Marked for review&nbsp;&nbsp;&nbsp;<span class='pagenumber-grey' style='height: 15px;width: 15px;'></span>&nbsp;Not seen yet </div><br>";
      }
    } else {
      pageHtmlStr += "<BR><BR><div class='smallText'> <span class='pagenumber-current' style='height: 15px;width: 15px;'></span>&nbsp;Current Question&nbsp;&nbsp;&nbsp;<span class='pagenumber-green' style='height: 15px;width: 15px;'></span>&nbsp;Answered&nbsp;&nbsp;&nbsp;" +
        "<span class='pagenumber-red' style='height: 15px;width: 15px;'></span>&nbsp;Unanswered&nbsp;&nbsp;&nbsp;<span class='pagenumber-blue' style='height: 15px;width: 15px;'></span>&nbsp;Marked for review&nbsp;&nbsp;&nbsp;<span class='pagenumber-grey' style='height: 15px;width: 15px;'></span>&nbsp;Not seen yet </div>";
    }
  }
  else{
    if (index == (quiz.questions.length - 1)) {
      if (quiz.questions.length > 1) htmlStr = prev + done;
      else  htmlStr = done;
    }

    else if (index < (quiz.questions.length - 1)) {

      //if (index != 0) htmlStr = prev + review + next;
      //else htmlStr = review + next;

    }
    htmlStr += "<BR><BR><BR>";
    var pageNoClass = "pagenumber-grey";

    for (var index = 0; index < quiz.questions.length; ++index) {
      pageNoClass = "pagenumber-grey";
      if (quiz.questions[index].status == 'skipped') pageNoClass = "pagenumber-red";
      else if (quiz.questions[index].status == 'review') pageNoClass = "pagenumber-blue";
      else if (quiz.questions[index].status == 'answered') pageNoClass = "pagenumber-green";
      if (index == quiz.currentIndex) pageNoClass = "pagenumber-current";
      pageHtmlStr += "&nbsp;&nbsp;<a href='javascript:goToQuestion(" + index + ")' class='" + pageNoClass + "' style='margin-bottom: 20px'>" + (index + 1) + "</a>";
    }

    pageHtmlStr += "<BR><BR><div class='smallText'> <span class='pagenumber-current' style='height: 15px;width: 15px;'></span>&nbsp;Current Question&nbsp;&nbsp;&nbsp;<span class='pagenumber-green' style='height: 15px;width: 15px;'></span>&nbsp;Answered&nbsp;&nbsp;&nbsp;" +
      "<span class='pagenumber-red' style='height: 15px;width: 15px;'></span>&nbsp;Unanswered&nbsp;&nbsp;&nbsp;<span class='pagenumber-blue' style='height: 15px;width: 15px;'></span>&nbsp;Marked for review&nbsp;&nbsp;&nbsp;<span class='pagenumber-grey' style='height: 15px;width: 15px;'></span>&nbsp;Not seen yet </div>";

  }
  //$("#"+modalName).find('#footer-containter').text(""); // to remove old ones
  if (siteId==4 || siteId== -6) {
    if ("Multiple Choice Questions" == quiz.type||"Fill in the blanks" == quiz.type){
      bottomButtons.innerHTML = htmlStr;
      var questionNumberButtons = document.createElement('div');
      questionNumberButtons.innerHTML = pageHtmlStr;
      $("#"+modalName).find('#questionumber-containter').text(""); // to remove old ones
      $("#"+modalName).find('#questionumber-containter')[0].appendChild(questionNumberButtons);
    } else {
      bottomButtons.innerHTML = htmlStr+pageHtmlStr;
    }

   // $("#"+modalName).find('#footer-containter').text(""); // to remove old ones
    //$("#"+modalName).find('#footer-containter')[0].appendChild(bottomButtons);
  } else {
    if ("Multiple Choice Questions" == quiz.type||"Fill in the blanks" == quiz.type){
      bottomButtons.innerHTML = htmlStr;
      var questionNumberButtons = document.createElement('div');
      questionNumberButtons.innerHTML = pageHtmlStr;
      $("#"+modalName).find('#questionumber-containter').text(""); // to remove old ones
      $("#"+modalName).find('#questionumber-containter')[0].appendChild(questionNumberButtons);
    } else {
      bottomButtons.innerHTML = htmlStr+pageHtmlStr;
      $("#"+modalName).find('#footer-containter')[0].appendChild(bottomButtons);
    }

  }
}

function scoreAndShowAnswers(data){

    dataStorer = data;
  if(immediateFeedback){
    initializeQuestions(data);
  }
  else {
    if (quiz.mode == "read") {
      quiz.isPassage = false;
      if ("true" == data.isPassage) {
        quiz.isPassage = true;
        quiz.passage = data.passage;
      }
    }

    if ("Opposites" == quiz.type) scoreAndShowAnswersOPP(data);
    else if ("True or False" == quiz.type) scoreAndShowAnswersTOF(data);
    else if ("Match the answers" == quiz.type) scoreAndShowAnswersMTA(data);
    else {
            scoreAndShowAnswersMCQ(data);
            $('#questionNumberContainer').addClass('col-md-4');
            $('#questionNumberContainer').removeClass('hidden');
            $('#individualQuestionContainer').addClass('col-md-8');

    }
    $('.loading-icon').addClass('hidden');
  }
}

function nextQ(){
  var answered = saveAnswer();

  if(skipable) {
    $("#" + modalName).find('.unanswered').text("");
    showNextQuestion(1 + (quiz.currentIndex));
  }else {
    if (answered) {
      $("#"+modalName).find('.unanswered').text("");
      showNextQuestion(1 + (quiz.currentIndex));
    } else {
      $("#"+modalName).find('.unanswered').text("Please answer this question before moving on");
    }
  }
}
function prevQ(){
  saveAnswer();
  showNextQuestion((quiz.currentIndex)-1);
}
function done(){
  $('#score-container-div #score-container').show();
  $('.loading-icon').removeClass('hidden');
  $('.next-btn').html("Close");
  $('.next-btn').addClass('close-modal');
  $('.next-btn').attr('href', '#');
  $('#quizModal #review-footer').show();
  $('#review-footer .close-modal').html("Close");
  $('#score-container-div').show();
  $('.quiz-modal-body').hide();
  $('#quiz-modal-footer').hide();
  $('#quiz-modal-footer-mobile').hide();
  $('#answer-holder').hide();
  //$('#answerpassage').hide();
  $('.question-section-main').hide();
  $('#questionumber-containter').hide();
  $('#quiz-Done').hide();
  if ("Match the answers" == quiz.type) {
    scoreAndShowAnswers(quiz);
  } else {
    var answered = saveAnswer();
    if (skipable) {
      $("#" + modalName).find('.unanswered').text("");
      if(immediateFeedback){
        immediateFeedback=false;
        scoreAndShowAnswers(dataStorer);
      }else {
          getAnswers(quiz.link);
      }
    } else {
      if (answered) {
        $("#" + modalName).find('.unanswered').text("");
        getAnswers(quiz.link);

      } else {
        $("#" + modalName).find('.unanswered').text("Please answer this question before moving on");
      }
    }
  }
  if(siteId !==4 || siteId !==-6) {
    $('#score-container').show();
  }

    $('html,body').animate({
            scrollTop: $("#afterGenerateTest").offset().top},
        'slow');

}


function retrieveUserAnswerCheckBox (index) {

  var ua = userAnswers[index];
  if(ua)
    return {'ans1': ua.ans1 ? true : false, 'ans2': ua.ans2 ? true : false, 'ans3': ua.ans3 ? true : false, 'ans4': ua.ans4 ? true : false, 'ans5': ua.ans5 ? true : false};
  else
    return undefined;
}

function retrieveUserAnswerTextInput(index) {
  var ua = userAnswers[index];
  if(ua)
    return ua;
  else
    return "";
}


function resetUserAnswers(){
  userAnswers = []

}

function initializeAnswers(){

  for(var index = 0; index < quiz.questions.length; ++index) {
    quiz.questions[index].status='unseen';
    if ("Multiple Choice Questions" == quiz.type) userAnswers[index] = {
      'ans1': null,
      'ans2': null,
      'ans3': null,
      'ans4': null,
      'ans5': null,
      'skipped':"true"
    }
    else if ("Fill in the blanks" == quiz.type) {
      var qstn = quiz.originalQuestions[index].ps;
      qstn = qstn.split('__');

      var ans;
      userAnswers[index] = {};
      for (var i = 1; i < qstn.length; i++) {
        ans = 'ans' + i;
        if (!$('#fibans' + i).val()) userAnswers[index][ans] = '';
      }
    } else if ("True or False" == quiz.type) {
      //userAnswers[index]="skipped";
      userAnswers[index] = {};
      userAnswers[index]['ans'] = 'skipped';
      userAnswers[index]['id'] = '';
    }
  }
  quiz.questions[0].status='current';

  //resetting timer
  clearInterval(timer);
  isRunning = false;
  minsLeft = interval;
  secsLeft = 0;
  showTime();

}

function playAgain() {
  showQuiz(currentQuizLink,currentQuizId);
}

function scoreMessage(score,length){
  if("true"==quiz.generated){
    //dont update

  }else {
    //updateQuizPoints(currentQuizId, score.correctAnswers);
  }

  {
    endTime =  new Date();
    var percent = (score.correctAnswers/length)*100;
    var accuracy = 0;
     var timeTaken = (endTime - startTime);
     var noOfQuestionsAttempted = (score.correctAnswers+score.wrongAnswers);
     var noOfQuestionsPerHour = 3600 / ((timeTaken/1000)/noOfQuestionsAttempted);

    if(score.correctAnswers > 0) {
      accuracy = (score.correctAnswers/(score.correctAnswers+score.wrongAnswers))*100;
    }
    var msgStr;
    var imgSrc = "";
    var answerStr = "";
    var pageNoClass = "skipped-question";

    // for(var index = 0; index < quiz.questions.length; ++index) {
    //   pageNoClass = "skipped-question";
    //   if (quiz.questions[index].status == 'skipped') pageNoClass = "skipped-question";
    //   else if (quiz.questions[index].status == 'review') pageNoClass = "review-question";
    //   else if (quiz.questions[index].status == 'answered') pageNoClass = "correct-question";
    //   if (index == quiz.currentIndex) pageNoClass = "skipped-question";
    //   answerStr+= "<a href='#"+index+"' class='" + pageNoClass + "'>" + (index + 1) + "</a>";
    // }


    if(percent > 90) {
      imgSrc = "/assets/booksmojo/medal-gold.png";
      msgStr = "<div class='medal-picture'>"+
        "<img class='img-responsive' src='"+imgSrc+"' alt=''/>" +
        "</div>"+
        "<div class='practice-score-string'>"+
        "<p class='practice-score-string-score'> Your score is "+score.correctAnswers+"</p>"+
        "<p>"+"Bravo"+"</p>"+
        "<p>"+"Fancy another quiz?"+"</p>"+
        "</div>";
    }
    else if(percent > 70) {
      imgSrc = "/assets/booksmojo/medal-silver.png";
      msgStr = "<div class='medal-picture'>"+
        "<img class='img-responsive' src='"+imgSrc+"' alt=''/>" +
        "</div>"+
        "<div class='practice-score-string'>"+
        "<p class='practice-score-string-score'> Your score is "+score.correctAnswers+"</p>"+
        "<p>"+"That's Ok"+"</p>"+
        "<p>"+"Practice More"+"</p>"+
        "</div>";
    }
    else if(percent > 40) {
      imgSrc = "/assets/booksmojo/medal-bronz.png";
      msgStr = "<div class='medal-picture'>"+
        "<img class='img-responsive' src='"+imgSrc+"' alt=''/>" +
        "</div>"+
        "<div class='practice-score-string'>"+
        "<p class='practice-score-string-score'> Your score is "+score.correctAnswers+"</p>"+
        "<p>"+"That's Ok"+"</p>"+
        "<p>"+"Practice More"+"</p>"+
        "</div>";
    }
    else  {
      imgSrc = "/assets/booksmojo/medal-none.png";
      msgStr = "<div class='medal-picture'>"+
        "<img class='img-responsive' src='"+imgSrc+"' alt=''/>" +
        "</div>"+
        "<div class='practice-score-string'>"+
        "<p class='practice-score-string-score'> Your score is "+score.correctAnswers+"</p>"+
        "<p>"+"That's Ok"+"</p>"+
        "<p>"+"Practice More"+"</p>"+
        "</div>";
    }
    msgStr = "<div class='practice-score-container'>" +
      "<div class='practice-score'>"+msgStr+"</div>"+
      "</div>"+
      "<div class='answer-summary'>"+
      "<p class='summary-heading'>"+"Summary"+"</p>"+
      "<p class='short-heading'>"+"(Total Questions: "+length+")"+"</p>"+
      "<div class='score-summary row'>"+
      "<div class='col-md-4 col-xs-4'>"+
      "<p>Correct Answers"+"<p>"+
      "<p class='correct-answers'>"+score.correctAnswers+"</p>"+
      "</div>"+
      "<div class='col-md-4 col-xs-4'>"+
      "<p>Wrong Answers"+"<p>"+
      "<p class='wrong-answers'>"+score.wrongAnswers+"</p>"+
      "</div>"+
      "<div class='col-md-4 col-xs-4'>"+
      "<p>Skipped Questions"+"<p>"+
      "<p class='skipped-answers'>"+score.skipped+"</p>"+
      "</div>"+
      "</div>"+
      "<div class='accuracy-summary row'>"+
      "<div class='col-md-12 d-flex justify-content-around' style='text-align:center;'>"+
      "<p class='time-taken'>"+"</p>"+
      msToTime((endTime - startTime))+
      "<p class='answer-accuracy' style='margin-left:15px;'>"+"</p>"+
      "<span class='time-span'>"+Math.round(accuracy)+"%"+"<span class='clearfix'>Accuracy</span>"+"</span>"+
      "<p class='question-hr' style='margin-left:15px;'>"+"</p>"+
      "<span class='time-span' style='border: none;'>"+Math.round(noOfQuestionsPerHour)+"<span class='clearfix'>Questions/Hr</span>"+"</span>"+
      "</div>"+
      "</div>"+
      "</div>"
       ;

    if(subjects.length>0) {
      msgStr +=    "<div class='analysis-summary'>" +
        "<div class='col-md-12'>" +
        "<p class='row-heading'>"+ "Analysis" + "</p>" +
        "<div class='analysis-by-book-detail'>";
      var subjectScoreClass;
      for (i = 0; i < subjects.length; i++) {
        var subjectDetail = subjectDetails[subjects[i].replace(/\W+/g, '')];
        var subjectScorePercent = (subjectDetail.correctAnswers/subjectDetail.noOfQuestions)*100;
        if(subjectScorePercent < 40) subjectScoreClass="badge-danger";
        else if(subjectScorePercent < 80) subjectScoreClass="badge-warning";
        else subjectScoreClass="badge-success";
        msgStr += "<a href='#"+subjects[i].replace(/\W+/g, '')+"' class='analysis-book-name' data-toggle='collapse' aria-expanded='false'>";

      if(siteId==1) {
          msgStr += "<span style='overflow: hidden;text-overflow: ellipsis;display: -webkit-box;-webkit-line-clamp: 2;-webkit-box-orient: vertical;'>" + subjects[i] + "</span>" +
              "<span class='badge ml-2 " + subjectScoreClass + "' style='font-weight: normal;font-size: 100%;display: flex;align-items: center;'>" + subjectDetail.correctAnswers + "/" + subjectDetail.noOfQuestions + "</span>";
      } else {
          msgStr += "<span>" + subjects[i] + "</span>"+
              "<span class='badge ml-2 " + subjectScoreClass + "' style='font-weight: normal;font-size: 100%;'>" + subjectDetail.correctAnswers + "/" + subjectDetail.noOfQuestions + "</span>";
      }

          msgStr +="<i class='icon-chevron simple'></i>" +
          "</a>" +
          "<hr class='collapse-hr'>" +
          "<div class='collapse collapsed-detail-container' id='"+subjects[i].replace(/\W+/g, '')+"'>"+
          "<ul class='collapsed-detail-wrapper'>" ;
        var chapterKeys = Object.keys(subjectDetail.chapters);
        for(j=0;j<chapterKeys.length;j++) {
          msgStr += "<li class='collapsed-detail-list-item'>"+
            "<p class='collapsed-detail-list-item-chapter'>" + subjectDetail.chapters[''+chapterKeys[j]].name +
            "<span class='collapsed-detail-list-item-chapter-score'>" + subjectDetail.chapters[''+chapterKeys[j]].correctAnswers+ "/" +subjectDetail.chapters[''+chapterKeys[j]].noOfQuestions+ "</span>" +
            "</p>" +
            "</li>";

        }
        msgStr += "</ul>" +
          "</div>" ;
      }
      msgStr +=
        "<a href='#collapsed-table' class='expand-table-btn' data-toggle='collapse' aria-expanded='false'>" + "View in depth details" +"</a>" +
        "<div class='col-md-12 table-div collapse' id='collapsed-table'>" +
        "<table class='table table-responsive tabel-striped detailed-table mb-0'>" +
        "<thead>";
      msgStr +=
        "<tr>" +
        "<th class='table-chapters'>" + "Chapter" + "</th>"+
        "<th>" + "Total Questions" + "</th>"+
        "<th>" + "Skipped" + "</th>"+
        "<th class='correct'>" + "Correct" + "</th>"+
        "<th class='incorrect'>" + "Incorrect" + "</th>"+
        "</tr>";
      for (i = 0; i < subjects.length; i++) {
        var subjectDetail = subjectDetails[subjects[i].replace(/\W+/g, '')];
        var chapterKeys = Object.keys(subjectDetail.chapters);
        for(j=0;j<chapterKeys.length;j++) {
          msgStr +=  "<tr>" +
            "<td class='table-chapters-data'>" + subjectDetail.chapters[''+chapterKeys[j]].name + "</td>"+
            "<td>" +  subjectDetail.chapters[''+chapterKeys[j]].noOfQuestions + "</td>"+
            "<td>" +  subjectDetail.chapters[''+chapterKeys[j]].skipped + "</td>"+
            "<td class='correct'>" + subjectDetail.chapters[''+chapterKeys[j]].correctAnswers + "</td>"+
            "<td class='incorrect'>" +  subjectDetail.chapters[''+chapterKeys[j]].wrongAnswers + "</td>"+
            "</tr>";

        }
      }
      msgStr +=   "</thead>" +
        "</table>" +
        "</div>" +
        "</div>" +
        "</div>" ;
    }

    var suggestion="";
    if(percent==100){
      suggestion+="You have aced it. Great job. Continue with the same focus to do well in your exams. ";
    }
    else if(percent>90){
      suggestion+="You have got "+Math.round(percent)+"% right answers. Try little harder and you can get that 100. ";
    }
    else if(percent>80){
      suggestion+="You have got "+Math.round(percent)+"% right answers. Little more effort and practice can definitely take you higher. ";
    }else if(percent>60){
      suggestion+="You have got "+Math.round(percent)+"% right answers. Keep practicing more and you will definitely get better at this. ";
    }
    else if(percent>40){
      suggestion+="You have got "+Math.round(percent)+"% right answers. Keep practicing more and you will definitely get better at this. Go through the concepts again and understand better. ";
    }else{
      suggestion+="You have got "+Math.round(percent)+"% right answers. No need to lose heart. Learn the concepts again, understand it better. By understanding the concepts and practicing you can definitely get better. ";
    }

    if(noOfQuestionsPerHour > 60){
      suggestion+="You are answering the quiz very fast. Pace your answering to improve your performance. Since you are able to answer the questions relatively fast, spend the remaining time " +
        "to review your answers. ";
    }else  if(noOfQuestionsPerHour < 40){
      suggestion+= "You are answering the quiz slowly. Pace your answering to improve your performance. Attempt the questions which you know the answer first and then try the ones which you " +
        "are not sure. ";
    }else{
      suggestion+= "You answering pace is good. " ;
    }

    if(accuracy<80){
      suggestion+="You accuracy is "+Math.round(accuracy)+" percent. Number of wrong answers will have reduce your overall score due to negative marking. Do not attempt the question if you are not sure of the answer. "
        ;
    }else if(accuracy>79&&accuracy<91){
      suggestion+= "You accuracy is "+Math.round(accuracy)+" percent.You are doing good. Number of wrong answers will have reduce your overall score due to negative marking. Do not " +
        "attempt the question if you are not sure of the answer. "
      ;
    }else if(accuracy>90&&accuracy<100){
      suggestion+= "You accuracy is "+Math.round(accuracy)+" percent.You are doing great.Keep going, you can get to 100 percent. "
      ;
    }else if(accuracy==100){
      suggestion+= "Well done in terms of accuracy. All your attempted answers are correct. "
      ;
    }




    if(nameoftheuser){
      suggestion = "Hey "+nameoftheuser+", "+suggestion;
    }
    msgStr += "<div class='div-separator'></div>" +
      "<div class='suggestions-for-user'>" +
      "<p class='row-heading'>"+ "Suggestions" + "</p>" +
        "<hr class='collapse-hr'>" +
      "<p class='suggested-topics'>" + suggestion +
      "</p>";
    msgStr +=   "</div>";

    if(showBookRec==true) {
      msgStr += "<div class='div-separator'></div>" +
      "<div class='demo-books col-md-12 col-sm-12 col-xs-12'>" +
      "<p class='suggested-books'>" + "Suggested books for you" + "</p>" +
      "<hr class='collapse-hr'>" +
      "<div class='col-md-3 col-sm-4 col-xs-12 demo-book-wrapper'>" +
      "<a href='https://arihantbooks.com/40-days-chemistry-for-neet' target='_blank'>" +
      "<div class='demo-book-img-wrapper'>" +
      "<img src='https://arihantbooks.com/resources/image/19/21/d.jpg' class='img-responsive'>" +
      "</div>" +
      "<div class='demo-book-info'>" +
      "<p class='demo-book-name'>"+ "40 Days Chemistry for NEET" + "</p>" +
      "</div>" +
      "</a>" +
      "</div>"+

      "<div class='col-md-3 col-sm-4 col-xs-12 demo-book-wrapper'>" +
      "<a href='https://arihantbooks.com/neet-10-practice-sets?keyword=NEET%2010%20Practice%20Sets' target='_blank'>" +
      "<div class='demo-book-img-wrapper'>" +
      "<img src='https://arihantbooks.com/resources/image/19/2a/8.jpg' class='img-responsive'>" +
      "</div>" +
      "<div class='demo-book-info'>" +
      "<p class='demo-book-name'>"+ "NEET 10 Practice Sets" + "</p>" +
      "</div>" +
      "</a>" +
      "</div>"+

      "<div class='col-md-3 col-sm-4 col-xs-12 demo-book-wrapper'>" +
      "<a href='https://arihantbooks.com/11-years-cbse-aipmt--neet--solved-papers-2006-2016?path=__' target='_blank'>" +
      "<div class='demo-book-img-wrapper'>" +
      "<img src='https://arihantbooks.com/resources/image/19/21/9.jpg' class='img-responsive'>" +
      "</div>" +
      "<div class='demo-book-info'>" +
      "<p class='demo-book-name'>"+ "12 Years Solved Papers CBSE AIPMT & NEET" + "</p>" +
      "</div>" +
      "</a>" +
      "</div>"+

      "<div class='col-md-3 col-sm-4 col-xs-12 demo-book-wrapper'>" +
      "<a href='https://arihantbooks.com/combined-higher-secondary-and-constable/ssc-10-2-solved-papers-sanyukt-higher-secondary' target='_blank'>" +
      "<div class='demo-book-img-wrapper'>" +
      "<img src='https://arihantbooks.com/resources/image/19/53/e.jpg' class='img-responsive'>" +
      "</div>" +
      "<div class='demo-book-info'>" +
      "<p class='demo-book-name'>"+ "SSC (10+2) Solved Papers Sanyukt Higher Secondary" + "</p>" +
      "</div>" +
      "</a>" +
      "</div>"+
      "</div>" +
      "</div>";
    }
    return msgStr;
  }
}



function seeResults() {
  $('#score-container-div #score-container').show();
  $('.next-btn').html("Close");
  $('.next-btn').addClass('close-modal');
  $('.next-btn').attr('href', '#');
  $('#quizModal #review-footer').show();
  $('#review-footer .close-modal').html("Close");
  $('#score-container-div').show();
  $('.quiz-modal-body').hide();
  $('#quiz-modal-footer').hide();
  $('#quiz-modal-footer-mobile').hide();
  $('#answer-holder').hide();
  //$('#answerpassage').hide();
  $('.question-section-main').hide();
  $('#questionumber-containter').hide();
  $('#quiz-Done').hide();

    $('html,body').animate({
            scrollTop: $("#afterGenerateTest").offset().top},
        'slow');
}

function showPerformanceDetails() {
  $('#quizModal #review-footer').hide();
  $('.mcq-learn').show();
  $('#score-container-div').hide();
  $('#review-footer').hide();
  $('.mcq-question-div').removeClass('col-md-12').addClass('col-md-8');
  $('.questionumber-containte').show();
  $('.quiz-modal-body').show();
  $('#answer-holder').show();
  $('#questionumber-containter').show();
  $('#score-container').hide();
  $('#quiz-modal-footer').show();
  $('#quiz-modal-footer-mobile').show();
  $('#quiz-Done').show();
  $('#quiz-Done').html("See Result");
  $('#quiz-Done-mobile').html("See Results");
  $('#quiz-Done').attr('href', 'javascript:seeResults();');
  $('#quiz-Done').css({
    'color' : '#444444'
  });
  $('.previous-btn').hide();
  $('.next-btn').html("Close");
  $('.next-btn').addClass('close-modal');
  $('.next-btn').attr('href', '#');
  if($(window).width() > 576) {
      document.getElementById('prevButton').setAttribute("style", "opacity: 0;cursor: default;");
  } else {
      document.getElementById('prevButton').setAttribute("style", "display:none !important;");
  }

    $('html,body').animate({
            scrollTop: $("#afterGenerateTest").offset().top},
        'slow');

}

function msToTime(duration) {
  var milliseconds = parseInt((duration%1000)/100),
    seconds = parseInt((duration/1000)%60),
    minutes = parseInt((duration/(1000*60))%60),
    hours = parseInt((duration/(1000*60*60))%24);
  var timeStr="";
  var timeStrHeading = "<span class='clearfix'>Time</span>";
  if(siteId == 4 || siteId == -6) {
    if(hours>0) timeStr=hours+" hours ";
    if(minutes>0) timeStr+=minutes+" min ";
    timeStr +=seconds+"."+milliseconds+" seconds";
  } else {
    if(hours>0) timeStr="<span class='time-span'>"+hours+"h"+"</span>";
    if(minutes>0) timeStr+="<span class='time-span'>"+minutes+"m"+"</span>";
    timeStr +="<span class='time-span'>"+seconds+"."+milliseconds+"s"+timeStrHeading+"</span>";
  }
  return timeStr;
}

function showResultDetails(){
  $("#resultdetails").toggle(600);
  if((document.getElementById("showperlink").text).indexOf('Show')>-1) {
    document.getElementById("showperlink").innerHTML = "Hide Performance Details&nbsp;&nbsp;<i class='fa fa-minus'></i>";

  }
  else
    document.getElementById("showperlink").innerHTML="Show Performance Details&nbsp;&nbsp;<i class='fa fa-plus'></i>";
}
var marksData = [];
var marksAccuracyData=[];

var chartsReady=false;

var topperScore=0;
var averageScore=0;
var userAverage=0;

function displayChart(){
  if(chartsReady) drawChart();
}

google.charts.load('current', {'packages':['bar','corechart']});

function showQuizScoreAnalytics(data){
  $('.loading-icon').addClass('hidden');
  $('.quiz-modal-body').show();
  document.getElementById("analyticsid").innerHTML = "";
  marksData = [];
  marksAccuracyData=[];
  var noTestImg = "<img src='/assets/desert_plants.png' class='img-responsive'>";
  if(data.status!="NOT_LOGGED") {
    if(data.totalAttempts!="0") {
      topperScore = data.topperScore;
      averageScore = data.averageScore;
      userAverage = data.userAverage;
    }


    var analytics = data.results;
    var date;
    var htmlStr ="";

    var name = "";
    var accuracy = 0;
    var correctpercentage=0;

    for (var i = 0; i < analytics.length; ++i) {
      date = "";
      accuracy = (analytics[i].correctAnswers / (analytics[i].correctAnswers + analytics[i].wrongAnswers)) * 100;
      correctpercentage  = (analytics[i].correctAnswers / (analytics[i].correctAnswers + analytics[i].wrongAnswers + analytics[i].skipped)) * 100;
      marksData.unshift(['' + moment(analytics[i].quizDate).format("DD-MMM-YY hh:mm"), analytics[i].correctAnswers]);
      marksAccuracyData.unshift(['' + moment(analytics[i].quizDate).format("DD-MMM-YY hh:mm"), Math.round(correctpercentage),Math.round(accuracy)]);

    }
    htmlStr = "<div class='row justify-content-around exercise-score-container pt-3 pb-3 bgScore'>"+
      "<div class='text-center'>"+
      "<p class='exercise-text'>Topper's Score"+"<p>"+
      "<p class='exercise-scores'>"+"<span>"+parseFloat(Math.round(topperScore * 100) / 100).toFixed(2)+"</span>"+"</p>"+
      "</div>"+
      "<div class='text-center'>"+
      "<p class='exercise-text'>Avg. Score"+"<p>"+
      "<p class='exercise-scores'>"+"<span>"+parseFloat(Math.round(averageScore * 100) / 100).toFixed(2)+"</span>"+"</p>"+
      "</div>"+
      "<div class='text-center'>"+
      "<p class='exercise-text'>Your Avg. Score"+"<p>"+
      "<p class='exercise-scores'>"+"<span>"+parseFloat(Math.round(userAverage * 100) / 100).toFixed(2)+"</span>"+"</p>"+
      "</div>"+
      "</div>";
    if(data.status=="OK") {
      htmlStr += "<div class='row'>"+
        "<div class=' total-question'>"+
        "<p class='total-question-num'>Total no. of Questions: <span class='questions'>"+data.totalQuestions+"</span></p>"+
        "</div>"+
        "</div>"+
        "<div id='chart_div'></div>"+
        //   "<div class='row'><div class='col-md-8 col-md-offset-2'><h4>Performance comparison</h4></div></div><div class='row2'><div class='col-md-10 col-md-offset-2'>" +
        // "<div id='chart_div2'></div> </div></div><hr>"+
        "<div class='row'><div class='col-md-8'><h4>Correct answers and accuracy</h4></div></div><div class='row'><div class='col-md-12'>" +
        "<div id='chart_div1'></div> </div></div>";
      marksData.unshift(['Date', 'Score']);
      marksAccuracyData.unshift(['Date', 'Correct Answers', 'Accuracy']);
      // google.charts.setOnLoadCallback(drawChart);
      document.getElementById("analyticsid").innerHTML = htmlStr;
      drawChart();
    } else {
      htmlStr +=
        "<div class='row answer-summary' style='position: relative; border-bottom:0;'>"+
        "<div class='col-md-12 col-sm-12 col-xs-12 take-test'>" +
        noTestImg+
        "<p class='no-test-text'>Please attempt the test to see your score."+"</p>";
      "</div>"+
      "</div>";

    }
    document.getElementById("analyticsid").innerHTML = htmlStr;


  } else {
    document.getElementById("analyticsid").innerHTML = "<div class='row answer-summary' style='position: relative; border-bottom:0; min-height: 750px; max-height: 750px;'>"+
      "<div class='col-md-12 col-sm-12 col-xs-12 take-test'>" +
      noTestImg+
      "<p class='no-test-text'>Please <a href='/funlearn/signIn' class=\"plz-login\">Login!</a> to see this."+"</p>";
    "</div>";
  }
}

function drawChart() {
  var data = google.visualization.arrayToDataTable(marksData);

  var options = {
    bars: 'vertical',
    vAxis: {format: 'decimal'},
    bar: {groupWidth: "95%"},
    legend: {position: "none"},
    height: 200,
    width: 1000,
    colors: ['#1b9e77']
  };

  var chart = new google.charts.Bar(document.getElementById('chart_div'));

  chart.draw(data, google.charts.Bar.convertOptions(options));

  var data1 = google.visualization.arrayToDataTable(marksAccuracyData);

  var options1 = {
    curveType: 'function',
    legend: {position: "none"},
    width: 1000
  };

  var chart1 = new google.visualization.LineChart(document.getElementById('chart_div1'));

  chart1.draw(data1, options1);

  var data2 = google.visualization.arrayToDataTable([
    ['User', 'Score'],
    ['Topper',Math.round(topperScore)],            // RGB value
    ['Average', Math.round(averageScore)],            // English color name
    ['Your Average Score', Math.round(userAverage)]
  ]);
  var options2 = {
    bars: 'vertical',
    height: 400,
    width: 800,
    colors: ['#fc635e']
  };

  var chart3 = new google.charts.Bar(document.getElementById('chart_div2'));

  chart3.draw(data2, google.charts.Bar.convertOptions(options2));
}

function shuffleArray(array) {
  for (var i = array.length - 1; i > 0; i--) {
    var j = Math.floor(Math.random() * (i + 1));
    var temp = array[i];
    array[i] = array[j];
    array[j] = temp;
  }
  return array;
}



