


/* ============ */
/* EXAMPLE TOUR */
/* ============ */
var tour = {
  id: 'hello-wonderslate',
  steps: [
//    {
//      target: 'hopscotch-title',
//      title: 'Welcome to Wonderslate!',
//      content: 'Hey there! This is an example of Wonderslate Tour. Let\'s take you in a tour of Wonderslate, How it works, What are the best features of Wonderslate etc, just follow me !.',
//      placement: 'bottom',
//      arrowOffset: 60
//    },
    {
      target: document.querySelectorAll('#general-use-desc code')[1],
      title: 'Where to begin',
      content: 'Check out our notes, mind maps, quizzes, free video tutorials, etc. but Sign In or Register for more!',
      placement: 'right',
      yOffset: -20
    },
    {
      target: 'register-popover',
      title: 'Facebook',
      content: 'Register using your Facebook account.',
      placement: 'bottom',
      xOffset: 270,
      yOffset: 57
    },
    {
      target: 'signIn-popover',
      title: 'Google+',
      content: 'Register using your G+ account. ',
      placement: 'bottom',
      xOffset: 410,
      yOffset: 55
    },
    {
      target: 'profile-popover',
      placement: 'right',
      title: 'Your Profile',
      content: 'You can create, edit and update your profile by clicking on the profile picture.',
      yOffset: -21
    },
    {
      target: 'editProfile-popover',
      placement: 'bottom',
      title: 'Edit your profile',
      content: 'If you want to edit your profile click on your profile picture.',
      xOffset: 55,
      yOffset: -70
    },
    {
      target: 'start-tour-popover',
      placement: 'right',
      title: 'Discover',
      content: 'You can search for the contents using Discover drop downs.',
      yOffset: -112
    },
    {
      target: 'video-popover',
      placement: 'bottom',
      title: 'Quiz',    
      content: 'Take a quiz here to practice',
      xOffset: 321,
      yOffset: 65
    },
    {
      target: 'add-popover',
      placement: 'top',
      title: 'Adding your study materials',
      content: 'Click on the add button to see all feature options and choose and click on any one. For example, if you want to upload a note then  click on that & choose your file.',
      xOffset: 70,
      yOffset: -70
    },
    {
      target: 'add-chapter-topic-popover',
      placement: 'bottom',
      title: 'Adding new chapter /topic ',
      content: 'To add new chapter choose your Syllabus, Grade, Subject and finally enter Chapter/Topic. Then click on the Add button. ',
      yOffset: -68,
      xOffset: 279
    },
    {
      target: 'my-library-popover',
      placement: 'bottom',
      title: 'Add your favourite things in your library',
      content: 'This is place which will store all your favourites and the study materials you have created.'
    },
    {
      target: 'find-friends1-popover',
      placement: 'right',
      title: 'Find your friends',
      content: 'Click on the Find Friends option and search for your friends by typing their name or W.I.N number.',
      yOffset: -180,
      xOffset: -120
    },
    {
      target: 'find-friends2-popover',
      placement: 'bottom',
      title: 'Add your friends',
      content: 'Beside the profile link of a friend who is not yet added to your circle, you will find an add sign. On clicking on it, a request to be friends shall be sent.',
      yOffset: -60,
      xOffset: 377
    },
    {
      target: 'groups-popover',
      placement: 'top',
      title: 'How to create group',
      content: 'To create a group you have to click on the groups option from the top navigation bar, then just simply click on the Create Group button & type your group name.',
      xOffset: 512,
      yOffset: -110
    },
    {
      target: 'create-groups-popover',
      placement: 'right',
      title: 'Your new group',
      content: 'Type your group name & click on the <b>Create</b> button.',
      xOffset: -294,
      yOffset: -123
    },
    {
      target: 'notifications-popover',
      placement: 'top',
      title: 'Your notifications',
      content: 'To check your notifications go to the top navigation bar & click on it.',
      xOffset: 657,
      yOffset: -150
    },
    {
      target: 'hopscotch-title',
      placement: 'bottom',
      title: 'You\'re all set!',
      content: 'Now we hope, it\'ll help you to use Wonderslate !'
    }
   
  ],
  showPrevButton: true,
  scrollTopMargin: 100
},

/* ========== */
/* TOUR SETUP */
/* ========== */
addClickListener = function(el, fn) {
  if (el.addEventListener) {
    el.addEventListener('click', fn, false);
  }
  else {
    el.attachEvent('onclick', fn);
  }
},

init = function() {
  var startBtnId = 'startTourBtn',
      calloutId = 'startTourCallout',
      mgr = hopscotch.getCalloutManager(),
      state = hopscotch.getState();

  if (state && state.indexOf('hello-wonderslate:') === 0) {
    // Already started the tour at some point!
    hopscotch.startTour(tour);
  }
  else {
    // Looking at the page for the first(?) time.
    setTimeout(function() {
      mgr.createCallout({
        id: calloutId,
        target: startBtnId,
        placement: 'right',
        title: 'Start the tour',
        content: '',
        yOffset: -25,
        arrowOffset: 20,
        width: 240
      });
    }, 100);
  }

  addClickListener(document.getElementById(startBtnId), function() {
    if (!hopscotch.isActive) {
      mgr.removeAllCallouts();
      hopscotch.startTour(tour);
    }
  });
};

init();


