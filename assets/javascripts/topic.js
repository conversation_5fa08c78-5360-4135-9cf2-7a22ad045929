var formattedTopicData = {};
var downloadFileId;
var activityNumber=0;
var chapterReadPresent=false;
var chapterReadId=-1;
var chapterBookId=-1;
var chapterChapterId=-1;
var currentReadId=-1;
var studySetResId =-1;
var chapterName;
var chapterGrade;
var chapterSyllabus;
var chapterSubject;
var chapterLevel;
var qas;
var revisionPresent=false;
var instructorControlledBook=false;
var instructor=false;
var linkName;
var link;
var readPresent=false;
var qandaPresent=false;
var quizPresent=false;
var videoPresent = false;
var notesPresent = false;
var videoAvailableInApp=false;
var chapterDesc="";
var seenResources;
var receivedRes=false;
var receivedResType;
var receivedResLink;
var resSharing;
var fullData = [];

function SortAscByDateCreated(x,y) {
    return ((x.dateCreated == y.dateCreated) ? 0 : ((x.dateCreated > y.dateCreated) ? 1 : -1 ));
}

function SortDescByDateCreated(x,y) {
    return ((x.dateCreated == y.dateCreated) ? 0 : ((x.dateCreated < y.dateCreated) ? 1 : -1 ));
}

function formatTopicData(data){

    "use strict";
    fullData = [];
    console.log('fuu',fullData);
    videoAvailableInApp=false;
    formattedTopicData.quizs = [];
    formattedTopicData.videos = [];
    formattedTopicData.relVidoes = [];
    formattedTopicData.mindmaps = [];
    formattedTopicData.notes = [];
    formattedTopicData.weblinks = [];
    formattedTopicData.qAndA = [];
    formattedTopicData.flashCards = [];
    formattedTopicData.shortQAndA = [];
    formattedTopicData.keyValues = [];
    formattedTopicData.userCreatedNotes = [];
    formattedTopicData.videoexplanation=[];
    
    var length = data.length;
    if(length == 0) {
        //addpopover();
    }

    data.sort(SortAscByDateCreated);

    for(var i =0; i < length; ++i){
        var item = data[i];
        if(i==0){
            chapterName = item.topicName;
            chapterGrade = item.grade;
            chapterSyllabus = item.syllabus;
            chapterSubject = item.subject;
            chapterLevel = item.level;
            chapterDesc = item.chapterDesc;
        }

        if(item.resType=='Reference Videos'&&item.resLink==''){
            videoAvailableInApp=true;
            continue;
        }

        var temp = {};
        temp.id = item.id;
        temp.link = item.resLink;
        temp.title = item.resName;
        temp.topicId = item.topicId;
        temp.topicName = item.topicName;
        temp.syllabus = item.syllabus;
        temp.grade = item.grade;
        temp.subject = item.subject;
        temp.dateCreated = item.dateCreated;
        temp.resType = item.resType;
        temp.canDelete = item.canDelete;
        temp.canEdit = item.canEdit;
        temp.canApprove = item.canApprove;
        temp.creatorname = item.creatorname;
        temp.wonderSlateEmployee = item.wonderSlateEmployee;
        temp.noOfViews = item.noOfViews;
        temp.noOfLikes = item.noOfLikes;
        temp.noOfFavourites = item.noOfFavourites;
        temp.canShare = item.canShare;
        temp.canPublish = item.canPublish;
        temp.sharing = item.sharing;
        temp.userId = item.userId;
        temp.quizMode = item.quizMode;
        temp.hasAlreadyLiked = item.hasAlreadyLiked;
        temp.hasAlreadyFav = item.hasAlreadyFav;
        temp.chapterDesc = item.chapterDesc;
        temp.noOfPages = item.noOfPages;
        temp.bookId = item.bookId;
        temp.fileType=item.fileType;
        temp.revisionPresent = item.revisionPresent;
        temp.eBook = item.eBook;
        temp.filename = item.filename;
        temp.testStarted =  item.testStarted;
        temp.videoPlayer = item.videoPlayer;
        temp.testStartDate = item.testStartDate;
        temp.testEndDate = item.testEndDate;
        temp.testEnded = item.testEnded;
        temp.allowComments = item.allowComments;
        temp.displayComments = item.displayComments;
        temp.testResultDate = item.testResultDate;
        temp.testResultAnnounced = item.testResultAnnounced;
        temp.downloadlink1 = item.downloadlink1;
        temp.downloadlink2 = item.downloadlink2;
        temp.downloadlink3 = item.downloadlink3;
        temp.createdBy = item.createdBy;

        if(receivedResId==temp.id){
            receivedRes=true;
            receivedResType=temp.resType;
            receivedResLink=temp.link;
            resSharing = temp.sharing;
        }
        fullData.push(temp);
        //making videos and reference videos to work together
        switch(item.resType){
            case 'Mind Maps':
                formattedTopicData.mindmaps.push(temp);
                break;
            
            case 'Videos':
                formattedTopicData.relVidoes.push(temp);
                break;
            
            case 'Notes':
                if(item.sharing==null)
                        formattedTopicData.notes.push(temp);
                else
                    formattedTopicData.userCreatedNotes.push(temp);
                break;
            
            case 'Reference Videos':
                formattedTopicData.relVidoes.push(temp);
                break;
            
            case 'Reference Web Links':
                formattedTopicData.weblinks.push(temp);
                break;
            
            case 'QA':
                    formattedTopicData.qAndA.push(temp);
                break;
            
            case 'Short QA':
                formattedTopicData.shortQAndA.push(temp);
                break;
            
            case 'KeyValues':
                formattedTopicData.keyValues.push(temp);
                break;

            case 'videoexplanation':
                formattedTopicData.videoexplanation.push(temp);
                break;
            
            default:
            temp.type = item.resType;
            formattedTopicData.quizs.push(temp);
        }
    }

    fullData.sort(SortAscByDateCreated);
}

function initializeTopicData(data) {
    $("#newResourceTable").show();
    activityNumber=1;
    studySetResId = -1;
    formatTopicData(data.results);
    seenResources = data.seenResouces;
    readPresent=false;
    qandaPresent=false;
    quizPresent=false;
    videoPresent = false;
    notesPresent = false;

    if("bookauthor" == data.mode) {
        displayChapterForAuthor(data.results);
    } else {
        if("book"==pageType){
            chapterReadPresent=false;
            chapterReadId=-1;
            chapterBookId=-1;
            chapterChapterId=-1;
        }
        console.log("coming here before calling populateAllSection");
     if(siteId!=12) populateAllSection(data);
        //hide all tabs first. Open only which has content
        $("#tabread").parents('li').hide();
        $('.zoom-icons-list-item').hide();
        $('.section-btns').hide();
        $("#tablongqa").parents('li').hide();
        $("#tab-short-qa").parents('li').hide();
        $("#tabquiz").parents('li').hide();
        $("#tab-flash-cards").parents('li').hide();
        if(formattedTopicData.videos.length > 0){
            populateVideoSection(formattedTopicData.videos);
        } else {
            $("withnovideos").show();
        }

        if(formattedTopicData.notes.length > 0){
            $("#tabread").parents('li').show();
            $('.zoom-icons-list-item').show();
            $('.section-btns').show();
            readPresent=true;
            populateReadSection(formattedTopicData.notes);
        }
    
        if(formattedTopicData.userCreatedNotes.length > 0){
            $("#tabusernotes").parents('li').show();
            populateNotesSection(formattedTopicData.userCreatedNotes);
            notesPresent = true;
        }

        if(formattedTopicData.qAndA.length > 0){
            $("#tablongqa").parents('li').show();
            populateQASection(formattedTopicData.qAndA);
        
            if(!readPresent&&siteId==9){
                $('#chapter-details-tabs a[href="#long-qa"]').tab('show');
                qandaPresent = true;
            }
        }

        if(formattedTopicData.shortQAndA.length > 0){
            $("#tab-short-qa").parents('li').show();
            populateQASection(formattedTopicData.shortQAndA);
            
            if(!readPresent && !qandaPresent&&siteId==9){
                $('#chapter-details-tabs a[href="#tab-short-qa"]').tab('show');
            }
        }

        if(formattedTopicData.mindmaps.length>0)
            populateContentGenericMethod(formattedTopicData.mindmaps, 'mindmaps');
    
        if(formattedTopicData.quizs.length > 0){
            $("#tabquiz").parents('li').show();
            quizPresent = true;
            populateQuizSection(formattedTopicData.quizs);
            
            if(!readPresent && !qandaPresent&&siteId==9){
                $('#chapter-details-tabs a[href="#learn"]').tab('show');
            }
        }
       
        if(formattedTopicData.relVidoes.length > 0){
            $("#tabvideo").parents('li').show();
            videoPresent = true;
            populateVideoSection(formattedTopicData.relVidoes);
            $("#videoAddButton").show();
            $("#withnovideos").hide();
            $("#videoInAppOnly").hide();
        } else {
            $("#withnovideos").show();
            $("#videoAddButton").hide();
            
            if(videoAvailableInApp)  $("#videoInAppOnly").show();
        }    
       
        additional.innerHTML="";
        
        if(formattedTopicData.weblinks.length > 0){
            $("#tabweblinks").parents('li').show();
            populateWebLinks(formattedTopicData.weblinks);
            $("#addRefButton").show();
            $("#withnoweblinks").hide();
            
            if(siteId!=9 && !readPresent && !quizPresent && !notesPresent){
                $('#chapter-details-tabs a[href="#additional"]').tab('show');
            }
        } else {
            $("#withnoweblinks").show();
            $("#addRefButton").hide();
            $("#additional-refs").hide();
        }
    
        if(siteId==9 && formattedTopicData.keyValues.length > 0){
            $("#tab-flash-cards").parents('li').show();
            populateFlashcardSection(formattedTopicData.keyValues);
      
            if(!readPresent && !qandaPresent && !quizPresent && !videoPresent){
                $('#chapter-details-tabs a[href="#flash-cards"]').tab('show');
            }
        }

        $('[data-toggle="popover"]').popover();
        $('.loading-icon').addClass('hidden');
        $("#tabStudySet").parents('li').show();
        
        if(formattedTopicData.keyValues.length > 0){
            populateRevisionSection(formattedTopicData.keyValues);
        }
       
        if(data.results[0].revisionPresent){
            revisionPresent=true;
        } else {
            $("#tabStudySet").parents('li').hide();
            $("#ExportToStudySet").hide();
        }

        if(data.results.length>0 && siteId==9 && quizPresent){
            $("#tabcreatetest").parents('li').show();
        }

        if(notesCreationMode){
            $('#chapter-details-tabs a[href="#userNotes"]').tab('show');
            notesCreationMode= false;
        }

    }

    if(receivedRes){
       if("Multiple Choice Questions"==receivedResType){
             window.location="/funlearn/quiz?fromMode=book&resId="+receivedResId+"&quizMode=practice&quizId="+receivedResLink;
       } else if("Notes"==receivedResType){
           if(resSharing==null) {
                displayReadingMaterial(receivedResId);
           } else {
                $('#chapter-details-tabs a[href="#userNotes"]').tab('show');
                getNotesData(receivedResId);
           }
       } else if("Reference Web Links"==receivedResType){alert
            $('#chapter-details-tabs a[href="#additional"]').tab('show');
            openWebRef(receivedResId,receivedResLink);
       } else if("KeyValues"==receivedResType){
            $('#chapter-details-tabs a[href="#studySets"]').tab('show');
            getStudySets(receivedResId);
       } else if("Reference Videos"==receivedResType){
            $('#chapter-details-tabs a[href="#videos"]').tab('show');
            playVideo(receivedResLink,receivedResId);
       }
   }
}

function displayChapterForAuthor(allItems){
    if(!("sage" == defaultSiteName)) {
        myResources(allItems);
    }
    var addedContents="";
    $("#chapterdetails").show(500);
    if(("sage" == defaultSiteName)) {
        addedContents += " <thead>\n" +
            "                <tr>\n" +
            "                    <th>Name</th>\n" +
            "                    <th>Type</th>\n" +
            "                    <th>Action</th>\n" +
            "                    <th class='hideArrow'></th>\n" +
            "                    <th class='hideArrow'></th>\n" +
            "                    <th>Resource Id</th>\n" +
            "                    <th class='hideArrow'></th>\n" +
            "                    <th>Added by</th>\n" +
            "                </tr>\n" +
            "                </thead>";


        if (formattedTopicData.notes.length > 0) {
            for (var i = 0; i < formattedTopicData.notes.length; ++i) {
                var data = formattedTopicData.notes[i];
                console.log(data);


                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;Reading material</td>";
                    if (siteId != 12) {
                        addedContents += "<td> <a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")' class='darkgrey'>" +
                            "&nbsp;<span class='light10text'>View</span> </a></td>";
                    }
                    addedContents += "<td>&nbsp;<a href='javascript:editHTML(\"" + "test" + "\"," + data.id + ")' class='darkgrey'><span class='light10text'>Edit</span></a></td>" +
                        "<td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td>Read Material Number: <b>" + data.id + "</b></td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")'  class='btn btn-primary'>View</a>" +
                        "<a href='javascript:editHTML(\"" + "test" + "\"," + data.id + ")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                        "</div>" +
                        "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }

        if (formattedTopicData.quizs.length > 0) {
            for (var i = 0; i < formattedTopicData.quizs.length; ++i) {
                var data = formattedTopicData.quizs[i];
                if ("sage" == defaultSiteName) {
                    if (data.resType == "Multiple Choice Questions") {
                        if (siteId == 9) {
                            addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;" + data.resType + "</td>" +
                                "<td>" +
                                "<div class='flex-line'>" +
                                " <a href='javascript:showQuizRead(" + data.link + "," + data.id + ")' style='padding-right: 7px'>Learn</a>" +
                                "<a href='javascript:showQuiz(" + data.link + "," + data.id + ");'>Practice</a>" +
                                "</div>" +
                                "</td>" +
                                "<td><a href='javascript:editMCQ(" + data.id + ")'>Edit</a></td><td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                                "<td>Quiz Number: <b>" + data.id + "</b></td>" +
                                "<td></td>" +
                                "<td>" + data.creatorname + "</td>" +
                                "</tr>";
                        } else {
                            addedContents += "<tr><td>&nbsp;" + data.title + "</td>" +
                                "<td>&nbsp;" + data.resType + "</td>" +
                                "<td>" +
                                "<div class='flex-line'>" +
                                "<a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.link + "&resId=" + data.id + "' style='padding-right: 7px'>Learn</a>" +
                                "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&quizId=" + data.link + "&resId=" + data.id + "'>Practice</a>" +
                                "</div>" +
                                "</td>" +

                                "<td><a href='javascript:editMCQ(" + data.id + ")'>Edit</a></td>" +
                                "<td><a href='/admin/videoExplanationUpdate?quizId=" + data.id + "' style='padding: 0 7px; line-height: normal;' class='text-primary'>Support information upload</a></td>" +
                                "<td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td><td>Quiz Number: <b>" + data.id + "</b></td>" +
                                "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                                "<td>" + data.creatorname + "</td>" +
                                "</tr>";
                        }
                    } else if (data.resType == "AWS Video") {
                        addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;" + data.resType + "</td><td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                            "<td>" + data.creatorname + "</td>" +
                            "</tr>";
                    } else
                        addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;" + data.resType + "</td><td>&nbsp;<a href='javascript:showQuizRead(" + data.link + "," + data.id + ")'>Learn</a>" +
                            "&nbsp;&nbsp;<a href='javascript:showQuiz(" + data.link + "," + data.id + ")' class='btn btn-primary'>Practice</a></td>" +
                            "<td>" + data.creatorname + "</td>" +
                            "</tr>";
                } else {
                    if (data.resType == "Multiple Choice Questions") {
                        if (siteId == 9) {
                            addedContents += "<tr><td>" + data.id + "</td>" +
                                "<td>" + data.title + "</td>" +
                                "<td>" + data.resType + "</td>" +
                                "<td>" +
                                "<div class='d-flex justify-content-center action-align'>" +
                                "<a href='javascript:showQuizRead(" + data.link + "," + data.id + ")' class='btn btn-primary'>View</a>" +
                                "<a href='javascript:showQuiz(" + data.link + "," + data.id + ");' class='btn btn-primary'>Practice</a>" +
                                "<a href='javascript:editMCQ(" + data.id + ")' class='btn btn-primary'>Edit</a>" +
                                "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                                "</div>" +
                                "</td>" +
                                "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                                "<td>" + data.creatorname + "</td>" +
                                "</tr>";
                        } else {
                            addedContents += "<tr><td>" + data.id + "</td>" +
                                "<td>" + data.title + "</td>" +
                                "<td>" + data.resType + "</td>" +
                                "<td>" +
                                "<div class='d-flex justify-content-center action-align align-items-center'>" +
                                "<a href='/funlearn/quiz?fromMode=book&quizMode=learn&quizId=" + data.link + "&resId=" + data.id + "' class='btn btn-primary'>Learn</a>" +
                                "<a href='/funlearn/quiz?fromMode=book&quizMode=practice&quizId=" + data.link + "&resId=" + data.id + "' class='btn btn-primary'>Practice</a>" +
                                "<a href='javascript:editMCQ(" + data.id + ")' class='btn btn-primary'>Edit</a>" +
                                "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                                "</div>" +
                                "<a href='/admin/videoExplanationUpdate?quizId=" + data.id + "' style='padding:0 7px; line-height: normal;font-size: 13px;' class='text-primary mt-2'>Support information upload</a>" +
                                "</td>" +
                                "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                                "<td>" + data.creatorname + "</td>" +
                                "</tr>";

                        }
                    } else if (data.resType == "AWS Video") {
                        addedContents += "<tr><td>" + data.id + "</td>" +
                            "<td>" + data.title + "</td>" +
                            "<td>" + data.resType + "</td>" +
                            "<td>" +
                            "<div class='d-flex justify-content-center action-align'>" +
                            "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                            "</div>" +
                            "</td>" +
                            "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                            "<td>" + data.creatorname + "</td>" +
                            "</tr>";
                    } else {
                        addedContents += "<tr><td>" + data.id + "</td>" +
                            "<td>" + data.title + "</td>" +
                            "<td>" + data.resType + "</td>" +
                            "<td>" +
                            "<div class='d-flex justify-content-center action-align'>" +
                            "<a href='javascript:showQuizRead(" + data.link + "," + data.id + ")' class='btn btn-primary'>Learn</a>" +
                            "<a href='javascript:showQuiz(" + data.link + "," + data.id + ")' class='btn btn-primary'>Practice</a>" +
                            "</div>" +
                            "</td>" +
                            "<td></td>" +
                            "<td>" + data.creatorname + "</td>" +
                            "</tr>";
                    }
                }
            }
        }

        console.log(formattedTopicData.relVidoes);
        if (formattedTopicData.relVidoes.length > 0) {
            for (var i = 0; i < formattedTopicData.relVidoes.length; ++i) {
                var data = formattedTopicData.relVidoes[i];
                var displayTestStartDate = '';
                var displayTestEndDate = '';
                if (data.testStartDate != '' && data.testStartDate != 'Invalid date') displayTestStartDate = moment(data.testStartDate).utc(false).format("YYYY-MM-DD HH:mm");
                if (data.testEndDate != '' && data.testEndDate != 'Invalid date') displayTestEndDate = moment(data.testEndDate).utc(false).format("YYYY-MM-DD HH:mm");
                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;" + data.resType + "</td>" +
                        "<td>" +
                        "<a href='javascript:playVideo(\"" + data.link + "\"," + data.id + ")'>Watch</a></td>" +
                        "<td>" +
                        "<a href='javascript:editResource(\"" + data.link + "\",\"" + data.id + "\",\"" + data.title.replace(/'/g,"&#39;") + "\",\"" + data.videoPlayer + "\",\"" + data.allowComments + "\",\"" + data.displayComments + "\",\"" + displayTestStartDate + "\",\"" + displayTestEndDate + "\",\"" + data.downloadlink1 + "\",\"" + data.downloadlink2 + "\",\"" + data.downloadlink3 + "\")'>Edit</a>" +
                        "</td>" +
                        "<td><a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td>" + displayTestStartDate + "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='javascript:playVideo(\"" + data.link + "\"," + data.id + ")' class='btn btn-primary'>Watch</a>" +
                        "<a href='javascript:editResource(\"" + data.link + "\",\"" + data.id + "\",\"" + data.title.replace(/'/g,"&#39;") + "\",\"" + data.videoPlayer + "\",\"" + data.allowComments + "\",\"" + data.displayComments + "\",\"" + displayTestStartDate + "\",\"" + displayTestEndDate + "\",\"" + data.downloadlink1 + "\",\"" + data.downloadlink2 + "\",\"" + data.downloadlink3 + "\")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResourceVideo(" + data.id + ",\"" + data.testStartDate + "\")' class='btn btn-danger'>Delete12121212</a>" +
                        "</div>" +
                        "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }


        if (formattedTopicData.videos.length > 0) {
            for (var i = 0; i < formattedTopicData.videos.length; ++i) {
                var data = formattedTopicData.videos[i];
                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td>" +
                        "<td>&nbsp;" + data.resType + "</td>" +
                        "<td>&nbsp;<a href='javascript:playVideo(\"" + data.link + "\"," + data.id + ")'>Watch</a></td>" +
                        "<td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='javascript:playVideo(\"" + data.link + "\"," + data.id + ")' class='btn btn-primary'>Watch</a>" +
                        "<a href='javascript:editQA(" + data.id + ")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                        "</div>" +
                        "</td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }

        if (formattedTopicData.weblinks.length > 0) {
            for (var i = 0; i < formattedTopicData.weblinks.length; ++i) {
                var data = formattedTopicData.weblinks[i];
                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;" + data.resType + "</td>" +
                        "<td>&nbsp;<a href='" + data.link.replace(/#/g,":") + "' target='_blank'>Open</a></td>" +
                        "<td><a href='javascript:editWeblinks(\"" + data.id + "\",\"" + data.title.replace(/'/g,"&#39;") + "\",\"" + data.link + "\")'>Edit</a></td><td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td>Weblink Number: <b>" + data.id + "</b></td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='" + data.link.replace(/#/g,":") + "' class='btn btn-primary'>View</a>" +
                        "<a href='javascript:editWeblinks(\"" + data.id + "\",\"" + data.title.replace(/'/g,"&#39;") + "\",\"" + data.link + "\")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                        "</div>" +
                        "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }

        if (formattedTopicData.qAndA.length > 0) {
            for (var i = 0; i < formattedTopicData.qAndA.length; ++i) {
                var data = formattedTopicData.qAndA[i];
                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;Question / Answers </td>" +
                        "<td> <a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")' class='darkgrey'>" +
                        "<span class='light10text'>View</span> </a></td>" +
                        "<td><a href='javascript:editQA(" + data.id + ")' class='darkgrey'><span class='light10text'>Edit</span></a></td>" +
                        "<td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td>QA Number: <b>" + data.id + "</b></td>" + "<td></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")' class='btn btn-primary'>View</a>" +
                        "<a href='javascript:editQA(" + data.id + ")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                        "</div>" +
                        "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }
        if (formattedTopicData.shortQAndA.length > 0) {
            for (var i = 0; i < formattedTopicData.shortQAndA.length; ++i) {
                var data = formattedTopicData.shortQAndA[i];
                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;Question / Answers </td><td> <a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")' class='darkgrey'>" +
                        "<span class='light10text'>View</span> </a></td><td><a href='javascript:editQA(" + data.id + ")' class='darkgrey'><span class='light10text'>Edit</span></a></td><td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td>QA Number: <b>" + data.id + "</b></td>" + "<td></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")' class='btn btn-primary'>View</a>" +
                        "<a href='javascript:editQA(" + data.id + ")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                        "</div>" +
                        "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }
        if (formattedTopicData.keyValues.length > 0) {
            for (var i = 0; i < formattedTopicData.keyValues.length; ++i) {
                var data = formattedTopicData.keyValues[i];
                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td>" +
                        "<td>&nbsp;Flashcards </td><td> " +
                        "<span class='light10text'>View</span> </a></td>" +
                        "<td><a href='javascript:editFlashCards(" + data.id + ")' class='darkgrey'><span class='light10text'>Edit</span></a></td>" +
                        "<td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td>Revision Number: <b>" + data.id + "</b></td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")' class='btn btn-primary'>View</a>" +
                        "<a href='javascript:editFlashCards(" + data.id + ")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                        "</div>" +
                        "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }

        if (formattedTopicData.videoexplanation.length > 0) {
            for (var i = 0; i < formattedTopicData.videoexplanation.length; ++i) {
                var data = formattedTopicData.videoexplanation[i];
                if ("sage" == defaultSiteName) {
                    addedContents += "<tr><td>&nbsp;" + data.title + "</td><td>&nbsp;Video Explanation</td><td> " +
                        "<span class='light10text'>View</span> </a></td><td><a href='javascript:editVideoHTML(" + data.id + ")' class='darkgrey'><span class='light10text'>Edit</span></a></td><td>&nbsp;<a href='javascript:delResource(" + data.id + ")' class='text-danger'><span class='light10text'>Delete</span></a></td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                } else {
                    addedContents += "<tr><td>" + data.id + "</td>" +
                        "<td>" + data.title + "</td>" +
                        "<td>" + data.resType + "</td>" +
                        "<td>" +
                        "<div class='d-flex justify-content-center action-align'>" +
                        "<a href='javascript:showBook(" + data.id + "," + data.bookId + "," + data.topicId + "," + data.noOfPages + ")' class='btn btn-primary'>View</a>" +
                        "<a href='javascript:editVideoHTML(" + data.id + ")' class='btn btn-primary'>Edit</a>" +
                        "<a href='javascript:delResource(" + data.id + ")' class='btn btn-danger'>Delete</a>" +
                        "</div>" +
                        "</td>" +
                        "<td><a href='javascript:getDeepLink(" + chapterId + "," + data.id + ",\"" + data.resType + "\")' class='text-primary'>Deep Link</a></td>" +
                        "<td>" + data.creatorname + "</td>" +
                        "</tr>";
                }
            }
        }
        document.getElementById("addedContents").innerHTML = addedContents;
    }
}


function playHostedVideo(id){
  showLoader();
  var video = document.getElementById('hostedvideo');
  var source = document.createElement('source');

  source.setAttribute('src', '/funlearn/getMP3?id='+id);

  video.appendChild(source);


  //video.play();

  $("#hostedvideoclose").click(function () {
    $("video").each(function () { this.pause() });

  });


  video.oncanplaythrough = function() {
    $("#hostedvideoModal").modal('show');
    removeLoader();
  };
}






function showDocument(id) {
  var docSRC = serverPath+"/funlearn/download?id="+id;
  downloadFileId = id;
  //to clear previous content
  var iframe = document.getElementById("doc-iframe"); var html = "";
  //iframe.contentWindow.document.open(); iframe.contentWindow.document.write(html); iframe.contentWindow.document.close();

  $("#docModal" + ' iframe').attr('src', docSRC);

  $('#doc-iframe').load(function(){
    iframe = document.getElementById("doc-iframe");
    // I spent almost a day to arrive at this condition
    //if(iframe.contentWindow.document.body!=null) {
    //  console.log("loaded with content"+iframe.contentWindow.document.body.innerHTML)
    $("#docModal").modal('show');
    //}
  });

  $('#docModal').on('show.bs.modal', function () {
    $('.doc-content').css('height',$( window ).height()*0.9);
    $('.modal.modal-wide .modal-body').css('overflow-y', 'auto');
    $('.modal.modal-wide .modal-body').css('min-height', $('.doc-content').height() * 0.9);
    $('.modal.modal-wide .modal-body iframe').css('min-height', $('.doc-content').height() * 0.8);
  });

}





function printX(resType){
  if(resType=='Notes') $("#notes-iframe").get(0).contentWindow.print();
  else if(resType=='Doc') $("#doc-iframe").get(0).contentWindow.print();
}

function downloadFile(){
  window.open(serverPath+"/funlearn/downloadFile?id="+downloadFileId);
}

function download(id){
  window.open(serverPath+"/funlearn/downloadFile?id="+id);
}

function editItem(id,resType){
  window.open(serverPath+"/funlearn/quizcreator?id="+id+"&topicId="+topicId+"&mode=edit&&approveMode="+approveMode+"&resourceType="+resType,"_self");
}

function editNotes(id,resType){
  window.open(serverPath+"/funlearn/notescreator?id="+id+"&topicId="+topicId+"&mode=edit&&approveMode="+approveMode+"&resourceType="+resType,"_self");
}

function removeItem(id){
  if(confirm("Are you sure to delete this item?")) {
    window.open(serverPath + "/creation/removeItem?id=" + id + "&topicId=" + topicId + "&mode=edit&approveMode="+approveMode,"_self");
  }
}

function approveItem(id){
  window.open(serverPath + "/creation/approveItem?id=" + id + "&topicId=" + topicId + "&mode=edit&approveMode="+approveMode,"_self");
}

function updateLog(id,columnName){
//    Do not use - AA
 // var url = serverPath +"/log/updateCount?id="+id+"&columnName="+columnName;
 // $.get(url);
}

function showWeb(link,id)
{
  if(link.indexOf("http")==-1&&link.indexOf("https")==-1) link = "http://"+link;
  window.open(link, "_blank");
}


function updateLike(id,count){
  document.getElementById('Like-'+id).innerHTML = "Liked";
  document.getElementById('Like-count-'+id).innerHTML = (count+1);
  updateLog(id,"noOfLikes");
}

function updateFavourite(id){
  document.getElementById('Favourite-'+id).innerHTML = "Saved";
  updateLog(id,"noOfFavourites");
}

function sentForApproval(data){
  if("public"==data.status) document.getElementById('Publish-'+data.id).innerHTML = "Published";
  else document.getElementById('Publish-'+data.id).innerHTML = "Sent for approval";
}

function displayGroupsForSharing(data){
  var groups = data.results;
  var resourceId = data.resourceId;
  var imageStr;
  var htmlStr="<br> <span class='smallerText'><b>** Share with groups by clicking on </b> <a href='#'><i class='fa fa-share fa-x'></i></a></span><br>";
  if(groups.length ==0)  htmlStr="<div class='row'><div class=' col-md-12 '><b><span class='smallerText'>This content is already shared with all your groups.</span></b></div></div>";
  for(var i = 0; i < groups.length; ++i) {
    if("null"==""+groups[i].profilepic) imageStr="<a href='#'> <i class='fa fa-group fa-3x'></i></a>";
    else imageStr = "<img src='/funlearn/showProfileImage?id="+groups[i].groupId+"&fileName="+groups[i].profilepic+"&type=group' width='40'>";

    if(i%3 == 0) htmlStr+="<div class='row'><div class='col-md-4'>";
    else htmlStr+="<div class='col-md-4'>";

    htmlStr+= "<div class='row'>" +
    "<div class='col-md-3 vcenter'>"+imageStr+"</div>"+
    "<div class='col-md-9 vcenter'><b><span class='smallerText'> "+groups[i].name+"</span></b>"+
    "&nbsp;&nbsp;<span id='groupoption" + groups[i].groupId + "'><a href='javascript:addGroupShare("+groups[i].groupId+","+resourceId+")'><i class='fa fa-share fa-1x'></i></a></span></div>"+
    "</div></div>";
    if(i%3 == 2) htmlStr+="</div><hr class='myhrline'>";
  }
  //if the no items is not divisible by 3
  if(groups.length%3 != 0) htmlStr+="</div><hr class='myhrline'>";
  document.getElementById('groupResource').innerHTML= htmlStr;
}
function htmlDecode( html ) {
  var a = document.createElement( 'a' ); a.innerHTML = html;
  return a.textContent;
};

function playVideo(videoLink,id){
    if(videoLink.includes("/")){
        var videoSRC=videoLink.replace(/#/g,":");
    }else{
        var videoSRC="https://www.youtube.com/embed/"+videoLink;
    }
    var videoSRCauto = videoSRC + "?autoplay=1";
    $("#videoModal" + ' iframe').attr('src', videoSRCauto);
    $("#videoModal").modal('show');
    $("#youtubeclose").click(function () {
        $("#videoModal" + ' iframe').attr('src', videoSRC);
    });

    if(loggedInUser){
        if(allTabMode)
            updateUserView(id,"all","videos");
        else
            updateUserView(id,"videos","videos");
    }
    else{
        if(allTabMode)
            updateView(id,"all","videos");
        else
            updateView(id,"videos","videos");
    }
}
var modelresLink = '';
var modelresName= '';
var modelresId= '';
var modalallowComments = '';
var modaldisplayComments = '';
var modalvideoPlayer='';
var modalStartDate = '';
var modalEndDate = '';
var downloadlinkone = '';
var downloadlinktwo = '';
var downloadlinkthree = '';
var modelweblinktitle = '';
var modelweblinkurl = '';
var modelweblinkresId='';
function editResource(videoLink,id,title,videoPlayer,allowComments,displayComments,testStartDate,testEndDate,downloadLink1,downloadLink2,downloadLink3){

    if(testStartDate == '' || testStartDate == 'Invalid date') modalStartDate = '';
   else modalStartDate = moment(testStartDate).utc(false).format("YYYY-MM-DD HH:mm");
    if(testEndDate == '' || testEndDate == 'Invalid date') modalEndDate = '';
    else modalEndDate = moment(testEndDate).utc(false).format("YYYY-MM-DD HH:mm");;
    modelresLink = videoLink;
    modelresName = htmlDecode(title.replace(/'/g,"&#39;"));
    modelresId = id;
    modalvideoPlayer=videoPlayer;
    modalCustom=videoPlayer;
    modalallowComments=allowComments;
    modaldisplayComments=displayComments;
    downloadlinkone = downloadLink1.replace(/#/g,":").replace(/null/g," ");
    downloadlinktwo = downloadLink2.replace(/#/g,":").replace(/null/g," ");
    downloadlinkthree = downloadLink3.replace(/#/g,":").replace(/null/g," ");

    $("#modalCommentSection").hide();
    $("#removePhone").modal('show');

}
$("#removePhone").on('shown.bs.modal', function(){
   $("#restitle").val(modelresName);
    if(modelresLink!="blank" && modelresLink!="") {
        if (modelresLink.includes("/")) {
            $("#resname").val(modelresLink.replace(/#/g,":"));
        } else {
            $("#resname").val("https://www.youtube.com/watch?v=" + modelresLink);
        }
    }else{
        $("#resname").val("");
    }

    $("#resid").val(modelresId);
    $('#modalTestStartDate').val(modalStartDate);
    $('#modalTestEndDate').val(modalEndDate);
    $('#downloadlink1edit').val(downloadlinkone);
    $('#downloadlink2edit').val(downloadlinktwo);
    $('#downloadlink3edit').val(downloadlinkthree);

    if(modalvideoPlayer=="custom"){
        if(modalCustom=="custom") document.getElementById("modalCustom").checked=true;
        if(modalallowComments=="on") document.getElementById("modalAllowComments").checked=true;
        if(modaldisplayComments=="on") document.getElementById("modalDisplayComments").checked=true;
        $("#modalCommentSection").show();
    }else {
        if(modalallowComments=="on") document.getElementById("modalAllowComments").checked=true;
        if(modaldisplayComments=="on") document.getElementById("modalDisplayComments").checked=true;
        document.getElementById("modalYoutube").checked=true;
    }
});


function editWeblinks(id,title,link){
    modelweblinkresId = id;
    modelweblinktitle=title;
    modelweblinkurl=link.replace(/#/g,":");

    $("#removePhone1").modal('show');

}
$("#removePhone1").on('shown.bs.modal', function(){
    $('#weblinktitleedit').val(modelweblinktitle);
    $('#weblinkurledit').val(modelweblinkurl);
    $('#weblinkresid').val(modelweblinkresId);

});


//removing the copy thingy
// document.getElementById('read').ondragstart = function () {
// //     return false; };
// document.getElementById('study-set-from-notes').ondragstart = function () {
//     return false; };
// document.getElementById('falsh-card-modal-body').ondragstart = function () {
//     return false; };
$(document).ready(function() {
    $('#user-notes').bind('copy paste', function(e) {
        e.preventDefault();
    });
});
$(document).ready(function() {
    $('#study-set-from-notes').bind('copy paste', function(e) {
        e.preventDefault();
    });
});
$(document).ready(function() {
    $('#read').bind('copy paste', function(e) {
        e.preventDefault();
    });
});

$(document).ready(function() {
    $('#falsh-card-modal-body').bind('copy paste', function(e) {
        e.preventDefault();
    });
});
$(document).ready(function() {
    $('#study-set-wrapper').bind('copy', function(e) {
        e.preventDefault();
    });
});
$(document).ready(function() {
    $('#mcqquestionsection').bind('copy', function(e) {
        e.preventDefault();
    });
});
// document.getElementById('mcqquestionsection').ondragstart = function () {
//     return false; };

