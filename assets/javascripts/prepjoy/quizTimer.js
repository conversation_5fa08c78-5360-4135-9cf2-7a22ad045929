function Countdown(elem, seconds) {
    timeObj = {};
    timeObj.elem = elem;
    timeObj.seconds = seconds;
    timeObj.totalTime = seconds * 100;
    timeObj.usedTime = 0;
    timeObj.startTime = +new Date();
    timeObj.timer = null;

    timeObj.count = function() {
        timeObj.usedTime = Math.floor((+new Date() - timeObj.startTime) / 10); ///get system used time
        tt = timeObj.totalTime - timeObj.usedTime;  //calculate and reduce total time

        if (tt <= 0) {
            timeObj.elem.innerHTML = '00:00';
            if(quizMode!='testSeries') {
                showCorrectAnswer();
                setTimeout(function (){
                    nextQue();
                },1000)
            }
            clearInterval(timeObj.timer);
        } else {
            var mi = Math.floor(tt / (60 * 100));
            var ss = Math.floor((tt - mi * 60 * 100) / 100);
            var ms = tt - Math.floor(tt / 100) * 100;
            newTime=timeObj.fillZero(ss) + "." + timeObj.fillZero(ms);
            newTime=Number(newTime);
            globalTimer=newTime;
            if(quizMode!='testSeries') {
                botPlay(globalTimer, chatBotAnswerTime);//goes for bot playing
            }
            if(timeValue>6000) {
                timeObj.elem.innerHTML = timeObj.fillZero(mi) + "." + timeObj.fillZero(ss);
            }
            else{
                timeObj.elem.innerHTML =  timeObj.fillZero(ss);
            }
        }
    };



    timeObj.start = function() {
        if(!timeObj.timer){
            timeObj.timer = setInterval(timeObj.count, 1);

        }
    };

    timeObj.stop = function() {

        if (timeObj.timer) clearInterval(timeObj.timer);
    };

    timeObj.fillZero = function(num) {
        return num < 10 ? '0' + num : num;
    };

    return timeObj;
}

var startTime, interval;



function stop(){
    clearInterval(interval);
}

function updateDisplay(currentTime){
   document.getElementById('test_timer').innerHTML=(currentTime / 1000).toFixed(2);
}
var testInterval;
function clockStart() {
        startTime = Date.now();
    testInterval = setInterval(function(){
            updateDisplay(Date.now() - startTime);
        },100);
    }

    function clockReset() {
        clearInterval(testInterval);

    }


function clockPause() {
        clearInterval(testInterval);

    }

   function clockResume() {
       clockStart();
    }

function clockRestart() {
    clockReset();
    clockStart();
 }

