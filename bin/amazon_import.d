#!/bin/sh

# - Copyright (c) 2018 WonderSlate, Inc. All Rights Reserved. -
#
#  --- Setting local Environment --
HOME=/u01/ws; export HOME;
TOMCAT1=/u01/apache-tomcat-8.5.13; export TOMCAT1;
UPLOAD_HOME=/u01/ws/exports; export UPLOAD_HOME;

CLASSPATH=$HOME/lib/javax.mail.jar:$HOME/lib/activation.jar:$HOME/bin; export CLASSPATH;

DATE=`date '+%Y-%h-%d'`; export DATE;
LONGDATE=`date '+%Y-%h-%d-%H-%M'`; export LONGDATE;

LOG_DATE=`date -d "5 hours 30 minutes" '+%d-%h-%Y %H:%M'`; export LOG_DATE;
TIMESTAMP=$UPLOAD_HOME/logs/import_amazon_$DATE.log;

cleartime() {
 echo "" > $TIMESTAMP ;
}

logtime() {
	echo "$1" >> $TIMESTAMP ;
}

cleartime;

if [ "$1Test" = "Test" ]; then
  echo ""
  echo "Syntax: ./amazon_import.d <environment>"
  echo "environment could be qa, test, live or eu."
  echo ""
  exit 0
fi

if [ "$1" != "qa" ] && [ "$1" != "test" ] && [ "$1" != "live" ] && [ "$1" != "eu" ]; then
  echo ""
  echo "Syntax: ./amazon_import.d <environment>"
  echo "environment could be qa, test, live or eu."
  echo ""
  exit 0
fi

logtime "Beginning the amazon import process.";
logtime "";
logtime "Staring at $LOG_DATE";

#sending out email 
java -classpath $CLASSPATH email "<EMAIL>" "Server `cat /u01/ws/bin/servername` amazon import begin notification" "`cat $TIMESTAMP`";

cd $TOMCAT1/bin;

if [ "$1" != "qa" ]; then
    cp /efs/amazon_import.txt .
fi

if [ "$1" = "live" ]; then
    mysql -u ws -h 172.31.28.253 -pws ws < amazon_import.sql
elif [ "$1" = "eu" ]; then
    mysql -u ws -h 172.31.43.188 -pws ws < amazon_import.sql
else
    mysql -u wsqa -pwsqa wsqa < amazon_import.sql
fi

logtime "";
logtime "Done! at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

java -classpath $CLASSPATH email "<EMAIL>" "Server `cat /u01/ws/bin/servername` amazon import complete notification" "`cat $TIMESTAMP`";

