#!/bin/sh

# - Copyright (c) 2019 WonderSlate, Inc. All Rights Reserved. -
#
#  --- Setting local Environment --
HOME=/u01/apache-tomcat-8.5.13; export HOME;
CLASSPATH=$HOME/jars/*:$HOME/bin:$HOME/bin/*; export CLASSPATH;


if [ "$#" -ne 1 ]; then
  echo ""
  echo "Syntax is ./send_post_po_noticiation <envi>"
  echo "Parameter envi could be test, live or eu."
  echo ""
  exit 0
fi

if [ "$1Test" != "Test" ] && [ "$1" != "live" ] && [ "$1" != "eu" ] && [ "$1" != "test" ]; then
  echo ""
  echo "Syntax is ./send_post_po_noticiation <envi>"
  echo "Parameter envi could be test, live or eu."
  echo ""
  exit 0
fi

java -classpath $CLASSPATH PostPOSentTextMsg $1
