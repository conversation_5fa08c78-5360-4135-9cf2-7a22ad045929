import java.sql.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.io.File;

public class CopyDbBookToFs {
    public static void main(String args[]) {
        if(args.length!=4 || (args.length==4 && (!args[0].toLowerCase().equals("test") && !args[0].toLowerCase().equals("live") && !args[0].toLowerCase().equals("eu")) ||
                 (!args[1].toLowerCase().equals("true") && !args[1].toLowerCase().equals("false")) ||
                (!args[2].toLowerCase().equals("true") && !args[2].toLowerCase().equals("false")))) {
            System.out.println("\n****** Syntax: java -Dfile.encoding=UTF-8 CopyDbBookToFs <Environment> <Online> <Overwrite> <BookID>");
            System.out.println("****** Environment could be test, live or eu.");
            System.out.println("****** Online could be true or false. Online is when data is entered via editor.");
            System.out.println("****** Overwrite could be true or false.");
            System.out.println("****** BookID could be 0 for all books or comma(,) separated ids to be processed.\n");
            return;
        }

        int bookId=0;
        String extraTable="";
        String extraCondition="";

        if(!args[3].trim().equals("0")){
            extraTable = ", chapters_mst cm";
            extraCondition = " and rd.chapter_id=cm.id and cm.book_id in ("+args[3]+")";
        }

        String dbURL = (args[0].toLowerCase().equals("test")?"localhost:3306/wsqa":(args[0].toLowerCase().equals("live")?"localhost:3306/ws":"*************:3306/ws"));
        String dbCreds = (args[0].toLowerCase().equals("test")?"wsqa":(args[0].toLowerCase().equals("live")?"ws":"ws"));
        
        boolean online = args[1].toLowerCase().equals("true");
        boolean overwrite = args[2].toLowerCase().equals("true");
        boolean fileExists;
        String localDir = "D:/Wonderslate329/";

        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection con = DriverManager.getConnection("jdbc:mysql://"+dbURL+"?useUnicode=yes&characterEncoding=UTF-8&useSSL=false", dbCreds, dbCreds);

            Statement stmt = con.createStatement();

            String sql = "select distinct rd.id, rd.res_link from resource_dtl rd, resource_dtl_sub rds"+ extraTable +" where res_type='Notes' and rd.id>0 " +
                    "and rd.modified_file is not null and rds.resource_id=rd.id"+extraCondition;

            if(online) {
                sql+=" and (lower(rds.filename) not like '%.html' and  lower(rds.filename) not like '%.xhtml')";
            } else {
                sql+=" and (lower(rds.filename) like '%.html' or  lower(rds.filename) like '%.xhtml')";
            }

            ResultSet rs = stmt.executeQuery(sql);

            Statement stmt1;
            ResultSet rs1;

            String path, file;
            File aFile;
            byte[] filedata,temp,temp1;
            boolean isZipBook;

            while (rs.next()) {
                path = rs.getString(2);
                file = (!System.getProperty("os.name").toLowerCase().contains("windows")?"/u01/apache-tomcat-8.5.13/bin/":localDir)+
                        path.substring(0, (path.lastIndexOf(".")==-1?path.length():path.lastIndexOf(".")))+".ws";

                aFile = new File(file);
                fileExists = aFile.exists();

                if(fileExists && !overwrite) {
                    System.out.println("Writing to file " + file + " - Exists - not overwriting!");
                    continue;
                }

                if(!fileExists) {
                    aFile.getParentFile().mkdirs();
                    aFile.createNewFile();
                }

                isZipBook = path.toLowerCase().endsWith(".zip") || path.toLowerCase().endsWith(".pdf");
                sql = "select filename,filedata from resource_dtl_sub where resource_id=" + rs.getInt(1);

                if(isZipBook)
                    sql+=" order by CONVERT(replace(replace(filename,substr(filename,1,INSTR(filename, '/')),''),'.html',''),UNSIGNED INTEGER)";

                stmt1 = con.createStatement();
                rs1 = stmt1.executeQuery(sql);

                filedata = null;

                while(rs1.next()) {
                    if(rs1.getString(1).toLowerCase().indexOf("cover.")!=-1) continue;

                    temp1= rs1.getBytes(2);

                    if(filedata==null) {
                        filedata = temp1;
                    } else {
                        temp = new byte[filedata.length + temp1.length];

                        System.arraycopy(filedata, 0, temp, 0, filedata.length);
                        System.arraycopy(temp1, 0, temp, filedata.length, temp1.length);

                        filedata = temp;
                    }
                }

                try {
                    System.out.print("Writing to file " + file);
                    Files.write(Paths.get(file), filedata);
                    System.out.println(" - Done"+(fileExists?" - Exists - overwriting":"")+"!");
                } catch (Exception e1) {
                    System.out.println(" - Could not write!");
                }
                //break;
            }

            con.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}