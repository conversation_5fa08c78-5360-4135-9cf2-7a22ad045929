#!/bin/sh

# - Copyright (c) 2018 WonderSlate, Inc. All Rights Reserved. -
#
#  --- Setting local Environment --
UPLOAD_HOME=/u01/ws/exports; export UPLOAD_HOME;
HOME=/u01/ws; export HOME;
TOMCAT1=/u01/apache-tomcat-8.5.13; export TOMCAT1;
TOMCAT2=/u01/apache-tomcat-8.5.13-1; export TOMCAT2;

CLASSPATH=$HOME/lib/javax.mail.jar:$HOME/lib/activation.jar:$HOME/bin; export CLASSPATH;

DATE=`date '+%Y-%h-%d'`; export DATE;

LOG_DATE=`date -d "5 hours 30 minutes" '+%d-%h-%Y %H:%M'`; export LOG_DATE;
TIMESTAMP=$UPLOAD_HOME/logs/restart_$DATE.log;

cleartime() {
 echo "" > $TIMESTAMP ;
}

logtime() {
	echo "$1" >> $TIMESTAMP ;
}

cleartime;

if [ "$1Test" != "Test" ] && [ "$1" != "force" ]; then
  echo ""
  echo "Syntax is ./restart_server force"
  echo "Parameter 'force' is optional."
  echo "If used servers will be restarted irrespective of the load."
  echo ""
  exit 0
fi

LOAD_BIT="10"

if [ "$1" = "force" ]; then
  CPU_LOAD="1000"
  logtime "Force restarting the servers by setting CPU load to 1000.";
  logtime "";  
else
  #CPU load calculation
  CPU_LOAD=`uptime | awk -v var="$LOAD_BIT" '{print $var}' | cut -d "," -f 1`

  if [ "$CPU_LOAD" = "average:" ]; then
    LOAD_BIT="11"
    CPU_LOAD=`uptime | awk -v var="$LOAD_BIT" '{print $var}' | cut -d "," -f 1`
  fi  
fi

#echo $CPU_LOAD
#echo $LOAD_BIT
#CPU_LOAD=50;

logtime "Beginning the restart process.";
logtime "";
logtime "Staring at $LOG_DATE";
logtime "CPU load is $CPU_LOAD";

#disabling the below cpu load check to decide restart or not. restarting anyway
CPU_LOAD=$( printf "%.0f" $CPU_LOAD )
if [ "$CPU_LOAD" -le "10" ]; then
    cleartime;
    logtime "Restart process NOT initiated since CPU load less than checkpoint (10).";
    logtime "";
    logtime "Checked at $LOG_DATE";
    logtime "CPU load is $CPU_LOAD";

    #sending out email 
    java -classpath $CLASSPATH email "<EMAIL>" "Server `cat /u01/ws/bin/servername` restart not required notification" "`cat $TIMESTAMP`";
    exit 0
fi

#sending out email 
java -classpath $CLASSPATH email "<EMAIL>" "Server `cat /u01/ws/bin/servername` restart begin notification" "`cat $TIMESTAMP`";

logtime "";
logtime "Stopping http service at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

#stopping http service
service httpd stop;

logtime "";
logtime "Shutting server 1 at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

cd $TOMCAT1/bin; 
./shutdown.sh
sleep 10

logtime "Shutting server 2 at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

cd $TOMCAT2/bin; 
./shutdown.sh
sleep 10

logtime "";

CPU_LOAD=$( printf "%.0f" $CPU_LOAD )

while [ "$CPU_LOAD" -ge "5" ]
do
    sleep 60; 
    CPU_LOAD=`uptime | awk -v var="$LOAD_BIT" '{print $var}' | cut -d "," -f 1`;
    
    if [ "$CPU_LOAD" = "average:" ]; then
        LOAD_BIT="11"
        CPU_LOAD=`uptime | awk -v var="$LOAD_BIT" '{print $var}' | cut -d "," -f 1`
    fi      
    
    #CPU_LOAD=$((CPU_LOAD-10))
    echo $CPU_LOAD
    logtime "CPU load after 1 minute is $CPU_LOAD";
    CPU_LOAD=$( printf "%.0f" $CPU_LOAD );   
done

logtime "";
logtime "CPU load after shutdown complete is $CPU_LOAD";

logtime "";
logtime "Checking wonderslate.war has been modified on server 1";

HOME_WAR_SIZE=`wc -c ~ec2-user/wonderslate329-0.1.war | cut -d' ' -f1`;
TOMCAT_WAR_SIZE=`wc -c $TOMCAT1/site/wonderslate.war | cut -d' ' -f1`;

if [ $HOME_WAR_SIZE -ne $TOMCAT_WAR_SIZE ]
then
    logtime "";
    logtime "Updating war on server 1 as sizes are different. $HOME_WAR_SIZE (home), $TOMCAT_WAR_SIZE (tomcat) ";
    cp ~ec2-user/wonderslate329-0.1.war $TOMCAT1/site/wonderslate.war;
else
    logtime "";
    logtime "Not updating war on server 1 as sizes are same. $HOME_WAR_SIZE (home), $TOMCAT_WAR_SIZE (tomcat) ";
fi

logtime "";
logtime "Bringing up server 1 at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

cd $TOMCAT1/bin; 
./startup.sh

sleep 120

logtime "";
logtime "Checking wonderslate.war has been modified on server 2";

TOMCAT_WAR_SIZE=`wc -c $TOMCAT2/site/wonderslate.war | cut -d' ' -f1`;

if [ $HOME_WAR_SIZE -ne $TOMCAT_WAR_SIZE ]
then
    logtime "";
    logtime "Updating war on server 2 as sizes are different. $HOME_WAR_SIZE (home), $TOMCAT_WAR_SIZE (tomcat) ";
    cp ~ec2-user/wonderslate329-0.1.war $TOMCAT2/site/wonderslate.war;
else
    logtime "";
    logtime "Not updating war on server 2 as sizes are same. $HOME_WAR_SIZE (home), $TOMCAT_WAR_SIZE (tomcat) ";
fi

logtime "";
logtime "Bringing up server 2 at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

cd $TOMCAT2/bin; 
./startup.sh

sleep 120

logtime "";
logtime "Starting http service at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

#starting http service
service httpd start;

logtime "";
logtime "Done! at `date -d \"5 hours 30 minutes\" '+%d-%h-%Y %H:%M'`";

java -classpath $CLASSPATH email "<EMAIL>" "Server `cat /u01/ws/bin/servername` restart complete notification" "`cat $TIMESTAMP`";
