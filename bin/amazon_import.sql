delete from amazon_data;
delete from amazon_data_o;

-- LOAD DATA LOCAL INFILE 'C:/Users/<USER>/Desktop/teste.txt'
LOAD DATA LOCAL INFILE 'amazon_import.txt'
INTO TABLE amazon_data_o
-- FIELDS TERMINATED BY '\t' ESCAPED BY '\n'
FIELDS TERMINATED BY '\t'
-- ENCLOSED BY '"'
LINES TERMINATED BY '\n'
IGNORE 0 ROWS
-- (asin, @vtitle, cover_image, book_link, @vrating, @vtotal_reviews, level, syllabus, grade, subject)
-- SET rating =  IF(@vrating='',null,@vrating),
-- total_reviews = IF(@vtotal_reviews='',null,@vtotal_reviews),
-- title = IF(instr(@vtitle,'\n')=-1,@vtitle,replace(@vtitle,'\n',''))
; 
-- SHOW WARNINGS;

-- inserting distinct rows to amazon_data table
insert into amazon_data select * from amazon_data_o group by asin;

-- sql to insert missing/new asins imported
insert into books_mst(version,asin,title,cover_image,buylink1,rating,total_reviews,date_created,date_published,description,site_id,status,book_type) 
select 0,asin,titlez,cover_image,book_link,rating,total_reviews,SYSDATE(),SYSDATE(),title,1,'published','print' from 
(select asin,(case when length(title)>500 then SUBSTRING(title,1,500) else title end) titlez,
cover_image,book_link,rating,total_reviews,title from amazon_data where asin 
in (select a.asin from amazon_data a LEFT JOIN books_mst b on a.asin=b.asin where b.asin is null) and title is not null and subject is not null) amz_data; 

-- sql to insert new asin book ids into books tag dtl table
insert into books_tag_dtl(version,book_id,grade,level,subject,syllabus)  
select 0,bm.id,ad.grade,ad.level,ad.subject,ad.syllabus 
from books_mst bm, amazon_data ad 
where bm.asin is not null 
and bm.asin=ad.asin
and bm.id not in (select distinct book_id from books_tag_dtl btd);