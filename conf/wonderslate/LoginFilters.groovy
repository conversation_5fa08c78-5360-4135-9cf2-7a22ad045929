package wonderslate


class LoginFilters {

    //add filters here whenever a new private/white label is added. Do not add for booksMojo, that is the default site
    def filters = {
        justBooks(controller:'books', action:'index') {
            before = {
                session.setAttribute("siteId",new Integer(1));
                session.setAttribute("entryController","books");

            }

        }

    }
}
