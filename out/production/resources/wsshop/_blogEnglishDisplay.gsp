
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@400;700&display=swap" rel="stylesheet">
<body>
<main class="blog_container">
    <article>
        <header>
            <h1>${title}</h1>
            <div id="introduction">${info.introduction}</div>
        </header>
        <aside>
            <h2>Table of Contents</h2>
            <ol class="tableOfContent">
                <%if(info.fullForm!=null&&!"".equals(info.fullForm)){%>
                <li><a href="#fullForm">${topLevelTitle} Full Form</a></li>
                <%}%>
                <li><a href="#introductionMain">What is ${topLevelTitle}?</a></li>
                <%if(info.eligibility!=null&&!"".equals(info.eligibility)){%>
                <li><a href="#eligibility">${topLevelTitle} Eligibility</a></li>
                <%}%>
                <%if(info.examPattern!=null&&!"".equals(info.examPattern)){%>
                <li><a href="#examPattern">${topLevelTitle} Exam Pattern</a></li>
                <%}%>
                <%if(info.fullSyllabus!=null&&!"".equals(info.fullSyllabus)){%>
                <li><a href="#fullSyllabus">${topLevelTitle} Full Syllabus</a></li>
                <%}%>
                <%if(info.applicationProcess!=null&&!"".equals(info.applicationProcess)){%>
                <li><a href="#applicationProcess">${topLevelTitle} Application Process</a></li>
                <%}%>
                <%if(info.examDates!=null&&!"".equals(info.examDates)){%>
                <li><a href="#examDates">${topLevelTitle} Exam Dates</a></li>
                <%}%>
                <li><a href="#bestEBooks">Best Seller eBooks for ${fullTitle} (Best price and Immediate Access)</a></li>
                <%if(info.bestBooks!=null&&!"".equals(info.bestBooks)){%>
                <li><a href="#bestBooks">Best Books for ${fullTitle}</a></li>
                <%}%>
                <%if(info.youtubeChannels!=null&&!"".equals(info.youtubeChannels)){%>
                <li><a href="#youtubeChannels">Best YouTube channels for ${fullTitle}</a></li>
                <%}%>
                <%if(info.pqpLinks!=null&&!"".equals(info.pqpLinks)){%>
                <li><a href="#pqpLinks">${fullTitle} Previous Years Question Papers</a></li>
                <%}%>
                <%if(info.generalLinks!=null&&!"".equals(info.generalLinks)){%>
                <li><a href="#generalLinks">Useful links for ${topLevelTitle}</a></li>
                <%}%>
                <%if(info.generalInfo!=null&&!"".equals(info.generalInfo)){%>
                <li><a href="#generalInfo">${topLevelTitle} General Information</a></li>
                <%}%>
                <%if(info.faq!=null&&!"".equals(info.faq)){%>
                <li><a href="#faq">${topLevelTitle} Frequently Asked Questions</a></li>
                <%}%>

            </ol>
        </aside>
        <div>
            <%if(info.fullForm!=null&&!"".equals(info.fullForm)){%>
            <section id="fullForm">
                <h3>${topLevelTitle} full form</h3>
                <div id="fullFormText">${info.fullForm}</div>
            </section>
            <%}%>
            <section id="introductionMain">
                <h3>What is ${topLevelTitle}?</h3>
                <div id="introductionMainText">${info.introduction}</div>
            </section>
            <%if(info.eligibility!=null&&!"".equals(info.eligibility)){%>
            <section id="eligibility">
                <h3>${topLevelTitle} Eligibility </h3>
                <div id="eligibilityText">${info.eligibility}</div>
            </section>
            <%}%>
            <%if(info.examPattern!=null&&!"".equals(info.examPattern)){%>
            <section id="examPattern">
                <h3>${topLevelTitle} Exam Pattern</h3>
                <div id="examPatternText">${info.examPattern}</div>
            </section>
            <%}%>
            <%if(info.fullSyllabus!=null&&!"".equals(info.fullSyllabus)){%>
            <section id="fullSyllabus">
                <h3>${topLevelTitle} Full Syllabus</h3>
                <div id="fullSyllabusText">${info.fullSyllabus}</div>
            </section>
            <%}%>
            <%if(info.applicationProcess!=null&&!"".equals(info.applicationProcess)){%>
            <section id="applicationProcess">
                <h3>${topLevelTitle} Application Process</h3>
                <div id="applicationProcessText">${info.applicationProcess}</div>
            </section>
            <%}%>
            <%if(info.examDates!=null&&!"".equals(info.examDates)){%>
            <section id="examDates">
                <h3>${topLevelTitle} Exam Dates</h3>
                <div id="examDatesText">${info.examDates}</div>
            </section>
            <%}%>
            <section id="bestEBooks">
                <h3>Best Seller eBooks for ${fullTitle} (Best price and Immediate Access)</h3>
                <div id="bestEBooksText"></div>
            </section>
            <%if(info.bestBooks!=null&&!"".equals(info.bestBooks)){%>
            <section id="bestBooks">
                <h3>Best Books for ${fullTitle}</h3>
                <div id="bestBooksText">${info.bestBooks}</div>
            </section>
            <%}%>
            <%if(info.youtubeChannels!=null&&!"".equals(info.youtubeChannels)){%>
            <section id="youtubeChannels">
                <h3>Best YouTube channels for ${fullTitle}</h3>
                <div id="youtubeChannelsText">${info.youtubeChannels}</div>
            </section>
            <%}%>
            <%if(info.pqpLinks!=null&&!"".equals(info.pqpLinks)){%>
            <section id="pqpLinks">
                <h3>${fullTitle} Previous Years Question Papers</h3>
                <div id="pqpLinksText">${info.pqpLinks}</div>
            </section>
            <%}%>
            <%if(info.generalLinks!=null&&!"".equals(info.generalLinks)){%>
            <section id="generalLinks">
                <h3>Useful links for ${topLevelTitle}</h3>
                <div id="generalLinksText">${info.generalLinks}</div>
            </section>
            <%}%>
            <%if(info.generalInfo!=null&&!"".equals(info.generalInfo)){%>
            <section id="generalInfo">
                <h3>${topLevelTitle} General Information</h3>
                <div id="generalInfoText">${info.generalInfo}</div>
            </section>
            <%}%>
            <%if(info.faq!=null&&!"".equals(info.faq)){%>
            <section id="faq">
                <h3>${topLevelTitle} Frequently Asked Questions</h3>
                <div id="faqText">${info.faq}</div>
            </section>
            <%}%>

        </div>
    </article>
    <%if("1".equals(""+session["siteId"]) || "27".equals(""+session["siteId"])){%>
    <div class="bookDetails__container" style="margin-top: 1.5rem;">
        <g:render template="/resources/ebookFeatures"></g:render>
    </div>
    <%}%>
</main>
<g:render template="/books/footer_new"></g:render>

</body>
<script>
    document.getElementById("introduction").innerHTML=document.getElementById("introduction").innerText;
    <%if(info.fullForm!=null&&!"".equals(info.fullForm)){%>
    document.getElementById("fullFormText").innerHTML=document.getElementById("fullFormText").innerText;
    <%}%>
    document.getElementById("introductionMainText").innerHTML=document.getElementById("introductionMainText").innerText;
    <%if(info.eligibility!=null&&!"".equals(info.eligibility)){%>
    document.getElementById("eligibilityText").innerHTML=document.getElementById("eligibilityText").innerText;
    <%}%>
    <%if(info.examPattern!=null&&!"".equals(info.examPattern)){%>
    document.getElementById("examPatternText").innerHTML=document.getElementById("examPatternText").innerText;
    <%}%>
    <%if(info.fullSyllabus!=null&&!"".equals(info.fullSyllabus)){%>
    document.getElementById("fullSyllabusText").innerHTML=document.getElementById("fullSyllabusText").innerText;
    <%}%>
    <%if(info.applicationProcess!=null&&!"".equals(info.applicationProcess)){%>
    document.getElementById("applicationProcessText").innerHTML=document.getElementById("applicationProcessText").innerText;
    <%}%>
    <%if(info.examDates!=null&&!"".equals(info.examDates)){%>
    document.getElementById("examDatesText").innerHTML=document.getElementById("examDatesText").innerText;
    <%}%>
    <%if(info.faq!=null&&!"".equals(info.faq)){%>
    document.getElementById("faqText").innerHTML=document.getElementById("faqText").innerText;
    <%}%>
    <%if(info.pqpLinks!=null&&!"".equals(info.pqpLinks)){%>
    document.getElementById("pqpLinksText").innerHTML=document.getElementById("pqpLinksText").innerText;
    <%}%>
    <%if(info.generalLinks!=null&&!"".equals(info.generalLinks)){%>
    document.getElementById("generalLinksText").innerHTML=document.getElementById("generalLinksText").innerText;
    <%}%>
    <%if(info.generalInfo!=null&&!"".equals(info.generalInfo)){%>
    document.getElementById("generalInfoText").innerHTML=document.getElementById("generalInfoText").innerText;
    <%}%>
    <%if(info.bestBooks!=null&&!"".equals(info.bestBooks)){%>
    document.getElementById("bestBooksText").innerHTML=document.getElementById("bestBooksText").innerText;
    <%}%>
    <%if(info.youtubeChannels!=null&&!"".equals(info.youtubeChannels)){%>
    document.getElementById("youtubeChannelsText").innerHTML=document.getElementById("youtubeChannelsText").innerText;
    <%}%>
    var data = { "books":"${booksList.get("books")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'),
        "publishers":"${booksList.get("publishers")}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&')
    };

    var booksListTable = "<table cellpadding='5' cellspacing='5' class='table table-striped table-hover'><tr><th></th><th>Book Title</th><th>Publisher</th><th></th></tr>";

    var books = JSON.parse(data.books);

    var noOfBooks = books.length;
    if(noOfBooks>10) noOfBooks=10;
    var imgSrc;
    for(var i=0; i<noOfBooks;i++){
        imgSrc = books[i].coverImage;
        if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
            imgSrc = books[i].coverImage;
            imgSrc = imgSrc.replace("~", ":");
        } else {
            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=thumbnail";
        }
        booksListTable +="<tr><td>" +"<img src='" + imgSrc + "' alt='Book Cover Image' width='50'/></td>"+
            "<td>"+books[i].title+"</td>"+
            "<td>"+books[i].publisher+"</td>" +
            "<td>"+"<a href='/" + replaceAll(books[i].title,' ','-')+"/ebook-details?siteName=books&bookId=" + books[i].id + "&preview=true' target='_blank'>More details</a></td>"+

            "</tr>"
    }
    booksListTable +="</table>"
    if(noOfBooks>0)
        document.getElementById("bestEBooksText").innerHTML = booksListTable;
    else
        document.getElementById("bestEBooksText").innerHTML = "<b> Best seller list not available currently. Please try after some time.";

</script>
</html>
