<div class="chatViewer" style="flex: 30%;">
    <div id="gpt_loader" class="gpt_loader">
        <div class="spinner"></div>
        <div class="introText">
            <%if("Yes".equals(gptcustomloader) && gptloaderpath !=null){%>
            <h3 id="loaderSub" style="margin-top: 12px;">
                <img src="/privatelabel/showPrivatelabelImage?siteId=${""+session["siteId"]}&fileName=${gptloaderpath}"style="width: 90px;margin-top: -8px;"  />
            </h3>
            <%}else {%>
            <h3 id="loaderSub" style="margin-top: 12px;">
                <img src="/assets/resource/ibookgpt-logo-light.svg" style="width: 120px;margin-top: -8px;"/>
            </h3>
            <%}%>
        </div>
    </div>
    <div class="conversation">
        <div id="defaultPromptPanel">
            <div class="introText" style="text-align: center">
                <%if("Yes".equals(gptcustomloader) && gptloaderpath !=null){%>
                <h3 style="justify-content: center">
                    <img src="/privatelabel/showPrivatelabelImage?siteId=${""+session["siteId"]}&fileName=${gptloaderpath}"style="width: 90px;"  />
                </h3>
                <%}else {%>
                <h3 style="justify-content: center">
                    <img src="/assets/resource/ibookgpt-logo-light.svg" style="width: 120px;"/>
                </h3>
                <%}%>
            </div>
        </div>

        <div class="messages">
            <p id="prevChat" style="display:none;text-align: center">Loading previous chat...</p>
            <div id="messages" style="display:flex;flex-direction: column"></div>
            <div class="is-typing" style="display:none;">
                <div class="jump1"></div>
                <div class="jump2"></div>
                <div class="jump3"></div>
                <p id="storepdfState" style="display:none;color: #d0d0d0;">Updating memory...</p>
            </div>
        </div>
        <div class="clearChat">
            <button onclick="showClearChatBtn()" id="showClearChatBtn" style="right: 0px;"><i class="fa-solid fa-chevron-left"></i></button>
            <button onclick="clearChat()" id="clearChatBtn">Clear Chat <i class="fa-solid fa-delete-left"></i></button>
        </div>
    </div>
    <div class="legalText">
        <div class="chat" id="customChatInput">
            <div class="chatInputWrapper">
                <div id="capture-result-wrapper" style="width: 72px;">
                    <span class="snipCancel" style="display: none">
                        <i class="fa-solid fa-xmark"></i>
                    </span>
                    <div id="capture-result"></div>
                </div>
                <textarea type="text" id="chatInput" class="chatInputField" placeholder="Ask any question about the chapter..." oninput="auto_grow(this)"></textarea>

            </div>
            <div class='chatInputOptions'>
                <div>
                    <button class='formulaBtn'>
                        <i class='fa-solid fa-square-root-variable'></i>
                    </button>
                    <button id="start-capture-btn" class="inptOtpBtn"><i class="fa-solid fa-crop-simple"></i></button>
                    <button id="snip-btn" class="inptOtpBtn">Snip</button>
                </div>

                <button class='sendIcon' id='sendBtn'>
                    <i class='fa-solid fa-paper-plane'></i>
                </button>
            </div>
        </div>
        <p>iBookGPT's answers are based on the provided book and might have errors.</p>
    </div>
    <div class="notesAndHighlights">
        <div class="apiLoader">
            <div class="spinner"></div>
        </div>
        <div class="hnHeader">
            <p>Highlights and Notes <i class="fa-solid fa-arrows-rotate refresHN" id="refresHN"></i></p>
            <i class="fa-solid fa-xmark" id="closeHNSlider"></i>
        </div>
        <div class="hnContent" id="hnContent">
        </div>
    </div>
</div>
<script>
    const gptSpinnerloader = document.getElementById("gpt_loader")
    document.addEventListener("DOMContentLoaded",()=>{
        gptSpinnerloader.style.display = 'none'
    })
</script>
