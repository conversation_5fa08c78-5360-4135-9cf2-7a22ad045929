<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Prompt Template Manager</h3>
                    <div class="form-group" id="intrst-area">
                    </div>
                </div>
                <!-- create template dropdown -->
                <div class="form-group col-6">
                    <label>Templates</label>
                    <g:select  class="form-control" id="templateId" name="templateId" from="${promptTemplates}" optionKey="id" optionValue="name"
                               title="Select Template"  noSelection="['':'Select']" onChange="getTemplateDetails()"/>
                </div>

                <div class="form-group">
                    <h5 style="color: #0b7cc5">Prompts List</h5>
                    <input type="checkbox" id="selectAllPrompts" name="selectAllPrompts" value="all">
                    <label for="selectAllPrompts">Select All</label>
                    <div id="promptsList" style="display: flex; flex-wrap: wrap;">
                        <g:each in="${prompts}" var="prompt">
                            <div style="margin-right: 10px;">
                                <input type="checkbox" id="${prompt.promptType}" name="prompt" value="${prompt.promptType}">
                                <label for="${prompt.promptType}">${prompt.promptLabel}&nbsp;${prompt.parentPromptType!=null?"("+prompt.parentPromptType+")":""}</label>
                            <br>
                            </div>
                        </g:each>
                    </div>
                </div>
                <div class="form-group">
                    <label for="templateName">Template Name</label>
                    <input type="text" class="form-control" id="templateName" name="templateName" placeholder="Enter Template Name"><br>
                    <button id="createTempalate" class="btn btn-primary" style="margin-top: 20px;" onclick="javascript:createTemplate()">Create Template</button>&nbsp;&nbsp;
                    <button id="editContent" class="btn btn-primary" style="margin-top: 20px;" onclick="javascript:saveEditedTemplate()">Save Edited Template</button>&nbsp;&nbsp;
                    <button id="deleteTemplate" class="btn btn-primary" style="margin-top: 20px;" onclick="javascript:deleteTemplate()">Delete Template</button>&nbsp;&nbsp;

                    </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid adminForm" >
    <div class="row" id="">
        <div class='col-md-9 main' style=" margin: 10px auto; float: none; padding: 15px;">
            <div>
                <div class="form-group" id="selectedChaptersList">
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
  //select all for prompts logic
    $('#selectAllPrompts').click(function(event) {
        if(this.checked) {
            $('#promptsList input[type=checkbox]').each(function() {
                this.checked = true;
            });
        } else {
            $('#promptsList input[type=checkbox]').each(function() {
                this.checked = false;
            });
        }
    });

    function createTemplate(){
        var templateName = $('#templateName').val();
        var prompts ;
        // get selected prompts and store it as comma separated string in prompts variable
        $('#promptsList input[type=checkbox]').each(function() {
            if(this.checked) {
                if(prompts){
                    prompts = prompts + "," + this.value;
                }else{
                    prompts = this.value;
                }
            }
        });


        if(templateName && prompts){
            $.ajax({
                url: '/autogpt/addPromptTemplate',
                type: 'POST',
                data: {
                    templateName: templateName,
                    promptIds: prompts
                },
                success: function(response){
                    if(response){
                        alert("Template created successfully");
                        //refresh the page
                        location.reload();
                    }else{
                        alert("Error in creating template");
                    }
                }
            });
    }else{
        alert("Please enter template name and select prompts");
    }
    }

    function getTemplateDetails(){
        var templateId = $('#templateId').val();
        $.ajax({
            url: '/autogpt/getTemplateDetails',
            type: 'POST',
            data: {
                templateId: templateId
            },
            success: function(response){
                if(response){
                    $('#templateName').val(response.templateName);
                    $('#promptsList input[type=checkbox]').each(function() {
                        this.checked = false;
                    });
                    var prompts = response.prompts.split(",");
                    for(var i=0; i<prompts.length; i++){
                        $('#'+prompts[i]).prop('checked', true);
                    }

                }else{
                    alert("Error in getting template details");
                }
            }
        });
    }

    function saveEditedTemplate(){
        var templateId = $('#templateId').val();
        var templateName = $('#templateName').val();
        var prompts ;
        // get selected prompts and store it as comma separated string in prompts variable
        $('#promptsList input[type=checkbox]').each(function() {
            if(this.checked) {
                if(prompts){
                    prompts = prompts + "," + this.value;
                }else{
                    prompts = this.value;
                }
            }
        });
        if(templateId && templateName && prompts){
            $.ajax({
                url: '/autogpt/editPromptTemplate',
                type: 'POST',
                data: {
                    templateId: templateId,
                    templateName: templateName,
                    promptIds: prompts
                },
                success: function(response){
                    if(response){
                        alert("Template updated successfully");
                        //refresh the page
                        location.reload();
                    }else{
                        alert("Error in updating template");
                    }
                }
            });
        }else{
            alert("Please enter template name and select prompts");
        }
    }

    function deleteTemplate(){
        var templateId = $('#templateId').val();
        if(templateId){
            //add confirm code first
            if(confirm("Are you sure you want to delete this template?")) {
                $.ajax({
                    url: '/autogpt/deletePromptTemplate',
                    type: 'POST',
                    data: {
                        templateId: templateId
                    },
                    success: function (response) {
                        if (response) {
                            alert("Template deleted successfully");
                            //refresh the page
                            location.reload();
                        } else {
                            alert("Error in deleting template");
                        }
                    }
                });
            }
        }else{
            alert("Please select template to delete");
        }
    }

</script>
</body>
</html>
