<%@ page import="java.text.SimpleDateFormat" %>


<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>
.multiselect-active {
    color: #000000 !important; }
span.multiselect-native-select {
    position: relative; }

span.multiselect-native-select select {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    height: 1px !important;
    margin: -1px -1px -1px -3px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
    left: 50%;
    top: 30px; }

.multiselect-container {
    position: absolute;
    list-style-type: none;
    margin: 0;
    padding: 0; }

.multiselect-container .input-group {
    margin: 5px; }

.multiselect-container .multiselect-reset .input-group {
    width: 93%; }

.multiselect-container > li {
    padding: 0; }

.multiselect-container > li > a.multiselect-all label {
    font-weight: 700; }

.multiselect-container > li.multiselect-group label {
    margin: 0;
    padding: 3px 20px;
    height: 100%;
    font-weight: 700; }

.multiselect-container > li.multiselect-group-clickable label {
    cursor: pointer; }

.multiselect-container > li > a {
    padding: 0; }

.multiselect-container > li > a > label {
    margin: 0;
    height: 100%;
    cursor: pointer;
    font-weight: 400;
    padding: 3px 20px 3px 40px; }

.multiselect-container > li > a > label.checkbox,
.multiselect-container > li > a > label.radio {
    margin: 0; }

.multiselect-container > li > a > label > input[type=checkbox] {
    margin-bottom: 5px; }

.btn-group > .btn-group:nth-child(2) > .multiselect.btn {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px; }
.multiselect .caret {
    display: none; }

.form-group .btn-group {
    width: 100%; }
.form-group .btn-group .multiselect.dropdown-toggle {
    width: 100% !important;
    height: 44px;
    line-height: normal;
    background-color: #FFFFFF;
    text-align: left;
    padding: 12px 0 12px 16px;
    box-shadow: none;
    border: 1px solid #cccccc;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 15px;
    color: #000000 !important;
    font-weight: normal; }
.form-group .btn-group .multiselect-container {
    width: 100%;
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64) !important;
    max-height: 250px;
    overflow: hidden;
    overflow-y: auto; }
.form-group .btn-group .multiselect-container li {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64) !important; }
.form-group .btn-group .multiselect-container a {
    line-height: normal;
    font-size: 15px;
    background-color: #FFFFFF;
    color: rgba(68, 68, 68, 0.64) !important; }
.modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
}
.modal.show .modal-dialog {
    -webkit-transform: none;
    transform: none;
}
.modal.fade .modal-dialog {
    transition: -webkit-transform .3s ease-out;
    transition: transform .3s ease-out;
    transition: transform .3s ease-out,-webkit-transform .3s ease-out;
    -webkit-transform: translate(0,-50px);
    transform: translate(0,-50px);
}
.modal-content{
    width: 100%;
}
@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <h2 class="text-center mb-3">USER ACCESS</h2>
                <label for="userValue" style="display: block;">Get User</label>
                <div class="form-group">

                    <input type="text" class="form-control col-md-4 mb-3 border" name="userValue" id="userValue"  placeholder="Email/mobile No.">
                    <input type="hidden" class="form-control" name="hiddenUserId" id="hiddenUserId">

                    <button class="btn btn-primary btn-lg col-2 mb-3"  onclick="getUser();">Get User</button>
                    <div class="form-group" id="intrst-area">
                    </div>

                </div>
                <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                <div id="successmsg" style="display: none"></div>
                <div id="batchUsers" class="mb-4" style="display: none"></div>
                <label for="userValue" style="display: block;">Role of the User</label>
                <div class="form-group mb-3" id="intrst-area1">
                </div>
                <button class="btn btn-primary btn-lg col-2"  onclick="submitUserAccess(event)">Submit</button>
            </div>
        </div>



    </div>
</div>

<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>

<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<script>
    var oldUsername;
    var roleArr="";
    function getUser(){
        if(document.getElementById("userValue").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the user Email/mobile No."
            $("#errormsg").show();
        }
        else {
            $("#errormsg").hide();
            $("#successmsg").hide();
            $("#batchUsers").show();
            var userValue = document.getElementById("userValue").value;
            <g:remoteFunction controller="log" action="getUserAccess" params="'userValue='+userValue" onSuccess = "showUsers(data);"/>
        }

    }
    function showUser1s() {
        <g:remoteFunction controller="log" action="getUserRole" onSuccess="showData(data)"/>
    }
    function showData(data){
        var strHtml= "";
        roleArr = data.userList;
if(roleArr != undefined && roleArr.length >0){
  strHtml = strHtml + " <select class=\"form-control sage-input-select\" onchange='changed(event)' id=\"selectCheckbox\" multiple=\"multiple\" name=\"interests\">\n" ;
    for(var i = 0 ;i< roleArr.length; i++){
    strHtml = strHtml + "<option value=\""+roleArr[i].id+"\" >" + roleArr[i].authority +"</option>\n";
    }
    strHtml = strHtml + "</select>";
    document.getElementById("intrst-area1").innerHTML=strHtml;
    $('#selectCheckbox').multiselect();
    // document.getElementById("selectCheckbox").innerHTML=strHtml;
}
    }
   function changed(event){

   }


   function submitUserAccess(event) {
       var accessds = "";
       if(($('#selectCheckbox').val()) != null && ($('#selectCheckbox').val()) != undefined ){
       for (var i = 0; i < ($('#selectCheckbox').val()).length; i++) {
           accessds = accessds + $('#selectCheckbox').val()[i] + ",";
       }
   }
       if(accessds.includes(",")) {
           accessds = accessds.substring(0, accessds.length - 1);
       }
        var hiddenUserId=document.getElementById("hiddenUserId").value
       <g:remoteFunction controller="log" action="deleteandUpdateUserRole" params="'userId='+hiddenUserId+'&accessds='+accessds" />
       alert("Updated Successfully!");
       location.reload();
   }


    function showUsers(data){
        if(data.status=="OK") {
            var userId = data.userList[0].id;
            document.getElementById("hiddenUserId").value=userId;
            <g:remoteFunction controller="log" action="getUserRole" params="'userId='+userId" onSuccess="showRoleuser(data)"/>
        }

        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-hover table-bordered'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Username</th>\n" +
            "                            <th>Email</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){

            var users = data.userList;
            for(var i=0;i<users.length;i++){
                var username =users[i].username;
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+users[i].username+"</td>" +
                    "<td>"+users[i].email+"</td>" +
                    "</tr>";
            }
            htmlStr += "</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "No user found with this information.";
        }
        $("#batchUsers").show();

    }

    function showRoleuser(data) {
       var strHtml = "";
       var userRoleArr = [];
       for(var i = 0; i<data.userList.length;i++){
           userRoleArr[i] = data.userList[i].role_id;
       }
        if(roleArr != undefined && roleArr.length >0){
            strHtml = strHtml + " <select class=\"form-control sage-input-select\" onchange='changed(event)' id=\"selectCheckbox\" multiple=\"multiple\" name=\"interests\">\n" ;
            for(var i = 0 ;i< roleArr.length; i++){
                if(userRoleArr.indexOf(roleArr[i].id) > -1) strHtml = strHtml + "<option value=\""+roleArr[i].id+"\" selected>" + roleArr[i].authority +"</option>\n";
                else strHtml = strHtml + "<option value=\""+roleArr[i].id+"\" >" + roleArr[i].authority +"</option>\n";
            }
            strHtml = strHtml + "</select>";
            document.getElementById("intrst-area1").innerHTML=strHtml;
            $('#selectCheckbox').multiselect();
        }
    }
    window.onload=showUser1s();
    $(document).ready(function() {$('#selectCheckbox').multiselect(); });
</script>

</body>
</html>
