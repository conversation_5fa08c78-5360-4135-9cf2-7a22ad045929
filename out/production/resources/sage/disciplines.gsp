<%@ page import="javax.servlet.http.Cookie" %>
<%@ page import="java.text.SimpleDateFormat" %>
<%@ page import="java.text.DateFormat" %>
<%@ page import="java.net.URLDecoder"%>
<%
    String requestURL = request.getRequestURL().toString();
    String servletPath = request.getServletPath();
    String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

    session.setAttribute("servername", appURL);
    def newCookie = new javax.servlet.http.Cookie("siteName", "sage");
    newCookie.path = "/"
    response.addCookie newCookie;
%>
<g:render template="/sage/navheader"></g:render>
<%
    def notLoggedIn=false
    if(session.getAttribute("userdetails")==null) notLoggedIn=true
%>
<div class="container-fluid read-book-container">
  <div class="row book-read-tabs" style="margin-right: 0;">
    <div class="col-md-3 col-sm-3 col-xs-4 read-book-name hidden-xs">
      <p class="read-book-title">Disciplines</p>
    </div>
    <div class="col-md-9 col-xs-12 col-sm-9 tabs-section">          
      <ul class="nav nav-tabs chapter-tabs" id="chapter-details-tabs" role="tablist">
        <li class="pull-right">
          <button data-toggle="collapse" data-target="#sort-by-dropdown" class="btn btn-default btn-sort dropdown-toggle" type="button" onclick="javascript:showSortDropdown();" id="sort-btn">
            Sort by: <i class="material-icons pull-right">chevron_right</i>
          </button>
        </li>
      </ul>
    </div>
  </div>
  <div class="row chapter-read-material-wrapper">
    <div class="col-md-3 col-xs-6 col-sm-3 read-book-chapters">
      <ol class="read-book-chapters-wrapper">
        <%if(disciplines!=null){
          disciplines.each{
          if(params.discipline!=null&it.discipline.equals(params.discipline)){
        %>
        <li class="chapter-name chapter-name-active">
          <a href="/sage/disciplines?discipline=${it.discipline}" class="sage-dropdown-menu-item-link">${it.discipline} (${it.noOfBooks})</a>
        </li>
        <%}else{%>
          <li class="chapter-name">
            <a href="/sage/disciplines?discipline=${it.discipline}" class="sage-dropdown-menu-item-link">${it.discipline} (${it.noOfBooks})</a>
          </li>
          <%}}
        }%>
      </ol>
    </div>
    <a class="hidden-sm hidden-md hidden-lg" href="javascript:showHideChaptersMobile();" id="hideShowDivMobile">Chapters</a>
    <a class="hidden-sm hidden-xs hidden-md" href="javascript:showHideChaptersTab();" id="hideShowDivTab"></a>
    <div id="book-read-material" class="col-md-9 col-sm-9 col-xs-12 book-read-material books-content-wrapper">
        <ul class="dropdown-menu" id="sort-by-dropdown">
          <li><a href="/sage/disciplines?discipline=${params.discipline}&sort=datePublished&order=desc" <%="datePublished".equals(params.sort&&"desc".equals(params.order))%>>Publication date – Newest to Oldest</a></li>
          <li><a href="/sage/disciplines?discipline=${params.discipline}&sort=datePublished&order=asc" <%="datePublished".equals(params.sort&&"asc".equals(params.order))%>>Publication date – Oldest to Newest</a></li>
          <li><a href="/sage/disciplines?discipline=${params.discipline}&sort=title&order=asc">Title – A to Z</a></li>
          <li><a href="/sage/disciplines?discipline=${params.discipline}&sort=title&order=desc">Title – Z to A</a></li>
          <li><a href="/sage/disciplines?discipline=${params.discipline}&sort=authors&order=asc">Author – A to Z</a></li>
          <li><a href="/sage/disciplines?discipline=${params.discipline}&sort=authors&order=desc">Author – Z to A</a></li>
        </ul>
      <%books.each{book->%>
      <div class='book-wrapper'>
        <%if(notLoggedIn){%>
        <a href="/sage?isbn=${book.isbn}">
          <%}else{
          if(session.getAttribute("userdetails")!=null&&"student".equals(session.getAttribute("userdetails").userType)){%>

          <a href="/sage/doris?siteName=sage&bookId=${book.id}">
            <%}else{%>
           <a href="/sage/instructorResources?isbn=${book.isbn}&siteName=sage">
      <%}}%>
            <div class='book-item'>
              <div class='book-img-wrapper'>
                <img class='book-image' src="/funlearn/showProfileImage?id=${book.id}&fileName=${book.coverImage}&type=books&imgType=passport"; alt=''/>
              </div>
              <div class='book-info'>
                <p class='book-name'>${book.title}</p>
                <p class='book-description'><%=java.net.URLDecoder.decode(book.description!=null?book.description:"", "UTF-8")%></p>
                <p class='author-name'><%=book.authors.replaceAll(",",", ")%></p>
<%   
    int pubYear = 0;
    String pubMonth = "";
    
    if(book!=null && book.datePublished!=null) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(book.datePublished);
        pubYear = cal.get(Calendar.YEAR);
        pubMonth = new SimpleDateFormat("MMM").format(cal.getTime())
    }
%>
                <p class='published-date'>Published: <%=pubYear!=0?pubMonth+" "+pubYear:""%></p>
              </div>
            </div>
          </a>
        </div>
        <%}%>
      </div>
    </div>
  </div>
  <g:render template="/sage/footer"></g:render>
<script>
function showHideChaptersMobile() {
  if($('.read-book-chapters').css("margin-left") == "-600px") {
    $('.read-book-chapters').animate({"margin-left": '0'}, 'fast');
    $('.section-btns').animate({"width": '50%'});
    $('#hideShowDivMobile').addClass('rotated');
    $('#hideShowDivMobile').animate({'left' : '65%'}, 'fast');
    $('body').css({
      'overflow' : 'hidden'
    });
  } else {
    $('.read-book-chapters').animate({"margin-left": '-=600'});
    $('#hideShowDivMobile').removeClass('rotated');
    $('#hideShowDivMobile').animate({'left' : '-27px'}, 'fast');
    $('.section-btns').animate({"width": '100%'});
    $('body').css({
      'overflow' : 'auto'
    });
  }
}

function showHideChaptersTab() {
  if($('.read-book-chapters').css("margin-left") == "-500px") {
    $('.read-book-chapters').animate({"margin-left": '0'});
    $('.book-read-material').animate({"width": '100%'});
    $('.section-btns').animate({"width": '50%'});
    $('#hideShowDivTab').addClass('rotated');
    $('#hideShowDivTab').animate({'left' : '50%'});
    $('body').addClass('no-scroll');
  } else {
    $('.read-book-chapters').animate({"margin-left": '-=500'});
    $('.book-read-material').animate({"width": '100%'});
    $('.section-btns').animate({"width": '100%'});
    $('#hideShowDivTab').removeClass('rotated');
    $('#hideShowDivTab').animate({'left' : '0'}, 'fast');
    $('body').removeClass('no-scroll');
  }
}

var url = new URL(document.location);
var sort = url.searchParams.get("sort");
var order = url.searchParams.get("order");
var discipline = url.searchParams.get("discipline");

$(function(){
    $('#sort-by-dropdown a').each(function(){
        var $this = $(this);
        if($this.attr('href') == '/sage/disciplines?discipline='+discipline+ '&sort=' +sort + '&order=' + order) {
            $('#sort-btn').html($this.html() + "<i class='material-icons pull-right'>chevron_right</i>");
        }
    });
});

function showSortDropdown() {
    $('#sort-by-dropdown').toggleClass('sort-by-dropdown');
    $(document).mouseup(function(e) {
        var sortItems = $('#sort-by-dropdown');
        if (!sortItems.is(e.target) && sortItems.has(e.target).length === 0) {
            $(sortItems).removeClass('sort-by-dropdown');
        }
    });
}
</script>
