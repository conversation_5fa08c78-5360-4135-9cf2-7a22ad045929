<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>


<script>
  var loggedIn=false;
</script>
<sec:ifLoggedIn>
  <script>
    loggedIn=true;
  </script>
</sec:ifLoggedIn>
<style>
.sage-body .sage-banner {
  display: none;
}
</style>


<!--<div>-->
<div class="loading-icon hidden">
  <div class="loader-wrapper">
    <div class="loader">Loading</div>
  </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 475px);">
  <div class='row'>
    <div class='col-md-12 main' style="max-width: 900px; margin: 40px auto; float: none; padding: 15px;">
      <div id="content-books">
        <div class="form-group">
          <label for="subTab" style="display: block;">Tab Links</label>
          <select name="subTab" id="subTab" class="form-control" style="width: 36%; display: inline-block;">
            <option value="" disabled="disabled" selected>Select Tab</option>
            <option label="---Instructor Resources---" disabled="disabled" class="option-label">---Instructor Resources---</option>
            <option value="teachingNotes">Teaching Notes</option>
            <option value="teachingSlides">Teaching Slides</option>
            <option value="sampleChapters">Sample Chapters</option>
            <option value="mediaLinks">Media Links</option>
            <option value="exercises">Exercises</option>
            <option label="---Left sidebar links---" disabled="disabled" class="option-label">---Left sidebar links---</option>
            <option value="bookDescription">Book Description</option>
            <option value="detailedContents">Detailed Contents</option>
            <option value="aboutTheAuthors">About The Authors</option>
            <option value="requestInspectionCopy">Request Inspection Copy</option>
            <option value="viewCompleteList">View Complete List</option>
            <option value="buyNow">Buy Now</option>
          </select>
        </div>
        <div class="form-group">
          <input type="text" class="form-control" name="link" id="link" size="50" maxlength="255" placeholder="Link">
        </div>
        <div class="form-group">
          <input type="text" class="form-control" name="linkName" id="linkName" size="50" maxlength="255" placeholder="Link name">
        </div>
        <div class="form-group">
          <button class="btn export-study-set btn-book-buy" style="float: none; width: 162px;" onclick="addTabLink()">Add</button>
        </div>
        
        <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
        <div id="successmsg" style="display: none"></div>
        <div id="batchUsers" style="display: none"></div>
      </div>
    </div>
  </div>
</div>


<g:render template="/${session['entryController']}/footer"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>

<script>
  function addTabLink(){
    $('.loading-icon').addClass('hidden');
    $("#errormsg").hide();
    $("#successmsg").hide();
    var subTab = document.getElementById("subTab");

    if(subTab.selectedIndex==0){
      document.getElementById("errormsg").innerHTML="Please select a tab to add the link."
      $("#errormsg").show();

    }else if(document.getElementById("link").value===""){
      document.getElementById("errormsg").innerHTML="Please enter the link."
      $("#errormsg").show();
    }
    else if((subTab.value=="teachingNotes"||subTab.value=="teachingSlides"||subTab.value=="sampleChapters"||subTab.value=="mediaLinks"||subTab.value=="exercises")&&document.getElementById("linkName").value===""){
      document.getElementById("errormsg").innerHTML="Please enter the link name."
      $("#errormsg").show();
    }
    else{
      $('.loading-icon').removeClass('hidden');
      $("#errormsg").hide();
      var subTab = subTab.value;

      var link = document.getElementById("link").value;
      var linkName = encodeURIComponent(document.getElementById("linkName").value);
      console.log
      if((subTab=="bookDescription"||subTab=="detailedContents"||subTab=="aboutTheAuthors"||subTab=="requestInspectionCopy"||subTab=="viewCompleteList")){
        console.log("assinging empty string");
        linkName="Sample";
      }
      <g:remoteFunction controller="sage" action="insertInstructorResource" params="'subTab='+subTab+'&link='+link+'&linkName='+linkName+'&bookId=${params.bookId}'" onSuccess = "linkAdded(data);"/>
    }
  }

  function linkAdded(data){
    $("#errormsg").hide();
    $("#successmsg").hide();
    $('.loading-icon').addClass('hidden');
    if(data.status=="error"){
      document.getElementById("errormsg").innerHTML="Failure in adding the link. Please try later";
      $("#errormsg").show();
    }
    else{
      document.getElementById("successmsg").innerHTML=data.status;
      document.getElementById("link").value="";
      document.getElementById("linkName").value="";
      $("#successmsg").show();
      getInstructorResources();
    }
  }
  
  function delTabLink(id){
    <g:remoteFunction controller="sage" action="deleteInstructorResource" params="'id='+id" onSuccess = "getInstructorResources();"/>    
  }

  function getInstructorResources(){
    <g:remoteFunction controller="wonderpublish" action="getInstructorResources" params="'bookId=${params.bookId}'" onSuccess = "showLinks(data);"/>
  }

  function showLinks(data){
    $('.loading-icon').addClass('hidden');
    var htmlStr="<h4>Links</h4>\n" +
      "                    <table class='table table-responsive table-striped table-bordered'>\n" +
      "                        <tr>\n" +
      "                            <th>Tab</th>\n" +
      "                            <th>Link</th>\n" +
      "                            <th>Link Name</th>\n" +
      "                            <th>Action</th>\n" +
      "                        </tr>\n" ;


      var links = data.instructorResources;
       for(i=0;i<links.length;i++){
        htmlStr +="<tr><td style='text-transform:capitalize;'>"+links[i].subTab+"</td>"+
          "<td>"+links[i].link+"</td>"+
          "<td>"+links[i].linkName+"</td>" +
          "<td><a href='javascript:delTabLink("+links[i].id+")' class='text-danger'>Delete</a></td></tr>";
      }
      htmlStr +="                        \n" +
        "                    </table>";
      document.getElementById("batchUsers").innerHTML= htmlStr;

    $("#batchUsers").show();

  }
  getInstructorResources();
</script>


</body>
</html>
