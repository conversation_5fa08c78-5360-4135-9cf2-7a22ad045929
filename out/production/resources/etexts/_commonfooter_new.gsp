<%@ page import="com.wonderslate.data.SiteMst" %>
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.1/jquery.min.js"></script>
<script src="https://apis.google.com/js/api:client.js"></script>
<asset:javascript src="multiselect.js"/>

<script type="text/javascript">
    var nameoftheuser="";
    var showBookRec=false;
    <%  if(session['userdetails']!=null && session['userdetails'].name!=null) { %>
    nameoftheuser = "${session['userdetails'].name}";
    <%
    }

    if(session['userdetails']!=null && session['userdetails'].username!=null &&
            "<EMAIL>".equals(session['userdetails'].username)){
%>
    showBookRec= true;
    <% } %>
    var siteId = "<%=session.getAttribute("siteId")%>";
    jQuery(document).ready(function() {
        var windowW = window.innerWidth || $(window).width();
        var fullwidth;
        var fullscreen;

        if (windowW > 767) {
            fullwidth = "off";
            fullscreen = "on";
        } else {
            fullwidth = "on";
            fullscreen = "off";
        }
    }); //ready
</script>
<script>
    function scrollFunction() {
        if(!$("#myBtn").length) return;

        if (document.body.scrollTop > 20 || document.documentElement.scrollTop > 20) {
            $("#myBtn").css("display", "block");
        } else {
            $("#myBtn").css("display", "none");
        }
    }

    // When the user clicks on the button, scroll to the top of the document
    function topFunction() {
        document.body.scrollTop = 0; // For Chrome, Safari and Opera
        document.documentElement.scrollTop = 0; // For IE and Firefox
    }

    if(siteId !==6) {
        $(window).scroll(function() {
            scrollFunction();
            var scroll = $(window).scrollTop();
            if (scroll >= 100) {
                $('header').addClass('header-shadow');
                $('body').addClass('custom-fix');
            } else {
                $('header').removeClass('header-shadow');
                $('body').removeClass('custom-fix');
            }
        });
    }


</script>

<script>
    $('.user-profile-dropdown').mouseenter(function() {
        $(this).addClass('open');
    }).mouseleave(function() {
        $(this).removeClass('open');
    });

    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailed').style.display = 'none';
        } else {
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }
    }

    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";

    function getTopicsMap(mode){
        <g:remoteFunction controller="funlearn" action="topicsMap"  onSuccess='initializeDataIndex(data);'
                params="'country='+country+'&mode='+mode" />
    }

    <%if("true".equals(showDiscover)||"true".equals(homeDiscover)){%>
    getTopicsMap('topic');
    <%}%>

    $('#chapter-details-tabs a').click(function (e) {
        e.preventDefault()
        $(this).tab('show');
    });

    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function deleteCookie(cookiename){
        document.cookie = cookiename + '=;expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }

    function updateQuizPoints(quizId,correctAnswers) {
        <g:remoteFunction controller="funlearn" action="addQuizPoints" params="'quizId='+quizId+'&correctAnswers='+correctAnswers" />
    }

    function getStars(stars) {
        var starsString="";
        var starsString = "<span class='orange'>";

        for(var i = 0; i < 5; i++) {
            if(stars >= 1) {
                stars = stars - 1;
                starsString += "<i class='fa fa-star fa-x'>";
            } else if(stars > 0 && stars <= 1) {
                stars = stars - 1;
                starsString += "<i class='fa fa-star-half-empty fa-x'></i>";
            } else {
                starsString += "<i class='fa fa-star-o fa-x'>";
            }
        }

        starsString += "</span>";
        return starsString;
    }

    function showLoader(){
        $('body').removeClass('loaded');
    }

    function removeLoader(){
        $('body').addClass('loaded');
    }

    $('.smartbanner').on('click', '#close-advert', function() {
        $('html, body').addClass('no-padding');
    });

    function showRegistration() {
        showSignUpModal();
        <g:remoteFunction controller="wonderpublish" action="storeBookIdForPurchase" params="'bookId='+purchaseBookId" />
    }

    $(document).ready(function() {
        $(window).scroll(function() {
            var sliderHeight = $('.smart-books-slider').height() || $('.arihant-slider').height();
            var headerHeight = $('.sticky-header').height() || $('.mobile-header').height();
            var scrollPage = $(window).scrollTop();
            var siteId;

            if(siteId != 9) {
                if(scrollPage >= headerHeight) {
                    $('.sticky-header').addClass('fixed-header');
                    $('.tab-sub-categories, .book-read-tabs').addClass('fixed-tabs-catos-mobile').css({
                        'top' : '0',
                        'transition': 'all .3s ease'
                    });
                } else {
                    $('.sticky-header').removeClass('fixed-header');
                    $('.tab-sub-categories, .book-read-tabs').removeClass('fixed-tabs-catos-mobile').css({
                        'top' : ''
                    });
                }

                if(scrollPage >= sliderHeight) {
                    $('.tabs-holder').addClass('fixed-tabs-holder-desktop').css({
                        'top' : headerHeight
                    });

                    $('.tab-sub-categories').addClass('tab-margin');
                } else {
                    $('.tabs-holder').removeClass('fixed-tabs-holder-desktop').css({
                        'top' : ''
                    });

                    $('.tab-sub-categories').removeClass('tab-margin');
                }
            }
        });

        $('#htmlreadingcontent').scroll(function() {
            if($('#htmlreadingcontent').scrollTop() > 100) {
                $('.section-btns').slideUp();
            }

            if($('#htmlreadingcontent').scrollTop() <= 100) {
                $('.section-btns').slideDown();
            }

            if($('#htmlreadingcontent').scrollTop() + $('#htmlreadingcontent').scrollTop() == $(document).height()) {
                $('.section-btns').slideDown();
            }
        });
    });

    $('.tab-sub-categories-item').on('click', '.tab-sub-categories-item-btn', function() {
        $('.tab-sub-categories-item .tab-sub-categories-item-btn').removeClass('active');
        $(this).addClass('active');
    });

    function showSignup() {
        $('#social-login').finish().delay().fadeOut(100, function(){
            $('#social-signup').fadeIn(100);
            $('#sign-up-div').hide();
        });
    }

    function showSignIn() {
        $('#social-signup').finish().delay().fadeOut(100, function(){
            $('#social-login').fadeIn(100);
            $('#sign-in-div').hide();
        });
    }

    function hideSocialSignIn() {
        $('#social-login').finish().delay().fadeOut(100, function(){
            $('#sign-in-div').fadeIn(100);
        });
    }

    function hideSocialSignUp() {
        $('#social-signup').finish().delay().fadeOut(100, function(){
            $('#sign-up-div').fadeIn(100);
        });
    }

    function showFGpassword() {
        $('#sign-in-div').finish().delay().fadeOut(100, function(){
            $('#forgot-password-div').fadeIn(100);
        });
    }

    function bckLogin() {
        $('#forgot-password-div').finish().delay().fadeOut(100, function(){
            $('#sign-in-div').fadeIn(100);
            $('#reset-password-div').hide();
            $('#google-password-div').hide();
        });
    }

    function showSignUpModal() {
        $('#signin-modal').modal('show');
        $('#social-signup').show();
        $('#social-login').hide();
        $('#sign-up-div').hide();
        $('#sign-in-div').hide();
        $('#forgot-password-div').hide();
    }

    function showSignInModal() {
        $('#loginSignup').modal('show');
        $('#connecting-div').hide();
        $('#social-signup').hide();
        $('#social-login').show();
        $('#sign-in-div').hide();
        $('#sign-up-div').hide();
        $('#loginFailed').hide();
        $('#forgot-password-div').hide();
    }


    $(document).ready(function(){
        var emailRegex = /^\w+([-+.'][^\s]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        var mobileRegex = /[0-9]{10}/;
        var otpRegex = /[0-9]{6}/;

        $('.email-input').on('keyup',function(){
            if (!$(this).val().match(emailRegex)) {
                $('.email-input').addClass('has-error');
            } else {
                $('.email-input').removeClass('has-error');
                $('.email-input').addClass('is-correct');
            }
        });

        $('.mobile-input').on('keyup',function(e){
            $(this).attr('maxlength', '10');
            $(this).attr('minlength', '10');
            if (!$(this).val().match(mobileRegex)) {
                $('.mobile-input').addClass('has-error');
                $('.input-error-tooltip').show();
            } else {
                $('.mobile-input').removeClass('has-error');
                $('.mobile-input').addClass('is-correct');
                $('.mobile-error').hide();
                $('.input-error-tooltip').show();
            }

            if($.inArray(e.keyCode, [46, 8, 9, 27, 13, 110, 190]) !== -1 || (e.keyCode === 65 && (e.ctrlKey === true || e.metaKey === true)) || (e.keyCode >= 35 && e.keyCode <= 40)) {
                return;
            } else {
                $('.input-error-tooltip').hide();
            }
            if((e.shiftKey || (e.keyCode < 48 || e.keyCode > 57)) && (e.keyCode < 96 || e.keyCode > 105)) {
                e.preventDefault();
                $('.input-error-tooltip').show();
            }
        });
    });

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(find, 'g'), replace);
    }

    function elementExists(elementId){
        var element =  document.getElementById(elementId);
        if (typeof(element) != 'undefined' && element != null) {
            return true;
        } else {
            return false;
        }
    }

    $(document).ready(function(){
        <%  if("true".equals(params.loginFailed)) { %>
        document.getElementById('loginFailed').innerText='Login failed. Please try again!';
        if(siteId!="12") {
            //for the cases where we have to do force login and then logout
            var simultaneosCookie = getCookie("SimulError");
            if("Fail"==simultaneosCookie) {
                document.getElementById('loginFailed').innerText='The user cannot login as he already logged in from multiple devices';
                deleteCookie("SimulError");
            }

            loginOpen();
            document.getElementById('loginFailed').style.display = 'block';

        }
        <%  } else if("true".equals(params.showSignIn)) { %>
        showSignInModal();
        hideSocialSignIn();
        <%  } else { %>
        //for the cases where we have to do force login and then logout
        var simultaneosCookie = getCookie("SimulError");

        if("Fail"==simultaneosCookie) {
            showSignInModal();
            hideSocialSignIn();
            document.getElementById('loginFailed').style.display = 'block';
            deleteCookie("SimulError");
        }
        <%  } %>
    });

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }
</script>

<%
        if("eutkarsh".equals(grailsApplication.config.grails.appServer.default)){
%>
<asset:javascript src="analyticsUtkarsh.js"/>
<%      } else { %>
<asset:javascript src="analytics.js"/>
<%
        }

%>
<script>
    var lastReadTopicId = 0;
</script>



<script type="text/javascript">


    <% com.wonderslate.data.SiteMst sm = SiteMst.findById(session['siteId']); %>
    var googleUser = {};
    var startApp = function() {
        gapi.load('auth2', function(){
            // Retrieve the singleton for the GoogleAuth library and set up the client.
            auth2 = gapi.auth2.init({
                client_id: '${sm.googleClientId}',
                cookiepolicy: 'single_host_origin',
                // Request scopes in addition to 'profile' and 'email'
                //scope: 'additional_scope'
            });
            attachSignin(document.getElementById('googleSignInButton'));
            attachSignin(document.getElementById('googleSignInButton1'));
        });
    };

    function attachSignin(element) {
        auth2.attachClickHandler(element, {},
            function (googleUser) {
                $('.loading-icon').removeClass('hidden');
                var profile = googleUser.getBasicProfile();
                googleUser.getBasicProfile().getName();
                // Useful data for your client-side scripts:
                var profile = googleUser.getBasicProfile();
                var password =  profile.getId();
                var name = profile.getGivenName();
                var email = profile.getEmail();

                // The ID token you need to pass to your backend:
                var id_token = googleUser.getAuthResponse().id_token;
                $('#loginSignup').modal('hide');

                <g:remoteFunction controller="creation" action="addUser"
                        params="'username=Google'+email+'&email='+email+'&password='+password+'&name='+name" onSuccess='loginReturned(data);'></g:remoteFunction>
            }, function (error) {

            });
    }

    function loginReturned(data){
        <%  if(sm.id.intValue()==12) {%>
        window.location.href = "/evidya/library";
        <%  } else { %>
        window.location.href = "/library";
        <%  } %>
    }

    function googleSignInCalled(){
        $('#loginSignup').modal('hide');
    }

    startApp();
</script>
