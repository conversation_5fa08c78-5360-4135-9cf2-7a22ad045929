<g:render template="/wonderpublish/buyOrAdd"></g:render>

<style>
@media (max-width:400px){
    #emailSentModal .modal-body p {
        font-size: 15px !important;
    }
    #emailSentModal .modal-body p br {
        display: none;
    }
}
</style>

<div class="modal" id="deleteBook">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header" style="min-height: 50px;">
                <h4 class="modal-title"></h4>

            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="font-size: 18px;" id="remove-msg">Are you sure you want to Remove book from your library?.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-primary btn-primary1" onclick="javascript:bookDelete();">Yes</button>
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
            </div>

        </div>
    </div>
</div>
<div class="modal" id="emailSentModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center mb-0 py-3" style="font-size: 18px;font-family: 'HelveticaNeue-Roman';">That's a great choice! Thank you for <br>recommending the book to your Library.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-sm btn-primary col-4 col-md-3" data-dismiss="modal" style="font-family: 'HelveticaNeue-Roman';">Okay</button>
            </div>

        </div>
    </div>
</div>
<script>
    var searchMode=false;
    var getbookId;
    var books ;
    var booksTags;
    var tempBooks=[];

    var indexSubject="${params.subject}";
    var indexGrade="${params.grade}";
    var indexLanguage="${params.language}";
    var sortBy="title";
    var previousMode="";
    var previousModeValue="";
    var myLibraryMode=false;
    var instituteLibraryCalled=false;
    var totalNumberOfBooksPresent=0;
    var numberOfBooksPerPage=10;
    var totalNumberOfPages=0;
    var numberOfPageNumbersPerUnit=10;
    var currentLastPageNumber=0;
    var bookIds=[];
    var storeBooksData;
    var libraryBooksData = "";
    var libraryBooksList=",";
    var libraryAccess="${showLibrary}";
    function goToPageNumber(pageNumber){
        var startIndex = (pageNumber*numberOfBooksPerPage);
        var endIndex = (pageNumber+1)*numberOfBooksPerPage;
        if(endIndex>tempBooks.length) endIndex=tempBooks.length;
        document.getElementById("topPaginationMessage").innerHTML="<span>"+(startIndex+1)+"-"+endIndex+"</span> of <span>"+tempBooks.length+"</span> eTexbooks";
        displayBooksPagewise(startIndex,endIndex)
    }

    function displayBooksPagewise(startIndex,endIndex){

        $("html, body").animate({ scrollTop: 0 }, "fast");

        $('.loading-icon').removeClass('hidden');
        var htmlStr="";
        var language="";
        var imgSrc="";
        // console.log(data);

        for(var i=startIndex;i<endIndex;i++){

            if(tempBooks[i].language==null||""==tempBooks[i].langauge||"null"==tempBooks[i].language) language="English";
            else {
                language=tempBooks[i].language;
            }
            imgSrc = "/funlearn/showProfileImage?id=" + tempBooks[i].id + "&fileName=" + tempBooks[i].coverImage + "&type=books&imgType=passport";
            htmlStr +="<div class=\"d-flex bookContainer flex-wrap flex-md-nowrap\">\n" +
                "                             <div>" ;


            if(instituteLibraryCalled||libraryBooksList.indexOf(","+tempBooks[i].id+",")>-1){
                htmlStr +=  "<a class='lib-showcase' href='/library/" + encodeURIComponent(tempBooks[i].title).replace(/'/g,"&#39;").toLowerCase() + "?bookId=" + tempBooks[i].id + "&siteName=${session['entryController']}'>" +
                    "<img src='" + imgSrc + "' class=\"img-responsive bookImage\" alt=\"\">" +
                    "</a>" ;
            }
            else{
                htmlStr +=  "<img src='" + imgSrc + "' class=\"img-responsive bookImage position-relative\" alt=\"\">";
                htmlStr +="<img src='/assets/evidya/lock.png' class='lock-img'>";
                // htmlStr += "<button class='btn btn-primary' style='margin-top: 10px' > Suggest this book </button>"
            }

            htmlStr += "</div>\n"
            htmlStr +=   "                             <div class=\"textWrapper\">\n" +
                "                                 <h3>"+tempBooks[i].title+"</h3>\n" +
                "                                 <p class=\"editBy mt-2\"><span>"+replaceAll(tempBooks[i].authors ? tempBooks[i].authors:"",/[-,~]/g,", ")+"</span></p>\n" +

                "                             </div>\n" +
                "                             <div>\n" +
                "<div class='language"+" "+language.toLowerCase()+"'>"+"<img src='/assets/evidya/languagechange.svg'>"+"<span>"+language+"</span>"+"</div>";
            if(libraryAccess!="false" && libraryBooksData.institutionEmail != undefined && libraryBooksData.institutionEmail != null && libraryBooksData.institutionEmail != '' && !(instituteLibraryCalled||libraryBooksList.indexOf(","+tempBooks[i].id+",")>-1)){
                htmlStr += "<a style='position: absolute;\n" +
                    "  bottom: 30px;\n" +
                    "  right: 10px;' onclick='sendEmail({\"siteName\":\"evidya\"," +
                    "\"toEmail\":\""+libraryBooksData.institutionEmail+"\","+
                    "\"title\":\""+(tempBooks[i].title).replace(/'/g,"&#39;")+"\","+
                    "\"id\":\""+tempBooks[i].id+"\","+
                    "\"coverImage\":\""+tempBooks[i].coverImage+"\","+
                    "\"author\":\""+replaceAll(tempBooks[i].authors ? tempBooks[i].authors:"",/[-,~]/g,", ")+"\""+
                    "})' " +
                    "href='javascript:'" +
                    // "\"mailto:"
                    // +libraryBooksData.institutionEmail+"?cc=<EMAIL>&Subject=Recommend Book&body= I%20recommend%20this%20book%20to%20be%20added%20to%20the%20SAGE%20e-Vidya%20collection!%0D%0A"
                    // +tempBooks[i].title+"%20By "+tempBooks[i].authors+"%0D%0A"+"\"" +
                    " target=\"_top\" class='suggest_this_book'>Suggest This Book</a>" ;
            }
            htmlStr +=  "                             </div>\n" ;

            if(instituteLibraryCalled&&!bookIds.toString().includes(tempBooks[i].id)) {
                htmlStr +="                             <div class=\"addLibrary\">\n" +
                    "                                 <a href=\"javascript:addToMyLibrary(" + tempBooks[i].id + "," + tempBooks[i].offerPrice + ",'" + tempBooks[i].title.replace(/'/g,"\\'") + "');\" class=\"d-flex\"><i class=\"material-icons\">\n" +
                    "                                     add_circle_outline\n" +
                    "                                 </i>Add to My Library</a>\n" +
                    "                             </div>\n";
            }
            else if(instituteLibraryCalled&&bookIds.toString().includes(tempBooks[i].id)) {
                htmlStr +="<div class=\"addLibrary\">\n" +
                    "                                 <a href='#' class=\"d-flex\"><i class=\"material-icons\">\n" +
                    "                                    done_all \n" +
                    "                                 </i>Added to My Library</a>\n" +
                    "                             </div>\n";
            }


            htmlStr +="                         </div>";
        }

        document.getElementById("booksDisplayList").innerHTML=htmlStr;

        $('.loading-icon').addClass('hidden');
    }

    function displayMyLibraryBooks(books){
        if(bookssearch1.length == 0) bookssearch1 = books;
        $("html, body").animate({ scrollTop: 0 }, "fast");

        $('.loading-icon').removeClass('hidden');
        var htmlStr="";
        var language="";
        var imgSrc="";
        for(var i=0;i<books.length;i++){
            bookIds.push(books[i].id);
            if(books[i].language==null||""==books[i].langauge||"null"==books[i].language) language="English";
            else {
                language=books[i].language;
            }
            imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            htmlStr +="<div class=\"d-flex bookContainer flex-wrap flex-md-nowrap\">\n" +
                "                             <div>" ;

            htmlStr +=  "<a class='lib-showcase' href='/library/" + encodeURIComponent(books[i].title).replace(/'/g,"&#39;").toLowerCase() + "?bookId=" + books[i].id + "&siteName=${session['entryController']}'>" +
                "<img src='" + imgSrc + "' class=\"img-responsive bookImage\" alt=\"\">" +
                "</a>" ;

            htmlStr += "</div>\n" +
                "                             <div class=\"textWrapper\">\n" +
                "                                 <h3>"+books[i].title+"</h3>\n" +
                "                                 <p class=\"editBy mt-2\"><span>"+replaceAll(books[i].authors ? books[i].authors:"",/[-,~]/g,", ")+"</span></p>\n" +

                "                             </div>\n" +
                "                             <div>\n" +
                "<div class='language"+" "+language.toLowerCase()+"'>"+"<img src='/assets/evidya/languagechange.svg'>"+"<span>"+language+"</span>"+"</div>"+
                "                             </div>\n" ;

            htmlStr +="                             <div class=\"addLibrary\">\n" +
                "                                 <a href=\"javascript:deleteModal(" + books[i].id + ");\" class=\"d-flex\"><i class=\"material-icons\">\n" +
                "                                     remove_circle_outline\n" +
                "                                 </i>Remove from My Library</a>\n" +
                "                             </div>\n";



            htmlStr +="                         </div>";
        }
        if(tempBooks.length>numberOfBooksPerPage) {
            displayBooksPagewise(0,numberOfBooksPerPage);
        }else{
            displayBooksPagewise(0,tempBooks.length);
        }

        document.getElementById("booksDisplayListMyLibrary").innerHTML=htmlStr;


        $('.loading-icon').addClass('hidden');

    }
    function displayBooks(mode,modeValue,gradeVal="") {
        var searchbook = '';
        if (document.getElementById("search-book") != null) searchbook = document.getElementById("search-book").value;
        if (searchbook == '' && searchMode == true) {
            window.location.href = window.location.href.split('?')[0] + '?mode=' + mode + '&modeValue=' + modeValue;
            if (window.location.href) {
                $('.loading-icon').removeClass('hidden');
            }
        }

        if (!myLibraryMode) {
            $("#bottomPagination").hide();
            $("#topPaginationMessage").hide();
        }

        totalNumberOfBooksPresent = 0;
        tempBooks = [];


        var foundBook = false;

        if (indexSubject) {
            mode = "subject";
            modeValue = indexSubject;
            indexSubject = "";
        } else if (indexGrade) {
            mode = "grade";
            modeValue = indexGrade;
            indexGrade = "";
        } else if (indexLanguage) {
            mode = "language";
            modeValue = indexLanguage;
            indexLanguage = "";
        }
        previousMode = mode;

        // if(books!=null){
        if(elementExists("searcherrormsg")) {
            $("#searcherrormsg").hide();
        }
        previousModeValue = modeValue;
        for (var i = 0; i < books.length; i++) {
            if(elementExists("libraryDesc")) {
                document.getElementById("libraryDesc").innerHTML = " ";
            }

            if ("subject" == mode) {

                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].subject == modeValue && booksTags[j].grade==gradeVal) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("language" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && books[i].language == modeValue) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("grade" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";

                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].grade == modeValue) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;

            }

            // all the filtering logic is over. Time to count number of books present
            totalNumberOfBooksPresent++;
            tempBooks.push(books[i]);


        }
        // }
        //     else{
        //         if(elementExists("searcherrormsg")) {
        //             document.getElementById("searcherrormsg").innerText = "No Results Found";
        //             $("#searcherrormsg").show();
        //         }
        //
        //
        //     }


        totalNumberOfPages  =  Math.floor(totalNumberOfBooksPresent/numberOfBooksPerPage);
        if(totalNumberOfBooksPresent%numberOfBooksPerPage>0) totalNumberOfPages++;
        if(myLibraryMode){
            displayBooksPagewise(0,tempBooks.length);

        }
        else{
            if(elementExists("libraryDesc")) {
                if (intest) {
                    document.getElementById("libraryDesc").innerHTML = "Your library now has access to " + totalNumberOfBooksPresent + " titles from etexts store";
                }else{
                    document.getElementById("libraryDesc").innerHTML = " ";
                }
            }

            if(tempBooks.length>numberOfBooksPerPage) {
                $("#bottomPagination").show();
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+numberOfBooksPerPage+"</span> of <span>"+tempBooks.length+"</span> eTexbooks";
                $("#topPaginationMessage").show();

                var pageListStr="";
                var pageNumbersToShow=totalNumberOfPages;
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) pageNumbersToShow=numberOfPageNumbersPerUnit;
                for(var index=0;index<pageNumbersToShow;index++){
                    pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
                }
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) {
                    pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward(1);'>>></a></li>";
                    currentLastPageNumber=numberOfPageNumbersPerUnit;
                }
                document.getElementById("paginationList").innerHTML=pageListStr;
                displayBooksPagewise(0,numberOfBooksPerPage);
            }else{
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+tempBooks.length+"</span> of <span>"+tempBooks.length+"</span> eTextbooks";
                $("#topPaginationMessage").show();

                displayBooksPagewise(0,tempBooks.length);
            }
        }
        if(instituteLibraryCalled){
            <%if(session["userdetails"]!=null){%>
            getUserLibraryBooks();
            <%}%>
        }

    }
    function removeFilter()
    {
        document.getElementById("displayResults").innerHTML="";
        displayBooks("all", "");
    }

    function moveForward(currentIndex){
        var startIndex=(currentIndex)*numberOfPageNumbersPerUnit;
        var endIndex=((currentIndex+1)*numberOfPageNumbersPerUnit);
        var pageListStr="";
        if(endIndex>totalNumberOfPages) endIndex=totalNumberOfPages;
        if(currentIndex>0) pageListStr +="<li class='page-item previous'><a class='page-link' href='javascript:moveBackward("+currentIndex+");'><<</a></li>";
        for(var index=startIndex;index<endIndex;index++){
            pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
        }
        if(totalNumberOfPages>endIndex){
            currentIndex++;
            pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward("+currentIndex+");'>>></a></li>";
        }

        document.getElementById("paginationList").innerHTML=pageListStr;

    }

    function moveBackward(currentIndex){
        var startIndex=((currentIndex-1)*numberOfPageNumbersPerUnit);
        var endIndex=((currentIndex)*numberOfPageNumbersPerUnit);
        currentIndex--;
        var pageListStr="";
        if(currentIndex>0) pageListStr +="<li class='page-item previous'><a class='page-link' href='javascript:moveBackward("+currentIndex+");'><<</a></li>";
        for(var index=startIndex;index<endIndex;index++){
            pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
        }
        if(totalNumberOfPages>endIndex){
            currentIndex++;
            pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward("+currentIndex+");'>>></a></li>";
        }

        document.getElementById("paginationList").innerHTML=pageListStr;

    }
    var bookssearch=[];
    function booksListReceived(data){
        if(data.status!="Nothing present") {
            if (bookssearch.length == 0) bookssearch = JSON.parse(data.books);
        }
        var params = false;
        var mode = '';
        var modeValue = '';
        if(data.status=="Nothing present") {
            window.location.href="/etexts/store";
        }else {
            books = JSON.parse(data.books);
            booksTags = JSON.parse(data.booksTag);
            <%if("language".equals(params.mode) || "subject".equals(params.mode)){%>
            params = true;
            mode = "${params.mode}";
            modeValue = "${params.modeValue}";
            <%}%>
            if(params){
                displayBooks(mode,modeValue);
            }
            else if(myLibraryMode){
                displayMyLibraryBooks(books);
                document.getElementById("badgeDisplay").innerHTML="<p><img src=\"${assetPath(src: 'evidya/Reader_Badge.svg')}\" class=\"img-responsive\" alt=\"\">"+data.instituteName+"</p>";
            }else {
                if (indexGrade)
                    displayBooks("discipline", indexGrade);
                else displayBooks("all", "");
            }
        }
        myVar = setTimeout(getLibraryBooks, 1000);
    }

    function booksListReceived1(data){
        if(data.status!="Nothing present") {
            if (bookssearch.length == 0) bookssearch = JSON.parse(data.books);
        }
        var params = false;
        var mode = '';
        var modeValue = '';
        if(data.status=="Nothing present") {
            window.location.href="/etexts/store";
        }else {
            books = JSON.parse(data.books);
            booksTags = JSON.parse(data.booksTag);
            <%if("language".equals(params.mode) || "subject".equals(params.mode)){%>
            params = true;
            mode = "${params.mode}";
            modeValue = "${params.modeValue}";
            <%}%>
            if(params){
                displayBooksDisp(mode,modeValue);
            }
            else if(myLibraryMode){
                displayMyLibraryBooks(books);
                document.getElementById("badgeDisplay").innerHTML="<p><img src=\"${assetPath(src: 'evidya/Reader_Badge.svg')}\" class=\"img-responsive\" alt=\"\">"+data.instituteName+"</p>";
            }else {
                if (indexGrade)
                    displayBooksDisp("discipline", indexGrade);
                else displayBooks("all", "");
            }
        }

    }
    
    function displayBooksDisp(mode,modeValue,gradeVal="") {
        var searchbook = '';
        if (document.getElementById("search-book") != null) searchbook = document.getElementById("search-book").value;
        if (searchbook == '' && searchMode == true) {
            window.location.href = window.location.href.split('?')[0] + '?mode=' + mode + '&modeValue=' + modeValue;
            if (window.location.href) {
                $('.loading-icon').removeClass('hidden');
            }
        }
        if (!myLibraryMode) {
            $("#bottomPagination").hide();
            $("#topPaginationMessage").hide();
        }
        totalNumberOfBooksPresent = 0;
        tempBooks = [];
        var foundBook = false;
        if (indexSubject) {
            mode = "subject";
            modeValue = indexSubject;
            indexSubject = "";
        } else if (indexGrade) {
            mode = "grade";
            modeValue = indexGrade;
            indexGrade = "";
        } else if (indexLanguage) {
            mode = "language";
            modeValue = indexLanguage;
            indexLanguage = "";
        }
        previousMode = mode;
        // if(books!=null){
        if(elementExists("searcherrormsg")) {
            $("#searcherrormsg").hide();
        }
        previousModeValue = modeValue;
        for (var i = 0; i < books.length; i++) {
            if(elementExists("libraryDesc")) {
                document.getElementById("libraryDesc").innerHTML = " ";
            }
            if ("subject" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].subject == modeValue && booksTags[j].grade == gradeVal && libraryBooksList.indexOf(","+books[i].id+",")>-1) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("language" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && books[i].language == modeValue &&  libraryBooksList.indexOf(","+books[i].id+",")>-1) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("grade" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].grade == modeValue && libraryBooksList.indexOf(","+books[i].id+",")>-1) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            }
            // all the filtering logic is over. Time to count number of books present
            totalNumberOfBooksPresent++;
            tempBooks.push(books[i]);
        }
        for (var i = 0; i < books.length; i++) {
            if(elementExists("libraryDesc")) {
                document.getElementById("libraryDesc").innerHTML = " ";
            }
            if ("subject" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].subject == modeValue && booksTags[j].grade == gradeVal && libraryBooksList.indexOf(","+books[i].id+",")<0) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("language" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && books[i].language == modeValue && libraryBooksList.indexOf(","+books[i].id+",")<0) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            } else if ("grade" == mode) {
                document.getElementById("displayResults").innerHTML = "<p>Showing Results for:</p>\n" +
                    "                   <button class=\"btn btn-default d-flex\" id=\"filterDesc\"> Filtered by " + modeValue + " <i class=\"material-icons\" onclick='removeFilter();'>\n" +
                    "                       close\n" +
                    "                   </i></button>";
                foundBook = false;
                for (j = 0; j < booksTags.length; j++) {
                    if (booksTags[j].bookId == books[i].id && booksTags[j].grade == modeValue && libraryBooksList.indexOf(","+books[i].id+",")<0) {
                        foundBook = true;
                        break;
                    }
                }
                if (!foundBook) continue;
            }
            // all the filtering logic is over. Time to count number of books present
            totalNumberOfBooksPresent++;
            tempBooks.push(books[i]);
        }
        totalNumberOfPages  =  Math.floor(totalNumberOfBooksPresent/numberOfBooksPerPage);
        if(totalNumberOfBooksPresent%numberOfBooksPerPage>0) totalNumberOfPages++;
        if(myLibraryMode){
            displayBooksPagewise(0,tempBooks.length);
        }
        else{
            if(elementExists("libraryDesc")) {
                if (intest) {
                    document.getElementById("libraryDesc").innerHTML = "Your library now has access to " + totalNumberOfBooksPresent + " titles from evidya store";
                }else{
                    document.getElementById("libraryDesc").innerHTML = " ";
                }
            }
            if(tempBooks.length>numberOfBooksPerPage) {
                $("#bottomPagination").show();
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+numberOfBooksPerPage+"</span> of <span>"+tempBooks.length+"</span> items";
                $("#topPaginationMessage").show();
                var pageListStr="";
                var pageNumbersToShow=totalNumberOfPages;
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) pageNumbersToShow=numberOfPageNumbersPerUnit;
                for(var index=0;index<pageNumbersToShow;index++){
                    pageListStr +="<li class=\"page-item \"><a class=\"page-link\" href=\"javascript:goToPageNumber("+index+");\">"+(index+1)+"</a></li>";
                }
                if(totalNumberOfPages>numberOfPageNumbersPerUnit) {
                    pageListStr +="<li class='page-item next'><a class='page-link' href='javascript:moveForward(1);'>>></a></li>";
                    currentLastPageNumber=numberOfPageNumbersPerUnit;
                }
                document.getElementById("paginationList").innerHTML=pageListStr;
                displayBooksPagewise(0,numberOfBooksPerPage);
            }else{
                document.getElementById("topPaginationMessage").innerHTML="<span>1-"+tempBooks.length+"</span> of <span>"+tempBooks.length+"</span> items";
                $("#topPaginationMessage").show();
                displayBooksPagewise(0,tempBooks.length);
            }
        }
        if(instituteLibraryCalled){
            <%if(session["userdetails"]!=null){%>
            getUserLibraryBooks();
            <%}%>
        }
    }
    function sendEmail(data){
        var url = $.param( data );
        <g:remoteFunction controller="creation" action="etextsSuggestbookemail" params="url" onSuccess = "emailSent(data);"/>
    }

    function emailSent(data) {
        $("#emailSentModal").modal("show");
        //alert("This book is suggested to the library!");
    }
</script>
<script>
    var etextUrl = location.href;
    if (etextUrl.indexOf('grade=Humanities%20and%20Social%20Science') >= 0) {
        $('#humanitiestoggle').removeClass('minus');
        $('#managementtoggle').addClass('minus');
        $('#upsctoggle').addClass('minus');
        $('#Management').hide();
        $('#UPSC').hide();

    }
    else if(etextUrl.indexOf('grade=Management') >= 0){
        $('#humanitiestoggle').addClass('minus');
        $('#managementtoggle').removeClass('minus');
        $('#upsctoggle').addClass('minus');
        $('#Humanities').hide();
        $('#UPSC').hide();
    }
    else if(etextUrl.indexOf('grade=UPSC') >= 0){
        $('#humanitiestoggle').addClass('minus');
        $('#managementtoggle').addClass('minus');
        $('#upsctoggle').removeClass('minus');
        $('#Humanities').hide();
        $('#Management').hide();
    }

</script>
