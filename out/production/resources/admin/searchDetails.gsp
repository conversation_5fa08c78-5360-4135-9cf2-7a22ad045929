<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<g:render template="/books/navheader_new"></g:render>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="row justify-content-center p-4" style="min-height: 400px;">
    <div class="col-md-9 shadow rounded p-4 m-4">
        <div class="row justify-content-center">
            <div class="d-flex align-items-center mx-4">
                <div class="form-group col">
                    <label for="startDate">From Date</label><br>
                    <input type="text" class="w-100 form-control" id="startDate" name="startDate" placeholder="From Date" autocomplete="off" >
                </div>
            </div>
            <div class="d-flex align-items-center mx-4">
                <div class="form-group col">
                    <label for="endDate">To Date</label><br>
                    <input type="text" class="w-100 form-control" id="endDate" name="endDate" placeholder="To Date" autocomplete="off" >
                </div>
            </div>
            <div class="d-flex align-items-center mx-4">
                <button class="btn btn-primary mt-3" onclick="downloadData()" >Download</button>
            </div>
        </div>

    </div>
</div>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        //startView: 1,
        //todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
    function downloadData() {
        var StartDate = document.getElementById("startDate").value;
        var EndDate = document.getElementById("endDate").value;
        var inValidStartDate = false;
        if (document.getElementById("startDate").value != "" &&  document.getElementById("endDate").value != ""){
            var  startDate1 = new Date(StartDate.split('-')[2],StartDate.split('-')[1],StartDate.split('-')[0]);
            var  endDate1 = new Date(EndDate.split('-')[2],EndDate.split('-')[1],EndDate.split('-')[0]);
            if(endDate1.getTime() < startDate1.getTime()) inValidStartDate = true;
        }
        if (document.getElementById("startDate").value === "" || document.getElementById("endDate").value === "") {
            alert("Please Select From Date and To Date.")
        } else if (inValidStartDate){
            alert("Please enter valid From Date. From Date cannot be greater then To date")
        }
        else {
            window.location.href = "/admin/searchDetails?download=true&fromDate="+StartDate+"&toDate="+EndDate
        }
    }
</script>
<g:render template="/books/footer_new"></g:render>