<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container adminForm" style="min-height: calc(100vh - 160px);">

    <h2  class="text-center mt-3" >Publisher Folders size</h2>
    <div class="row">
        <table class="table publisher-table table-bordered" id="publisherFiles">
            <table id="publisherFileSize" class="table table-sm table-bordered table-hover chapter-table">
                <thead class="bg-primary text-white text-center">
                <tr>
                    <th>Publisher Id</th>
                    <th>Publisher name</th>
                    <th>File Size</th>
                </tr>
                </thead>
                <tbody id="publisherFileSizeContent">

                </tbody>
            </table>
        </table>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>

    function cartDetails(){
        <%if(publishers!=null && publishers.size()>0){%>
        <g:each in="${publishers}" var="publish" status="i">
            var publisherid =  ${publish.id};
            <g:remoteFunction controller="admin" action="getFolderSizeByPublisherId"  params="'publisherId='+publisherid" onSuccess = "cartDetailsData(data)"/>
        </g:each>
        <%}else if(publishers==[]){%>
        <%}%>
    }

    var trHtml="";
    var listArr=[];
    function cartDetailsData(data){
        listArr.push(data);
            trHtml += "<tr>"+
                    "<td>"+data.publisherId+"</td>" +
                "<td>"+data.publisherName+"</td>" +
                "<td>"+data.folderSize+"</td>" +
                "</tr>";
        document.getElementById("publisherFileSizeContent").innerHTML = trHtml;
    }
    cartDetails();
</script>
</body>
</html>