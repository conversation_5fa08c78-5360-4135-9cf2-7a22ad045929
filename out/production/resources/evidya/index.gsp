<%@ page import  = 'javax.servlet.http.Cookie' %>
<%  String requestURL = request.getRequestURL().toString();
    String servletPath = request.getServletPath();
    String appURL = requestURL.substring (0, requestURL.indexOf(servletPath));
    session.setAttribute("servername", appURL);
    def newCookie = new javax.servlet.http.Cookie( "siteName", "evidya");
    newCookie.path = "/"
    response.addCookie newCookie;
%>

<g:render template="/evidya/navheader_new"></g:render>


<section class="content-Preview">
    %{--<div class="container">--}%
        %{--<h2 class="mt-4">SAGE e-book viewer</h2>--}%
        %{--<div class="img-wrapper">--}%
            %{--<img src="${assetPath(src: 'sage/sageBook.jpg')}" class="img-responsive sage-logo" alt="">--}%
            %{--<div class="text-wrapper">--}%
                %{--<h4>Handbook of Advances Marketing in an era of Disruptions</h4>--}%
            %{--</div>--}%
            %{--<div class="zoomImage">--}%
                %{--<img src="${assetPath(src: 'sage/sageBook.jpg')}" class="img-responsive sage-logo" alt="">--}%
            %{--</div>--}%
        %{--</div>--}%

    %{--</div>--}%

    <div class="container">
        <div class="mt-5">
            <h4>Welcome Students,</h4>
            <h4>Researchers and Professors</h4>
        </div>
        <div class="row">
            <div class="col-4">
               <div class="imgWrapper">
                   <img src="${assetPath(src: 'sage/Tablet.png')}" class="img-responsive sage-logo" alt="">
                   <div class="content-wrappers">
                       <h2>Empower your research and learning with SAGE eBooks</h2>
                       <a href="/evidya/store?grade=Business&homepage=filter"><img src="${assetPath(src: 'sage/Management_Icon.svg')}" class="img-responsive" alt=""><span>Management</span></a>
                       <a href="/evidya/store?grade=Social Science&homepage=filter">  <img src="${assetPath(src: 'sage/Social_Sciences_Icon.svg')}" class="img-responsive" alt=""><span>Social Science</span></a>
                       <a href="/evidya/store?grade=Non-Fiction&homepage=filter">  <img src="${assetPath(src: 'sage/Non-fiction.svg')}" class="img-responsive" alt=""><span>Non- Fiction</span></a>
                       <a href="/evidya/store?grade=UPSC Resources&homepage=filter">  <img src="${assetPath(src: 'sage/UPSC.svg')}" class="img-responsive" alt=""><span>UP<small style="font-size: 100%;font-weight: bold;position: relative;left: -0.75px;">SC</small> Resources</span></a>
                       <a href="/evidya/store?grade=SAGE Spectrum&homepage=filter">  <img src="${assetPath(src: 'sage/spectrum.svg')}" class="img-responsive" alt=""><span>SAGE Spectrum</span></a>
                   </div>
               </div>
            </div>
            <div class="col-2 mt-3 ml-5">
                  <div class="circle mt-5">
                     <img src="${assetPath(src: 'sage/Explore_Resources_Icon.svg')}" class="img-responsive" alt="">
                      <a href="/evidya/store">Explore Resources</a>
                  </div>
                <div class="circle mt-4">
                    <img src="${assetPath(src: 'sage/Free_Trial_Icon.svg')}" class="img-responsive" alt="">
                    <a href="javascript:SendLinkByMail();">Free Trial</a>
                </div>
                <div class="circle mt-4">
                    <img src="${assetPath(src: 'sage/Login_Icon.svg')}" class="img-responsive" alt="">
                    <a data-toggle="modal" data-target="#loginBenifits">Login <br>Benefits</a>
                </div>
            </div>
            <div class="col-5 d-flex align-items-center justify-content-end">
                <div class="text-center">
                    <p>Thousands of hand-picked titles by world-class authors on relevant topics across Social Sciences and Management.</p>
                    <div class="searchbar">
                        <input type="text" placeholder="What would you like to read today?" class="searchSage typeahead" autocomplete="off" id="search-book">
                        <button> <img src="${assetPath(src: 'sage/Search_Icon.svg')}" class="img-responsive" alt=""></button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<section>

    <div class="modal fade" id="loginBenifits">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <h3>You must login to use Notes.</h3>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                   <h2>Why should you register with us?</h2>
                   <p>Registering with us:</p>
                    <ul>
                        <li>Seamless browsing experience</li>
                        <li>Personalises your own dashboard.</li>
                        <li>Automatically resume reading from your last read page.</li>
                        <li>Know what others in your institute are reading.</li>
                        <li>Add notes and annotations.</li>
                        <li>Fonts and viewing for better reading experience.</li>
                    </ul>
                <button type="button" class="ml-4 mt-4 btn btn-login cmn-login" onclick="evidyaloginOpen();">Login</button>
                <button type="button" class="mt-4 ml-4 btn btn-close" data-dismiss="modal">Close</button>
                </div>

                <!-- Modal footer -->
                <div class="modal-footer">

                </div>

            </div>
        </div>
    </div>
</section>

<g:render template="/evidya/footer_new"></g:render>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>

<script>
var updatesignup=false;
var updateUserId=0;
    jQuery(document).ready(function ($) {
        if("${session["userdetails"]}"==null || "${session["userdetails"]}"=='' ) {

            if ("${userEmail}" != "" && "${sageLogin}"!="true") {
                var updateEmail = htmlDecode("${userEmail}")
                $('#username').val(updateEmail).attr('readonly', true);


                $('.evidyaLogin').show();

                $('.evidyaloginWrapper').hide();
                $('.evidyaSignup').show();
                updatesignup = true;
                updateUserId = "${userId}"
            }
            else if("${userEmail}" != "" && "${sageLogin}"=="true"){
                $(window).load(function () {
                    $("#signUpLimitModal").modal("show");
                });
            }
        }

    });
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            console.log("query="+query+" and process="+process);
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks1(data) {
                    console.log("data="+data.status);
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString = document.getElementById("search-book").value;
        window.location.href = "/evidya/store?search=true&searchString="+searchString;

    }


    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();
            }
        }
    });

</script>

<script>
    function SendLinkByMail() {
        var subject= "Free Trial | eVidya";
        var body = "";
        body += "";
        var uri = "mailto:<EMAIL>?subject=";
        uri += encodeURIComponent(subject);
        uri += "&body=";
        uri += encodeURIComponent(body);
        window.open(uri);
    }
    if('${params.fplink}'==='expired'){
        $('.evidyaLogin').show();
        alert("The reset password link is expired, Please try again by clicking on forgot password.")
    }
</script>