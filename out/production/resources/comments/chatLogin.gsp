
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<body>
<% if("welcome".equals(session["entryController"])){%>
<section style="height: calc(100vh - 150px);">
    <%}else{%>
    <section style="height: calc(100vh - 389px);">

   <%}%>
<div class="container">
    <div class="row text-center align-items-center">
<form class="form-inline mt-4" onkeypress="onKeyPressFunction(event)">

    <label for="resId" class="mb-2 mr-sm-2">ResID</label>
    <input type="number" class="form-control mb-2 mr-sm-2" id="resId" placeholder="Enter ResID" name="resId" style="margin-top: 0;">

    <button type="button" class="btn btn-primary mb-2" onclick="store();">Submit</button>
</form>


</div>
</div>
</section>
<script>
    var page = "${params.page}";
    function store(){
        var resVal= document.getElementById("resId");
        var url
        if(page=="livechat"){

            var url = "/comments/livechat?resId="+resVal.value;
            }else{

            var url = "/comments/livechatPolling?resId="+resVal.value;
        }
       window.location.href=url
    }

    function onKeyPressFunction(evt) {
        var keyID = (evt.charCode) ? evt.charCode : ((evt.which) ? evt.which : evt.keyCode);
        if(keyID == 13){
            evt.preventDefault();
            var resVal= document.getElementById("resId");
            if(page=="livechat"){
                var url = "/comments/livechat?resId="+resVal.value;
            }else{
                var url = "/comments/livechatPolling?resId="+resVal.value;
            }
            window.location.href=url
        }
    }
</script>
</body>
<g:render template="/${session['entryController']}/footer_new"></g:render>
