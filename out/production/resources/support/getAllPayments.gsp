<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<style>
.custom_container_new{
    width: calc(100% - 10%);
    margin: 0 auto;
}
.priceListTable tr th,
.priceListTable tr td{
    padding: 10px;
}
.priceListTable tr td > *{
    width: 100%;
}
.add-btn,
.delete-btn{
    display: block;
    text-align: center;
    padding: 5px 5px;
    border-radius: 5px;
    color: rgba(0,0,0,0.6);
}
.priceCusIcons{
    width: 25px;
}
.add-btn{
    border-color: rgba(0,0,0,0.5);
}
.delete-btn{
    color: red;
}
.priceListTable tr td select,
.priceListTable tr td input{
    width: 135px;
    padding: 4px;
    border-radius: 5px;
    border: 1px solid rgba(0,0,0,0.5);
    text-align: center;
    font-size: 14px;
}
.priceListTable tr td select:focus-visible,
.priceListTable tr td input:focus-visible{
    outline: 1px solid rgba(0,0,0,0.5);
}
.priceListForm{
    overflow-x: scroll;
}
.priceListTable tbody{
    text-align: center;
}
.priceListTable tbody tr:hover {
    background-color: transparent;
}

@media (max-width: 768px){

}
.form-control {
    width: 50%;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
.Failed{
    color: #dc0202;
}
.Captured{
    color: green;
}
.Refunded{
    color: dodgerblue;
}
.Authorized{
    color: blue;
}
</style>
<div class="custom_container_new">
<h3>Payment Finder</h3>

    <form id="searchForm">
        <div class="form-group">
            <label for="id">ID:</label>
            <input type="text" class="form-control input-sm" name="id" id="id">
        </div>

        <div class="form-group">
            <label for="email">Email:</label>
            <input type="text" class="form-control" name="email" id="email">
        </div>

        <div class="form-group">
            <label for="mobile">Mobile:</label>
            <input type="text" class="form-control" name="mobile" id="mobile">
        </div>


        <div class="form-group">
            <label for="fromDate">From Date:</label>
            <input type="text" class="form-control" name="fromDate" id="fromDate">
        </div>

        <div class="form-group">
            <label for="toDate">To Date:</label>
            <input type="text" class="form-control" name="toDate" id="toDate">
        </div>

        <input type="hidden" name="mode" value="submit">
        <input type="submit" class="btn btn-primary" value="Search">
    </form>



    <div id="results">

    </div>
<div id="paymentTable">

</div>

</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>

    function getPaymentDetails(razorPayId){
        document.getElementById("results").innerHTML="";
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="support" action="getPaymentDetails" params="'razorPayId='+razorPayId" onSuccess="paymentDetails(data)"></g:remoteFunction>

    }
     function paymentDetails(data){
         $('.loading-icon').addClass('hidden');
         document.getElementById("results").innerHTML=data.details;
     }
    function fixPayment(razorPayId){
        document.getElementById("results").innerHTML="";
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="selfService" params="'razorPayId='+razorPayId" onSuccess="fixedResults(data)"></g:remoteFunction>
    }

    function fixedResults(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById("results").innerHTML=data.status;
    }
    $(document).ready(function() {
        $("#searchForm").submit(function(event) {
            event.preventDefault(); // Prevent form submission
            document.getElementById("results").innerHTML="";
            var formData = $(this).serialize(); // Serialize form data
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: "${createLink(controller: 'support', action: 'getAllPayments')}",
                type: "post",
                data: formData,
                success: function(data) {
                    // Update table with new results
                    paymentsDataReceived(data);
                },
                error: function() {
                    alert("Failed to retrieve payment data.");
                }
            });
        });
    });

    function paymentsDataReceived(data){
        $('.loading-icon').addClass('hidden');
        var payments = data.payments;
      var htmlStr ="<table class='w-100 priceListTable table table-sm table-bordered table-hover dataTable'>\n" +
          "        <tr>\n" +
          "            <th>ID</th>\n" +
          "            <th>Amount</th>\n" +
          "            <th>Email</th>\n" +
          "            <th>Mobile</th>\n" +
          "            <th>Login Id</th>\n" +
          "            <th>Status</th>\n" +
          "            <th></th>\n" +
          "            <th></th>\n" +
          "            <!-- Add more columns as needed -->\n" +
          "        </tr>";
        for(var i=0;i<payments.length;i++){
            htmlStr+= "        <tr>\n" +
                "            <td>"+payments[i].razorPaymentId+"</td>\n" +
                "            <td>"+payments[i].amount+"</td>\n" +
                "            <td>"+payments[i].email+"</td>\n" +
                "            <td>"+payments[i].contact+"</td>\n" +
                "            <td>"+payments[i].username+"</td>\n" +
                "            <td class='"+payments[i].status+"'>"+payments[i].status+"</td>\n" +
                "            <td>"+payments[i].username+"</td>\n" +
                "            <td><a href='javascript:getPaymentDetails(\""+payments[i].razorPaymentId+"\");' class='Authorized'>Get Details</a></td>\n" +
                "            <td><a href='javascript:fixPayment(\""+payments[i].razorPaymentId+"\");' class='Authorized'>Fix</a></td>\n" +

                "            <!-- Add more columns as needed -->\n" +
                "        </tr>";
        }
        htmlStr +="</table>"
        if(payments.length>0){
            document.getElementById("paymentTable").innerHTML=htmlStr;
        }else{
            document.getElementById("paymentTable").innerHTML="No records found.";
        }

    }

    $('#fromDate, #toDate').datepicker({
        format: 'dd/mm/yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
</script>

