<%@ page contentType="text/html"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html>
<head>
    <title></title>
    <meta content="text/html; charset=utf-8" http-equiv="Content-Type"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <style>
    * {
        box-sizing: border-box;
    }

    body {
        margin: 0;
        padding: 0;
    }

    a[x-apple-data-detectors] {
        color: inherit !important;
        text-decoration: inherit !important;
    }

    #MessageViewBody a {
        color: inherit;
        text-decoration: none;
    }

    p {
        line-height: inherit
    }

    .desktop_hide,
    .desktop_hide table {
        mso-hide: all;
        display: none;
        max-height: 0px;
        overflow: hidden;
    }

    .menu_block.desktop_hide .menu-links span {
        mso-hide: all;
    }
    .bookImg-Wrapper img{
        height: 125px !important;
        object-fit: contain;
        width: 230px !important;
    }
    .books-list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
    }

    .book-title p {
        display: inline-block;
        width: 120px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    @media (max-width:700px) {
        .desktop_hide table.icons-inner {
            display: inline-block !important;
        }
        .books-list {
            display: block !important;
        }
        .books-list .column {
            width: 100% !important;
        }
        .book-title p {
            width: 100% !important;
        }

        .icons-inner {
            text-align: center;
        }

        .icons-inner td {
            margin: 0 auto;
        }

        .image_block img.big,
        .row-content {
            width: 100% !important;
        }

        .mobile_hide {
            display: none;
        }

        .stack .column {
            width: 100%;
            display: block;
        }

        .mobile_hide {
            min-height: 0;
            max-height: 0;
            max-width: 0;
            overflow: hidden;
            font-size: 0px;
        }

        .desktop_hide,
        .desktop_hide table {
            display: table !important;
            max-height: none !important;
        }

        .reverse {
            display: table;
            width: 100%;
        }

        .reverse .column.first {
            display: table-footer-group !important;
        }

        .reverse .column.last {
            display: table-header-group !important;
        }

        .row-10 td.column.first>table,
        .row-10 td.column.last>table {
            padding-left: 0;
            padding-right: 0;
        }

        .row-10 td.column.first .border,
        .row-10 td.column.last .border {
            border-top: 0;
            border-right: 0px;
            border-bottom: 0;
            border-left: 0;
        }
        .youtube-link, .youtube-link p {
            text-align: left !important;
        }
    }
    </style>
</head>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
%>
<body style="background-color: #e3ded2; margin: 0; padding: 15px; -webkit-text-size-adjust: none; text-size-adjust: none;">
<table border="0" cellpadding="0" cellspacing="0" class="nl-container" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #e3ded2;" width="100%">
    <tbody>
    <tr>
        <td>

            <!----  header  --->
            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row row-1" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                <tbody>
                <tr>
                    <td>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000; width: 680px;" width="680">
                            <tbody>
                            <tr>
                                <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-left: 10px; padding-right: 10px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="16.666666666666668%">
                                    <table border="0" cellpadding="0" cellspacing="0" class="image_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                                        <tr>
                                            <td style="width:100%;padding-right:0px;padding-left:0px;padding-top:10px;padding-bottom: 10px">
                                                <div align="center" style="line-height:10px;height: 55px"><img alt="${siteName}" src="${serverURL}/assets/siteImages/${siteName}.png" style="display: block; height: 100%; border: 0;width: 200px;object-fit: contain" title="${siteName}" /></div>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>

            <!----- showcase content ----->
            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row row-6" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                <tbody>
                <tr>
                    <td>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; color: #000000; width: 680px;" width="680">
                            <tbody>
                            <tr>
                                <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; background-color: #fcf8f0; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="60%">
                                    <table border="0" cellpadding="0" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                                        <tr>
                                            <td style="padding-left:20px;padding-top:30px;">
                                                <div style="font-family: sans-serif">
                                                    <div class="txtTinyMce-wrapper" style="font-size: 14px; mso-line-height-alt: 21px; color: #393d47; line-height: 1.5; font-family: Helvetica Neue, Helvetica, Arial, sans-serif;">
                                                        <p style="margin: 0; font-size: 14px; mso-line-height-alt: 45px;"><span style="font-size:30px;"><strong>Save <span style="color:#f2994a;">10%</span> on your next Book Purchase!</strong></span></p>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                    <table border="0" cellpadding="0" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                                        <tr>
                                            <td style="padding-bottom:20px;padding-left:20px;padding-right:20px;padding-top:10px;">
                                                <div style="font-family: sans-serif">
                                                    <div class="txtTinyMce-wrapper" style="font-size: 14px; mso-line-height-alt: 21px; color: #393d47; line-height: 1.5; font-family: Helvetica Neue, Helvetica, Arial, sans-serif;">
                                                        <p style="margin: 0; font-size: 14px; text-align: left;">We hope you are happy with your last purchase.</p>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                                <td class="column column-2" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; background-color: #fcf8f0; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="40%">
                                    <table border="0" cellpadding="0" cellspacing="0" class="image_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                                        <tr>
                                            <td style="width:100%;padding-right:0px;padding-left:0px;padding-top:20px;">
                                                <div align="center" style="line-height:10px"><img alt="Wonderslate cart" class="big" src="${serverURL}/assets/automaticEmailImages/showcase-2.png" style="display: block; height: auto; border: 0; width: 359px; max-width: 100%;" title="wonderslate books" width="359"/></div>
                                            </td>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>

            <!--- UI divider (white)  --->
            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000; width: 680px;" width="680">
                <tbody>
                <tr>
                    <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="100%">
                        <div class="spacer_block" style="height:40px;line-height:40px;font-size:1px;"> </div>
                    </td>
                </tr>
                </tbody>
            </table>

            <div class="row-content books-list" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fcf8f0; color: #000000; width: 680px;margin: 0 auto;" width="680">

                <% if(relatedBooks!=null){
                    for(int i=0;i<relatedBooks.size();i++){
                %>

                <%
                        String bookTitle=""
                        Long bookId = 0
                        String publisher=""
                        bookTitle = relatedBooks[i].title
                        bookId = relatedBooks[i].bookId
                        publisher = relatedBooks[i].publisher

                %>
                <!---- book 1  ---->
                <div class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;width: 20%;" width="20%">
                    <table border="0" cellpadding="0" cellspacing="0" class="image_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                        <tr>
                            <td style="width:100%;padding-right:0px;padding-left:0px;padding-top:5px;">
                                <div align="center" style="line-height:10px;margin: 20px" class="bookImg-Wrapper"><img  src="${serverURL}/funlearn/showProfileImage?id=${relatedBooks[i].bookId}&fileName=${relatedBooks[i].coverImage}&type=books&imgType=webp" style="display: block; height: auto; border: 0; width: 226px; max-width: 100%;" title="${relatedBooks[i].bookTitle}" width="226"/></div>
                            </td>
                        </tr>
                    </table>
                    <table border="0" cellpadding="0" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                        <tr>
                            <td style="padding-bottom:5px;padding-left:10px;padding-right:10px;">
                                <div style="font-family: sans-serif">
                                    <div class="txtTinyMce-wrapper book-title" style="font-size: 14px; mso-line-height-alt: 16.8px; color: #393d47; line-height: 1.2; font-family: Helvetica Neue, Helvetica, Arial, sans-serif;">
                                        <p style="margin: 0; font-size: 14px; text-align: center;"><span style="font-size:14px;"><strong>${relatedBooks[i].bookTitle}</strong></span></p>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>

                    <table border="0" cellpadding="0" cellspacing="0" class="button_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                        <tr>
                            <td style="padding-left:20px;padding-right:20px;text-align:left;padding-bottom:40px;">
                                <div style="color:#ffffff; text-align: center;font-family:Arial, sans-serif; font-size:14px">
                                    <a href="${webUrl}/${relatedBooks[i].urlTitle}/ebook-details?siteName=${siteName}&bookId=${relatedBooks[i].bookId}&publisher=${relatedBooks[i].publisher}&preview=true&emailCampaign=true&username=${encodedUsername}" style="text-decoration:none;display:inline-block;color:#ffffff;background-color:#f2994a;border-radius:4px;width:auto;border-top:0px solid #8a3b8f;font-weight:400;border-right:0px solid #8a3b8f;border-bottom:0px solid #8a3b8f;border-left:0px solid #8a3b8f;padding-top:5px;padding-bottom:5px;font-family:Helvetica Neue, Helvetica, Arial, sans-serif;text-align:center;mso-border-alt:none;word-break:keep-all;" target="_blank"><span style="padding-left:15px;padding-right:15px;font-size:14px;display:inline-block;letter-spacing:normal;"><span style="font-size: 12px; line-height: 2; word-break: break-word; mso-line-height-alt: 24px;"><strong><span style="font-size: 14px; line-height: 24px;">Buy Now</span></strong></span></span></a>
                                </div>
                            </td>
                        </tr>
                    </table>
                </div>
                <%}%>
                <!--end of for loop -->
                <%}else{%>

                <%}%>
            </div>

            <%
                String youtubeURL = ""
                String appPlaystoreLink = ""
                String appstoreLink = ""
                if(siteName=="books") {
                    youtubeURL = "https://www.youtube.com/channel/UCMJv3HwAgBCwqWsZ63wIWwA"
                    appPlaystoreLink = "https://play.google.com/store/search?q=wonderslate&c=apps&hl=en-GB"
                    appstoreLink = "https://apps.apple.com/us/app/wonderslate/id1438381878"
                }
                if(siteName == "arihant"){
                    youtubeURL = "https://www.youtube.com/c/ArihantPublicationIndiaLimited"
                    appPlaystoreLink = "https://play.google.com/store/apps/details?id=com.arihant.publication&hl=en-GB"
                }
                if (siteName == "oswaal"){
                    youtubeURL = "https://www.youtube.com/c/oswaalbooks-learningmadesimple"
                    appPlaystoreLink = "https://play.google.com/store/apps/details?id=com.oswaal.publication&hl=en-GB"
                }
                if (siteName == "radianbooks"){
                    youtubeURL == "https://www.youtube.com/channel/UCiPyhMrmCrodkODIJr6D17w"
                    appPlaystoreLink = "https://play.google.com/store/apps/details?id=com.radian.publication&hl=en-GB"
                }
                if (siteName == "oswalpublisher"){
                    youtubeURL == "https://www.youtube.com/channel/UCuNkDaAVD4MuagCXgEAy-1A"
                    appPlaystoreLink = "#"
                }
            %>


            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; color: #000000; width: 680px;" width="680">
                <tbody>
                <tr>
                    <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; background-color: #FFF; padding-bottom: 5px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="100%">
                        <table border="0" cellpadding="0" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                            <tr>
                                <td style="padding-bottom:20px;padding-left:30px;padding-right:30px;padding-top:30px;">
                                    <div style="font-family: sans-serif">
                                        <div class="txtTinyMce-wrapper" style="font-size: 14px; mso-line-height-alt: 21px; color: #333; line-height: 1.5; font-family: Helvetica Neue, Helvetica, Arial, sans-serif;">
                                            <p style="margin: 0; font-size: 14px; text-align: center;">Thank you for shopping with us. For more great content, explore our <a href="${webUrl}/${siteName}/store?emailCampaign=true" style="text-decoration: none;color:#f2994a;font-weight: 600" target="_blank">Books Store.</a></span> Feel free to reply with any query or to request our assistance, we will be happy to help you. </p>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td style="padding-bottom:10px;padding-left:10px;padding-right:10px;text-align:center;background-color: #FFF;">
                        <div align="center">
                            <div class="txtTinyMce-wrapper" style="font-size: 12px; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; mso-line-height-alt: 14.399999999999999px; color: #000; line-height: 1.2;">
                                <p style="margin: 0; font-size: 18px; text-align: center;"><span style="color:#000000;"><strong><span style="">Help us improve</span></strong></span></p>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="padding-bottom:10px;padding-left:10px;padding-right:10px;text-align:center;background-color: #FFF;">
                        <div align="center">
                            <center style="color:#ffffff; font-family:Arial, sans-serif; font-size:16px"><a href="${appPlaystoreLink}" style="text-decoration:none;display:inline-block;color:#f2994a;background-color:#fff;border-radius:2px;width:auto;border:2px solid #f2994a;padding-top:03px;padding-bottom:03px;font-family:Helvetica Neue, Helvetica, Arial, sans-serif;text-align:center;mso-border-alt:none;word-break:keep-all;border-radius: 5px;" target="_blank"><span style="padding-left:15px;padding-right:15px;font-size:16px;display:inline-block;letter-spacing:normal;"><span style="font-size: 12px; line-height: 2; word-break: break-word; mso-line-height-alt: 24px;"><span data-mce-style="font-size: 16px; line-height: 32px;" style="font-size: 16px; line-height: 32px;"><strong><span data-mce-style="line-height: 32px;" style="line-height: 32px;">Rate your experience</span></strong></span></span></span></a>
                            </center>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>

            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #ffffff; color: #000000; width: 680px;" width="680">
                <tbody>
                <tr>
                    <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="100%">
                        <div class="spacer_block" style="height:40px;line-height:40px;font-size:1px;"> </div>
                    </td>
                </tr>
                </tbody>
            </table>

            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbf4e7; color: #000000; width: 680px;" width="680">
                <tbody>
                <tr>
                    <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-left: 10px; padding-right: 10px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="50%">
                        <table border="0" cellpadding="0" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                            <tr>
                                <td style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px;">
                                    <div style="font-family: sans-serif">
                                        <div class="txtTinyMce-wrapper" style="font-size: 12px; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; mso-line-height-alt: 14.399999999999999px; color: #000; line-height: 1.2;">
                                            <p style="margin: 0; font-size: 18px; text-align: left;"><span style="color:#000000;"><strong><span style="">For the best experience install the app now!</span></strong></span></p>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                        <table border="0" cellpadding="0" cellspacing="0" class="social_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                            <tr>
                                <td style="padding-left:10px;padding-right:10px;text-align:left;padding-bottom:5px;">
                                    <table align="left" border="0" cellpadding="0" cellspacing="0" class="social-table" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;width: 100%" >
                                        <tr>
                                            <td>
                                                <div style="font-family: sans-serif">
                                                    <a href="${appPlaystoreLink}" target="_blank">
                                                        <img src="${serverURL}/assets/automaticEmailImages/playstore.png" alt="wonderslate andriod" width="120px"/>
                                                    </a>
                                                </div>
                                            </td>
                                            <%if(appstoreLink=="") {%>

                                            <%} else {%>
                                            <td>
                                                <div style="font-family: sans-serif">
                                                    <a href="${appstoreLink}" target="_blank">
                                                        <img src="${serverURL}/assets/automaticEmailImages/appstore.png" alt="wonderslate andriod" width="120px"/>
                                                    </a>
                                                </a>
                                                </div>
                                            </td>
                                            <%}%>
                                        </tr>
                                    </table>
                                </td>
                            </tr>
                        </table>
                    </td>
                    <td class="column column-2 youtube-link" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: center; vertical-align: top; padding-left: 10px; padding-right: 10px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="50%">
                        <table border="0" cellpadding="0" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                            <tr>
                                <td style="padding-bottom:10px;padding-left:10px;padding-right:10px;padding-top:15px;">
                                    <div style="font-family: sans-serif">
                                        <div class="txtTinyMce-wrapper" style="font-size: 12px; font-family: Helvetica Neue, Helvetica, Arial, sans-serif; mso-line-height-alt: 14.399999999999999px; color: #000; line-height: 1.2;">
                                            <p style="margin: 0; font-size: 18px; text-align: center;"><strong><span style="">For the best learning videos</span></strong></p>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                        <table border="0" cellpadding="10" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                            <tr>
                                <td>
                                    <div style="font-family: sans-serif">
                                        <a href="${youtubeURL}" target="_blank"><img src="${serverURL}/assets/eduwonder/youtube.png" style="width: 100px;"/></a>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>

            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row row-16" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt;" width="100%">
                <tbody>
                <tr>
                    <td>
                        <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; background-color: #fbf4e7; color: #000000; width: 680px;" width="680">
                            <tbody>
                            <tr>
                                <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; padding-top: 0px; padding-bottom: 0px; border-top: 0px; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="100%">
                                    <div class="spacer_block" style="height:40px;line-height:40px;font-size:1px;"> </div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>

            <!--- WS address --->
            <table align="center" border="0" cellpadding="0" cellspacing="0" class="row-content stack" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; color: #000000; width: 680px;" width="680">
                <tbody>
                <tr>
                    <td class="column column-1" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; font-weight: 400; text-align: left; vertical-align: top; background-color: #fff4de; padding-bottom: 5px; border-top: 1px solid #DDD; border-right: 0px; border-bottom: 0px; border-left: 0px;" width="100%">
                        <table border="0" cellpadding="0" cellspacing="0" class="text_block" role="presentation" style="mso-table-lspace: 0pt; mso-table-rspace: 0pt; word-break: break-word;" width="100%">
                            <tr>
                                <td style="padding-bottom:20px;padding-left:30px;padding-right:30px;padding-top:10px;">
                                    <div style="font-family: sans-serif">
                                        <div class="txtTinyMce-wrapper" style="font-size: 11px; mso-line-height-alt: 21px; color: #333; line-height: 1.5; font-family: Helvetica Neue, Helvetica, Arial, sans-serif;">
                                            <%if(siteName.equals("arihant")) {%>
                                            <p style="margin: 0; font-size: 11px; text-align: center;">This email has been sent to you by Arihant Publications India Limited.
                                            Ramchhaya Building, 15, Daryaganj, New Delhi - 110002.</p>
                                            <%} else if(siteName.equals("oswaal")) {%>
                                            <p style="margin: 0; font-size: 11px; text-align: center;">This email has been sent to you by Oswaal Books and Learning Private Limited.
                                            37, sahitya kunj, M.G road, Agra, Uttar Pradesh- 282002.</p>
                                            <%} else if(siteName.equals("radianbooks")) {%>
                                            <p style="margin: 0; font-size: 11px; text-align: center;">This email has been sent to you by Radian Book Company.
                                            37, Kailash Enclave, Pitampura, Delhi - 110034.</p>
                                            <%} else if(siteName.equals("oswalpublisher")) {%>
                                            <p style="margin: 0; font-size: 11px; text-align: center;">This email has been sent to you by Oswal Printers and Publishers Pvt Ltd.
                                            Ground Floor, 1 / 12 Sahitya Kunj, M.G Road, Agra - 282002, Uttar Pradesh (India).</p>
                                            <%} else {%>
                                            <p style="margin: 0; font-size: 11px; text-align: center;">This email has been sent to you by Wonderslate Technologies Pvt Ltd.
                                            No 401 Fourth Floor, Sapthagiri Apartments, No. 30, 10th Cross, 15th Main Rd, Raj Mahal Vilas Extension, Sadashivanagar, Bengaluru, 560080</p>
                                            <%}%>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                </tbody>
            </table>
        </td>
    </tr>
    </tbody>
</table>
</body>
</html>