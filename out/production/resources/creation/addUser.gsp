<g:render template="/funlearn/mainheader"></g:render>


<div class="section topsection" >
        <div class="container">
            <div class="row">
                <div class="col-md-12">
                    <h4>User Registration</h4>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12">
                    <form class="form-horizontal" role="form" name="adduser" action="addUser" method="post">
                        <div class="form-group">
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="username" name="username" placeholder="username">
                            </div>
                            <div class="col-sm-4"><span class="smallerText">** can use your email id also as username</span></div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <input type="password" class="form-control" id="password" name="password" placeholder="Password" value="">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="name" name="name" placeholder="Name">
                            </div>
                        </div>
                        <div class="form-group">
                         <div class="col-sm-4">
                                <input type="email" class="form-control" id="email"  name="email" placeholder="Email">
                            </div>
                        </div>
                        <div class="form-group">
                           <div class="col-sm-4">
                                <input type="text" class="form-control" id="qualification" name="qualification" placeholder="Qualification">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="profession" name="profession" placeholder="Profession">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="workplace" name="workplace" placeholder="School or Company">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="city" name="city" placeholder="City">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <input type="text" class="form-control" id="country" name="country" placeholder="Country" value="India">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <textarea  rows="4" class="form-control" id="summary" name="summary" placeholder="summary"></textarea>
                            </div>
                        </div>




                        <div class="alert alert-warning col-sm-4" style="display: none">
                            Please complete required fields marked in red.
                        </div>
                        <div class="form-group">
                            <div class="col-sm-offset-2 col-sm-10">
                                <button type="button" onclick="javascript:formSubmit()" class="btn btn-primary">Add</button>
                            </div>
                        </div>
                        <input type="hidden" name="mode" value="submit">
                    </form>
                </div>
            </div>
        </div>

</div>

<g:render template="/funlearn/footer"></g:render>
<asset:javascript src="jquery-1.11.2.min.js"/>
<script>
    var flds = new Array (
                    'username',
                    'name',
                     'password',
                    'email',
            'qualification',
            'profession',
            'workplace',
            'city',
            'country',
            'summary'
            );
    function formSubmit() {
        if (validate()) {

        document.adduser.submit();
    }
    }

    function validate(){
        var allFilled=true
        $('.alert').hide();
        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).addClass('has-error');
                $("#"+flds[i]).closest('.form-group').addClass('has-error');
                allFilled = false;
            }
            else{
                $("#"+flds[i]).removeClass('has-error');
                $("#"+flds[i]).closest('.form-group').removeClass('has-error');
            }

        }
        if(!allFilled){
            $('.alert').show();

        }
        return allFilled;

    }



</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>
</body>