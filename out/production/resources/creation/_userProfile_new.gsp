<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>
<div class="user-profile-orders container">
    <ul class="nav nav-tabs user-profile-tabs" role="tablist" id="profile-tabs">
        <li role="presentation" class="active">
            <a href="#profile" aria-controls="profile" role="tab" data-toggle="tab">Profile</a>
        </li>

        <li role="presentation">
            <a href="#orders" aria-controls="orders" role="tab" data-toggle="tab">Your Orders</a>
        </li>
    </ul>
    <div class="tab-content user-profile-orders-tab-content">
        <div role="tabpanel" class="tab-pane user-profile-tab fade in active" id="profile">
            <div class="user-profile-image">
                <form enctype="multipart/form-data" role="form" name="uploadProfileImage" id="uploadProfileImage" action="/creation/uploadProfileImage" method="post">
                    <input type="hidden" name="type" value="user">
                    <input type="hidden" name="source" value="userProfile">
                    <input type="hidden" name="sourceController" value="creation">
                    <%if(session['userdetails'].profilepic!=null){%>
                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="img-responsive">
                    <%} else { %> <img src="${assetPath(src: 'wonderslate/user-icon.png')}" class="img-responsive" alt=""><%}%>
                    <input id="fileoption1" class="hidden" name="file" type="file"  accept="image/png, image/jpeg, image/gif" onchange="updateProfile();"/>

                    <a href="javascript:updateImage();" class="upload-user-image">
                        <i class="fa fa-pencil"></i>
                    </a>
                </form>
            </div>
        <div class="user-profile-details">
            <g:form name="userProfile" url="[action:'updateUserProfile',controller:'creation']" method="post">
                <input type="text" class="user-profile-input" name='name' id="name" value="<%= session["userdetails"]!=null?session["userdetails"].name:"" %>">
                <%if((session['userdetails'].email==null) || (session['userdetails'].email=="<EMAIL>") || (session['userdetails'].email=="")){%>
                <input type="email" name="email" id="email" class="user-profile-input" placeholder="Please update you email">
                <%} else { %>
                <input type="email" name="email" id="email" class="user-profile-input" value="<%= session["userdetails"]!=null?session["userdetails"].email:""%>" disabled="disabled">
                <% } %>

                <div class="user-profile-password" style="display: <%=session['userdetails'].username.startsWith("Google")?"none":"block"%>;">
                    <input type="password" id="user-password" class="user-profile-input" value="••••••••••••" name="password" readonly disabled="disabled" style="border-top-left-radius: 0; border-top-right-radius: 0;">
                    <a href="#change-password-modal" data-toggle="modal" id="change-user-password" class="change-password-profile">Change password</a>
                </div>
                <input type="tel" name="mobile" id="mobile" class="user-profile-input" value="<%= session["userdetails"]!=null?session["userdetails"].mobile:""%>">
                </div>
                <div class="update-profile-area">
                    <button onclick="javascript:formSubmit();" class="btn update-profile-btn waves-effect pull-right">Update</button>
                </div>
                <input type="hidden" name="mode" value="update">
            </g:form>
        </div>
        <div role="tabpanel" class="tab-pane user-order-tab fade" id="orders">
            <div class="users-orders" id="orders-wrapper">
                <p class="purchase-date">13th October 2017</p>
                <div class="users-orders-details-wrapper"></div>
            </div>
        </div>
    </div>
</div>
<g:render template="/creation/changePasswordModal"></g:render>
<g:render template="/wonderpublish/bookReviewModal"></g:render>
<g:render template="/${session['entryController']}/footer"></g:render>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="moment-timezone.js"/>
<asset:javascript src="moment-timezone-utils.js"/>
<script>
    $('#profile-tabs a').click(function(e) {
        e.preventDefault();
        $(this).tab('show');
    });
    $("ul.nav-tabs > li > a").on("shown.bs.tab", function(e) {
        var id = $(e.target).attr("href").substr(1);
        window.location.hash = id;
    });
    var hash = window.location.hash;
    $('#profile-tabs a[href="' + hash + '"]').tab('show');

    <g:remoteFunction controller="creation" action="getUserOrdersList"  onSuccess='displayOrderDetails(data);'/>

    function displayOrderDetails(data) {
        var orders = data.results;
        var htmlStr = "";
        var bookPurchaseAmount = "";
        var imgSrc = "";
        var date = "";
        var orderNo="";
        var paymentId="";

        for(var i = 0; i < orders.length; i++) {
            bookPurchaseAmount = orders[i].amount;
            if(orders[i].poFor=="free") orderNo="FR"+orders[i].purchaseId;
            else orderNo="PO"+orders[i].purchaseId;

            if(bookPurchaseAmount == null || bookPurchaseAmount == 0 || bookPurchaseAmount == undefined) {
                bookPurchaseAmount = "Free";
            } else {
                bookPurchaseAmount = "<i class='fa fa-inr'></i>"+" "+orders[i].amount;
            }

            if (orders[i].coverImage == null || orders[i].coverImage == "null" || orders[i].coverImage == "") {
                imgSrc = "/assets/booksmojo/img_cover_placeholder.png";
            } else {
                imgSrc = "/funlearn/showProfileImage?id="+orders[i].bookId+"&fileName="+orders[i].coverImage+"&type=books&imgType=passport";
            }

            if(orders[i].paymentId == null || orders[i].paymentId == 0 || orders[i].paymentId == undefined) {
                paymentId = "-";
            }else{
                paymentId = orders[i].paymentId;
            }

            moment.tz.add("Asia/Calcutta|HMT BURT IST IST|-5R.k -6u -5u -6u|01232|-18LFR.k 1unn.k HB0 7zX0");
            moment.tz.link("Asia/Calcutta|Asia/Kolkata");
            date = orders[i].orderedDate;

            if(date != null || date != undefined) {
                date = moment.tz(orders[i].orderedDate, "Asia/Calcutta").format("DD MMM, YYYY");
            } else {
                date = "";
            }

            htmlStr+= "<p class='purchase-date'>"+date+"</p>" +
                "<div class='users-orders-details-wrapper'>"+
                "<div class='users-orders-details'>"+
                "<div class='user-purchase-order-detail'>" +
                "<p class='order-purchase-id'>"+"Purchase Id: "+orderNo+"</p>" +
                "<div class='order-book-image'>" +
                "<img src='"+imgSrc+"'class='img-responsive' alt=''>" +
                "</div>" +
                "<div class='order-book-detail book-info'>" +
                "<p class='preview-book-name'>"+orders[i].title+"</p>" +
                //                "<p class='author-name'>"+"Wonderslate"+"</p>" +
                "<p class='offer-price'>"+bookPurchaseAmount+"</p>" +
                //                "<p class='original-price'>220</p>" +
                "</div>" +
                "</div>" +
                "<div class='order-payment-details'>" +
                "<div class='order-review-section'>" +
                "<a href='javascript:showReviewModal("+orders[i].bookId+");' class='pull-left order-book-review'>Write a review</a>" +
                "<a href='mailto:<%=grailsApplication.config.grails.mail.default.from%>>?subject="+orders[i].title+"' class='pull-right order-book-help'>Need help?</a>" +
                "</div>" +
                "<div class='col-sm-12 col-md-12 col-xs-9' style='padding: 0;'>" +
                "<p class='order-payment-id-label'>PAYMENT ID</p>" +
                "<p class='order-payment-id'>"+paymentId+"</p>" +
                "</div>" +
                "<div class='col-sm-12 col-md-6 col-xs-12 total-paid-amt' style='padding: 0;'>" +
                "<p class='order-payment-label'>TOTAL PAID</p>" +
                "<p class='order-payment-id'>"+bookPurchaseAmount+"</p>" +
                "</div>";

            if(orders[i].poFor=="Chapter"){
                htmlStr+=    "<div class='col-sm-12 col-md-6 col-xs-12 total-paid-amt' style='padding: 0; float: right;'>" +
                    "<p class='order-payment-label'>CHAPTERS UNLOCKED</p>" +
                    "<p class='order-payment-id'>"+"0"+orders[i].noOfChapters+"</p>" +
                    "</div>";
            }

            htmlStr+=        "</div>" +
                "</div>" +
                "</div>";
        }

        document.getElementById('orders-wrapper').innerHTML= htmlStr;
    }
</script>
