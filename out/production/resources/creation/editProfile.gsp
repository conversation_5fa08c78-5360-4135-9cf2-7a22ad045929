
<g:render template="/funlearn/navheader"></g:render>
<style>
.image-upload > input
{
    display: none;
}
</style>
<asset:stylesheet href="imageoverlay.css"/>



<div>
    <div class="container-fluid light-grey-background">
        <div id='content' class='row'>
            <div class='col-md-2  col-md-offset-1 sidebar'>
                <br>
                <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadprofile" id="uploadprofile" action="/creation/uploadprofile" method="post">
                    <input type="hidden" name="type" value="user">
                    <input type="hidden" name="source" value="editProfile">
                    <input type="hidden" name="sourceController" value="creation">
                    <div class="row"><div class="col-md-12 text-center image-wrapper overlay-fade-in">
                        <%if(session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" width="100">
                        <%}else{%> <a href="#"><i class="fa fa-user fa-5x"></i><br>(Add your profile picture here)</a><%}%>
                        <div class="image-overlay-content image-upload">
                            <p><br></p>
                            <div id="filelabel1"><label for="fileoption1"><span class="smallText" style="cursor: pointer;color:white">Update Profile Picture</span></label></div>
                            <input id="fileoption1" name="file" type="file"  accept="image/png, image/jpeg, image/gif" onchange="updateProfile();"/>

                        </div>
                    </div></div>
                </form>
                <br>
            </div>
            <div class='col-md-7 col-md-offset-1 main'>
                <h5>BASIC INFORMATION:</h5>
            <g:form name="editprofile" url="[action:'editProfile',controller:'creation']" method="post">
              <div class="BasicInfo main-shadow">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="name">Name:</label>
                                <input type="text" class="form-control" name='name' id="name" value="${session['userdetails'].name}" placeholder="Name">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="">You Are a:</label><br>
                            <label class="checkbox-inline"><input type="checkbox"  name='student' <%=("on".equals(""+session['userdetails'].student))?"checked":""%>>Student</label>
                            <label class="checkbox-inline"><input type="checkbox" name='teacher' <%=("on".equals(""+session['userdetails'].teacher))?"checked":""%>>Teacher</label>
                            <label class="checkbox-inline"><input type="checkbox" name='parent' <%=("on".equals(""+session['userdetails'].parent))?"checked":""%>>Parent</label>
                        </div>
                    </div>

                </div>
                
                <div class="row">
                    <div class="col-md-2">
                        <div class="form-group">
                            <label for="syllabusType">Level:</label><br>
                            <g:select class="form-control" name="syllabusType" from="${com.wonderslate.data.LevelsMst.listOrderBySortBy()}" optionKey="name" optionValue="name" value="${session['userdetails'].registeredFrom}"
                                      noSelection="['':'Choose Level']" onchange="javascript:getBoardsForSyllabusType(this.value)"/> &nbsp;
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="syllabus">Syllabus:</label><br>
                            <select class="form-control" name="syllabus" id="syllabus"><option>Choose Syllabus</option></select> &nbsp;
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="classStudying">Grade/Class:</label>
                            <input type="text" class="form-control" name='classStudying' id="classStudying" value="${session['userdetails'].classStudying}" placeholder="Class">
                        </div>
                    </div>

                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="school">School / Institution:</label>
                                <input type="text" class="form-control" name='school' id="school" value="${session['userdetails'].school}" placeholder="School">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="city">Place(City/Town/Village):</label>
                            <input type="text" class="form-control" name='city' id="city" value="${session['userdetails'].city}" placeholder="City">
                        </div>
                    </div>

                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="qualification">Qualification:</label>
                            <input type="text" class="form-control" name='qualification' id="qualification" value="${session['userdetails'].qualification}" placeholder="Qualification">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="country">Country:</label><br>
                            <g:countrySelect class="form-control" name="country" default="ind" value="${session['userdetails'].country}"/>
                        </div>
                    </div>
                </div>    
                
             </div><!--  End Of Basic Info-->
             
             <h5>ADDITIONAL INFORMATION:</h5>
             <div class="AdditionalInfo main-shadow">
                 <div class="row">
                    <div class="col-md-6"> 
                        <div class="form-group">
                            <label for="interests">Interests:</label>
                                <input type="text" class="form-control" name='interests' id="interests" value="${session['userdetails'].interests}" placeholder="Interests">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="introduction">About Yourself:</label>
                                <textarea class="form-control" rows="3" name='introduction' id="introduction" value="${session['userdetails'].introduction}" placeholder="Description"></textarea>
                        </div>
                    </div>    
                 </div>
                 
                 <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="webpage">Webpage:</label>
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-link"></i></span> 
                                    <input type="text" class="form-control" name='webpage' id="webpage" value="${session['userdetails'].webpage}" placeholder="Link of your webpage">
                                </div>    
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="facebook">Facebook:</label>
                                <div class="input-group">
                                <span class="input-group-addon"><i class="fa fa-link"></i></span>        
                                <input type="text" class="form-control" name='facebook' id="facebook" value="${session['userdetails'].facebook}" placeholder="Link Of Your Facebook Page">
                                </div>
                        </div>
                    </div>       
                 </div>
                 
                 <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="linkedin">LinkedIn:</label>       
                                <div class="input-group">
                                   <span class="input-group-addon"><i class="fa fa-link"></i></span>
                                   <input type="text" class="form-control" name='linkedin' id="linkedin" value="${session['userdetails'].linkedin}" placeholder="Link of your LinkedIn page">
                                </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="twitter">Twitter:</label>       
                                <div class="input-group">
                                   <span class="input-group-addon"><i class="fa fa-link"></i></span>
                                   <input type="text" class="form-control" name='twitter' id="twitter" value="${session['userdetails'].twitter}" placeholder="Link of your Twitter page">
                                </div>
                        </div>
                    </div>    
                 </div> 
                 
             </div>   <!--End Of Additional Info--> 
                
                
                <div class="alert alert-warning col-sm-12 text-left" style="display: none;">
                    <div class="col-md-2"></div>
                    <div class="col-md-8">
                        ** Please complete required fields marked in red.</div>
                </div>
                <br>
                <div class="row">
                    <div class="col-md-4"></div>
                    <div class="col-md-4">
                        <button class="btn btn-lg btn-primary btn-block" type="button" onclick="javascript:formSubmit()">Update</button>
                        <span class="help-block"></span>
                    </div>
                    <div class="col-md-4 vcenter"></div>
                </div>
                <input type="hidden" name="mode" value="update">
                </g:form>
                </div>
            <div class='col-md-2 col-md-offset-1 sidebar'>
                <br>
            </div>
        </div>
    </div>



    <g:render template="/funlearn/footer"></g:render>
</div>

<asset:javascript src="searchContents.js"/>
<asset:javascript src="quiz.js"/>
<asset:javascript src="topic.js"/>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="generic.js"/>
<script>
    var syllabus = "${session['userdetails'].syllabus}"
    function formSubmit() {


        var flds = new Array ('name','syllabus');

        if (genericValidate(flds)) {

           document.editprofile.submit();
       }
}


    function updateProfile(){
            document.uploadprofile.submit();
    }

    function getBoardsForSyllabusType(level){
        <g:remoteFunction controller="funlearn" action="getBoardsForSyllabusType"  onSuccess='displaySyllabus(data);'
        params="'syllabusType='+level"/>
    }

    function displaySyllabus(data){
        var select = document.getElementById("syllabus");
        var tempboards = data.boards;
        var boardsLength = tempboards.length;
        select.options.length = 1;
        // if("selectedSubject"==fieldName) select.options.length = 2;

        for(var i=0; i<boardsLength; i++) {
            var opt = tempboards[i].board;
            var el = document.createElement("option");
            el.textContent = opt;
            el.value = opt;
            if(opt==syllabus) el.selected=true;
            select.appendChild(el);
        }

        select.focus();
    }
    <%if(session['userdetails'].registeredFrom!=null){%>
    getBoardsForSyllabusType('<%= session['userdetails'].registeredFrom %>')
    <%}%>

</script>


<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>
</body>
</html>