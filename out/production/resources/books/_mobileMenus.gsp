<div class="d-sm-none mobile-profile">
    <%if(session["userdetails"]==null){%>
    <li class="nav-item" style="margin-right: 0.5rem;">
        <a class="nav-link" onclick="loginOpen()">LOGIN</a>
    </li>
    <li class="nav-item">
        <a class="nav-link" onclick="signUpOpen()">SIGN UP</a>
    </li>
    <%}else{%>
    <li class="nav-item">
        <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle">
            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle">
            <%}%>
        </a>
        <div class="dropdown-menu dropdown-menu-right">
            <div class="media p-3">
                <a href="/creation/userProfile">
                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                    <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                    <%}%>
                    <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                </a>
                <div class="media-body">
                    <p class="user-name">Hello,<span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                    <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                </div>
            </div>
            <a class="dropdown-item order-pr" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
            %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
            <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
        </div>
    </li>
    <%}%>
</div>