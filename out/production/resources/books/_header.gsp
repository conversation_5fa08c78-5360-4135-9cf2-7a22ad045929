<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"Next-gen Learning Platform - UPSC | JEE | NEET - Wonderslate"%></title>
    <meta name="description" content="Next-gen learning platform which offers Smart eBooks for competitive exams such as JEE, NEET, UPSC, Banking and much more. Learn, practice and analyze your preparation now!">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <meta name="theme-color" content="#6F58D8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0,maximum-scale=1.0, user-scalable=0" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <asset:stylesheet href="wonderslate/material.css"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css">
    <asset:stylesheet href="wonderslate/headerws.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">

    <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
    <asset:stylesheet href="landingpage/homepageStyle.css" async="true"/>
    <asset:stylesheet href="landingpage/fonts/flaticon.css" async="true"/>
    <asset:stylesheet href="landingpage/smartbanner.css" async="true"/>
    <link rel="stylesheet" href="/assets/katex.min.css">

    <!-- General styles for admin & user pages -->
    <asset:stylesheet href="wonderslate/ws_general.css" async="true"/>

    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.min.js"></script>
    <asset:javascript src="wonderslate/material.min.js"/>

    <style>
    @media only screen and (max-device-width: 767px) and (min-device-width: 320px) {

        iframe#drift-widget {
            top: 0 !important;
        }
    }
    @media only screen and (max-width: 767px) {
        .bar {
            margin-top: 0;
        }
        .bar ul.dropdown-menu  {
            margin-top: 33px;
            width: 300px;
        }
        .bar ul.dropdown-menu li a:last-child{
            color: black !important;
        }
        .navbar {
            justify-content: flex-start;
        }
    }
    @media only screen and (min-width: 768px) {
        .bar ul.dropdown-menu  {
            margin-top: 33px;
            width: 450px;
        }
        .bar ul.dropdown-menu li a:last-child {
            color: black !important;
        }
    }
    .pomodoro_section {
        position: relative;
    }
    #canvasProgressBar {
        position: absolute;
        top: -6px;
        left: -3px;
    }
    </style>

    <!-- Google Tag Manager -->
    <script>(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
    })(window,document,'script','dataLayer','GTM-MH384WF');</script>
    <!-- End Google Tag Manager -->

    <script>
        var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
        function openMenu() {
            $('.right-menu').css('display','flex');
        }
        function closeMenu() {
            $('.right-menu').hide();
        }

        if($(window).width()>=768) {
            $(window).scroll(function () {
                var scroll = $(window).scrollTop();
                if (scroll >= 10) {
                    $(".normalHeader").addClass("addSticky");
                    $('.subMenu').addClass('addFix');
                    $('.bookTemplate').addClass('reFix');
                } else if (scroll < 9) {
                    $(".normalHeader").removeClass("addSticky");
                    $('.subMenu').removeClass('addFix');
                    $('.bookTemplate').removeClass('reFix');
                }
            });
        }
    </script>
</head>

<body>

<!-- Google Tag Manager (noscript) -->
<noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-MH384WF"
                  height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
<!-- End Google Tag Manager (noscript) -->

<g:render template="/books/signIn"></g:render>

<!-- Header -->
<div class="mdl-layout mdl-js-layout mdl-layout--fixed-header">

<header class="mdl-layout__header mdl-layout__header--transparent d-flex">
    <div class="mdl-layout__header-row mt-4 px-xl-5">

        <span class="mdl-layout-title mt-2 mt-sm-3">
            <a href="/books/index">
                <img src="${assetPath(src: 'ws/ws-new-logo.svg')}" class="d-none d-lg-block">
                <img src="${assetPath(src: 'ws/mobile-ws-logo.svg')}" class="d-block d-lg-none">
            </a>
        </span>

        <div class="pomodoro_section">
            <a href="javascript:openCurrentPomodoro();">
                <canvas id="canvasProgressBar" width=90 height=40></canvas>
            </a>
            <a href="javascript:openCurrentPomodoro();" id="pomodoroTimer"></a>
        </div>

        %{--<div class="notify_icon mt-3 ml-3 d-block d-md-none">
            <a href="#">
                <i class="material-icons">notifications_active</i>
                <small>3</small>
            </a>
        </div>--}%

        <div class="mdl-layout-spacer"></div>

        <nav class="mdl-navigation mt-2">
            <sec:ifNotLoggedIn>
                <a href="javascript:loginOpen();" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-xl-flex">
                    <img src="${assetPath(src: 'ws/icon-menu-home.svg')}"> My Home
                </a>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
            <a href="/books/home" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-xl-flex">
                <img src="${assetPath(src: 'ws/icon-menu-home.svg')}"> My Home
            </a>
            </sec:ifLoggedIn>
            <a href="/ebooks" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-xl-flex">
                <img src="${assetPath(src: 'ws/icon-menu-ebook-store.svg')}"> eBooks Store
            </a>
            <sec:ifLoggedIn>
            <a href="/test-generator" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-md-flex">
                <img src="${assetPath(src: 'ws/icon-menu-create-test.svg')}"> Create Test
            </a>
            <a href="/usermanagement/orders" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-md-flex">
                <img src="${assetPath(src: 'ws/icon-menu-order-history.svg')}"> Orders History
            </a>
            </sec:ifLoggedIn>
            <a href="/doubts" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-md-flex">
                <img src="${assetPath(src: 'ws/icon-menu-doubts.svg')}"> Doubts
            </a>
            <a href="/ebooks" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-md-flex">
                <img src="${assetPath(src: 'ws/icon-menu-search.svg')}"> Search
            </a>

            <sec:ifNotLoggedIn>
                <a href="javascript:loginOpen();" class="mdl-button mdl-js-button mdl-js-ripple-effect d-none d-md-flex">
                    Register or Login
                </a>
            </sec:ifNotLoggedIn>
            <sec:ifLoggedIn>
            <button id="profile-menu-lower-right" class="mdl-button mdl-js-button mdl-button--icon d-none d-sm-block">
                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">
                <%}%>
            </button>

            <ul class="mdl-menu mdl-menu--bottom-right mdl-js-menu mdl-js-ripple-effect d-none d-sm-block" for="profile-menu-lower-right">
                <div class="media p-3">
                    <a href="/usermanagement/editprofile" class="position-relative">
                        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle drop-profile"/>
                        <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle drop-profile"/>
                        <%}%>
                        <span class="edit-btn"><i class="material-icons">edit</i></span>
                    </a>
                    <div class="media-body ml-3">
                        <p class="user-name">Hello,<span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                        <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                    </div>
                </div>
                %{--<a class="dropdown-item order-pr" id="order-pr" href="/usermanagement/orders">My Orders</a>--}%
                <hr class="mt-0 mb-2 mx-3">
                <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
            </ul>
            </sec:ifLoggedIn>
        </nav>

    </div>
</header>

<!-- Hamburger Menu -->
<div class="mdl-layout__drawer">
    <span class="mdl-layout-title">
        <a href="/books/index">
            <img src="${assetPath(src: 'ws/ws-new-logo.svg')}">
        </a>
    </span>

    <sec:ifLoggedIn>
    <div class="mdl-layout__drawer_account d-block d-sm-none">
        <div class="d-flex flex-column align-items-center">
            <a href="/usermanagement/editprofile" class="position-relative mb-2">
                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">
                <%}%>
                <span class="edit-btn"><i class="material-icons">edit</i></span>
            </a>
            <p class="user-name text-center mb-2"><span><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
            <p class="user-mail text-center"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
        </div>
        <hr>
        <div class="d-flex flex-column">
            %{--<a class="mdl-button mdl-js-button mdl-js-ripple-effect" order-pr" href="/usermanagement/orders">My Orders</a>--}%
            <a class="mdl-button mdl-js-button mdl-js-ripple-effect" href="/logoff">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
        </div>
    </div>
    </sec:ifLoggedIn>

    <nav class="mdl-navigation d-none d-sm-flex">
        <sec:ifNotLoggedIn>
        <a class="mdl-button mdl-js-button mdl-js-ripple-effect" href="javascript:loginOpen();">
            <img src="${assetPath(src: 'ws/icon-menu-home.svg')}"> My Home
        </a>
        </sec:ifNotLoggedIn>
        <sec:ifLoggedIn>
            <a class="mdl-button mdl-js-button mdl-js-ripple-effect" href="/books/home">
                <img src="${assetPath(src: 'ws/icon-menu-home.svg')}"> My Home
            </a>
        </sec:ifLoggedIn>
        <a class="mdl-button mdl-js-button mdl-js-ripple-effect" href="/ebooks">
            <img src="${assetPath(src: 'ws/icon-menu-ebook-store.svg')}"> eBooks Store
        </a>
    </nav>
</div>

</div>

<div class="page_main__wrapper">
