<g:render template="/wonderpublish/wsFooter"></g:render>

<script>
    var userLoggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        userLoggedIn=true;
    </script>
</sec:ifLoggedIn>

<%if(params.tokenId==null&&session["appType"]==null&&session["instituteShopEnabled"]==null){%>
<div class="prepjoy_cta p-4 d-none">
    <div class="curved-bg"></div>
    <div class="container px-0">
        <div class="d-flex flex-wrap align-items-center justify-content-center text-center text-md-left col-12 col-lg-10 mx-auto cta_inner">
            <div class="cta_info my-2">
                <p class="text-white text-center">For bulk and library orders contact us at <br> <a href="mailto:<EMAIL>" class="mt-3 d-inline-block"><EMAIL></a> <br class="d-md-none"><span>or</span><br class="d-md-none"> <a href="https://wa.me/************" target="_blank"><i class="fab fa-whatsapp"></i> 8088443860</a></p>
            </div>
        </div>
    </div>
</div>

<footer class="footer-menus py-3">
    <div class="container-fluid footer-fluid">
        <div class="row mx-0">
            <div class="col-lg-5">
                <div class="row mx-0">
                    <div class="col-md-6">
                        <ul class="footer_logo_wrapper">
                            <li>
                                <div class="logo-wrapper-new">
                                    <img class="footer-logo" loading='lazy' src="${assetPath(src: 'wonderslate/footer-logo.webp')}" alt="Wonderslate Logo" width="85" height="72" style="width: 85px;height: 72px;">
                                </div>
                            </li>
                            <li class="desktop mt-3 mt-md-0">
                                <a href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&utm_source=signup%20mailer&utm_medium=email&utm_campaign=Wonderslate_App&utm_term=IITJEE" target="_blank">
                                    <button class="download-btns">
                                        <img class="footer-logo" loading='lazy' src="${assetPath(src: 'wonderslate/playstore.webp')}" alt="Wonderslate Playstore Link" width="150" height="53" style="width: 150px;height: 53px;">
                                    </button>
                                </a>
                                <a href="https://apps.apple.com/in/app/wonderslate/id1438381878" target="_blank">
                                    <button class="download-btns">
                                        <img class="footer-logo" loading='lazy' src="${assetPath(src: 'wonderslate/appstore.webp')}" alt="Wonderslate Appstore Link" width="150" height="53" style="width: 150px;height: 53px;">
                                    </button>
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div class="col-md-6 explore-section pl-0">
                        <div class="widget-ftr ipadVis">
                            <div id="vl"></div>
                            <div id="explore">
                                <p>Explore</p>
                                <ul>
                                    <sec:ifNotLoggedIn>
                                        <li>
                                            <a href="javascript:loginOpen();">Home</a>
                                        </li>
                                    </sec:ifNotLoggedIn>
                                    <sec:ifLoggedIn>
                                        <li>
                                            <a href="/books/home" >Home</a>
                                        </li>
                                    </sec:ifLoggedIn>
                                    <li><a href="/products">Products</a> </li>
                                    <li><a href="/books/description">Sell Books on Wonderslate</a></li>
                                    <li><a href="https://blog.wonderslate.com/" target="_blank">Blog</a> </li>
                                    <li><a href="/books/faq">FAQs</a> </li>
                                    <li><a href="/funlearn/termsandconditions">Terms & Conditions</a> </li>
                                    <li><a href="/funlearn/privacy">Privacy Policy</a> </li>
                                    <li><a href="/creation/returnPolicy">Return and Cancellation Policy</a></li>
                                </ul>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <div class="col-lg-7">
                <div class="row mx-0">

                    <div class="col-md-4 pl-0">
                        <div class="widget-ftr">
                            <!--<h4>Featured Publishers</h4> -->
                            <div class="w-100" id="trendingPublishers"></div>
                        </div>
                    </div>
                    <div class="col-md-4 pl-0">
                        <div class="widget-ftr">
                            <p>Connect</p>
                            <ul>
                                <li><a href="tel:+************">+91-8088443860</a> </li>
                                <li><a href="mailto:<EMAIL>"><EMAIL></a> </li>
                            </ul>
                            <ul class="social-icons mb-0">
                                <li><a href="https://www.facebook.com/wonderslatesmartbooks" target="_blank" title="facebook link"><i class="fab fa-facebook"></i></a> </li>
                                <li><a href="https://www.instagram.com/wonderslatesmartbooks/" target="_blank" title="instagram link"><i class="fab fa-instagram"></i></a></li>
                                <li><a href="https://www.youtube.com/channel/UCMJv3HwAgBCwqWsZ63wIWwA" target="_blank" title="youtube link"><i class="fab fa-youtube"></i></a> </li>
                                <li><a href="https://wa.me/************" target="_blank" title="whatsapp link"><i class="fab fa-whatsapp"></i></a></li>
                                <li><a href="https://t.me/wonderslate" target="_blank" title="telegram link"><i class="fab fa-telegram"></i></a></li>
                            </ul>
                            <ul>
                                <li>
                                    <button class="digitalLib-btn rounded w-100"><a href="/wsLibrary/index" target="_blank">Digital Library</a></button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-12 accept-payments text-center">
                <img loading='lazy' src="${assetPath(src:'wonderslate/accept-payments.webp')}" width="380" height="30" alt="Wonderslate Accept Payments" style="width: 380px;height: 30px;"/>
            </div>
        </div>
        <div class="row footer-secRow">
            <div class="col-md-6 copyright-footer"></div>
            <div class="col-md-6 company-name">Wonderslate Technologies Pvt Ltd.</div>
        </div>
    </div>
</footer>

<%if(!"true".equals(session["appInApp"])){%>
<div class="mobile-footer-nav d-md-none" id="mobile-footer-nav"></div>
<%}%>

<%}%>
<section class="showrank-modal">
    <div class="modal" id="rank-dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <h4 class="modal-title">Rank</h4>
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="content-rankWrapper">
                        <div class="container">
                            <div class="d-flex justify-content-around align-items-center">
                                <div>
                                    <div class="profile">  <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                        <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                        <%}%></div>
                                    <p class="yr-head">Your Score</p>
                                    <p class="no-ques" id="userScore"></p>
                                </div>
                                <div>
                                    <div>
                                        <p class="rank-head">Your Rank</p>
                                        <p class="user-rank" id="userRank"></p>
                                        <p class="yr-head" id="totalParticipants"></p>
                                        <p id="detailedResults"></p>
                                        <!--   <p class="total-students" id="attemptedBy">Attempted by 2024 students</p>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="container mt-4">
                        <table class="table text-center table-hover">
                            <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Name</th>
                                <th>Score</th>
                            </tr>
                            </thead>
                            <tbody id="rankDetails">


                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Modal footer -->
                <div class="modal-footer">

                </div>

            </div>
        </div>
    </div>
</section>
<div class="modal fade book-cart-modal modal-modifier" id="bookCartModal">
    <div class="modal-dialog modal-dialog-centered modal-dialog-modifier modal-dialog-zoom">
        <div class="modal-content modal-content-modifier">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <div class="modal-body modal-body-modifier text-center">
                <div id="addedIconAnimation" class="scaleAnimation" style="display: none;">
                    <svg class="checkmark" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 52 52">
                        <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none" />
                        <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8" />
                    </svg>
                </div>
                <div id="existIconAnimation" class="f-modal-alert" style="display: none;">
                    <div class="f-modal-icon f-modal-warning scaleAnimation">
                        <span class="f-modal-body pulseAnimationIns"></span>
                        <span class="f-modal-dot pulseAnimationIns"></span>
                    </div>
                </div>
                <h5 id="cartModalText" class="mt-3">eBook is added to your cart!</h5>

                <div id="cartModalBtns" class="d-none justify-content-center py-3 col-12">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-shadow col-6 col-md-5 mr-2" data-dismiss="modal" aria-label="Close">Continue Browsing</button>
                    <button type="button" onclick="goCartPage();" class="btn btn-lg btn-success btn-shadow border-0 col-6 col-md-5 ml-2" data-dismiss="modal" aria-label="Close">Go to Cart</button>
                </div>
                <div id="cartModalLibBtn" class="d-none justify-content-center py-3 col-12">
                    <button type="button" onclick="goLibraryPage();" class="btn btn-lg btn-success btn-shadow border-0 col-6 col-md-5 ml-2" data-dismiss="modal" aria-label="Close">Go to My eBooks</button>
                </div>
            </div>

        </div>
    </div>
</div>

<g:render template="/books/pomodoro"></g:render>

<script src="/assets/wonderslate/vendors.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>
<script>

    //Dynamic Year in Footer
    var strDate = new Date();
    var shortYear = strDate.getFullYear();
    $('.copyright-footer').html('&copy; Wonderslate ' + shortYear + " All rights reserved.");


    var mobileFooterNav =
        '        <div class="d-flex row justify-content-around align-items-center w-100">\n' +
        '            <a href="/books/myHome" class="home-menu d-flex align-items-center col">\n' +
        '                <img loading="lazy" class="mb-1 inactive" src="${assetPath(src: 'wonderslate/icon-mobile-home.webp')}" width="22" height="22" alt="Home">\n' +
        '                <img loading="lazy" class="mb-1 active d-none" src="${assetPath(src: 'wonderslate/icon-home-filled.webp')}" width="22" height="22" alt="Home">\n' +
        '                <p>My Home</p>\n' +
        '            </a>\n' +
        '<sec:ifLoggedIn>'+
        <%if(session["userInstitutes"]!=null&&session["userInstitutes"].size()>0){%>
        '            <a href="/institute/home?instituteId=${session["userInstitutes"][0].id}" class="institute-menu d-flex align-items-center col">\n' +
        '                <img loading="lazy" class="mb-1 inactive" src="${assetPath(src: 'wonderslate/icon-mobile-institute.webp')}" width="22" height="22" alt="Institute">\n' +
        '                <img loading="lazy" class="mb-1 active d-none" src="${assetPath(src: 'wonderslate/icon-institute-filled.webp')}" width="22" height="22" alt="Institute">\n' +
        '                <p><span class="institute-menu-text">${session["userInstitutes"][0].name}</span></p>\n' +
        '            </a>\n' +
        <%}%>
        '</sec:ifLoggedIn>'+
        '            <a href="/ebooks" class="ebooks-menu d-flex align-items-center col">\n' +
        '                <img loading="lazy" class="mb-1 inactive" src="${assetPath(src: 'wonderslate/icon-mobile-library.webp')}" width="22" height="22" alt="Library">\n' +
        '                <img loading="lazy" class="mb-1 active d-none" src="${assetPath(src: 'wonderslate/icon-ebooks-filled.webp')}" width="22" height="22" alt="Library">\n' +
        '                <p>Store</p>\n' +
        '            </a>\n' +
        '            <a href="/books/leaderboard" class="leaderboard-menu d-none align-items-center col">\n' +
        '                <img loading="lazy" class="mb-1 inactive" src="${assetPath(src: 'wonderslate/lb-icon.png')}" width="22" height="22" alt="Library">\n' +
        '                <img loading="lazy" class="mb-1 active d-none" src="${assetPath(src: 'wonderslate/lb-filled-icon.png')}" width="22" height="22" alt="Library">\n' +
        '                <p>LeaderBoard</p>\n' +
        '            </a>\n' +
        %{--        <%if(session["userdetails"]==null||session["userdetails"].username.indexOf("1_cookie_")!=0){%>--}%
        %{--        '<sec:ifLoggedIn>'+--}%
        %{--        '            <a href="javascript:checkLoginAndProceed(\'/groups/index\');" class="groups-menu d-flex align-items-center col">\n' +--}%
        %{--        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-study-groups.svg')}">\n' +--}%
        %{--        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-mobile-study-groups.svg')}">\n' +--}%
        %{--        '                <p>Study Groups</p>\n' +--}%
        %{--        '            </a>\n' +--}%
        %{--        '</sec:ifLoggedIn>'+--}%
        %{--        <%}%>--}%
        '        </div>';

    $(document).ready(function(){
        document.getElementById('mobile-footer-nav') ? document.getElementById('mobile-footer-nav').innerHTML = mobileFooterNav : null;

        var url = window.location.href;
        if(url.indexOf("/funlearn/quiz") != -1){
            $('.mobile-footer-nav').addClass('hide-menus');
        } else if(url.indexOf("/dashboard") != -1 || url.indexOf("/books/index") != -1 ){
            $('.mobile-footer-nav .home-menu').addClass('active-menu');
            $('.mobile-footer-nav .home-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .home-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .ebooks-menu, .mobile-footer-nav .institute-menu, .mobile-footer-nav .groups-menu,.mobile-footer-nav .leaderboard-menu').addClass('common-footer-nav');
        } else if(url.indexOf("/ebooks") != -1 || url.indexOf("/books/store") != -1){
            $('.mobile-footer-nav .ebooks-menu').addClass('active-menu');
            $('.mobile-footer-nav .ebooks-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .ebooks-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .home-menu, .mobile-footer-nav .institute-menu, .mobile-footer-nav .groups-menu,.mobile-footer-nav .leaderboard-menu').addClass('common-footer-nav');
        }
            // else if(url.indexOf("/groups/index") != -1){
            //     $('.mobile-footer-nav .groups-menu').addClass('active-menu');
            //     $('.mobile-footer-nav .groups-menu .active').removeClass('d-none');
            //     $('.mobile-footer-nav .groups-menu .inactive').addClass('d-none');
            //     $('.mobile-footer-nav .home-menu, .mobile-footer-nav .institute-menu, .mobile-footer-nav .ebooks-menu').addClass('common-footer-nav');
        // }
        else if(url.indexOf("/instituteHome") != -1 || url.indexOf("/institute/home") != -1){
            $('.mobile-footer-nav .institute-menu').addClass('active-menu');
            $('.mobile-footer-nav .institute-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .institute-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .home-menu, .mobile-footer-nav .ebooks-menu, .mobile-footer-nav .groups-menu,.mobile-footer-nav .leaderboard-menu').addClass('common-footer-nav');
        }else if(url.indexOf("/leaderboard") != -1 || url.indexOf("/books/leaderboard") != -1){
            $('.mobile-footer-nav .leaderboard-menu').addClass('active-menu');
            $('.mobile-footer-nav .leaderboard-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .leaderboard-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .home-menu, .mobile-footer-nav .ebooks-menu, .mobile-footer-nav .groups-menu,.mobile-footer-nav .institute-menu').addClass('common-footer-nav');
        } else {
            $('.mobile-footer-nav a').addClass('common-footer-nav');
        }

    });

</script>

<script>

    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailed').style.display = 'none';
        }
        else{
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }

    }


    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";
    var logOffMode=true;
    var onceLoggedOut=false;



    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }

    function updateQuizPoints(quizId,correctAnswers) {
        <g:remoteFunction controller="funlearn" action="addQuizPoints"
			params="'quizId='+quizId+'&correctAnswers='+correctAnswers"></g:remoteFunction>
    }

</script>

<script>
    window.addEventListener('keydown', function(event) {
        if (event.keyCode === 80 && (event.ctrlKey || event.metaKey) && !event.altKey && (!event.shiftKey || window.chrome || window.opera)) {
            event.preventDefault();
            if (event.stopImmediatePropagation) {
                event.stopImmediatePropagation();
            } else {
                event.stopPropagation();
            }
            return;
        }
    }, true);
    $('#quizQuestionSection').bind('copy paste', function(e) {
        e.preventDefault();
    });
</script>

<script>

    <sec:ifNotLoggedIn>
    $(window).on('load',function() {
        var userCookie = getCookie("userCookie");
        var forceLogin = false;
        <%if(forceLogin!=null&&"true".equals(forceLogin)){%>
        forceLogin=true;
        <%}%>


    });
    </sec:ifNotLoggedIn>
    <%if(session.getAttribute('instituteUrlName')==null){%>
    deleteCookie("instituteUrlName");
    <%}%>

    var userAgent = navigator.userAgent;
    var platform = navigator.platform;
    var browserName;

    if (userAgent.indexOf("MSIE") !== -1 || userAgent.indexOf("Trident/") !== -1) {
        browserName = "Internet Explorer";
    } else if (userAgent.indexOf("Edge") !== -1) {
        browserName = "Microsoft Edge";
    } else if (userAgent.indexOf("Firefox") !== -1) {
        browserName = "Mozilla Firefox";
    } else if (userAgent.indexOf("Chrome") !== -1) {
        browserName = "Google Chrome";
    } else if (userAgent.indexOf("Safari") !== -1) {
        browserName = "Apple Safari";
    } else {
        browserName = "Unknown";
    }
    if(platform== "win") {
        $("html").addClass("windows");
    }

    if(browserName == "Google Chrome") {
        $("html").addClass("chrome");
    } else if(browserName == "Mozilla Firefox") {
        $("html").addClass("mozilla");
    } else if(browserName == "Apple Safari") {
        $("html").addClass("safari");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
                $('.btn-primary-modifier').css('background-color','#A81175 !important');
                $('.btn-success-modifier').css('background-color','#27AE60 !important');
                $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
                $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
                $('.btn-warning-modifier').css('background-color','#F79420 !important');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    } else {
        $("html").addClass("others");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    }

</script>


<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
    function openMenu() {
        $('.right-menu').css('display','flex');
    }
    function closeMenu() {
        $('.right-menu').hide();
    }

    // Only for ebook page top navigation
    <%if(session["userInstitutes"]!=null&&session["userInstitutes"].size()>0){%>
    $("header.mdl-layout__header").addClass('show-institute-menu');
    <%}%>

    $(document).ready(function(){
        // Only for form element with new styles
        $(".form-control-modifier").on('focus', function (){
            $(this).parent().addClass('is-focused');
        }).on('blur', function () {
            $(this).parent().removeClass('is-focused');
        });

        <%if(session["sessionCount"]!=null&&session["sessionCount"].intValue()>3&&session["userdetails"]!=null&&session["userdetails"].username.indexOf("1_cookie_")==0){%>
        signupModal();
        <%}%>
    });

    if (window.performance && window.performance.navigation.type === window.performance.navigation.TYPE_BACK_FORWARD) {
        $('.loading-icon').addClass('hidden');
    }


</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-3-typeahead/4.0.2/bootstrap3-typeahead.min.js" defer></script>

<g:render template="/wsshop/cartScripts"></g:render>
<g:render template="/wsshop/searchScripts"></g:render>
