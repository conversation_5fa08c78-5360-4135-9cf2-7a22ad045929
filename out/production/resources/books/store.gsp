<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="landingpage/categories.css" async="true"/>


<style>.item{
    position: relative;
}
#drift-widget #root button{
    background:#F79420 !important;
}

</style>
<section class="banner p-0">

    <div class="container">
        <div class="col-12">
            <div class="jumbotron py-5 mb-0">
                <h1 class="mt-md-0 mt-3">Smart e-books, videos, test series, notes and many more</h1>
            </div>

            <div class="form-container d-flex justify-content-center">
                <form class="form-inline">
                    <input type="text" class="form-control search typeahead" name="search" id="search-book" autocomplete="off" placeholder="Search title,subject,author,ISBN,language etc." style="padding-right: 55px;">
                    <button type="button" class="btn" onclick="submitSearch()" id="search-btn"><i class="material-icons">search</i></button>
                </form>
            </div>
        </div>
    </div>
    <div class="container store store1_index" style="display: none" id="searchResults">
        <div class="d-flex justify-content-between align-items-center">
            <h3 id="ebooksHeader"></h3>

        </div>
        <div class="row mt-4" id="content-data-books-ebooks"></div>
        <div class="d-flex justify-content-between align-items-center pt-3">
            <h3 id="printBooksHeader">Print books</h3>
        </div>
        <div class="row mt-4" id="content-data-books-printbooks"></div>
    </div>

    <h2 class="text-center or-text mx-auto my-4"><small>or</small></h2>

    <g:render template="/wonderpublish/gradeSelector"></g:render>

</section>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<g:render template="/books/footer_new"></g:render>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>

    <%if(!"browse".equals(params.mode)){%>

    if(getCookie("storeLocation")){
        <%  if(!"true".equals(params.loginFailed)) { %>
        window.document.location = getCookie("storeLocation");
        <%}%>
    }
    <%}%>
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });
    $(document).ready(function(){
        $('#search-btn').attr('disabled',true);
        $('#search-book').keyup(function(){
            if($(this).val().length !=0)
                $('#search-btn').attr('disabled', false);
            else
                $('#search-btn').attr('disabled',true);
        })
    });
    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearch();
            }
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString =document.getElementById("search-book").value;
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="discover" action="search"  onSuccess='booksReceived(data);'
         params="'searchString='+searchString" />

    }


    function replaceAll(str, find, replace) {
        if(str==undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }
    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }


    function booksReceived(data){
        if(data.status=="Nothing present"){
            alert("No results found for your search. Please change the search criteria and search again.");
            $('.loading-icon').addClass('hidden');

        }else {
            $("#pills-tabContent").hide();
            $('.nav-tabs a[href="#' + getCookie("level") + '"]').removeClass("active");
            $("#searchResults").show();
            allBooksData = data;
            var books = data.books;
            
            displayBooks(books, data.booksTag);
        }

    }
    function displayBooks(books,booksTags){
        var noOfEbooks  = 0;
        var noOfPrintBooks = 0;
        var printHtmlStr="";
        var ebookHtmlStr="";
        var imgSrc = "";

        var bookUrl="";

        var offerPrice = "";
        var listPrice = "";

        for(var i=0; i<books.length;i++){
            var urlTag = "";

            for (var s = 0; s < booksTags.length; ++s) {
                if (booksTags[s].bookId == books[i].id) {
                    if ("School" == booksTags[s].level) urlTag = replaceAll(booksTags[s].syllabus,' ', '-').toLowerCase();
                    else urlTag = replaceAll(booksTags[s].grade,' ', '-').toLowerCase();

                }

            }

            if (books[i].offerPrice == 0 || books[i].offerPrice == 0.0 || books[i].offerPrice == null) {
                offerPrice = "FREE";
            } else {
                offerPrice = "&#x20b9 " + books[i].offerPrice;
            }

            if (books[i].listPrice == 0 || books[i].listPrice == 0.0 || books[i].listPrice == "null" || books[i].listPrice == null || books[i].listPrice == books[i].offerPrice) {
                listPrice = "";
            } else {
                listPrice = books[i].listPrice;
            }

            imgSrc = books[i].coverImage;
            if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
                imgSrc = books[i].coverImage;
                imgSrc = imgSrc.replace("~", ":");
            } else {
                imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            }

            if (books[i].bookType == "print") {
                if (books[i].buylink1 != null && !books[i].buylink1 == "") bookUrl = books[i].buylink1.replace("~", ":");
                printHtmlStr += "<div class='col-6 col-md-3 col-lg-2 d-flex d-sm-block justify-content-center mb-md-4 mb-3'>" +
                    "<div class='topSchoolBooks'>" +
                    "<div class='image-wrapper'>";

                printHtmlStr += "<a href=\"javascript:openAffliatePage('" + bookUrl + "'," + books[i].id + ")\">";

                printHtmlStr += "<div class='bookShadow'><img src='" + imgSrc + "' alt=''/></div>" +
                    "<h3>Print</h3>" +
                    "</a>" +
                    "</div>";

                printHtmlStr += "<a href=\"javascript:openAffliatePage('" + bookUrl + "'," + books[i].id + ")\">";
                printHtmlStr += "<div class='content-wrapper'>" +
                    "<h3>" + books[i].title + "</h3>";
                printHtmlStr += "</div>" +
                    "</a>" +
                    "</div>" +

                    "</div>";
                noOfPrintBooks++;
            } else {
                ebookHtmlStr += "<div class='col-6 col-md-3 col-lg-2 d-flex d-sm-block justify-content-center mb-md-4 mb-3'>" +
                    "<div class='topSchoolBooks'>" +
                    "<div class='image-wrapper'>";

                ebookHtmlStr += "<a href='/" + urlTag + "/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/book-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true'>"

                ebookHtmlStr += "<div class='bookShadow'><img src='" + imgSrc + "' alt=''/></div>" +
                    "<h3>eBook</h3>" +
                    "</a>" +
                    "</div>";

                ebookHtmlStr += "<a href='/" + urlTag + "/" + replaceAll(replaceAll(books[i].title, ' ', '-').toLowerCase(), '\'', '') + "/book-details?siteName=${session['entryController']}&bookId=" + books[i].id + "&preview=true'>"

                ebookHtmlStr += "<div class='content-wrapper'>" +
                    "<h3>" + books[i].title + "</h3>" +
                    "<p class='price'>" + offerPrice + "<span>" + listPrice + "</span>" + "</p>";
                ebookHtmlStr += "</div>" +
                    "</a>" +
                    "</div>" +

                    "</div>";
                noOfEbooks++;
            }

        }


        if(noOfEbooks>0)   {
            document.getElementById("ebooksHeader").innerHTML = "eBooks ("+noOfEbooks+")";
            document.getElementById("content-data-books-ebooks").innerHTML = ebookHtmlStr;
        }
        if(noOfPrintBooks>0) {
            document.getElementById("printBooksHeader").innerHTML = "Print books ("+noOfPrintBooks+")";
            document.getElementById("content-data-books-printbooks").innerHTML = printHtmlStr;
        }

        $('.loading-icon').addClass('hidden');
    }

    function openAffliatePage(link,bookId){
        <g:remoteFunction controller="log" action="affiliationLog" params="'bookId='+bookId"/>
        window.open(link, '_blank');
    }




</script>
<g:render template="/wonderpublish/gradeSelectorScript"></g:render>
</body>
</html>
