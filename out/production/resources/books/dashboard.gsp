<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/chapter.css"/>
<asset:stylesheet href="wonderslate/dashboard.css"/>
<asset:stylesheet href="wonderslate/flashcardHome.css" async="true"/>


<section class="dashboard_section">
    <div class="container">
        <div class="row align-items-center pt-3 pt-lg-5 pb-3 pb-lg-4 px-3 col-12 col-md-10 mx-auto">
            <div class="page_title pr-5 pr-md-4">
                <h3 class="text-radial-gradient d-block"><strong>Hi, <%= session["userdetails"]!=null?session["userdetails"].name:"" %>!</strong></h3>
            </div>
%{--            <div class="dashboard_notify">--}%
%{--                <a href="#">--}%
%{--                    <span class="material-icons">--}%
%{--                        notifications_active--}%
%{--                    </span>--}%
%{--                    <small>3</small>--}%
%{--                </a>--}%
%{--            </div>--}%
            <div class="test_generate_btn ml-4 d-none d-lg-block">
                <a href="/test-generator" class="btn d-flex align-items-center"><i class="material-icons pr-1">add</i> Generate A Test</a>
            </div>
        </div>

       <div class="row align-items-center mt-md-3 col-12 col-md-10 mx-auto px-0">
           <div class="library-btn col-12 col-md-12 col-lg-5 mb-3 pr-lg-2">
               <a href="/wsLibrary/myLibrary" class="btn btn-lg d-flex align-items-center justify-content-between border-0">
                   My Library
                   <span class="material-icons">
                        east
                   </span>
               </a>
           </div>
           <div class="todo-btn col-12 col-md-6 col-lg-3 mb-3 px-lg-2">
               <a href="/toDo/toDoListAndUpdate" class="btn btn-lg d-flex align-items-center justify-content-center border-0">
                   <span class="material-icons pr-2">
                       playlist_add_check
                   </span>
                   My To-Do
               </a>
           </div>
           %{--<div class="tracker-btn col-6 col-md-6 col-lg-2 mb-3 px-lg-2">
               <a href="#" class="btn btn-lg d-flex align-items-center justify-content-center border-0">
                   <span class="material-icons pr-2">
                       playlist_add_check
                   </span>
                   My Tracker
               </a>
           </div>--}%
       </div>

        <div class="row col-12 col-md-10 mx-auto px-0">
            <div class="dashboard-records col-12 mt-3">
                <div class="card border-0">
                    <ul class="nav nav-pills p-3" id="dashboardTabs" role="tablist">
                        <li class="nav-item">
                            <a class="nav-link active" id="activity-tab" data-toggle="tab" href="#myActivity" role="tab" aria-controls="myActivity" aria-selected="true">My Activity</a>
                        </li>
%{--                        <li class="line-seperator"></li>--}%
%{--                        <li class="nav-item">--}%
%{--                            <a class="nav-link" id="updates-tab" data-toggle="tab" href="#courseUpdate" role="tab" aria-controls="courseUpdate" aria-selected="false">Course Updates</a>--}%
%{--                        </li>--}%
                    </ul>

                    <div id="emptyActivity"></div>

                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="myActivity" role="tabpanel" aria-labelledby="activity-tab">

                            <div class="all-container" id="content-data-all">
                            </div>

                        </div>
%{--                        <div class="tab-pane fade" id="courseUpdate" role="tabpanel" aria-labelledby="updates-tab">--}%

%{--                        </div>--}%
                    </div>

                    <div id="showMore" class="text-center">
                        <a href="javascript:showMore();" class="d-flex align-items-center justify-content-center">
                            <span class="material-icons">
                                expand_more
                            </span>
                            Show More
                        </a>
                    </div>
                    <div id="overlay" class="d-none"></div>
                </div>
            </div>
        </div>

    </div>

    <div class="test_generate_mobile_action d-lg-none d-flex justify-content-between align-items-center">
        <a href="/test-generator" class="btn d-flex align-items-center"><i class="material-icons pr-1">add</i> Generate A Test</a>
        <span class="d-flex" onclick="javascript:openModal()" data-toggle="modal" data-target="#footer-menu-popover"> <i class="material-icons menu-icon-footer">menu</i> <i class="material-icons menu-close-icon-footer hidden">close</i></span>
    </div>
<sec:ifLoggedIn>
    <div class="footer-menu-popover modal fade" id="footer-menu-popover" tabindex="-1" role="dialog" aria-labelledby="footer-menu-popover" aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-body">
                    <div class="top-drop-menu">
                        <div onclick="javascript:handleMenuClick('/dashboard')">
                            <img src="${assetPath(src: 'ws/icon-mobile-home.svg')}"><p>My Home</p>
                        </div>
                        <div onclick="javascript:handleMenuClick('/wsLibrary/myLibrary')">
                            <img src="${assetPath(src: 'ws/icon-mobile-library.svg')}"><p>My Library</p>
                        </div>
                        <div onclick="javascript:handleMenuClick('/books/index')">
                            <img src="${assetPath(src: 'ws/icon-mobile-modules-1.svg')}"><p>Modules</p>
                        </div>
                        <div onclick="javascript:handleMenuClick('/flashcard')">
                            <i class="material-icons">style</i> <p>Flashcard</p>
                        </div>
                        <div onclick="javascript:handleMenuClick('/doubts')">
                            <img src="${assetPath(src: 'ws/icon-mobile-doubts.svg')}">
                            <p>Ask Doubts</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    </div>
</sec:ifLoggedIn>
<sec:ifNotLoggedIn>
    <div class="test_generate_mobile_action d-lg-none">
        <a href="/test-generator" class="btn d-flex align-items-center"><i class="material-icons pr-1">add</i> Generate A Test</a>
    </div>
</sec:ifNotLoggedIn>
</section>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<g:render template="/resources/relatedBooks"></g:render>


<g:render template="/${session['entryController']}/footer_new"></g:render>
<asset:javascript src="moment.min.js"/>
<script>
    function openModal() {
        $('#footer-menu-popover').modal('toggle');
    }
    $('#footer-menu-popover').on('show.bs.modal', function(){
        $('.menu-close-icon-footer').toggleClass("hidden");
        $('.menu-icon-footer').toggleClass("hidden");
    });
    $('#footer-menu-popover').on('hide.bs.modal', function(){
        $('.menu-close-icon-footer').toggleClass("hidden");
        $('.menu-icon-footer').toggleClass("hidden");
    });
    function handleMenuClick(url){
        <%if("android".equals(session["appType"])){%>
        if(url=='/books/index'){
            JSInterface.backToHome();
        }
        else if(url=='/dashboard'){
            JSInterface.backToDashboard();
        }
        else if(url=='/doubts'){
            JSInterface.backToDoubts();
        }
        else if(url=='/flashcards'){
            JSInterface.backToFlashcard();
        }

        <%}else if("ios".equals(session["appType"])) {%>

        if(url=='/books/index'){
            webkit.messageHandlers.backToHome.postMessage('');
        }
        else if(url=='/dashboard'){
            webkit.messageHandlers.backToDashboard.postMessage('');
        }
        else if(url=='/doubts'){
            webkit.messageHandlers.backToDoubts.postMessage('');
        }
        else if(url=='/flashcards'){
            webkit.messageHandlers.backToFlashcard.postMessage('');
        }
        <%} else{%>

        // If a mobile browser is being used
        const userAgent = navigator.userAgent || navigator.vendor || window.opera;
        if (/iPad|iPhone|iPod|Android/.test(userAgent)) {
            window.location.href = url; // this url is passed as an argument to function from menu items
            return;
        }
        <%}%>
    }
</script>
<script>
    var batchIndex = 0;


    function getUserActivity() {
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="log" action="getResViewByUser" params="'batchIndex='+batchIndex" onSuccess="showUserActivity(data)"/>
    }

    function showUserActivity(data){
         var cData = data.userLog;
        var cStr = "";
        if(cData.length>0) {
            var resMap = new Map();
            resMap.set('Notes',{color:'lightgreen',text:'Notes'});
            resMap.set('Reference Videos',{color:'pink',text:'Videos'});
            resMap.set('Videos',{color:'pink',text:'Videos'});
            resMap.set('Reference Web Links',{color:'green',text:'Link'});
            resMap.set('QA',{color:'violet',text:'Q & A'});
            resMap.set('KeyValues',{color:'yellow',text:'Flash Card'});
            resMap.set('Multiple Choice Questions',{color:'violet',text:'MCQ'});


            for (var i = 0; i < cData.length; ++i) {
                var data = cData[i];
                // if (typeof data.bookId !== 'undefined') console.log("bookId=" + data.bookId);
                cStr += "<div class=\"container-wrapper p-3 pb-4\" id='res_" + i + "'>\n" +
                    "<div class='d-flex justify-content-between align-items-center'>";

                cStr += "        <div class=\"media\">\n";
                cStr += "                    <a class=\"mb-0 readnow d-flex align-items-start\" href='/resources/ebook?resId=" + data.resourceDtlId + "&clickSource=myactivity&myActivity=true&newActivity=true'>";
                cStr += "   <div class='box "+resMap.get(data.resType).color+"'>" +
                    "<div>" +
                    " <i class=\"align-self-center\">\n" +
                    "                \n" +
                    "            </i>\n" +
                    "<p>"+resMap.get(data.resType).text+"</p>" +
                    "</div>" +
                    "</div>";
                cStr += "            <div class=\"media-body pl-3\">\n" +
                    "                <p class=\"title\">" + data.resName + "</p>\n";
                if (typeof data.bookName !== 'undefined'){
                    cStr += "                <p class='book_name'><small>" + data.bookName + "</small></p>\n";
                }
                cStr += "                <p class='updated_info mt-1'><small><em>Last used " + moment.utc(data.dateCreated).local().format("D MMM YYYY h:mm a") + "</em></small></p>\n";
                cStr += "            </div>\n" +
                    "</a>" +
                    "        </div>\n" +
                    "    </div>\n" + "</div>";
            }


            document.getElementById('content-data-all').innerHTML += cStr;
            $("#emptyActivity").hide();
        }else{
            var emptyStr = "<div class='empty_activity text-center pt-4 pb-5 mt-4 mb-5' style=\"display: block;\">\n" +
                "                <img src='${assetPath(src: 'ws/todo-empty.svg')}'>\n" +
                "                <p>Welcome Aboard!</br>You can see your activity here.</p>\n" +
                "            <p class='mt-3'><strong>Start using now!</strong></p>\n" +
                "            </div>";
            document.getElementById('emptyActivity').innerHTML += emptyStr;
            $("#showMore").hide();
        }
        $('.loading-icon').addClass('hidden');
    }

    function showMore(){
        batchIndex++;
        getUserActivity();
        $("#emptyActivity").hide();
    }
    //call for the first time
    getUserActivity();

    $(".test_generate_btn a, .test_generate_mobile_action a, .library-btn a, .todo-btn a").on('click', function () {
        $('.loading-icon').removeClass('hidden');
        setTimeout(function() {
            $('.mozilla .loading-icon').addClass('hidden');
        }, 1500);
    });

</script>
