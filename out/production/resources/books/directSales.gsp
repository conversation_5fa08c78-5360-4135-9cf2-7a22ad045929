<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js p-md-5 direct-sales">
    <div class="container">
        <div class="d-flex justify-content-center align-items-center mt-5 mt-md-0">
            <h3><strong>Direct Sales</strong></h3>
        </div>
        <form class="card card-modifier card-shadow border-0 col-md-8 mx-auto mt-4 p-4 p-lg-5" id="directSales" name="directSales">
            <div class="form-group col-md-10 mx-auto">
                <label>Institutes</label>
                <g:select id="instituteId" class="form-control" optionKey="id" optionValue="name"
                          name="instituteId" from="${institutes}" noSelection="['':'Select']"/>
            </div>
            <div class="form-group col-md-10 mx-auto">
                <label>Package <span class="text-danger">*</span></label>
                <g:select id="bookId" class="form-control" optionKey="id" optionValue="title"
                          name="bookId" from="${subscriptionPackages}" noSelection="['':'Select']"/>
            </div>
            <div class="form-group col-md-10 mx-auto">
                <label>Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" name="studentName" id="studentName">
            </div>
            <div class="form-group col-md-10 mx-auto">
                <label>Mobile <span class="text-danger">*</span></label>
                <input type="tel" class="form-control" name="userMobile" id="userMobile" minlength="10" maxlength="10" pattern="[0-9]*" oninput="numberOnly(this.id)">
            </div>
            <div class="form-group col-md-10 mx-auto">
                <label>Email</label>
                <input type="email" class="form-control" name="userEmail" id="userEmail">
            </div>
            <div class="form-group col-md-10 mx-auto text-center mt-4">
                <a class="btn btn-lg btn-primary btn-primary-modifier mx-2 mb-3" href="javascript:processSales('cash');">Add Book</a>
                <a class="btn btn-lg btn-success btn-success-modifier mx-2 mb-3" href="javascript:processSales('onlinePayment');">Send payment link</a>
            </div>

            <div class="form-group col-md-10 mx-auto text-center" id="processResult" style="display: none"></div>

        </form>
    </div>
</section>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    var userMobile;
    function processSales(paymentMethod){
        if(document.getElementById("bookId").selectedIndex==0){
            alert("Please select the book package to be added");
            document.getElementById("bookId").focus();
            $("#bookId").addClass("input-error");
        }else{
            if(document.getElementById("studentName").value==""){
                alert("Please enter the name");
                document.getElementById("studentName").focus();
                $("#studentName").addClass("input-error");
            }
            else{
                if(document.getElementById("userMobile").value==""){
                    alert("Please enter the mobile number");
                    document.getElementById("userMobile").focus();
                    $("#userMobile").addClass("input-error");
                }
                    else{
                        var submitForm = false;
                        if(document.getElementById("instituteId").selectedIndex==0){

                            if(confirm("Are you sure to continue without selecting institute?")){
                                submitForm = true;
                            }
                            else{
                                document.getElementById("instituteId").focus();
                            }

                        }else{
                            submitForm = true;
                        }

                        if(submitForm){
                            //submit the form
                            var userName = document.getElementById("studentName").value;
                            var bookId = document.getElementById("bookId")[document.getElementById("bookId").selectedIndex].value;
                            var instituteId = "";
                            if(document.getElementById("instituteId").selectedIndex>0)
                            instituteId = document.getElementById("instituteId")[document.getElementById("instituteId").selectedIndex].value;
                            var userEmail = document.getElementById("userEmail").value;
                            userMobile = document.getElementById("userMobile").value;
                            <g:remoteFunction controller="books" action="startOrderProcess" params="'studentName='+userName+'&bookId='+bookId+'&instituteId='+instituteId+'&userEmail='+userEmail+'&userMobile='+userMobile+'&paymentMethod='+paymentMethod" onSuccess = "processStarted(data);"/>
                            $('.loading-icon').removeClass('hidden');
                        }
                }
            }
        }

    }

    function processStarted(data){
        if(data.status == "Book added.") {
            document.getElementById("processResult").innerHTML="<p>"+data.status+"</p>";
        } else {
            document.getElementById("processResult").innerHTML="<p>Sent successfully via SMS to <strong>"+userMobile+".</strong></p><br><a href='https://wa.me/91"+userMobile+"?text="+encodeURIComponent(data.status)+"' target='_blank' class='btn btn-outline-success btn-outline-success-modifier payment-link-share'><i class='fab fa-whatsapp pr-2'></i> Share on WhatsApp</a>";
        }
        $("#processResult").show();
        $('.loading-icon').addClass('hidden');
        document.getElementById("directSales").reset();
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    $('#bookId').on('change', function () {
        $(this).removeClass('input-error');
        $("#processResult").html("").hide();
    });
    $('#studentName').on('keyup', function () {
        $(this).removeClass('input-error');
        $("#processResult").html("").hide();
    });
    $('#userMobile').on('keyup', function () {
        $(this).removeClass('input-error');
        $("#processResult").html("").hide();
    });
</script>
