<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<style>
.back_to_top {
    display: none !important;
}
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js pb-5 pt-0 leaderboard">
    <div class="container pt-4 pt-md-5 pb-5 mt-3 mb-4 mt-md-0">
        <div class="d-flex justify-content-start align-items-center page_title col-12 pb-2 px-0">
            <button id="goBack" class="border-0 mr-2 bg-transparent" onclick="javascript:window.history.back();"><i class="material-icons-round">keyboard_backspace</i></button>
            <h4><strong>Leaderboard</strong></h4>
            <button class="border-0 text-white rounded-circle ml-3 leaderboard-share" onclick="javascript:openShareContentModalGeneric('Hey! check out the quiz leaderboard.','${request.getRequestURL()}')"><i class="material-icons-round">share</i></button>
        </div>
        <g:render template="/books/leaderBoard"></g:render>
    </div>
</section>

<div id="dailyTestModal" class="modal modal-modifier fade" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <h4 class="mt-3">Take Daily Tests & Be on <br class="d-sm-none">our Leaderboard</h4>
                <p>Competitive Exams, General, Engineering, etc.</p>

                <div class="d-flex justify-content-center py-3">
                    <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-5 col-md-4 mr-2" data-dismiss="modal" aria-label="Close">Later</button>
                    <button type="button" class="btn btn-lg btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect border-0 col-5 col-md-4 ml-2" onclick="javascript:openDailyTest();">Take test now</button>
                </div>
            </div>

        </div>
    </div>
</div>

<div class="hidden-footer">
    <g:render template="/${session['entryController']}/footer_new"></g:render>
</div>

<g:render template="/resources/shareContent"></g:render>

<script>
    var selectedDate = "";

    var today = moment().format('DD MMMM YYYY');
    document.getElementById("selectedMonthYear").innerHTML = today;

    function selectMonth() {
        $('#datePicker').datepicker({
            maxViewMode: 0,
            format: 'dd-mm-yyyy',
            todayHighlight: true,
            maxDate:'0',
            endDate: "today",
        });
        $("#datePickerModal").modal('show');
    }

    $('#datePicker').on('changeDate', function(event) {
        var selectedCurrentDate = event.format('dd MM yyyy');
        document.getElementById("selectedMonthYear").innerHTML = selectedCurrentDate;
        selectedDate = $('#datePicker').datepicker('getFormattedDate');``
        $("#datePickerModal").modal('hide');

        currentDate = selectedDate.split('-').reverse().join('-');
        getDailyRank();
    });

    function openDailyTest(){
        window.location.href = '../prepjoy/dailyTest';
    }
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>

</body>
</html>
