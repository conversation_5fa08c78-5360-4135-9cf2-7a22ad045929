<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Wonderpublish</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));
    String requestURL = request.getRequestURL().toString();
    String servletPath = request.getServletPath();
    String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));

    session.setAttribute("servername", appURL);
    session.setAttribute("vtu","true");

%>
<body>

<div class="wpbackgroundsofa">
    <div class="container-fluid">
        <div class="header">
            <div class="row">
                <div class="col-md-5 col-md-offset-1">
                    <div class="brand">
                        <a  href="#">Visvesvaraya Technological University</a>
                    </div>
                </div>
                <div class="col-md-5 col-sm-5 text-right">



                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-10 col-md-offset-1 homemessage" ><BR><BR><BR>DIGITAL SMART BOOKS
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 col-md-offset-1 homemessagesmall" ><BR>Welcome to VTU's digital smart books library.
            </div>
        </div>

        <div class="row">
            <div class="col-md-11 col-md-offset-1 homemessagesmallest" ><BR><BR><a class="btn btn-lg  btn-primary smallText" type="button" href="javascript:showregister('login');" name="find" id="find">&nbsp;SIGN IN&nbsp;</a>&nbsp;&nbsp;
                <a class="btn btn-lg  btn-default smallText" type="button" href="javascript:showregister('signup');">&nbsp;REGISTER&nbsp;</a>
            </div>
        </div>

        <div class="row">
            <div class="col-md-11 col-md-offset-1 whitetext" ><BR><BR>Also download our app
            </div>
        </div>
        <div class="row">
            <div class="col-md-11 col-md-offset-1" ><BR><img src="${assetPath(src: 'apple.png')}" height="40">&nbsp;&nbsp;&nbsp;<img src="${assetPath(src: 'googleplay.png')}" height="40">
            </div>
        </div>
       <div class="row" style="background-color: white">
           <div class="col-md-12 text-center bottomElement whitetext">POWERED BY&nbsp;&nbsp;&nbsp;&nbsp;<img src="${assetPath(src: 'harbingerlogo.jpg')}" height="50">&nbsp;&nbsp;&nbsp;&nbsp;&&nbsp;&nbsp;&nbsp;&nbsp;<img src="${assetPath(src: 'logo-ws.png')}" height="50"></div>
       </div>
    </div>

</div>




<g:render   template="/creation/register"></g:render>




<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
<asset:javascript src="analytics.js"/>
<% } %>
<script language="JavaScript">
    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailed').style.display = 'none';
        }
        else{
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }

    }

</script>

</body>
</html>