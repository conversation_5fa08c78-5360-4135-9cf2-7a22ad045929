<style>
img, embed, object, video {
    max-width: 100%;
}
@media (max-width: 575.98px), (min-width: 576px) and (max-width: 767.98px) {
    .bannerImgPicture{
        width: 100% !important;
        height: 218px !important;
    }
}
</style>
<%if("27".equals(""+session["siteId"])){  %>
<style>
    .ebooks{
        padding-top: 0!important;
    }
</style>
<%}%>
<%if("true".equals(""+session["commonWhiteLabel"])){%>
<div class="banner-ws">
    <div id="slider-desktop" class="carousel slide w-100" data-ride="carousel">
        <ol class="carousel-indicators" id="slider-desktop-indicators">
        </ol>
        <div class="carousel-inner w-100" id="slider-desktop-views">
        </div>
    </div>
</div>

<script>
    var siteId = "${session["siteId"]}";
    var checkDescBanners = false;
    var checkMobBanners = false;
    var templateDesktop = "";
    var templateMobile = "";
    var defaultDeskImageUrl = "/assets/wonderslate/default-banner-ws.webp";
    var defaultMobImageUrl = "/assets/wonderslate/default-mobile-banner-ws.webp";

    function getBannerDetails(siteId) {
        $(".loading-icon").removeClass("hidden");
        <%if(session["publisherLogo"]==null){%>
        <g:remoteFunction controller="wonderpublish" action="getBannerdetails" params="'siteId='+siteId" onSuccess="showBanners(data);"/>
        <%}else{%>
        <g:remoteFunction controller="wonderpublish" action="getPublisherBannerdetails" params="'siteId='+siteId+'&publisherId=${session["publisherLogo"]}'" onSuccess="showBanners(data);"/>
        <%}%>
    }

   // getBannerDetails(siteId);
    showBanners();

    function showBanners() {
        var status="OK";
        var banners = JSON.parse("${bannerList}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));

        if(banners.length>0) {
         $('#slider-desktop-views,#slider-mobile-views').empty();

            // Banner slider
            $.each(banners, function (i, v) {
                var item = v;
                var htmlStr = '';
                var htmlStrMobile = '';
                var indicatorStr = '';
                var indicatorStrMobile = '';
                <%if(session["publisherLogo"]==null){%>
                var imageDescUrl = "/wonderpublish/showImage?id=" + v.id + "&fileName=" + v.imagePath + "&imgType=webp";

                var imageMobUrl = "/wonderpublish/showImage?id="+v.id+"&fileName="+v.imagePathMobile + "&imgType=webp";
                <%}else{%>
                var imageDescUrl =   "/wonderpublish/showImage?id=" + v.id + "&fromPub=true&fileName=" + v.imagePath + "&type=books&imgType=webp";

                var imageMobUrl =  "/wonderpublish /showImage?id=" + v.id + "&fromPub=true&fileName=" + v.imagePathMobile + "&type=books&imgType=webp";
                <%}%>

                function isMobileDevice() {
                    return /Mobi|Android/i.test(navigator.userAgent);
                }

                if (isMobileDevice()) {
                    var linkElement = document.createElement('link');
                    linkElement.rel = 'preload';
                    linkElement.fetchpriority = 'high';
                    linkElement.as = 'image';
                    linkElement.href = imageMobUrl;
                    linkElement.type = 'image/webp';
                    document.head.appendChild(linkElement);
                }else{
                    var linkElement = document.createElement('link');
                    linkElement.rel = 'preload';
                    linkElement.fetchpriority = 'high';
                    linkElement.as = 'image';
                    linkElement.href = imageDescUrl;
                    linkElement.type = 'image/webp';
                    document.head.appendChild(linkElement);
                }

                if(v.bookTitle) {
                    var bookTitle = "/" + replaceAll(replaceAll(v.bookTitle, ' ', '-').toLowerCase(), '\'', '');
                }
                var bookHyperLink = bookTitle + "/ebook-details?siteName=${session['entryController']}&bookId=" + v.bookId + "&preview=true";

                var actionLink = v.action!=undefined?v.action:"" //action field link
                var serverURL =  window.location.origin //getting base url

                var modified = actionLink.replaceAll('&#92;','\\')
                var modifiedString = modified.replace(/\\u([\d\w]{4})/g, function(match, group1) {
                    return String.fromCharCode(parseInt(group1, 16));
                });
                actionLink = modifiedString

                if(!actionLink.includes("https://"))
                    actionLink = serverURL + '/' + actionLink;



                indicatorStr += "<li data-target='#slider-desktop' class='border-light' data-slide-to='" + i + "'></li>";
                indicatorStrMobile += "<li data-target='#slider-mobile' class='border-light' data-slide-to='" + i + "'></li>";

                if(v.bookId) {
                    htmlStr += '<div class="carousel-item w-100">' +
                        '<a href="' + bookHyperLink + '" target="_blank" class="d-block">' +
                            '<picture >'+
                                '<source media="(min-width: 800px)" srcset="'+imageDescUrl+'" width="2000px" height="326px">';
                                if (v.imagePathMobile!="" && v.imagePathMobile!=null){
                                    htmlStr += '<source media="(min-width: 450px)" srcset="'+imageMobUrl+'" width="375px" height="auto">'+
                                    '<img src="'+imageDescUrl+'" srcset="'+imageMobUrl+'" alt="'+v.imageName+'" width="2000px" height="auto" class="bannerImgPicture">';
                                }else{
                                    htmlStr += '<img src="'+imageDescUrl+'" alt="'+v.imageName+'" width="2000px" height="auto" class="bannerImgPicture">';
                                }
                    htmlStr += '</picture>'+
                        '</a>' +
                        '</div>';
                } else if (v.action) {
                    htmlStr += '<div class="carousel-item w-100">' +
                        '<a href="' + actionLink + '" target="_blank" class="d-block">' +
                        '<picture >'+
                        '<source media="(min-width: 800px)" srcset="'+imageDescUrl+'" width="2000px" height="326px">';
                        if (v.imagePathMobile!="" && v.imagePathMobile!=null){
                            htmlStr += '<source media="(min-width: 450px)" srcset="'+imageMobUrl+'" width="375px" height="auto">'+
                                '<img src="'+imageDescUrl+'" srcset="'+imageMobUrl+'" alt="'+v.imageName+'" width="2000px" height="auto" class="bannerImgPicture">';
                        }else{
                            htmlStr += '<img src="'+imageDescUrl+'" alt="'+v.imageName+'" width="2000px" height="auto" class="bannerImgPicture">';
                        }
                        htmlStr += '</picture>'+
                        '</a>' +
                        '</div>';
                }else{
                    htmlStr += '<div class="carousel-item w-100">' +
                        '<picture >'+
                        '<source media="(min-width: 800px)" srcset="'+imageDescUrl+'" width="2000px" height="326px">';
                        if (v.imagePathMobile!="" && v.imagePathMobile!=null){
                            htmlStr += '<source media="(min-width: 450px)" srcset="'+imageMobUrl+'" width="375px" height="auto">'+
                                '<img src="'+imageDescUrl+'" srcset="'+imageMobUrl+'" alt="'+v.imageName+'" width="2000px" height="auto" class="bannerImgPicture">';
                        }else{
                            htmlStr += '<img src="'+imageDescUrl+'" alt="'+v.imageName+'" width="2000px" height="auto" class="bannerImgPicture">';
                        }
                        htmlStr += '</picture>'+
                        '</div>';
                }

                // If desktop banners are available
                if(v.imagePath) {
                    checkDescBanners = true;
                    $('#slider-desktop-views').append(htmlStr).find('.carousel-item:first-child').addClass('active');
                    $('#slider-desktop-indicators').append(indicatorStr).find('li:first-child').addClass('active');
                }
            });

        } else if(banners.length==0) {
            checkDescBanners = false; checkMobBanners = false;
        }

        // Showing empty banners based on condition
        <%if(session["publisherLogo"]==null){%>
        if(!checkDescBanners && !checkMobBanners) {
            templateDesktop += emptyDesktopBannerUI(defaultDeskImageUrl);
            $('#slider-desktop-views').append(templateDesktop);
            templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
            $('#slider-mobile-views').append(templateMobile);
            //$('.carousel-control-prev, .carousel-control-next').hide();
        } else if(!checkMobBanners) {
            templateMobile += emptyMobileBannerUI(defaultMobImageUrl);
            $('#slider-mobile-views').append(templateMobile);

        }
        <%}else{%>
        if(!checkDescBanners && !checkMobBanners) {
            templateDesktop +=  emptyBannerUI('${session["publisherName"]}','${session["publisherTagLine"]!=null?session["publisherTagLine"]:""}');
            $('#slider-desktop-views').append(templateDesktop);
            templateMobile += emptyBannerUI('${session["publisherName"]}','${session["publisherTagLine"]!=null?session["publisherTagLine"]:""}');
            $('#slider-mobile-views').append(templateMobile);
            //$('.carousel-control-prev, .carousel-control-next').hide();
        } else if(!checkMobBanners) {
            templateMobile += emptyBannerUI('${session["publisherName"]}','${session["publisherTagLine"]!=null?session["publisherTagLine"]:""}');
            $('#slider-mobile-views').append(templateMobile);

        }
        <%}%>

    }

    // If desktop banner images are empty calling this function
    function emptyDesktopBannerUI(defaultImage) {
        var emptyBannerDesk = '<div class="carousel-item w-100 active">' +
            '<img src="'+defaultImage+'" class="w-100 h-auto rounded banner-img" width="1600" height="600" alt="Wonderslate Desktop Banner">' +
            '</div>';
        return emptyBannerDesk;
    }

    // If mobile banner images are empty calling this function
    function emptyMobileBannerUI(defaultImage) {
        var emptyBannerMob = '<div class="carousel-item w-100 active">' +
            '<img src="'+defaultImage+'" class="w-100 h-auto rounded banner-img" width="600" height="350" alt="Wonderslate Mobile Banner">' +
            '</div>';
        return emptyBannerMob;
    }

    // If banner images are empty calling this function
    function emptyBannerUI(publisherName,publisherTagline) {
        var emptyBannerTemplate = "<div class='carousel-item w-100 active rounded'> <div class='d-flex justify-content-center align-items-center w-100 no-banners' style='min-height: 150px;background: #5EC7D7;'>" +
            "<div class='col-12 text-center p-4'>" +
            "<h1>"+publisherName+"</h1>"+
            "<p class='text-dark'>"+publisherTagline+"</p>"+
            "</div>"+
            "</div></div>";
        return emptyBannerTemplate;
    }

    function replaceAll(str, find, replace) {
        if (str == undefined) return str
        else return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
</script>

<%}%>
