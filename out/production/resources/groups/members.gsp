<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="groups/groupsStyle.css" async="true"/>

<style>
.user-info-img{
    width: 80%;
}
.admin-panel-header{
    top: 0;
}
.width-size{
    width: 50% !important;
}
.bt button{
    width: auto;
}
@media only screen and (max-width: 990px) {
    .width-size{
        width: 100% !important;
    }
    .makeAd{
        flex-grow: .95;
    }
    .bt button{
        width: auto;
    }
}
@media only screen and (max-width: 990px) {
    .width-size{
        width: 100% !important;
    }
    .makeAd{
        flex-grow: .95;

    }
}
.cancel-button {
    margin-right:5%;
}
.cancel-button span
{
    font-weight:300;
    color: rgba(68, 68, 68, 0.85);
}
.button-admin-exit{
    display:flex;
    justify-content:center;
    align-items:center;
    width: 30%;
    margin: auto;
    flex-direction: column;
}
button.exitGroupAsAdmin {
    outline:none;
    border:none;
    background: rgba(39, 174, 96, 1);
    color:white;
    width:100%;
    padding:2%;
    border-radius:5px;
    margin-top:10%;
    cursor: pointer;

}
.btn.btn-outline-primary.mt-0.makeAd.active
{
    color:white !important;
    font-weight:500;
}
.btn.btn-outline-primary.mt-0.makeAd:focus
{
    border:1px solid ;
}

@media (max-width:575px)
{

    section.createNewAdmin ul.pl-0 {
        width:90% !important;
    }
    .button-admin-exit
    {
        width:90% !important;
    }
}
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="admin-panel-header mt-4" id="admin-header">
    <div class="container width-size">
        <div class="header-wrapper d-flex align-items-center justify-content-between">
            <a href="javascript:window.location.replace('/groups/groupDtl?groupId=${params.groupId}')" class="back-btn d-flex">
                <img src="${assetPath(src: 'groups/pink-back.svg')}" class="mr-2"> Back
            </a>
        </div>
        <div class="header-title mt-3 d-flex align-items-center">
            <img src="${assetPath(src: 'groups/members.svg')}" class="mr-2">
            <h4 class="mb-0">Members</h4>
        </div>
    </div>
</section>

<section class="reportsection " id="memsssection">
    <div class="container width-size">
        <div class="memsec-header d-flex align-items-center">
            <a href="#" class="mr-2 active p-1" id="gp-ad" onclick="getAdminList()">admins </a>
            <div class="div mr-2"></div>
            <a href="#" class="" id="gp-mem" onclick="getMemberList()">members </a>
        </div>
    </div>
</section>

<section class="admins container width-size mt-3" id="ad-mem-sec">

</section>

<section class="mems container width-size " id="mem-sec">
    <section class="reqList mt-4 container" id="reqList">

    </section>
</section>

<section class="createNewAdmin" >
    <div class="createNewAdminnormaluser d-flex justify-content-center align-items-center" id="createNewAdmin">

    </div>
    <div class="button-admin-exit">
    <button type="button" class="exitGroupAsAdmin"   onclick="openexitgroupmodal()">Submit</button>
    </div>
</section>

<div id="groupUserExitModal" class="modal" tabindex="-1" role="dialog">
    <div class="modal-dialog  modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header border-bottom-0">
                <h5 class="modal-title">Exit Group</h5>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to exit the group?</p>
            </div>
            <div class="modal-footer border-top-0">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="exitFromGroup">Exit</button>
            </div>
        </div>
    </div>
</div>

<script>

    var currentUsername = "${session['userdetails'].username}";
    var imageSrc;

    function getMemberList(e){
        <g:remoteFunction controller="groups" action="viewGroupUsers" params="'groupId='+${params.groupId}" onSuccess="showMembers(data)" />
    }

    function getAdminList(){
        <g:remoteFunction controller="groups" action="viewGroupUsers" params="'groupId='+${params.groupId}" onSuccess="showAdmins(data)" />
    }

    function showMembers(data){

        $(".loading-icon").addClass("hidden");
        var list = data.groups;

        var htmlStr ="";

        if (list == "no records found"){
            htmlStr +=  "<div class=\"p-3 rounded shadow-sm\"><p>No members Found!<p/></div>"
        }else {
            htmlStr += "<ul class='pl-0'>";
            $.each(list,function (index,item){
                if(item.length == 0){
                    document.getElementById("reqList").innerHTML = '<p>No members found!</p>'
                }
                htmlStr += "<li class='d-flex align-items-center'>\n"+
                    "<div class='user-dp mr-3 d-flex align-items-center justify-content-center'>";
                if (item.profilepic != null){
                    imageSrc = "/funlearn/showProfileImage?id="+item.userId+"&fileName="+item.profilepic+"&type=user&imgType=passport";
                    htmlStr +=  "<img src='"+imageSrc+"' class=\"user-info-img\">";
                }else {
                    htmlStr +=   " <img src=\"${assetPath(src: 'groups/bear-1.svg')}\">";
                }
                htmlStr +=   "</div>\n"+
                    "<div class='user-apr'>\n"+
                    "  <div class='usrn mb-3>'\n"+
                    "<p class='mb-0' class='userNameCap'>"+item.name+"</p></div>";
                htmlStr += "<div class='bt d-flex flex-wrap align-items-center mt-2 '>\n";
                if((currentUsername != item.username) && (item.userType == "user" || item.userType == "admin")) {
                    htmlStr +="<button class=\"btn btn-outline-primary mr-2 flex-grow-1\" onclick=\"javascript:removeFromGroup('"+item.username+"');\">Remove from group </button>\n";
                }

                if(item.userType == "user") {
                    htmlStr +="<button class='btn btn-outline-primary mt-0 makeAd' onclick=\"javascript:makeUserAsAdmin('"+item.username+"');\">Make as admin</button>\n";
                }

                htmlStr +="</div>\n"+
                    "</div>";
                htmlStr += "</li>";
            });
            htmlStr += "</ul>"
        }

        document.getElementById("reqList").innerHTML = htmlStr
    }
    function getMemberTomakeAdmin(e){
        <g:remoteFunction controller="groups" action="viewGroupUsers" params="'groupId='+${params.groupId}" onSuccess="showMembersToMakeAdmin(data)" />
    }

    function showMembersToMakeAdmin(data){

        $(".loading-icon").addClass("hidden");
        var list = data.groups;

        var htmlStr ="";

        if (list == "no records found") {
            htmlStr += "<div class=\"p-3 rounded shadow-sm\"><p>No members Found!<p/></div>"
        }
        else  {
            htmlStr += "<ul class='pl-0' style='width:30%'>";
            $.each(list,function (index,item){
                var userRole = item.userType;
                if(item.length == 0){
                    document.getElementById("reqList").innerHTML = '<p>No members found!</p>'
                }


                if((currentUsername != item.username) && (item.userType == "user" )) {
                    htmlStr += "<li class='d-flex align-items-center mt-5'>\n"+
                        "<div class='user-dp mr-3 d-flex align-items-center justify-content-center'>";
                    if (item.profilepic != null){
                        imageSrc = "/funlearn/showProfileImage?id="+item.userId+"&fileName="+item.profilepic+"&type=user&imgType=passport";
                        htmlStr +=  "<img src='"+imageSrc+"' class=\"user-info-img\">";
                    }else {
                        htmlStr +=   " <img src=\"${assetPath(src: 'groups/bear-1.svg')}\">";
                    }
                    htmlStr +=   "</div>\n";
                    htmlStr+= "<div class='user-apr d-flex justify-content-center align-items-center w-100'>\n"+
                    "  <div class='usrn w-100'>\n"+
                    "<p class='mb-0' class='userNameCap'>"+item.name+"</p></div>";
                    htmlStr += "<div class='bt d-flex flex-wrap align-items-center mt-2 '>\n";
                    htmlStr +="<button class='btn btn-outline-primary mt-0 makeAd' onclick='addClassSelected(\""+item.username+"\")' id="+item.username+">Make as admin</button>\n";
                }

                htmlStr +="</div>\n"+
                    "</div>";
                htmlStr += "</li>";
            });
            htmlStr += "</ul>"
        }

        document.getElementById("createNewAdmin").innerHTML = htmlStr;
    }

    getMemberTomakeAdmin();



    getAdminList();

    function showAdmins(data){
        $(".loading-icon").addClass("hidden");
        var adList = data.groups;
        var htmlStr = "";
        if (adList == "no records found"){
            htmlStr +=  "<div class=\"p-3 rounded shadow-sm\"><p>No admins Found!<p/></div>"
        }else {
            htmlStr += "<ul class=\"pl-0 ul d-flex flex-column\">";
            for (var i=0;i<adList.length;i++) {
                currentUsername =  currentUsername.replace("&#64;","@")
                if (adList[i].userType == "admin") {
                    htmlStr += "<li class=\"d-flex align-items-center\">\n" +
                        "<div class=\"d-flex p-2 align-items-center mr-4\">\n" +
                        "<div class=\"userimg d-flex align-items-center justify-content-center mr-2\">";
                    if (adList[i].profilepic != null) {
                        imageSrc = "/funlearn/showProfileImage?id=" + adList[i].userId + "&fileName=" + adList[i].profilepic + "&type=user&imgType=passport";
                        htmlStr += "<img src='" + imageSrc + "' class=\"user-info-img\">";
                    } else {
                        htmlStr += " <img src=\"${assetPath(src: 'groups/bear-1.svg')}\">";
                    }
                    htmlStr += "</div>";
                    htmlStr += "<p class=\"mb-0 usn\">" + adList[i].name + "</p>";
                    htmlStr += "</div>";
                    if ((currentUsername != adList[i].username) && adList[i].userType == "admin") {
                        htmlStr += "<button onclick=\"javascript:removeAsAdmin('" + adList[i].username + "');\" class=\"btn btn-outline-primary\">Remove as admin</button>";
                    }
                    htmlStr += "</li>";
                }
            }
            htmlStr += "</ul>";
        }

        document.getElementById("ad-mem-sec").innerHTML = htmlStr;
    }

    var groupId = ${params.groupId};
    var username;
    var dataObj;
    function makeUserAsAdmin(cusername){
        $(".loading-icon").removeClass("hidden");
        username = cusername;
        dataObj ={
            groupId:groupId,
            username:username
        }
        $.ajax({
            url:"/groups/makeUserAsGroupAdmin",
            type:"POST",
            contentType:"application/json",
            dataType:"json",
            data:JSON.stringify(dataObj),
            success:function (data){
                $(".loading-icon").addClass("hidden");
                getMemberList();
            }
        })
    }

    function removeAsAdmin(username){
        $(".loading-icon").removeClass("hidden");
        username = username;
        dataObj ={
            groupId:groupId,
            username:username
        }
        $.ajax({
            url:"/groups/removeUserAsGroupAdmin",
            type:"POST",
            contentType:"application/json",
            data:JSON.stringify(dataObj),
            success:function (data){
                $(".loading-icon").addClass("hidden");
                getAdminList();
            }
        })
    }

    function removeFromGroup(rusername){
        $(".loading-icon").removeClass("hidden");
        username = rusername;
        <g:remoteFunction controller="groups" action="deleteUserFromGroup" params="'groupId='+groupId+'&username='+username" onSuccess="userRemoved(data)" />
    }

    function userRemoved(data){
        $(".loading-icon").addClass("hidden");
        getMemberList();
    }

</script>

<script>
    $("#mem-sec").hide();

    $("#gp-mem").click(function (e) {
        e.preventDefault();
        $("#mem-sec").show();
        $("#ad-mem-sec").hide();
        $("#gp-mem").addClass("active");
        $("#gp-ad").removeClass("active");
    });

    $("#gp-ad").click(function (e) {
        e.preventDefault();
        $("#mem-sec").hide();
        $("#ad-mem-sec").show();
        $("#gp-mem").removeClass("active");
        $("#gp-ad").addClass("active");
    });



    var urlSeparate=location.search.split('&')[1];

    if (urlSeparate == 'exitFlow=true')
    {
      $('#memsssection').hide();
        $('section#ad-mem-sec').hide();
        $('.createNewAdmin').show();

    }
    else if(urlSeparate != 'exitFlow=true')
    {
        $('#memsssection').show();
        $('section#ad-mem-sec').show();
        $('.createNewAdmin').hide();
    }



    function openexitgroupmodal(){

        var users=$(".btn.btn-outline-primary.mt-0.makeAd.active")
        if(users.length==0){
            $(".button-admin-exit p").empty();
       $('.button-admin-exit').append('<div><p style="color:red;">Please select atleast one user as admin</p></div>');
        }
    else{
            $('#groupUserExitModal').modal('show');
            $("#exitFromGroup").attr('onclick', 'javascript:exitFromGroup();');

        }
    }

    function exitFromGroup(){
        $('.loading-icon').addClass('hidden');
        var users=$(".btn.btn-outline-primary.mt-0.makeAd.active")
        var usernames=""
        for(var i=0;i<users.length;i++){
            usernames+=users[i].id+","
        }
        usernames = usernames.slice(0, -1)
        $(".loading-icon").removeClass("hidden");
        username = usernames;
        dataObj ={
            groupId:groupId,
            username:username
        }
        $.ajax({
            url:"/groups/makeUserAsGroupAdmin",
            type:"POST",
            contentType:"application/json",
            dataType:"json",
            data:JSON.stringify(dataObj),
            success:function (data){
                $(".loading-icon").addClass("hidden");
                <g:remoteFunction controller="groups" action="userExitGroup" params="'groupId='+groupId" onSuccess="userExitedFromGroup(data)" />
            }
        })
    }
    function userExitedFromGroup(data){

        if (data.status =="OK"){
            window.location.href = "/groups/index";
        }
    }
    function addClassSelected(id)
    {
        var classselected = document.getElementById(id);
        $(classselected).toggleClass('active');
    }

</script>

<g:render template="/${session['entryController']}/footer_new"></g:render>