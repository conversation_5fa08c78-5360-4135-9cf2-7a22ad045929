<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta2/css/all.min.css"/>
<asset:stylesheet href="ckeditor5/styles.css"/>
<asset:stylesheet href="groups/groupsStyle.css" />
<style>
.comments-info:not(.comments-info:first-child) {
    display:none;}
p.adminclass.ml-2 {
    font-size: 12px;
    font-style: italic;
    text-align: right;
    color: #ae3691;
    display: inline !important;
    padding: 3px;
    background: rgba(174,054,145,0.2);
    border-radius: 5px;
}
.btn-group.dropleft {
    display:none;
}
</style>
<section class="postList container width-size  p-0">
    <div class='Titlewall m-3 '><a href="javascript:history.back()" class='back-btn d-flex mr-2'> <i class="material-icons-round mr-1">keyboard_backspace</i> Back </a></div>
    <div id="postList" class="postList container">
    </div>
</section>


<g:render template="/books/footer_new"></g:render>
<g:render template="/resources/shareContent"></g:render>

<script>

    var dataObj;
    var groupId;
    var reason;
    var username;
    var currentUsername = "${session['userdetails'].username}";
    var validFileSize = true;
    var validFileFormat = true;
    var fileType = "";
    var imageSrc;
    var postId;
    var description;
    var commentedDate;
    var createCKEditor, updateCKEditor;
    var postImageSrc;
    var postFileSrc;
    var userExist;
    var admin = false;
    var userType
    var pageNo = 0;
    var tempPageNo;
    var icon;
    var commentUserName;
    var fileEmpty = false;
    var postLikedState;
    var postCount
    var postedDate;
    var joined = false;
    var groupType = 'group';
    var currentDate;
    var checkPostedDate = false;
    var checkCommentedDate = false;
    var pinned = false;
    var dict = {};

    groupId=location.search.split('groupId=')[1].split('&')[0];
    postId =location.search.split('postId=')[1].split('/')[0];



    function groupDetails(){
        $(".loading-icon").removeClass("hidden");
        dataObj = {
            groupId: groupId,
        };
        $.ajax({
            url: "/groups/getGroupDetailsById",
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                showGroupBasicDetails(data);
            },
        });
    }

    groupDetails();

    function showGroupBasicDetails(data) {
        var privilage = data.allPost;
        userType = data.userType;
        userExist = data.userExist;
        var privacyType = data.privacyType.toLowerCase();


        if (userType == 'admin' || userType == 'super_admin' ) {
            admin = true;
            $("#requestJoinGroup, #joinGroup, .joined-btn").css("display", "none");
            $('#showCoverImage').show();
        } else if (username != currentUsername) {
            $(".joined-btn").css("display", "flex");
            $("#admin").css("display", "none");
        } else if (privacyType == "private") {
            $("#requestJoinGroup").css("display", "flex");
            $("#joinGroup").css("display", "none");
        } else if (privacyType == "public") {
            $("#joinGroup").css("display", "flex");
            $("#requestJoinGroup").css("display", "none !important");
            $("#admin").hide();
        }
        if (privilage == "false" && userType != 'admin') {
            $('#privilage').hide();
        }

        if (userExist == 'yes') {
            joined = true;
            $("#joinGroup").css("display", "none");
            $("#requestJoinGroup").css("display", "none");
            $("#invite-btn").css("display", "flex");
            $(".emptyState").removeClass("d-flex").addClass("d-none")
            $("#unjoinedPrivate").addClass("d-none").removeClass("d-flex")
        }
        if (userExist == 'yes' && currentUsername == username && userType != 'admin' ) {
            joined = true;
            $("#joinGroup").css("display", "none");
            $("#requestJoinGroup").css("display", "none");
            $("#invite-btn").css("display", "flex");
            $(".emptyState").removeClass("d-flex").addClass("d-none")
            $("#unjoinedPrivate").addClass("d-none").removeClass("d-flex")
            $(".joined-btn").css("display", "flex");

        }
        if (userExist == 'yes' && privacyType == "private" && userType != 'admin') {
            $("#admin").hide();
            $(".joined-btn").css("display", "show");
        }
        if (privacyType == "private" && userExist == 'No') {
            $("#joinGroup").css("display", "none");
            $("#requestJoinGroup").css("display", "flex");
            $(".invite-btn").css("display", "none");
            $("#postList").hide();
            document.getElementById("postList").innerHTML = "";
            $("#showMore").hide();
            $(".statusText").text("This is a private group")
            $("#lock").addClass("fa-lock").removeClass("fa-lock-open")
        }
        if (privacyType == "public" && userExist == 'No') {
            $("#requestJoinGroup").css("display", "none");
            $(".invite-btn").css("display", "flex");
            $("#privilage").hide();
            $("#joinGroup").css("display", "flex");
            $(".joined-btn").css("display", "none");
            $("#unjoinedPrivate").addClass("d-none").removeClass("d-flex")
        }
        else if (userExist == 'No') {
            $(".textarea-container").hide();
            $(".joined-btn").css("display", "none");
            $("#invite-btn").css("display", "none");
            $(".emptyState").show()
            $("#showMorePosts").hide()
            $("#admin").hide();
        }
        if(data.groupType == 'channel')
        {
            groupType = 'channel';
            $("#invite-btn").css("display", "none");
            $('.header-Message.mt-4').hide();
            $(".joined-btn").css("display", "none");
            $(".btn-group.dropleft").css("display", "none");
            $("#admin").hide();
            $(".groupBanner").hide();
            $(".admin-header").hide();
            $("#slider-parent").css("display", "block");
        }
        if(data.groupType != 'channel' && userExist == 'yes'){
            $("#slider-parent").css("display", "block");
        }

        if(userType == 'super_admin' && data.groupType != 'channel' )
        {
            $('#privilage').hide();
            $(".joined-btn").css("display", "none");
            $("#admin").hide();
            $('.adoptionslist ul li:nth-child(5)').hide();
            $('.adoptionslist ul li:nth-child(4)').hide();
            $('.adoptionslist ul li:nth-child(3)').hide();
            $('.adoptionslist ul li:nth-child(2)').hide();
            $("#invite-btn").css("display", "none");
            $("label#showCoverImage").css("display", "none");

        }
    }



$(window).on('load', function(){
 groupId=location.search.split('groupId=')[1].split('&')[0];
 postId =location.search.split('postId=')[1].split('/')[0];
    getPostDetails();
})
function getPostDetails(){
    <g:remoteFunction controller="groups" action="getCompletePostDetails" params="'groupId='+groupId+'&postId='+postId" onSuccess='showpost(data);' />

}
function showpost(data)
{
    var htmlStr="";
    var postDetails= data.groupPostDetails;
    for (var i=0;i<postDetails.length;i++){
        var comments = postDetails[i].comments;
        var commentsCount = jQuery.parseJSON(postDetails[i].commentsCount);
        dict[postDetails[i].id] = 0;
         username = postDetails[i].createdBy;
        var pinnedpost = postDetails[i].pinned;
         postCount = numberFormatter(postDetails[i].postLikesCount)
        postedDate  = postDetails[i].dateCreated;
        currentDate = moment(postedDate).utc().format('YYYY-MM-DD');
        checkPostedDate = moment().isSame(currentDate, 'day');
        if(checkPostedDate) {
            postedDate = moment(postedDate).fromNow();
        } else {
            postedDate = moment(postedDate).format('lll');
        }

        htmlStr = htmlStr + "<div class=\"posts-card p-4 mt-3\">\n"
        if(postDetails[i].userType == 'admin' && groupType != 'channel')
        {
            htmlStr +="<div class='admin-header' style='text-align: right;'><p class=\"adminclass ml-2  \">"+postDetails[i].userType+"</p></div>"
        }
        htmlStr += "<div class=\"d-flex align-items-center justify-content-between\">\n" +
            "<div class=\"user-info d-flex align-items-center\">\n";

        if(postDetails[i].profilepic != null) {
            // imageSrc = "/funlearn/showProfileImage?id="+postDetails[i].userId+"&fileName="+postDetails[i].profilepic+"&type=user&imgType=passport";
            imageSrc = "/funlearn/showProfileImage?id="+postDetails[i].userId+"&fileName="+postDetails[i].profilepic+"&type=user&imgType=passport";
        } else {
            imageSrc = "${assetPath(src: 'landingpageImages/img_avatar3.png')}";
        }
        htmlStr += "<img src='"+imageSrc+"' class=\"user-info-img mr-3\">";

        if (postDetails[i].name == null){
            htmlStr +="<p class=\"mr-2 text-capitalize\">Unknown user</p>";
        }
        else if(postDetails[i].userType == 'admin' && groupType=="channel" ){
            htmlStr +="<p class=\"mr-2 text-capitalize\">Admin</p>";
        }
        else {
            htmlStr +="<p class=\"mr-2 text-capitalize\">"+postDetails[i].name+"</p>";
        }

        htmlStr +=   "<p class='time'>"+postedDate+"</p>";

        htmlStr +="</div>";

        htmlStr += " <div class=\"btn-group dropleft\">\n"

        if(groupType!="channel") {
            htmlStr += "<button type=\"button\" class=\"btn dropdown-toggle p-0\" data-toggle=\"dropdown\">\n" +
                "<img src=\"${assetPath(src: 'groups/mdi_more_vert.svg')}\">\n" +
                "</button>";
        }
        if(groupType=="channel" && userType=="admin") {
            htmlStr += "<button type=\"button\" class=\"btn dropdown-toggle p-0\" data-toggle=\"dropdown\">\n" +
                "<img src=\"${assetPath(src: 'groups/mdi_more_vert.svg')}\">\n" +
                "</button>";
        }
        htmlStr += "<div class=\"dropdown-menu\">";


        if(currentUsername != username && !admin) {
            htmlStr += "<ul class=\"px-3 mb-0 d-flex flex-column justify-content-center\">\n" +
                "<li>\n" +
                "<a href=\"javascript:showPostReportModal(" + postDetails[i].id + ")\">\n" +
                " <img src=\"${assetPath(src: 'groups/flag.svg')}\" class=\"mr-2\">Report post\n" +
                "</a>\n" +
                "</li>";
        }
        if(admin || currentUsername == username) {
            htmlStr += "<li>\n" +
                "<a href=\"javascript:deletePostModal(" + postDetails[i].id + ")\" class=\"deletePostBtn\">\n" +
                "<img src=\"${assetPath(src: 'groups/trash.svg')}\" class=\"mr-2\">Delete post\n" +
                "</a>\n" +
                "</li>";
        }
        if(admin ) {
            if(pinnedpost == "true"){
                htmlStr += "<li>\n" +
                    "<a href=\"javascript:pinPost(" + postDetails[i].id + ","+ postDetails[i].pinned+")\" class=\"unpinPostBtn\">\n" +
                    "<img src=\"${assetPath(src: 'groups/pin.svg')}\" class=\"mr-2\">Unpin post\n" +
                    "</a>\n" +
                    "</li>";
            }
            else {
                htmlStr += "<li>\n" +
                    "<a href=\"javascript:pinPost(" + postDetails[i].id + ","+ postDetails[i].pinned+")\" class=\"pinPostBtn\">\n" +
                    "<img src=\"${assetPath(src: 'groups/pin.svg')}\" class=\"mr-2\">Pin post\n" +
                    "</a>\n" +
                    "</li>";
            }

        }
        if ((currentUsername == username || (currentUsername == username && admin ) )&& userType!="super_admin"){
            htmlStr += "<li>\n" +
                "<a href=\"javascript:editPostModal(" + postDetails[i].id + ")\">\n" +
                "<img src=\"${assetPath(src: 'groups/edit-post.svg')}\" class=\"mr-2\">Edit post\n" +
                "</a>\n" +
                "</li>";
        }
        if(currentUsername != username && !admin  && postDetails[i].userType !='admin') {
            var userName = postDetails[i].createdBy;
            htmlStr += "<li>\n"+
                "<a href=\"javascript:showReportUserModal('"+userName+"');\">\n"+
                "<img src=\"${assetPath(src: 'groups/report-user.svg')}\" class=\"mr-2\">Report user\n"+
                "</a>\n"+
                "</li>";
        }
        htmlStr += "</ul></div></div></div>";
        htmlStr += "<div class='mb-3 mt-3' style='word-break: break-word'>\n";
        if(postDetails[i].description != null) {

            var description = createTextLinks_(postDetails[i].description);
            htmlStr += "<p class=\"post-description\" >" + description + "</p>\n";


        }

        if(postDetails[i].postImage != null) {
            postImageSrc = "/groups/showGroupPostImage?id="+postDetails[i].id+"&fileName="+postDetails[i].postImage;
            htmlStr += "<img  src='"+postImageSrc+"' class=\"post-img mb-3 mt-3\">\n";
        }

        if(postDetails[i].fileName != null) {
            postFileSrc = "/groups/downloadPostFile?id="+postDetails[i].id;
            htmlStr += "<a href='"+postFileSrc+"'>"+postDetails[i].fileName+" (Click to download file)</a>\n";
        }

        htmlStr += "</div>";


        htmlStr += "<div class=\"d-flex justify-content-between\">\n";
        htmlStr +="<div class=\"postOptions d-flex align-items-center\">\n";

        if(postCount>0){
            htmlStr +="<span style=\"font-size: 10px;color:rgba(68, 68, 68, 0.85);font-weight: 600;\">"+postCount+"</span>\n";
        }

        htmlStr +="<button class='d-flex align-items-center' onclick=\"javascript:likePost("+postDetails[i].id+","+postDetails[i].likedPost+");\" id=\"likingPost\">\n";
        if(postDetails[i].likedPost == "true") {
            htmlStr +=   "<img src=\"${assetPath(src: 'groups/like.svg')}\" id=\"likeImg\">"+
                "<span style='font-size: 11px;color:#2F80ED;margin-left: 5px;'>Liked!</span>";
        } else {
            htmlStr +=   "<img src=\"${assetPath(src: 'groups/unlike.svg')}\" id=\"likeImg\">"+
                "<span style='font-size: 11px;color:rgba(68, 68, 68, 0.85);margin-left: 5px;'>Like</span>";
        }
        htmlStr += "</button>";
        htmlStr += "</div>";
        htmlStr += "<div class=\"comment-bttn\">";
        htmlStr += "<button onclick=\"javascript:showComments(" + postDetails[i].id + ")\">\n";

        htmlStr +="<img src=\"${assetPath(src: 'groups/comment.svg')}\">\n"+
            " </button>";
        htmlStr += "</div></div>";

        htmlStr += "<div id='comments"+postDetails[i].id+"'>"+commentsUI(postDetails[i].id,comments)+"</div></div>";

    }

    document.getElementById("postList").innerHTML = htmlStr;


    if(postDetails.length>=10){
        $("#showMore").show();
    }
    var anchors = document.querySelectorAll('.post-description a');
    for (var i=0; i<anchors.length; i++){
        anchors[i].setAttribute('target', '_blank');
    }

}

    function numberFormatter(count) {
        if (count < 1e3) return count;
        if (count >= 1e3 && count < 1e6) return +(count / 1e3).toFixed(1) + "k";
        if (count >= 1e6 && count < 1e9) return +(count / 1e6).toFixed(1) + "M";
        if (count >= 1e9 && count < 1e12) return +(count / 1e9).toFixed(1) + "B";
        if (count >= 1e12) return +(count / 1e12).toFixed(1) + "T";
        return count;
    }
    function createTextLinks_(text) {
        return (text || "").replace(
            /([^\S]|^)(((https?\:\/\/)|(www\.))(\S+))/gi,
            function (match, space, url) {
                var hyperlink = url;
                if (!hyperlink.match("^https?://")) {
                    hyperlink = "http://" + hyperlink;
                }
                return space + '<a href="' + hyperlink + '">' + url + "</a>";
            }
        );
    }


    function commentsUI(id,comments) {
        var commentsTemplate = "";
        var replyText = "";
        commentsTemplate += "<div class=\"comments-sec mt-2 p-3\"><div id='inside-comments"+id+"' class='inside-comments'>";
        if(comments.length>0) {
            for (var i = 0; i < comments.length; i++) {
                commentedDate = comments[i].dateCreated;
                currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                checkCommentedDate = moment().isSame(currentDate, 'day');
                if(checkCommentedDate) {
                    commentedDate = moment(commentedDate).fromNow();
                } else {
                    commentedDate = moment(commentedDate).format('lll');
                }
                commentsTemplate += "<div class=\"comments-info\">" +
                    "<div class=\"user-info d-flex align-items-center\">";
                if(comments[i].profilepic != null) {
                    imageSrc = "/funlearn/showProfileImage?id="+comments[i].userId+"&fileName="+comments[i].profilepic+"&type=user&imgType=passport";
                } else {
                    imageSrc = "${assetPath(src: 'landingpageImages/img_avatar3.png')}";
                }
                commentsTemplate += "<img src="+imageSrc+" class=\"user-info-img mr-2\">";
                if(comments[i].name != null){
                    commentUserName = comments[i].name
                }else{
                    commentUserName = "Unknown user";
                }
                commentsTemplate +=  "<p class=\"mr-2 text-capitalize\" style=\"font-size: 12px;color: #444;\">"+commentUserName+"</p>" +
                    "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>" +
                    "</div>" +
                    "<div class=\"ml-5\" >" +
                    "<p style=\"font-size: 14px;color: #000;\">"+comments[i].description+"</p>" +
                    "</div>";

                commentsTemplate += "<div class=\"d-flex reply-sec ml-5 mb-2\">";

                var replies = comments[i].repliedDetailsByCommentsId;
                var repliesCount = jQuery.parseJSON(comments[i].repliedCount);
                if(repliesCount>0) {
                    if(repliesCount==1) {
                        replyText = "Reply";
                    } else {
                        replyText = "Replies";
                    }
                    commentsTemplate += "<p><a style='font-size: 11px;font-style: normal;' href='javascript:showReply("+comments[i].id+");'>"+numberFormatter(repliesCount)+" "+replyText+"</a></p>";
                }

                commentsTemplate += "<button onclick=\"javascript:replyTo('"+comments[i].name+"',"+comments[i].id+","+id+");\">Reply</button>"+
                    "</div>";

                commentsTemplate += "<div style=\"display: none;border-left: 1px solid #ddd;\" id=\"replies"+comments[i].id+"\" class=\"ml-5 mt-3 pl-3\">";

                for (var j = 0; j < replies.length; j++) {
                    commentedDate = replies[j].dateCreated;
                    currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                    checkCommentedDate = moment().isSame(currentDate, 'day');
                    if(checkCommentedDate) {
                        commentedDate = moment(commentedDate).fromNow();
                    } else {
                        commentedDate = moment(commentedDate).format('lll');
                    }
                    commentsTemplate += "<div class='reply-info pb-3'><div class=\"d-flex align-items-center mb-1\">"+
                        "<p style=\"font-size: 12px;color: #444;\" class=\"mr-2 text-capitalize\">"+(replies[j].name?replies[j].name:'Unknown User')+"</p>"+
                        "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>"+
                        "</div>"+
                        "<p style=\"font-size: 14px;color: #000;\">"+replies[j].description+"</p></div>";
                }
                commentsTemplate += "</div></div>";
            }
            commentsTemplate += "</div><div class=\"mt-3 comment-input-box\">"+
                "<div id='replyToText"+id+"' style='display:none;'></div>"+
                "<div class=\"input-group\" >"+
                "<input type=\"text\" id=\"commentText"+id+"\" class=\"comment-input col\" maxLength='500'   placeholder=\"Write your comment\">"+
                "<div class=\"input-group-append\" oninput='javascript:forcejoin()'>"+
                "<button class=\"btn btn-success btn-sm d-flex align-items-center\" type=\"button\" id='sendCommentBtn"+id+"' onclick='sendComment("+id+")' style=\"border-radius:0 10px 10px 0;border:0;\">"+
                "<i class=\"material-icons\" style=\"font-size: 18px;\">send</i>"+
                "</button>"+
                "</div>"+
                "</div>"+
                "<p id='errorComment"+id+"' class='form-text error-text text-danger' style='display: none;'>Please enter comment.</p>"+
                "</div>";
            if((comments.length>1)) {
                commentsTemplate+=  "<div class='text-center hideShowMore'>" +
                    "<a id='firstNine" + id + "' style='cursor:pointer;padding-top:3%;font-size: 14px;margin-bottom: 5px;color: #007bff;font-weight:600;' onclick='showfirstnine(" + id + ")' >Show more</a>" +
                    "<a id='showNext' class='showNext" + id + "' style='cursor:pointer;padding-top:3%;font-size: 14px;margin-bottom: 5px;color: #007bff;font-weight:600;display:none;' onclick='shownextten(" + id + ")'  >Show more</a></div>";
            }
        } else {
            commentsTemplate += "<img src='${assetPath(src: 'groups/speech.gif')}' class='noComt'/>\n"+
                "<p style='font-size: 13px;text-align: center;'>No comments found!</p>";
            commentsTemplate += "</div><div class=\"mt-3 comment-input-box\">"+
                "<div id='replyToText"+id+"' style='display:none;'></div>"+
                "<div class=\"input-group\" oninput='javascript:forcejoin()'>"+
                "<input type=\"text\" id=\"commentText"+id+"\" class=\"comment-input col\" maxLength='500'   placeholder=\"Write your comment\">"+
                "<div class=\"input-group-append\" oninput='javascript:forcejoin()'>"+
                "<button class=\"btn btn-success btn-sm d-flex align-items-center\" type=\"button\" id='sendCommentBtn"+id+"' onclick='sendComment("+id+")' style=\"border-radius:0 10px 10px 0;border:0;\">"+
                "<i class=\"material-icons\" style=\"font-size: 18px;\">send</i>"+
                "</button>"+
                "</div>"+
                "</div>"+
                "<p id='errorComment"+id+"' class='form-text error-text text-danger' style='display: none;'>Please enter comment.</p>"+
                "</div>";
        }


        commentsTemplate += "</div>";
        return commentsTemplate;
    }
    function showComments(id){
        $("#postList #comments"+id).slideToggle("show");
        $("#postList #commentText"+id).focus();
    }


    function sendComment(id) {
        dict[id]=0;

        description = $("#commentText"+id).val().trim();
        description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if(description == ""){
            description = $("#postList #commentText"+id).val().trim();
            description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
            var fromPostList = true;
        }
        dataObj = {
            groupId: groupId,
            postId: id,
            description: description,
        };
        if(description == "" || description == "null" || description == null) {
            $("#errorComment"+id).show();
        } else {
            $.ajax({
                url: "/groups/commentPostOfGroup",
                type: "POST",
                contentType: "application/json",
                dataType:"json",
                data: JSON.stringify(dataObj),
                success: function (data) {
                    if(data.status == "OK") {
                        $("#commentText"+id).val("");
                        getLatestComments(id);
                        commentPostId=id;
                        if (fromPostList == true){
                            $("#postList #inside-comments"+id).scrollTop($("#postList #inside-comments"+id)[0].scrollHeight);
                        }
                        else $("#post-slider #inside-comments"+id).scrollTop($("#post-slider #inside-comments"+id)[0].scrollHeight);

                    }
                },
            });
        }
    }

    function replyComment(commentId,id) {

        description = $("#commentText"+id).val().trim();
        description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
        if(description == ""){
            description = $("#postList #commentText"+id).val().trim();
            description = description.replace(/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, '');
            var fromPostList = true;
        }
        dataObj = {
            groupId: groupId,
            postId: id,
            commentId: commentId,
            description: description,
        };
        if(description == "" || description == "null" || description == null) {
            document.getElementById("errorMsg").innerHTML = '<p>Please enter comment.</p>';
            $("#errorMsg").addClass("show").removeClass("hide");
            setTimeout(function(){ $("#errorMsg").removeClass("show").addClass("hide"); }, 5000);
        } else {
            $.ajax({
                url: "/groups/replyForCommentOfGroup",
                type: "POST",
                contentType: "application/json",
                dataType:"json",
                data: JSON.stringify(dataObj),
                success: function (data) {
                    if(data.status == "OK") {
                        getLatestComments(id);
                        commentPostId=id;
                        $("#commentText"+id).val("");
                        cancelReply(commentId,id);

                        if (fromPostList == true){
                            $("#postList #inside-comments"+id).scrollTop($("#postList #inside-comments"+id)[0].scrollHeight);
                        }
                        else $("#post-slider #inside-comments"+id).scrollTop($("#post-slider #inside-comments"+id)[0].scrollHeight);

                    }
                },
            });
        }
    }

    function getLatestComments(id) {
        var commentsPageNo= dict[id];
        <g:remoteFunction controller="groups" action="getCommentListForPost" params="'postId='+id+'&pageNo='+commentsPageNo" onSuccess="showLatestComments(data,id)" />
    }

    function showLatestComments(data,id) {
        var commentsList = data.commentsList;

        $("#totalComments"+commentPostId).text(commentsList.length);
        $(' #comments'+commentPostId).html(commentsUI(commentPostId,commentsList));
    }
    function showReply(id){
        $("#post-slider #replies"+id).slideToggle("show");
        $("#postList #replies"+id).slideToggle("show");
    }
    function replyTo(name,commentId,id) {
        $("#commentText"+id).focus();
        $('#sendCommentBtn'+id).attr('onclick','javascript:replyComment('+commentId+','+id+');');
        document.getElementById("replyToText"+id).innerHTML = "<p style='font-size: 12px;font-style: italic;margin-bottom: 5px;color: #777;'>Replying to "+name +
            "<a style='font-size: 13px;padding-left: 5px;font-weight: 600;' href='javascript:cancelReply("+commentId+","+id+");'>Cancel</a></p>";
        $("#replyToText"+id).show();
        $("#postList #commentText"+id).focus();
        $('#postList #sendCommentBtn'+id).attr('onclick','javascript:replyComment('+commentId+','+id+');');
        document.querySelector("#postList #replyToText"+id).innerHTML = "<p style='font-size: 12px;font-style: italic;margin-bottom: 5px;color: #777;'>Replying to "+name +
            "<a style='font-size: 13px;padding-left: 5px;font-weight: 600;' href='javascript:cancelReply("+commentId+","+id+");'>Cancel</a></p>";
        $("#postList #replyToText"+id).show();
    }

    function cancelReply(commentId,id) {
        $('#sendCommentBtn'+id).attr('onclick','javascript:sendComment('+id+');');
        document.getElementById("replyToText"+id).innerHTML = "";
        $("#replyToText"+id).hide();
    }
    function likePost(id,likedPost) {
        $(".loading-icon").addClass("hidden");
        postId = id;
        if(!likedPost) {
            <g:remoteFunction controller="groups" action="likePostOfGroup" params="'groupId='+groupId+'&postId='+postId" onSuccess="postLikedOrDisliked(data)"/>
        } else if(likedPost) {
            <g:remoteFunction controller="groups" action="dislikePostOfGroup" params="'groupId='+groupId+'&postId='+postId" onSuccess="postLikedOrDisliked(data)" />

        }
    }
    function postLikedOrDisliked(data){
        $(".loading-icon").addClass("hidden");
        getPostDetails();
    }
    function showfirstnine(id){

        $('#inside-comments'+id+' .comments-info').css('display','block');
        $('.showNext'+id).css('display','block ');
        $('#firstNine'+id).css('display','none');

    }
    function shownextten(id){
        dict[id]+=1;
        var commentsPageNo= dict[id];
        commentPostId=id;
        <g:remoteFunction controller="groups" action="getCommentListForPost" params="'postId='+id+'&pageNo='+commentsPageNo" onSuccess="showPaginatedComments(data,id)" />
    }
    function showPaginatedComments(data,id) {
        var commentsList = data.commentsList;


        if(commentsList.length>0 && Array.isArray(commentsList)) {
            $("#totalComments" + commentPostId).text(commentsList.length);
            $("#comments" + commentPostId + " #inside-comments" + commentPostId).append(commentsNewUI(commentPostId, commentsList));
            $('#inside-comments'+id+' .comments-info').css('display','block');
        }
        else  $('.showNext'+id).css('display','none ');
    }
    function commentsNewUI(id,comments) {
        var commentsTemplate = "";
        var replyText = "";
        if(comments.length>0) {
            for (var i = 0; i < comments.length; i++) {
                commentedDate = comments[i].dateCreated;
                currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                checkCommentedDate = moment().isSame(currentDate, 'day');
                if(checkCommentedDate) {
                    commentedDate = moment(commentedDate).fromNow();
                } else {
                    commentedDate = moment(commentedDate).format('lll');
                }
                commentsTemplate += "<div class=\"comments-info\">" +
                    "<div class=\"user-info d-flex align-items-center\">";
                if(comments[i].profilepic != null) {
                    imageSrc = "/funlearn/showProfileImage?id="+comments[i].userId+"&fileName="+comments[i].profilepic+"&type=user&imgType=passport";
                } else {
                    imageSrc = "${assetPath(src: 'landingpageImages/img_avatar3.png')}";
                }
                commentsTemplate += "<img src="+imageSrc+" class=\"user-info-img mr-2\">";
                if(comments[i].name != null){
                    commentUserName = comments[i].name
                }else{
                    commentUserName = "Unknown user";
                }
                commentsTemplate +=  "<p class=\"mr-2 text-capitalize\" style=\"font-size: 12px;color: #444;\">"+commentUserName+"</p>" +
                    "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>" +
                    "</div>" +
                    "<div class=\"ml-5\" >" +
                    "<p style=\"font-size: 14px;color: #000;\">"+comments[i].description+"</p>" +
                    "</div>";

                commentsTemplate += "<div class=\"d-flex reply-sec ml-5 mb-2\">";

                var replies = comments[i].repliedDetailsByCommentsId;
                var repliesCount = jQuery.parseJSON(comments[i].repliedCount);
                if(repliesCount>0) {
                    if(repliesCount==1) {
                        replyText = "Reply";
                    } else {
                        replyText = "Replies";
                    }
                    commentsTemplate += "<p><a style='font-size: 11px;font-style: normal;' href='javascript:showReply("+comments[i].id+");'>"+numberFormatter(repliesCount)+" "+replyText+"</a></p>";
                }

                commentsTemplate += "<button onclick=\"javascript:replyTo('"+comments[i].name+"',"+comments[i].id+","+id+");\">Reply</button>"+
                    "</div>";

                commentsTemplate += "<div style=\"display: none;border-left: 1px solid #ddd;\" id=\"replies"+comments[i].id+"\" class=\"ml-5 mt-3 pl-3\">";

                for (var j = 0; j < replies.length; j++) {
                    commentedDate = replies[j].dateCreated;
                    currentDate = moment(commentedDate).utc().format('YYYY-MM-DD');
                    checkCommentedDate = moment().isSame(currentDate, 'day');
                    if(checkCommentedDate) {
                        commentedDate = moment(commentedDate).fromNow();
                    } else {
                        commentedDate = moment(commentedDate).format('lll');
                    }
                    commentsTemplate += "<div class='reply-info pb-3'><div class=\"d-flex align-items-center mb-1\">"+
                        "<p style=\"font-size: 12px;color: #444;\" class=\"mr-2 text-capitalize\">"+(replies[j].name?replies[j].name:'Unknown User')+"</p>"+
                        "<p style=\"font-size: 11px;color: rgba(68, 68, 68, 0.48);font-style: italic;\">"+commentedDate+"</p>"+
                        "</div>"+
                        "<p style=\"font-size: 14px;color: #000;\">"+replies[j].description+"</p></div>";
                }
                commentsTemplate += "</div></div>";
            }
        } else {
            commentsTemplate += "<img src='${assetPath(src: 'groups/speech.gif')}' class='noComt'/>\n"+
                "<p style='font-size: 13px;text-align: center;'>No comments found!</p>";
            hideCommentShowMore();
        }

        commentsTemplate += "</div><div class=\"mt-3 comment-input-box\">"+
            "<div id='replyToText"+id+"' style='display:none;'></div>"+
            "<div class=\"input-group\" >"+
            "</div>"+
            "</div>"+
            "<p id='errorComment"+id+"' class='form-text error-text text-danger' style='display: none;'>Please enter comment.</p>"+
            "</div>";

        commentsTemplate += "</div>";
        return commentsTemplate;
    }

</script>