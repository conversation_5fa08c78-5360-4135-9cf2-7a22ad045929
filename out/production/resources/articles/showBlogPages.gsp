<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information-admin p-5">
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <h3><strong>Syllabus Level English blogs</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">


            <div id="addInformation" class="mt-4 row justify-content-center align-items-start" >
                <div class="col-12">
                    <%  int i=0
                        syllabusEnglishBlogs.each {blog->
                            i++%>

                    <p>${i}.&nbsp;<a href ="/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').replace('/','-').toLowerCase()}/information/s/${blog.id}" target="_blank">${blog.syllabus}</a>&nbsp;&nbsp;
                    <a href ="/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').replace('/','-').toLowerCase()}/information/s/${blog.id}?videoMode=true" target="_blank">Create Video</a>
                </p>


                       <% }
                    %>
                </div>
            </div>

        </div>
    </div><br><br>
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <h3><strong>Syllabus Level Hindi blogs</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">


            <div  class="mt-4 row justify-content-center align-items-start" >
                <div class="col-12">
                    <%   i=0
                    syllabusHindiBlogs.each {blog->
                        i++%>
                    <p>${i}.&nbsp;<a href ="/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').replace('/','-').toLowerCase()}/hindi/s/${blog.id}" target="_blank">${blog.syllabus}</a></p>
                    <% }
                    %>
                </div>
            </div>

        </div>
    </div>

    <br><br>
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <h3><strong>Grade Level English blogs</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">


            <div  class="mt-4 row justify-content-center align-items-start" >
                <div class="col-12">
                    <%   i=0
                    gradeEnglishBlogs.each {blog->
                        i++%>

                    <p>${i}.&nbsp;<a href ="/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').replace('/','-').toLowerCase()}-${("" + blog.grade).replaceAll(' ', '-').replace('/','-').toLowerCase()}/information/g/${blog.id}" target="_blank">${blog.syllabus}-${blog.grade}</a>
                    <a href ="/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').replace('/','-').toLowerCase()}-${("" + blog.grade).replaceAll(' ', '-').replace('/','-').toLowerCase()}/information/g/${blog.id}?videoMode=true" target="_blank">Create Video</a></p>

                    <% } %>
                </div>
            </div>

        </div>
    </div>
    <br><br>
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <h3><strong>Grade Level Hindi blogs</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">


            <div  class="mt-4 row justify-content-center align-items-start" >
                <div class="col-12">
                    <%   i=0
                    gradeHindiBlogs.each {blog->
                        i++%>

                    <p>${i}.&nbsp;<a href ="/${("best-books-for-" + blog.syllabus).replaceAll(' ', '-').replace('/','-').toLowerCase()}-${("" + blog.grade).replaceAll(' ', '-').replace('/','-').toLowerCase()}/hindi/g/${blog.id}" target="_blank">${blog.syllabus}-${blog.grade}</a></p>

                    <% } %>
                </div>
            </div>

        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>




</body>
</html>
