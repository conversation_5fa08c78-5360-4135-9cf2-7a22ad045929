<%@ page import="com.wonderslate.data.ResourceType" %>
<!doctype html>
<html lang="en" >
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Wonderslate</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
    <asset:stylesheet href="clock.css"/>
</head>
<%
    String url = (request.getRequestURL()).toString();
    String serverUrl = url.substring(0,url.indexOf('/',9));
    session.setAttribute("syllabusType","school");
    session.setAttribute("country","India");    
%>
<body>
<nav class="navbar navbar-default navbar-fixed-top">
    <div style="padding-top:10px; padding-bottom:10px;" class="container">
        <div class="navbar-header">
            <span class="brand">
                <a style="padding-top:5px;" class="navbar-brand" href="/">
                    <img alt="brand" src="${assetPath(src: 'logo-ws.png')}">WONDERSLATE
                </a>
            </span>
        </div>        
        <div class="hidden-xs row row-right">
        <g:form name="searchForm">
            <div class="col-md-2  col-sm-6 col-xs-12 col-centered">
                <select required id="selectedBoard" name="selectedBoard" class="form-control" onchange="javascript:changeGrade(this.value)">
                    <option value=""  selected>Board</option>

                </select>
            </div>
            <div class="col-md-2 col-sm-6 col-xs-12 col-centered">
                <select required id="selectedGrade" name="selectedGrade"  class="form-control" onchange="javascript:changeSubject(this.value)">
                    <option value="" selected >Grade</option>
                </select>
            </div>
            <div class="col-md-2 col-sm-6 col-xs-12 col-centered">
                <select required id="selectedSubject" name="selectedSubject" class="form-control" onchange="javascript:changeTopic(this.value)">
                    <option value=""  selected >Subject</option>
                </select>
            </div>
            <div class="col-md-2 col-sm-6 col-centered col-xs-12">
                <select required id="selectedTitleAndId" name="selectedTitleAndId" class="form-control" onchange="javascript:loadDetailsPage(this)">
                    <option value=""  selected >Topic/Chapter</option>
                </select>
            </div>
        </g:form>        
        </div>
    </div>
</nav>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <button type="button" class="close" data-dismiss="modal"  aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <iframe width="100%" height="500px" src="" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>

<div  class="modal modal-wide fade" data-backdrop="static" data-keyboard="false" id="docModal" tabindex="-1" role="dialog" aria-labelledby="docModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content doc-content">
             <div class="modal-body">
                <button type="button" class="close" data-dismiss="modal"  aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <iframe width="100%"  id="doc-iframe" src="" frameborder="0" allowfullscreen>

                </iframe>
            </div>
            <div class="modal-footer">
                <div class="row text-center">
                    <button type="button" onclick="javascript:printDoc()" class="btn btn-primary" id="printbutton">Print</button>
                    <button type="button" onclick="javascript:downloadFile()" class="btn btn-primary" id="downloadbutton">Download</button>                    
                    <button type="button" data-dismiss="modal" class="btn btn-primary">Close</button>
                </div>

            </div>

        </div>

    </div>
</div>

<div class="top-container">
    <br>
    <div class="topic-banner">
        <div class="container">
            <div class="topic text-center">
                <img class="img-responsive center-block" src="${assetPath(src: 'topic-banner-top.png')}">
                <div><span class="topic-title">New and Featured</span><br/></div>
                <img class="img-responsive center-block" src="${assetPath(src: 'topic-banner-bottom.png')}">
            </div>
        </div>
    </div>    
    <br>
    <div class="container" id="contentRecent" style="display:none">
            <div class="row" id="content-header"></div>
            <div id="content-data" class="row">
            </div>
        <br>
    </div>    
    <g:render template="footer"></g:render>
</div>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="quiz.js"/>
<asset:javascript src="topic.js"/>
<asset:javascript src="searchContents.js"/>    
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="clock.js"/>
<%
String syllabusType=session.getAttribute("syllabusType")==null?"school":session.getAttribute("syllabusType");
String country=session.getAttribute("country")==null?"India":session.getAttribute("country");
%>    
<g:render template="mcq"></g:render>
<g:render template="fib"></g:render>
<g:render template="opp"></g:render>
<g:render template="tof"></g:render>
<script>
    var serverPath= "${request.contextPath}";
    var syllabusType="${syllabusType}";
    var country ="${country}";

    function getResTypeDetails(){
        <g:remoteFunction controller="funlearn" action="latestResTypeDetails" 
                params="'resType='+'${params.resType}'+'&grade='+'${params.grade}'+'&board='+'${params.board}'+'&syllabusType='+syllabusType+'&country='+country" 
                onSuccess="initializeResTypeData(data);doPopupQuiz();"/>
    }
    
    // quiz related functions
    function getQuestions(quizId){
        <g:remoteFunction controller="funlearn" action="quizQuestions" params="'quizId='+quizId"
                onSuccess = 'initializeQuestions(data);'/>
    }

    function getAnswers(quizId){
        <g:remoteFunction controller="funlearn" action="quizAnswers" params="'quizId='+quizId"
                onSuccess = "scoreAndShowAnswers(data);"/>
    }  
    
    function getQuestionAnswers(quizId){
        <g:remoteFunction controller="funlearn" action="quizQuestionAnswers" params="'quizId='+quizId"
                onSuccess = "scoreAndShowAnswers(data);"/>
    }    

    function doPopupOther() {
        if('${params.resType}'=='Videos') {
            playVideo('${params.link}','${params.id}');
        } else if('${params.resType}'=='Mind Maps') {
            showDocument(${params.id});
        }     
    }    
    
    function doPopupQuiz() {
        if('${params.resType}'!='Videos' && '${params.resType}'!='Mind Maps') {
            if('${params.mode}'=='read') {
                showQuizRead('${params.link}','${params.id}');              
            } else {
                showQuiz('${params.link}','${params.id}');          
            }
        }
    }   

    getResTypeDetails();    
    doPopupOther();
    
    function getTopicsMap(){
        <g:remoteFunction controller="funlearn" action="topicsMap" onSuccess='initializeDataIndex(data);' 
                params="'syllabusType='+syllabusType+'&country='+country" />
    }
    
    getTopicsMap(); 

    $(".lmodal").show();

    $(window).load(function(){
        $(".lmodal").hide();
    });         
</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>
<asset:javascript src="analytics.js"/>
<% } %>
</body>
</html>