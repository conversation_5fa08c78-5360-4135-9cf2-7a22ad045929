<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%if("true".equals(session["prepjoySite"])){%>
<style>
    .information-admin h2,
    .information-admin li{
        color: #fff !important;
    }
</style>
<%}%>

<div class="page-main-wrapper mdl-js information-admin p-5">
    <div class="container-fluid">
        <h2>Account Deletion Request for ${siteName} App</h2>

        <p><strong>Follow these steps to request account deletion:</strong></p>

        <ol>
            <li>
                <p><strong>Compose an Email:</strong></p>
                <p>Send an email to <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
            </li>

            <li>
                <p><strong>Include the Following Information:</strong></p>
                <ul>
                    <li>2.1. Clearly state your request to delete your account.</li>
                    <li>2.2. Provide the reason(s) for deleting your account.</li>
                    <li>2.3. Include your userId (registered Email or Mobile No.).</li>
                    <li>2.4. Specify your Account Name.</li>
                    <li>2.5. Mention the App Name associated with your account.</li>
                </ul>
            </li>

            <li>
                <p><strong>Processing Time:</strong></p>
                <p>Upon receiving your email, it will take 48 hours to process and delete your account.</p>
            </li>

            <li>
                <p><strong>Confirmation Email:</strong></p>
                <p>Once the account deletion is completed, you will receive a confirmation email in response to your initial request.</p>
            </li>

            <li>
                <p><strong>Post-Deletion Actions:</strong></p>
                <p>After your account has been deleted, you are free to create a new account.</p>
            </li>
        </ol>

        <p><strong>Important Information:</strong></p>

        <ul>
            <li>
                <p><strong>Types of Data Deleted:</strong></p>
                <p>All user-related data, including account details, preferences, and user library content (books).</p>
            </li>

            <li>
                <p><strong>Retention Period:</strong></p>
                <p>Account deletion is irreversible and occurs within 48 hours of the request.</p>
            </li>
        </ul>

        <p id="privacyText"></p>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>

