<g:render template="navheader"></g:render>
<g:render template="topicinclude"></g:render>

<style>
    
    div.hopscotch-bubble {
        border: 2px solid #f15a29;
        background: transparent;
        box-shadow: 0 2px 5px 0 white,
                0 2px 10px 0 white;
    }
    .hopscotch-bubble-container {
        background: transparent;
        background-color: rgba(0, 0, 0, 0.5);
        color: white;        
    }
    
    div.hopscotch-bubble .hopscotch-bubble-arrow-container.up .hopscotch-bubble-arrow {
        border: 1px solid #ffffff;
    }
    
</style>

<sec:ifAllGranted roles="ROLE_CAN_ADD">
    <g:render template="/creation/contentCreator"></g:render>
</sec:ifAllGranted>
<div class="wrapper">
    <div class="container-fluid">
        <div  class='row  main '>
            <div class='col-md-10 col-md-offset-1'>
                <%if(giveAdd!=null&&"true".equals(giveAdd)){%>
                <div class="row">
                    <div class="col-md-12 col-sm-12 col-centered col-xs-12 text-right" >
                        <sec:ifLoggedIn>
                            <% List resourceType = com.wonderslate.data.ResourceType.findAllByActive("true",[sort:"resourceType"]) %>
                            <div class="dropdown">
                                <button class="btn-lg btn-primary dropdown-toggle" type="button" data-toggle="dropdown" id="addingcontent">Add Content
                                    <span class="caret"></span></button>
                                <ul class="dropdown-menu multi-level">

                                    <sec:ifAllGranted roles="ROLE_ADMIN">
                                        <li><a href="javascript:addAlias()">Alias</a></li>
                                    </sec:ifAllGranted>
                                    <g:each var="resource" in="${resourceType}">
                                        <li class="dropdown-submenu">
                                            <a tabindex="-1" href="#">${resource.resourceType}</a>
                                            <ul class="dropdown-menu">

                                                <g:if test="${resource.allowCreate == 'true'}">
                                                    <li><a href="javascript:createResource('${resource.resourceType}','${resource.useType}')">Create</a></li>
                                                </g:if>
                                                <sec:ifAllGranted roles="ROLE_CAN_UPLOAD">
                                                    <g:if test="${resource.allowUpload == 'true'}">
                                                        <li><a href="javascript:addResource('${resource.resourceType}','${resource.useType}')">Upload</a></li>
                                                    </g:if>
                                                </sec:ifAllGranted>
                                            </ul>
                                        </li>
                                    </g:each>
                                </ul>
                            </div>
                        </sec:ifLoggedIn>
                        <sec:ifNotLoggedIn>
                            <button class="btn-lg btn-primary" type="button"  id="addingcontent" onclick="javascript:showregister('signup');">Add Your Content
                                    <span class="caret"></span></button>
                        </sec:ifNotLoggedIn>
                    </div>
                </div>
                <%}%>
                <div class="container" id="content-videos" style="display:none">

                    <div class="row" id="content-header-videos"></div>
                    <div id="content-data-videos" class="row row-centered">

                    </div>

                    <br>
                </div>
                <div class="container" id="content-relvideos" style="display:none">
                    <div class="row" id="content-header-relvideos"></div>
                    <div id="content-data-relvideos" class="row  row-centered">
                    </div>

                    <br>
                </div>

                <div class="container" id="content-notes" style="display:none">
                    <div class="row" id="content-header-notes"></div>
                    <div id="content-data-notes" class="row  row-centered">
                    </div>

                    <br>
                </div>

                <div class="container" id="content-mindmaps" style="display:none">
                    <div class="row" id="content-header-mindmaps"></div>
                    <div id="content-data-mindmaps" class="row  row-centered">
                    </div>

                    <br>
                </div>
                <div class="container" id="content-quiz" style="display:none">
                    <div class="row" id="content-header-quiz"></div>
                    <div id="content-data-quiz" class="row  row-centered">
                    </div>

                    <br>
                </div>

                <div class="container" id="content-weblinks" style="display:none">
                    <div class="row" id="content-header-weblinks"></div>
                    <div id="content-data-weblinks" class="row  row-centered">
                    </div>

                    <br>
                </div>
                <div  class="container" id="relvideo" style="display:none">
                    <div class="relvideo-section">
                        <!-- Header -->
                        <div class="header">
                            <div class="row" id="relvideo-header"></div>
                        </div>
                        <div class="content">
                            <div class="row row-centered" id="relvideo-container"></div>
                        </div>
                    </div>
                    <div class="smallerText text-center"><br>** The videos in this section is added to help the students understand the topic better. These videos does not belong to Wonderslate.<br>
                        If you feel this violates your copyright, please write to <NAME_EMAIL>. We will act on it immediately.</div>
                </div>
            </div>
            
            <br>
    <br>
    <br>
        </div>
        
    </div>
    <div class="push"></div>
</div>
    <g:render template="footer"></g:render>
<!--</div>-->
<script>
    var pageType='topic';
</script>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<asset:javascript src="hopscotch-0.1.1.js"/>
<asset:javascript src="addcontentspopover.js"/>
<asset:javascript src="addcontents.js"/>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>

<g:render template="topicscripts"></g:render>

<script>

    getTopicDetails('${topicId}','topic');
</script>


</body>
</html>