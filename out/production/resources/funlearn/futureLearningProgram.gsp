<g:render template="/${session['entryController']}/navheader_new"></g:render>

<asset:stylesheet href="wonderslate/aboutCourse.css" async="true"/>

<link href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.carousel.min.css" rel="stylesheet" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/assets/owl.theme.default.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/OwlCarousel2/2.3.4/owl.carousel.min.js"></script>

<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" integrity="sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css" />


<style>
#registerModal{
    z-index: 99999;
}
#smsge{
   z-index:9999999;
}
.form-control:focus{
    box-shadow: none;
    border: 2px solid red !important;
}
@media only screen and (max-width: 767px){
        .rModal{
            width: 100% !important;
        }
}
</style>

<main>
    <section>
        <div class="container mt-5 pb-4 p-lg-5 mb-4">
            <div class="row align-items-center">
                <div class="col-12 col-lg-6 sc-img mb-3">
                    <img src="${assetPath(src: 'wslibrary/learn.svg')}" class="banner-img">
                </div>
                <div class="col-12 col-lg-6 sc-h mb-3">
                    <h1 class="mb-3">
                        Smart Tools To Ace Your Studies
                    </h1>
                    <h4>
                        For every learner,
                        every learning community. <br />
                        For The Leaders of Tomorrow.
                    </h4>
                    <p class="mt-3">
                        We’re on a mission to provide a free, world-class futuristic
                        training <br />
                        for students, & educators to incorporate technology to enhance
                        their learning.
                    </p>
                    <div>
                        <button class="btn btn-primary  mr-4 mt-4" type="button">
                            <a href="#aboutCourse"  class="text-white text-decoration-none">About this course</a>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section class="bg-light p-lg-4 " id="aboutCourse">
        <div class="container">
            <div class="row">
                <section class="header h-50 p-3 col-lg-9">
                    <div class="container">
                        <div>
                            <h1>Smart Tools To Ace Your Studies</h1>
                        </div>
                        <div class="mt-3">
                            <ul class="tasks-list-group row d-flex pl-0 ">
                                <div class="col-lg-3 mb-2">
                                    <li class="mr-3">
                                        <i class="fas fa-user-friends mr-1 "></i>
                                        Class 1 to 12
                                    </li>
                                </div>

                                <div class="col-lg-4 mb-2">
                                    <li class="mr-3">
                                        <i class="fas fa-clock mr-1"></i>
                                        2 total hours over 5 days
                                    </li>
                                </div>
                                <div class="col-lg-4 mb-2">
                                    <li class="mr-3">
                                        <i class="fas fa-language mr-1"></i>
                                        English
                                    </li>
                                </div>

                                <div class="col-lg-3 mb-2">
                                    <li class="mr-3 ">
                                        <i class="fas fa-sticky-note mr-1"></i>
                                        1 Lecture
                                    </li>
                                </div>

                                <div class="col-lg-3 mb-2">
                                    <li class="mr-2">
                                        <i class="fas fa-smile-beam mr-1"></i>
                                        Beginner/Easy
                                    </li>
                                </div>
                            </ul>
                        </div>

                        <div class="mt-4">
                            <h1>On successful completion</h1>
                        </div>
                        <div>
                            <ul class="pl-0 gifts">
                                <li class="mt-2 ">
                                    <i class="fas fa-check text-success mr-1"></i>
                                    Access to Tools for Smart Learning
                                </li>
                                <li class="mt-2">
                                    <i class="fas fa-check text-success mr-1"></i>
                                    Certificate of Achievement
                                </li>
                                <li class="mt-2">
                                    <i class="fas fa-check text-success mr-1"></i>
                                    Goodies
                                </li>
                                <li class="mt-2">
                                    <i class="fas fa-check text-success mr-1"></i>
                                    Get Featured on Wonderpublish Magazine
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>

                <section class="col-lg-3 mt-lg-5 mb-3">
                    <div class="card color-green text-white mb-3 mx-auto" style="width: 320px">
                        <div class="card-body">
                            <p class="card-text ws-des"> Wonderslate Next-gen learning platform offering Smart Tools of Study and Smart eBooks for all exams on all levels.
                            Available on <span ><a href="https://apps.apple.com/in/app/wonderslate/id1438381878" target="_blank">iOS</a>  and <a href="https://play.google.com/store/apps/details?id=com.wonderslate.wonderpublish&hl=en-GB" target="_blank">Android</a> </span> </p>
                        </div>
                    </div>
                </section>
            </div>
        </div>

    </section>

    <section class="p-lg-5 features-sec bg-warning" id="fs">
        <div class="container p-4">
            <div class="features">
                <div class="card-group">
                    <div class="card mr-lg-4 rounded shadow border-0" data-aos="fade-right">
                        <div class="card-body text-center">
                            <h3 class="card-title text-black text-center mt-3 "> <span class="stat-count">10000</span>+</h3>
                            <h3 class="card-text text-success">Tests & MCQs</h3>
                        </div>
                    </div>
                    <div class="card mr-lg-4 rounded shadow border-0" data-aos="fade-down">
                        <div class="card-body text-center">
                            <h3 class="card-title text-black text-center mt-3 "> <span class="stat-count">5000</span>+</h3>
                            <h3 class="card-text text-success">eBooks & Courses</h3>
                        </div>
                    </div>
                    <div class="card mr-lg-4 rounded shadow border-0" data-aos="fade-left">
                        <div class="card-body text-center">
                            <h3 class="card-title text-black text-center mt-3"> <span class="stat-count">1</span> Million</h3>
                            <h3 class="card-text text-success">Active Students</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <section class=" testim p-lg-5 mt-3">
        <div class="container text-center" >
            <h2 class="mb-5 text-center offer text-dark mb-4">What others are saying?</h2>
            <div class="row align-items-center justify-content-center">
                <div class="col-md-4 margin-btm-20" data-aos="zoom-in-right">
                    <div class="quote dark">
                        <blockquote>
                            <p>
                                “Very engaging and interactive platform. It helps me cater my learning, revision and test phases equally.”
                            </p>
                            <div class="d-flex text-center justify-content-center stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </blockquote>
                    </div>
                    <div class="quote-footer text-right">
                        <div class="quote-author-img">
                            <img src="${assetPath(src: 'wslibrary/Ambika.jpg')}">
                        </div>
                        <h4>Ambika Singh Rajawat</h4>
                    </div>
                </div><!--colored quote box col-->
                <div class="col-md-4 margin-btm-20" data-aos="zoom-in-left">
                    <div class="quote green">
                        <blockquote>
                            <p>
                                “I like the study material [available] in this app.”
                            </p>
                            <div class="d-flex text-center justify-content-center stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                        </blockquote>
                    </div>
                    <div class="quote-footer text-right">
                        <div class="quote-author-img">
                            <img src="${assetPath(src: 'wslibrary/Ojasvi.jpeg')}" >
                        </div>
                        <h4>Ojasvi Choudhary</h4>
                    </div>
                </div>

                <div class="col-md-4 margin-btm-20" data-aos="zoom-in-left">
                    <div class="quote green">
                        <blockquote>
                            <p>
                                “I believe that learning is done best when there are opportunities for interaction and a method to cater to individual needs.”
                            </p>
                            <div class="d-flex text-center justify-content-center stars">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star-half-alt"></i>
                            </div>
                        </blockquote>
                    </div>
                    <div class="quote-footer text-right">
                        <div class="quote-author-img">
                            <img src="${assetPath(src: 'wslibrary/principal.jpeg')}" data-aos="zoom-in">
                        </div>
                        <h4>Principal Anuradha Krishnan, Primus Public School, Bengaluru</h4>
                    </div>
                </div>
            </div>
        </div>
    </section>




    <section class="three-step p-lg-5 bg-warning" id="three-step">
        <h2 class="text-center text-dark mt-2 mb-2">A Three-Step Learning Program</h2>
        <div>
            <p class="text-dark text-center">
                Learning at their own pace, students will get demos of the smart tools by Industry Experts where the students will
            </p>
        </div>
        <div class="container d-flex align-items-center pb-4 mt-4" >
            <div class="row">
                <div class="card-group">
                    <div class="card mr-lg-4 rounded shadow-lg p-4 border-0" data-aos="zoom-in-right">
                        <div class="card-body text-center">
                            %{--                        <h5 class="card-title text-black-50 text-center mt-3">1</h5>--}%
                            <img src="${assetPath(src: 'wslibrary/one.png')}"  class="rounded-circle img-fluid w-25 p-3">
                            <h4 class="card-text">Set Learning Goals</h4>
                        </div>
                    </div>
                    <div class="card mr-lg-4 rounded shadow-lg p-4 border-0" data-aos="zoom-in-down">
                        <div class="card-body text-center">
                            %{--                        <h5 class="card-title text-black-50 text-center mt-3">2</h5>--}%
                            <img src="${assetPath(src: 'wslibrary/two.png')}" class="rounded-circle img-fluid w-25 p-3">
                            <h4 class="card-text">Engage in the Learning Process</h4>
                        </div>
                    </div>
                    <div class="card rounded shadow-lg p-4 border-0" data-aos="zoom-in-left">
                        <div class="card-body text-center">
                            %{--                        <h5 class="card-title text-black-50 text-center mt-3">3</h5>--}%
                            <img src="${assetPath(src: 'wslibrary/three.png')}"  class="rounded-circle img-fluid w-25 p-3">
                            <h4 class="card-text"> Evaluate Learning</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div>
            <p class="text-center">* Each batch would consist of 10 students.</p>
        </div>
    </section>


    <section class="tasks p-lg-5 mt-3" data-aos="fade-right">
        <div class="container">
            <div class="row bg-light shadow rounded p-lg-5 p-3 task-content">
                <div class="col-12 col-lg-6 d-flex align-items-center flex-column  justify-content-center">
                    <div>
                        <h2 class="text-dark text-left mb-3">Tasks involved</h2>
                        <p class="text-left">Each student will use Wonderslate platform and its features to create their own Learning Material.</p>
                    </div>
                </div>
                <div class="col-12 col-lg-6 d-flex flex-column mt-3 pb-3">
                    <ul class="tasks-list-group pl-0">
                        <h4 class="mb-3">Identify Topics/Questions & Start Creating</h4>
                        <li>
                            <i class="fas fa-check text-success mr-1"></i> Your Own Mock Tests
                        </li>
                        <li><i class="fas fa-check text-success mr-1"></i> Revision Sets/Flashcards</li>
                        <li><i class="fas fa-check text-success mr-1"></i> Study Notes</li>
                        <li><i class="fas fa-check text-success mr-1"></i> Ask Questions & Get them resolved using the Wonderslate Doubts Module</li>
                        <li><i class="fas fa-check text-success mr-1"></i> Reference Video</li>
                        <li><i class="fas fa-check text-success mr-1"></i> To-Do List</li>
                    </ul>
                    <p>Wonderslate Content/Subject experts will Moderate the Learning Material created by each student & provide feedback to improve the efficiency of their overall</p>
                </div>
            </div>
        </div>
    </section>



    <section class="description mt-3 text-white " data-aos="fade-left">
        <div class="container shadow description-cont bg-warning p-lg-5">
            <div class="row d-flex p-3 text-dark">
                <div class="col-12 col-lg-6 mt-4">
                    <div>
                        <h4 class="mb-3">Are there any course requirements or prerequisites?</h4>
                        <ul class="pl-0 gifts">
                            <li class="mt-2">
                                <i class="fas fa-check text-success mr-1"></i>
                                A Computer (PC or MacBook) + Zoom (available free)
                            </li>
                            <li class="mt-2">
                                <i class="fas fa-check text-success mr-1"></i>
                                A Mobile Phone (In case computer is not available)
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="col-12 col-lg-6 mt-4 text-dark">
                    <div class="description-content">
                        <h4 class="mb-2">Description</h4>
                        <p>
                            Create your own resources. Engage in meaningful peer learning. Know the next-gen learning tools. Walk with us through the various stages of the training program.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div id="mss" style="display: none">
        <p>Continue</p>
    </div>

    <div class="modal fade  rounded" id="registerModal" tabindex="-1" role="dialog" aria-labelledby="registerModalLabel" data-backdrop="static">
        <div class="col-md-6 justify-content-center px-0">
            <div id="errormsg" class="alert alert-danger justify-content-center p-3 mt-2 m-0" role="alert" style="display: none;"></div>
            <div id="successmsg" class="alert alert-success justify-content-center p-3 mt-2 m-0" role="alert" style="display: none"></div>
        </div>
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content bg-warning shadow-lg w-75 rModal">
                <div class="modal-body ">
                    <h3 class="mb-3 text-white text-center" id="regMText">Register here!</h3>
                    <form class="register" novalidate id="registrationForm">
                        <div class="mb-2">
                            <input type="text" class="form-control" id="regName" placeholder="Name" required maxlength="30" minlength="3"/>
                            <div class="invalid-feedback text-dark" style="font-size: 14px">Please enter a valid name</div>
                        </div>

                        <div class="mb-2">
                            <input type="email" class="form-control" id="regEmail" placeholder="Email" required/>
                            <div class="invalid-feedback text-dark" style="font-size: 14px">Please enter a valid email</div>
                        </div>

                        <div class="mb-2">
                            <input type="number" class="form-control" id="regMobile"  maxlength="10" placeholder="Mobile Number" required/>
                            <div class="invalid-feedback text-dark" style="font-size: 14px">Enter a valid Number</div>
                        </div>

                        <div>
                            <input type="text" class="form-control mb-2" id="regSchool" placeholder="School"/>
                        </div>

                        <div class="mb-2">
                            <input type="text" class="form-control mb-2" id="regClass" placeholder="Class"/>
                        </div>

                        <div class="mb-2">
                            <textarea name="address" id="regAddress" cols="10" rows="4" class="form-control" placeholder="Address" required></textarea>
                            <div class="invalid-feedback text-dark" style="font-size: 14px">Please provide your address</div>
                        </div>

                        <div class="mt-3">
                            <p id="erMsg" class="text-center"></p>
                        </div>

                        <div class="text-center">
                            <button class="btn btn-danger mt-3" type="button"  onclick="closeRegModal()">Close</button>
                            <button class="btn btn-success mt-3" type="submit">Register</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>


    <section class="p-lg-5 d-flex mt-4" data-aos="flip-right">
        <div class="container">
            <div class="row align-items-center future p-lg-5 shadow rounded future-ready">
                <div class="col-12 col-lg-6 mt-5">
                    <img src="${assetPath(src: 'wslibrary/banner-image.svg')}" class="banner-img">
                </div>
                <div class="col-12 col-lg-6 future-content p-4">
                    <h2>Enroll Now!</h2>
                    <p class="mt-3">
                        Limited seats and batches are available.
                    </p>
                    <br>
                    <p>Send your registration to</p>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                    <br>
                    <p>Or</p>
                    <button class="btn btn-primary mr-4 mt-2" type="button" data-toggle="modal" data-target="#registerModal">Register here</button>
                </div>

            </div>
        </div>
    </section>
    <div class="modal fade" id="smsge" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered " role="document">
            <div class="modal-content w-75">
                <div class="text-center mt-4">
                    <h5 class="mb-3">Registered Successfully!</h5>
                    <p>Thank you for joining the training program.</p>
                </div>

                <div class="container mt-4 mb-4 text-center">
                    <button type="button" class="btn btn-primary " onclick="redirec()">Continue</button>
                </div>
            </div>
        </div>
    </div>
</main>




<script>


    $('#registrationForm').submit(function (e){
        var name = $('#regName').val();
        var email = $('#regEmail').val();
        var mobile = $('#regMobile').val();
        var className = $("#regClass").val();
        var school = $('#regSchool').val();
        var address = $('#regAddress').val();
        e.preventDefault();


        function validateEmail(email) {
            var emailReg = /^([\w-\.]+@([\w-]+\.)+[\w-]{2,4})?$/;
            return emailReg.test( email );
        }

        if (name == "" ){
            $("#erMsg").text("Fields cannot be empty").addClass("text-danger")
            $('#regName').focus()
            $(".form-control").css("border-color","red")
        }else if(name.length <3 || name.length >30){
            $("#erMsg").text("Name must be at least 3 characters").addClass("text-danger")
            $('#regName').focus()
            $(".form-control").css("border-color","red")
        }
        else if(email == "" ||!validateEmail(email)){
            $("#erMsg").text("Enter a valid email").addClass("text-danger")
            $('#regEmail').focus()
            $(".form-control").css("border-color","red")
        }else if (mobile.length <10 || mobile.length>10 || mobile == ""){
            $("#erMsg").text("Enter valid mobile number").addClass("text-danger")
            $('#regMobile').focus()
            $(".form-control").css("border-color","red")
        }else if(address == ""){
            $("#erMsg").text("Fields cannot be empty").addClass("text-danger")
            $('#regAddress').focus()
            $(".form-control").css("border-color","red")
        }
        else {
            takeUserDetails(name, email, mobile, className, school, address);
        }

    })



</script>
<script>
    function takeUserDetails(name, email, mobile, className, school, address){
        $("#errorStudentName").hide();
        $("#errorStudentMobile").hide();
        $("#errorStudentEmail").hide();
        var hasError = false;

        if(!hasError){
            <g:remoteFunction controller="funlearn" action="futureLearningProgram"
                params="'name='+name+'&email='+email+'&mobile='+mobile+'&school='+school+'&classStudying='+className+'&address='+address+'&registerUser=true'"
                onSuccess="saveSuccess(data)"/>
        }

    }



    function saveSuccess(data){
        console.log(data.status)
        if(data.status=="Success"){
            $("#smsge").modal("show")
            $("#registerModal").modal("hide");
        }else{
            $("#erMsg").text("User Is already registered").addClass("text-danger")
        }
    }

    function closeRegModal(){
        window.location.href = "/funlearn/futureLearningProgram"
    }

    function redirec(){
        window.location.href = "/funlearn/futureLearningProgram"
    }
</script>

%{--<script>--}%
%{--    // Example starter JavaScript for disabling form submissions if there are invalid fields--}%
%{--    (function () {--}%
%{--        "use strict";--}%
%{--        window.addEventListener(--}%
%{--            "load",--}%
%{--            function () {--}%
%{--                // Fetch all the forms we want to apply custom Bootstrap validation styles to--}%
%{--                var forms = document.getElementsByClassName("register");--}%
%{--                // Loop over them and prevent submission--}%
%{--                var validation = Array.prototype.filter.call(--}%
%{--                    forms,--}%
%{--                    function (form) {--}%
%{--                        form.addEventListener(--}%
%{--                            "submit",--}%
%{--                            function (event) {--}%
%{--                                if (form.checkValidity() === false) {--}%
%{--                                    event.preventDefault();--}%
%{--                                    event.stopPropagation();--}%
%{--                                    --}%
%{--                                }--}%
%{--                                // form.classList.add("was-validated");--}%

%{--                            },--}%
%{--                            false--}%
%{--                        );--}%
%{--                    }--}%
%{--                );--}%
%{--            },--}%
%{--            false--}%
%{--        );--}%
%{--    })();--}%
%{--</script>--}%
<script>

    function err(){
        $("#regEmail").focus();
        $("#regAddress").focus();
        $("#regMobile").focus();
    }

    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();

            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

</script>


<g:render template="/books/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>
<script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/8.6/highlight.min.js"></script>

<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>

<script>
    AOS.init({
        easing: 'ease-out-back',
        duration: 1000
    });

</script>