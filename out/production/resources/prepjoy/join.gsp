<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width">
    <title>PR WS --- Sender</title>
    <asset:stylesheet href="prepjoy/style.css" async="true"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
</head>
<body>

<div class="container">
    <h1 class="mt-2">Join</h1>


    <br>
    <div>
        <div>
            <span style="font-weight: bold">ID: </span>
            <div class="form-inline">
                <input type="text" class="form-control" id="receiver-id" title="Input the ID from Jo<PERSON>">
                <button class="btn btn-success ml-2"  id="connect-button">Connect</button>
            </div>
        </div>
        <br>
        <div>
            <div class="form-inline">
                <input type="text" class="form-control"  id="sendMessageBox" placeholder="Enter a message..." autofocus="true" />
                <button type="button" class="ml-2 btn btn-primary" id="sendButton">Send</button>
                <button type="button" class="ml-2 btn btn-secondary" id="clearMsgsButton">Clear</button>
            </div>
        </div>
    </div>
    <br>
    <div>

        <div>
            <div class="title">Status:</div>
            <div id="status" class="status"></div>
        </div>
        <div>
            <div class="title">Messages:</div>

            <div class="message" id="message"></div>
        </div>
    </div>
    <!--            <div>-->
    <!--                <div>-->
    <!--                    <button type="button" class="control-button" id="resetButton">Reset</button>-->
    <!--                </div>-->
    <!--                <div>-->
    <!--                    <button type="button" class="control-button" id="goButton">Go</button>-->
    <!--                </div>-->
    <!--            </div>-->
    <!--            <div>-->
    <!--                <div>-->
    <!--                    <button type="button" class="control-button" id="fadeButton">Fade</button>-->
    <!--                </div>-->
    <!--                <div>-->
    <!--                    <button type="button" class="control-button" id="offButton">Off</button>-->
    <!--                </div>-->
    <!--            </div>-->
</div>
<div class="meet-controls-bar">
    <button class="btn btn-default" onclick="startScreenShare()">Screen Share</button>
</div>
<div class="d-none">
    <p id="notification" hidden></p>
    <div class="entry-modal" id="entry-modal">
        <p>Create or Join Meeting</p>
        <input id="room-input" class="room-input" placeholder="Enter Room ID">
        <div>
            <button onclick="createRoom()">Create Room</button>
            <button onclick="joinRoom()">Join Room</button>
        </div>
    </div>


    <div class="meet-area">
        <!-- Remote Video Element-->
        <video id="remote-video"></video>

        <!-- Local Video Element-->
        <video id="local-video"></video>

    </div>
</div>

<script src="https://unpkg.com/peerjs@1.3.2/dist/peerjs.min.js"></script>
<script type="text/javascript">
    (function () {

        var lastPeerId = null;
        var peer = null; // own peer object
        var conn = null;
        var recvIdInput = document.getElementById("receiver-id");
        var status = document.getElementById("status");
        var message = document.getElementById("message");
        //var goButton = document.getElementById("goButton");
        // var resetButton = document.getElementById("resetButton");
        // var fadeButton = document.getElementById("fadeButton");
        // var offButton = document.getElementById("offButton");
        var sendMessageBox = document.getElementById("sendMessageBox");
        var sendButton = document.getElementById("sendButton");
        var clearMsgsButton = document.getElementById("clearMsgsButton");
        var connectButton = document.getElementById("connect-button");
        var cueString = "<span class=\"cueMsg\">Cue: </span>";

        /**
         * Create the Peer object for our end of the connection.
         *
         * Sets up callbacks that handle any events related to our
         * peer object.
         */
        function initialize() {
            // Create own peer object with connection to shared PeerJS server
            peer = new Peer(null, {
                debug: 2
            });

            peer.on('open', function (id) {
                // Workaround for peer.reconnect deleting previous id
                if (peer.id === null) {
                    console.log('Received null id from peer open');
                    peer.id = lastPeerId;
                } else {
                    lastPeerId = peer.id;
                }

                console.log('ID: ' + peer.id);

            });
            peer.on('connection', function (c) {
                // Disallow incoming connections
                c.on('open', function() {
                    c.send("Sender does not accept incoming connections");
                    setTimeout(function() { c.close(); }, 500);
                });
            });
            peer.on('disconnected', function () {
                status.innerHTML = "Connection lost. Please reconnect";
                console.log('Connection lost. Please reconnect');

                // Workaround for peer.reconnect deleting previous id
                peer.id = lastPeerId;
                peer._lastServerId = lastPeerId;
                peer.reconnect();
            });
            peer.on('close', function() {
                conn = null;
                status.innerHTML = "Connection destroyed. Please refresh";
                console.log('Connection destroyed');
            });
            peer.on('error', function (err) {
                console.log(err);
                alert('' + err);
            });
        };

        /**
         * Create the connection between the two Peers.
         *
         * Sets up callbacks that handle any events related to the
         * connection and data received on it.
         */

        function join() {
            // Close old connection
            if (conn) {
                conn.close();
            }

            // Create connection to destination peer specified in the input field
            conn = peer.connect(recvIdInput.value, {
                reliable: true
            });

            conn.on('open', (id) => {
                console.log("Connected with Id: " + id)
                getUserMedia({ video: false, audio: true }, (stream) => {
                    local_stream = stream;
                    setLocalStream(local_stream)
                    notify("Joining peer")
                    let call = conn.call(room_id, stream)
                    call.on('stream', (stream) => {
                        setRemoteStream(stream);
                    })
                    currentPeer = call;
                }, (err) => {
                    console.log(err)
                })
                joinRoom();
            });

            conn.on('open', function () {
                status.innerHTML = "Connected to: " + conn.peer;
                console.log("Connected to: " + conn.peer);

                // Check URL params for comamnds that should be sent immediately
                var command = getUrlParam("command");
                if (command)
                    conn.send(command);
            });
            // Handle incoming data (messages only since this is the signal sender)
            conn.on('data', function (data) {
                addMessage("<span class=\"peerMsg\">UserB:</span> " + data);
            });
            conn.on('close', function () {
                status.innerHTML = "Connection closed";
            });
        };

        /**
         * Get first "GET style" parameter from href.
         * This enables delivering an initial command upon page load.
         *
         * Would have been easier to use location.hash.
         */
        function getUrlParam(name) {
            name = name.replace(/[\[]/, "\\\[").replace(/[\]]/, "\\\]");
            var regexS = "[\\?&]" + name + "=([^&#]*)";
            var regex = new RegExp(regexS);
            var results = regex.exec(window.location.href);
            if (results == null)
                return null;
            else
                return results[1];
        };

        /**
         * Send a signal via the peer connection and add it to the log.
         * This will only occur if the connection is still alive.
         */
        function signal(sigName) {
            if (conn && conn.open) {
                conn.send(sigName);
                console.log(sigName + " signal sent");
                addMessage(cueString + sigName);
            } else {
                console.log('Connection is closed');
            }
        }



        function addMessage(msg) {
            var now = new Date();
            var h = now.getHours();
            var m = addZero(now.getMinutes());
            var s = addZero(now.getSeconds());

            if (h > 12)
                h -= 12;
            else if (h === 0)
                h = 12;

            function addZero(t) {
                if (t < 10)
                    t = "0" + t;
                return t;
            };

            message.innerHTML = "<br>" + msg + message.innerHTML;
        };

        function clearMessages() {
            message.innerHTML = "";
            addMessage("Msgs cleared");
        };

        // Listen for enter in message box
        sendMessageBox.addEventListener('keypress', function (e) {
            var event = e || window.event;
            var char = event.which || event.keyCode;
            if (char == '13')
                sendButton.click();
        });
        // Send message
        sendButton.addEventListener('click', function () {
            if (conn && conn.open) {
                var msg = sendMessageBox.value;
                sendMessageBox.value = "";
                conn.send(msg);
                console.log("Sent: " + msg);
                addMessage("<span class=\"selfMsg\">userA: </span> " + msg);
            } else {
                console.log('Connection is closed');
            }
        });

        // Clear messages box
        clearMsgsButton.addEventListener('click', clearMessages);
        // Start peer connection on click
        connectButton.addEventListener('click', join);

        // Since all our callbacks are setup, start the process of obtaining an ID
        initialize();
    })();
</script>
<script src="/assets/prepjoy/script.js"></script>
</body>
</html>
