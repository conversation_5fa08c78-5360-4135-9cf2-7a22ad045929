<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css"/>
<link rel="stylesheet" href="/assets/prepJoy/prepjoyWebsites/dailyTest.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.1.2/css/all.min.css"  crossorigin="anonymous" referrerpolicy="no-referrer" />
<link rel="stylesheet" href="/assets/prepJoy/prepjoyWebsites/instituteLeaderBoard.css">

<!-------- LOADER --------->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<!-------- DROPDOWNS TO GET SYLLABUS --------->
<section class="mx-auto dropdownslist" >
    <div class="container">
        <div class="daily__test-header mt-5">
            <div class="daily__test-header__title d-flex align-items-center">
                <h3 class="text-primary-modifier">
                    <strong>MCQ online tests </strong>
                </h3>
                <a href="/usermanagement/favouriteMcqs" class="favMcqBtnNav">Favourite MCQs</a>
            </div>
            <div class="d-flex align-items-center mt-3">
                <p style="font-size: 16px;font-weight: 500">Biggest collection of free mcq online tests for you to practice.</p>
            </div>
        </div>
        <div class="daily__test-selection mt-5">
            <div class="daily__test-selection__wrapper">
                <h3 class="text-primary-modifier startHereText"><strong>Get started</strong></h3>
                <div class="row mt-2">
                    <div class="form-group col-md-4" id="examGroupWrapper">
                        <label for="examGroup" class="text-primary-modifier">Step 1</label>
                        <select class="form-control" id="examGroup">

                        </select>
                    </div>
                    <div class="form-group col-md-4" id="examTypeWrapper" style="display:none;">
                        <label for="examType" class="text-primary-modifier">Step 2</label>
                        <select class="form-control" id="examType">
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <!-------- DAILY TEST DATES CARD --------->
        <div class="datesUIWrapper d-none align-items-center">
            <div class="scrollBtn scrollBtnLeft">
                <button class="d-none" id="scrollLeft"><i class="fa-sharp fa-solid fa-arrow-left"></i></button>
            </div>
            <div>
                <div class="d-flex align-items-center" style="margin-top: 1rem;">
                    <div class="daily__test-dates" id="datesCardList">
                    </div>
                </div>
            </div>
            <div class="scrollBtn scrollBtnRight">
                <button class="d-none" id="scrollRight"><i class="fa-sharp fa-solid fa-arrow-right"></i></button>
            </div>
        </div>
    </div>
</section>

<!-------- MCQ EBOOKS --------->
<section class="" style="display: none" id="mcqbooksblock">
    <div class="container pr-0">
        <div class="mcq__ebooks-header">
            <h3 class="text-primary-modifier"><strong>Top Selling </strong></h3>
        </div>

        <div class="mcq__ebooks-cards">
        </div>
    </div>
</section>

<!-------- LEADERBOARD --------->
<section class="leaderBoard">
    <div class="container leaderBoard__title">
        <h3 class="text-primary-modifier"><strong>Leaderboard</strong></h3>
    </div>
    <div class="container leaderBoard__wrapper mt-2 mb-5">
        <div class="leaderBoard__wrapper-contents mt-3">
            <div class="leaderBoard__wrapper-contents__tabs mb-4 d-none">
                <button class="btn inst__lbTab lbTab activeTab">Institute </button>
                <button class="btn allInd__lbTab lbTab">All India </button>
            </div>
            <div class="leaderBoard__wrapper-contents__datesTab mt-4">
                <button class="dailyBtn activeDate">Daily</button>
                <button class="weeklyBtn">Weekly</button>
                <button class="monthlyBtn">Monthly</button>
            </div>
            <div class="leaderBoard__wrapper-contents__list" >
                <div class="leaderBoard__wrapper-contents__list-wrapper" >
                    <table>
                        <thead>
                        <tr>
                            <th>Rank</th>
                            <th>Name</th>
                            <th style="text-align: end">Points</th>
                        </tr>
                        </thead>
                        <tbody id="leaderBoardList"></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="leaderBoard__wrapper-navigations mt-3">
            <h5 class="mb-3 d-none">Do you want to join the Leaderboard?</h5>
            <div class="leaderBoard__wrapper-navigations__cards d-flex d-lg-block justify-content-between" style="gap: 10px">
                <div class="leaderBoard__wrapper-navigations__card mb-3 analyticsDiv d-none">
                    <sec:ifLoggedIn>
                        <a href="/prepjoy/quizAnalytics" class="leaderBoard__wrapper-navigations__card-link">
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <a href="javascript:loginOpen();" >
                    </sec:ifNotLoggedIn>
                    <div class="card__sec-1">
                        <i class="material-icons-round d-lg-none">bar_chart</i>
                        <h4 class="text-center text-lg-left">Analytics</h4>
                        <p class="d-lg-block d-none">All your quiz analytics in one place. </p>
                    </div>
                    <button class="d-lg-block d-none"><i class="fa-solid fa-chevron-right"></i></button>
                </a>
                </div>
                <div class="leaderBoard__wrapper-navigations__card mb-3">
                    <sec:ifLoggedIn>
                        <a href="/books/myActivity" class="leaderBoard__wrapper-navigations__card-link">
                    </sec:ifLoggedIn>
                    <sec:ifNotLoggedIn>
                        <a href="javascript:loginOpen();" class="leaderBoard__wrapper-navigations__card-link">
                    </sec:ifNotLoggedIn>
                    <div class="card__sec-1">
                        <i class="material-icons-round d-lg-none">timeline</i>
                        <h4 class="text-center text-lg-left">Learning History</h4>
                        <p class="d-lg-block d-none">All your learning history here.</p>
                    </div>
                    <button class="d-lg-block d-none"><i class="fa-solid fa-chevron-right"></i></button>
                </a>
                </div>
                <div class="leaderBoard__wrapper-navigations__card mb-3">
                    <a href="" class="leaderBoard__wrapper-navigations__card-link" id="ebooksLink">
                        <div class="card__sec-1">
                            <i class="material-icons-round d-lg-none">local_library</i>
                            <h4 class="text-center text-lg-left">eBooks</h4>
                            <p class="d-lg-block d-none">World's No 1 smart eBooks store.</p>
                        </div>
                        <button class="d-lg-block d-none"><i class="fa-solid fa-chevron-right"></i></button>
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<div class="hidden-footer">
<g:render template="/${session['entryController']}/footer_new"></g:render>
</div>
<%if("true".equals(session["prepjoySite"])){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<% } %>
<script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>
<script>

    var siteId = "${session['siteId']}";
    var siteName="${session['siteName']}";
    var examGroup;
    var examType;
    var examGroupSelectedValue;
    var examTypeSelectedValue;
    var dailyTestId;
    var startDateValue;
    var latestDateValue;
    var selectedDateValue;
    var datesArLng;
    var dtsArr;
    var level = null;
    var syllabus = null;
    var grade = null;
    var subject = null;
    var prepjoySite = "${session['prepjoySite']}";

    //API 1: TO GET DAILY TEST TYPES
    function getDailyTestTypes(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="prepjoy" action="getDailyTestTypesForSite" params="'siteId='+siteId" onSuccess="updateExamGroupUI(data)" />
    }
    getDailyTestTypes();

    //UPDATING DROPDOWN UI
    function updateExamGroupUI(data){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        examGroup = JSON.parse(data.dailyTestTypes);
        examType = JSON.parse(data.dailyTests);
        var egHtml="";
        var selectedStr="";
        egHtml += "<option value=''>Select Exam</option>";
        var exam;
        for (var e=0;e<examGroup.length;e++){
            selectedStr="";
            <%if(params.testName!=null){%>
            exam = replaceAll(examGroup[e].examGroup,' - ',' ');
            exam = replaceAll(exam,'-','');
            exam = replaceAll(exam,' ','-');
            exam = exam.toLowerCase();

            var testName = "${params.testName}";
            testName = testName.toLowerCase();
            if(exam==testName){
                selectedStr=' selected';
                setCookie("selectedExamGroupType",examGroup[e].examGroup);
                examGroupSelectedValue = getCookie("selectedExamGroupType");
                updateExamTypeUI();
            }
            <%}else{%>
            if(getCookie("selectedExamGroupType")){
                if(examGroup[e].examGroup==getCookie("selectedExamGroupType")){
                    selectedStr=' selected';
                    examGroupSelectedValue = getCookie("selectedExamGroupType");
                    updateExamTypeUI();
                }
            }
            <%}%>
            egHtml +="<option value='"+examGroup[e].examGroup+"'"+selectedStr+">"+examGroup[e].examGroup+"</option>";
        }
        $("#examGroup").html(egHtml);

    }

    //UPDATING DROPDOWN UI
    function updateExamTypeUI(){
        var etType="";
        var selectedStr="";
        etType +="<option value=''>Select Test</option>"
        for (var t=0;t<examType.length;t++){
            selectedStr="";

            if (examType[t].examGroup == examGroupSelectedValue){
                if(getCookie("selectedExamType")){
                    if(examType[t].testName==getCookie("selectedExamType")){
                        selectedStr=' selected';
                        examTypeSelectedValue = getCookie("selectedExamType");
                        getDailyTestDates(examTypeSelectedValue);
                    }
                }
                etType += "<option value='"+examType[t].testName+"'"+selectedStr+">"+examType[t].testName+"</option>";
            }
        }
        $("#examType").html(etType);
        if (examGroupSelectedValue !=''){
            $("#examTypeWrapper").show();
            document.getElementById('datesCardList').innerHTML = "";
            document.getElementById('scrollRight').classList.add('d-none');
            document.getElementById('scrollLeft').classList.add('d-none');
            if ($("#examType").val()!=""){
                //document.querySelector('.startHereText').classList.remove('d-none');
            }else{
                //document.querySelector('.startHereText').classList.add('d-none');
            }

        }else{
            $("#examTypeWrapper").hide();
            document.getElementById('datesCardList').innerHTML = "";
            document.getElementById('scrollRight').classList.add('d-none');
            document.getElementById('scrollLeft').classList.add('d-none');
            //document.querySelector('.startHereText').classList.add('d-none');
        }

    }

    //API 3: TO GET DAILY TEST DATES
    function getDailyTestDates(examTypeSelectedValue){

        if (examTypeSelectedValue !=""){

            for (var d=0;d<examType.length;d++){
                if (examType[d].testName==examTypeSelectedValue  && examType[d].examGroup == examGroupSelectedValue){
                    dailyTestId = examType[d].dailyTestId;
                    level = examType[d].level;
                    syllabus = examType[d].syllabus;
                    grade = examType[d].grade;
                    subject = examType[d].subject;
                }
            }
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            <g:remoteFunction controller="prepjoy" action="getDailyTestsLatestAndStartDates" params="'dailyTestId='+dailyTestId+'&siteId='+siteId" onSuccess="updateExamDatesUI(data)" />
            <g:remoteFunction controller="wonderpublish" action="getNewBooksList"  onSuccess='latestMcqBooksReceived(data);'
                  params="'categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&pageNo='+pageNo+'&publisherId='+publisherId+'&getSubscription='+subscriptionBooks+'&mcqBook='+mcqBook" />

        }else{
            document.getElementById('datesCardList').innerHTML = "";
        }
    }

    function updateExamDatesUI(data){
        startDateValue = data.startingDate;
        latestDateValue = data.latestDate;
        getRangeDates(startDateValue, latestDateValue);
    }

    function getRangeDates(startDate, endDate) {
        var result = [];
        var datesListArr=[];
        var current = new Date(startDate);
        var end = new Date(endDate);
        var qHtml="";
        while (current <= end){
            result.push(current) && (current = new Date(current)) && current.setDate(current.getDate() + 1);
        }
        for (var i = 0; i<result.length; i ++ ) {
            datesListArr.push(result[i].toISOString().slice(0,10));
        }

        datesArLng = datesListArr.length-1;
        dtsArr = datesListArr;
        datesListArr = datesListArr.reverse();
        for (var q=0;q<datesListArr.length;q++){
            var [year, month, day] = datesListArr[q].split('-');
            var dateFormatted = [day, month, year].join('-');
            var d = new Date(datesListArr[q])
            var t = d.toDateString().split(" ");
            var finalDateVal = t[2] + " " + t[1] + " " + t[3];

            qHtml += "<div class='daily__test-dates__card' id='card-"+(q+1)+"' data-id='"+(q+1)+"'>"+
                     "  <div class='card-title'>"+
                     "    <h4>"+finalDateVal+"</h4>"+
                     "  </div>"+
                     "   <div class='card-options'>"+
                     "      <button onclick=\"javascript:openQuiz('"+dateFormatted+"','" + dailyTestId + "','','false',false);\">" +
                     "          <img src='${assetPath(src: 'prepJoy/prepjoy-website/play_ws.svg')}' >"+
                     "           Play" +
                     "       </button>"+
                     "      <button onclick=\"javascript:openQuiz('"+dateFormatted+"','" + dailyTestId + "','practice','false',false);\">" +
                     "          <img src='${assetPath(src: 'prepJoy/prepjoy-website/practice_ws.svg')}' style='width:15px !important'>"+
                     "           Practice" +
                     "      </button>"+
                     "      <button onclick=\"javascript:openQuiz('"+dateFormatted+"','" + dailyTestId + "','testSeries','false',false);\">" +
                     "          <img src='${assetPath(src: 'prepJoy/prepjoy-website/test_ws-4.svg')}' style='width:15px !important'>"+
                     "           Test" +
                     "      </button>"+
                     "      <button onclick=\"javascript:openQuiz('"+dateFormatted+"','" + dailyTestId + "','flashCard','false',false);\" id='flscard'>" +
                     "          <img src='${assetPath(src: 'prepJoy/prepjoy-website/learn_ws.svg')}' >"+
                     "           Flashcard" +
                     "      </button>"+
                     "      <button onclick=\"javascript:openQuiz('"+dateFormatted+"','" + dailyTestId + "','practice','true',false);\">" +
                     "          <img src='${assetPath(src: 'prepJoy/prepjoy-website/learn_ws.svg')}' >"+
                     "           Study" +
                     "      </button>";
                            <sec:ifLoggedIn>
            qHtml += "      <button onclick=\"javascript:openQuiz('"+dateFormatted+"','" + dailyTestId + "','practice','false',true);\">" +
                     "          <img src='${assetPath(src: 'prepJoy/prepjoy-website/history_ws.svg')}' >"+
                     "           History" +
                     "      </button>";
                            </sec:ifLoggedIn>
            qHtml += "    </div>"+
                     "</div>";
            }
            document.getElementById('datesCardList').innerHTML = qHtml;
            document.querySelector('.datesUIWrapper').classList.remove('d-none');
            document.querySelector('.datesUIWrapper').classList.add('d-flex');
            if (datesListArr.length>5){
                document.getElementById('scrollRight').classList.remove('d-none');
                document.getElementById('scrollLeft').classList.remove('d-none');
            }

            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
        }

        var quizDate;
        var quizResId;
        var qType;
        var lrnVal;
        var histry;

        //API 4: TO GET DAILY TEST QUESTIONS
        function openQuiz(d,i,type,lrn,history){
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            quizDate = d;
            quizResId = i;
            qType=type;
            lrnVal = lrn;
            histry = history;
            $.ajax({
                url:'/prepjoy/getDailyTests?dailyTestId='+quizResId+'&dateInput='+quizDate+'&siteId='+siteId,
                type:'GET' ,
                success: function(data) {
                    validateQuestions(data)
                },
                error: function(data){
                    validateQuestions(data)
                },
            });
        }

        function openHistory(dailyTestId){
            window.location.href = '/prepjoy/history?siteId='+"${session['siteId']}"+'&resId='+dailyTestId+'&quizType=dailyTests';
    }
    //FUNCTION TO REDIRECT TO QUIZ
    function validateQuestions(data){
        if (data.status=='OK'){
            if (lrnVal == 'false' && !histry && qType!='flashCard'){
                var url = "/prepjoy/prepJoyGame?dateInput="+quizDate+"&dailyTestId="+quizResId+"&quizType="+qType+"&learn=false&pubDesk=false&dailyTest=true";
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }else if(lrnVal == 'false' && histry && qType!='flashCard'){
                var url = '/prepjoy/history?siteId='+"${session['siteId']}"+'&resId='+data.realDailyTestDtlId+'&quizType=dailyTests';
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }else if (lrnVal=='false' && !histry && qType=='flashCard'){
               var url = "/resources/displayFlashCards?dateInput="+quizDate+"&dailyTestId="+quizResId+"&fromQuiz=true";
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }else{
                var url = "/funlearn/quiz?dateInput="+quizDate+"&dailyTestId="+quizResId+"&quizMode=learn&pubDesk=false&dailyTest=true";
                setTimeout(() => {
                    window.open(url, '_blank');
                })
            }
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
        }else {
            if (!prepjoySite){
                $('.loading-icon').addClass('hidden');
            }else{
                $('#loading').hide();
            }
            swal({
                title: "Quiz Not Available",
                icon: "error",
                button: "OK",
            });
        }
    }

    function openFlashCard(date,id){
        window.location.href="/resources/displayFlashCards?dateInput="+date+"&dailyTestId="+id+"&fromQuiz=true";
    }

    //EVENT LISTENER TO POPULATE EXAM TYPE BASED ON THE EX.GROUP SELECTION
    $("#examGroup").on('change',function (){
        examGroupSelectedValue = $(this).val();
        setCookie("selectedExamGroupType",examGroupSelectedValue);
        updateExamTypeUI();
        $('.navbtnsPrev,.navbtnsNext').hide();
    })

    //EVENT LISTENER TO POPULATE EXAM DATES BASED ON THE EX.TYPE SELECTION
    $("#examType").on('change',function (){
        examTypeSelectedValue = $(this).val();
        document.getElementById('datesCardList').innerHTML = "";
        setCookie("selectedExamType",examTypeSelectedValue);
        getDailyTestDates(examTypeSelectedValue);
        if ($("#examType").val()==""){
            //document.querySelector('.startHereText').classList.add('d-none');
        }else{
            //document.querySelector('.startHereText').classList.remove('d-none');
        }
        document.getElementById('scrollRight').classList.add('d-none');
        document.getElementById('scrollLeft').classList.add('d-none');
    })

    //PREVENTING CLICKS FOR INACTIVE SLIDES
    function enableClickActions(){
        for (var p=0;p<dtsArr.length;p++){
            var idVal = $('#card-'+(p+1)).attr("data-id");
            if (idVal == (swiper.activeIndex+1)){
                $('#play-'+(p+1)).removeAttr('disabled');
                $('#practice-'+(p+1)).removeAttr('disabled');
                $('#test-'+(p+1)).removeAttr('disabled');
                $('#learn-'+(p+1)).removeAttr('disabled');
                $('#history-'+(p+1)).removeAttr('disabled');
            }else{
                $('#play-'+(p+1)).attr('disabled','disabled');
                $('#practice-'+(p+1)).attr('disabled','disabled');
                $('#test-'+(p+1)).attr('disabled','disabled');
                $('#learn-'+(p+1)).attr('disabled','disabled');
                $('#history-'+(p+1)).attr('disabled','disabled');
            }
        }
    }



    //SHOWING MCQ EB0OKS

    var pageNo = 0;
    var publisherId = null;
    var subscriptionBooks = false;
    var mcqBook = true;

    function latestMcqBooksReceived(data){
        var mcqEbooksList = JSON.parse(data.books).slice(0,5);
        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];
        var listPrice;
        var offerPrice;
        var mcqHtml="";
        for (var m=0;m<mcqEbooksList.length;m++){
            if (mcqEbooksList[m].listPrice == 0 || mcqEbooksList[m].listPrice == 0.0 || mcqEbooksList[m].listPrice == "null" || mcqEbooksList[m].listPrice == null || mcqEbooksList[m].listPrice == mcqEbooksList[m].offerPrice) {
                listPrice = "";
            } else {
                listPrice = "&#x20b9 " + mcqEbooksList[m].listPrice;
            }

            if (mcqEbooksList[m].offerPrice == 0){
                offerPrice = 'Free'
            }else{
                offerPrice = "&#x20b9 "+mcqEbooksList[m].offerPrice
            }

            mcqHtml +="<div class='mcq__ebooks-cards__card'>";
            mcqHtml +="   <div class='books__card-img' onclick=\"openBookDetailsPage('"+mcqEbooksList[m].title+"','"+mcqEbooksList[m].id+"')\">";

            if(mcqEbooksList[m].coverImage!=null && mcqEbooksList[m].coverImage!="null" && mcqEbooksList[m].coverImage!=""){
                mcqHtml +="    <img loading='lazy' src='/funlearn/showProfileImage?id="+mcqEbooksList[m].id+"&fileName="+mcqEbooksList[m].coverImage+"&type=books&imgType=webp' alt=''>";
            }else{
                mcqHtml +="     <div class='uncover'><p>"+mcqEbooksList[m].title+"</p></div>";
            }
            mcqHtml +="</div>"+
                      "<div class='books__card-details'>"+
                      "   <h6 class='books__card-details__title' onclick=\"openBookDetailsPage('"+mcqEbooksList[m].title+"','"+mcqEbooksList[m].id+"')\">"+mcqEbooksList[m].title+"</h6>"+
                      "   <p class='books__card-details__publisher' onclick=\"openBookDetailsPage('"+mcqEbooksList[m].title+"','"+mcqEbooksList[m].id+"')\">"+mcqEbooksList[m].publisher+"</p>"+
                      "   <div>"+
                      "      <span class='books-details__listPrice'>"+listPrice+"</span>"+
                      "      <span class='books-details__sellingPrice'><strong>"+offerPrice+"</strong></span>"+
                      "   </div>";
                    if (userLoggedIn && mcqEbooksList[m].offerPrice>0){
                        mcqHtml +=      "   <div class='books__card-details__cart'>"+
                                     "<button class='books__details-cartBtn' onclick='addToCart("+mcqEbooksList[m].id+")'>"+
                               "         <img loading='lazy' src='/assets/wonderslate/cart-main.webp' width='20' height='20' alt='Cart Icon'>"+
                               "          Add to Cart"+
                               "      </button>"+
                        "   </div>";
                       }else if (!userLoggedIn && mcqEbooksList[m].offerPrice>0){
                        mcqHtml +=      "   <div class='books__card-details__cart'>";
                        mcqHtml +="<button class='books__details-cartBtn' onclick='loginOpen()'>"+
                            "         <img loading='lazy' src='/assets/wonderslate/cart-main.webp' width='20' height='20' alt='Cart Icon'>"+
                            "          Add to Cart"+
                            "      </button>";
                        mcqHtml += "   </div>";
                       }
            mcqHtml +=  "</div>"+
                      "</div>";
        }
        mcqHtml +="<div class='mcq__ebooks-cards__card'><a href=\"/ebooks?level="+level+"&syllabus="+syllabus+"&grade="+grade+"\" class='mcq__ebooks-cards__card d-flex justify-content-center align-items-center showmoreCard' style='height: 300px;background: url(/funlearn/showProfileImage?id="+mcqEbooksList[1].id+"&fileName="+mcqEbooksList[1].coverImage+"&type=books&imgType=webp) no-repeat center center /contain'>"+
            "<p>Show More Books</p>"+
            "</a></div>";

        $("#mcqbooksblock").show();
        document.querySelector('.mcq__ebooks-cards').innerHTML = mcqHtml;
        var fronts = document.querySelectorAll(".uncover");
        for(var i=0 ; i < fronts.length; i++) {
            fronts[i].style.background = colors[i%11];
        }

    }

    function openBookDetailsPage(title,id){
       var path  =  "/"+ title.replaceAll(' ','-') + "/ebook-details?siteName="+siteName+"&bookId="+id+"&preview=true";
       window.location.href = path;
    }


    //LEADERBOARD FUNCTIONALITY
    var currentDate = new Date().toISOString().split("T")[0];
    var instituteId = "<%= session["instituteId"] %>";
    var instLBTab = document.querySelector('.inst__lbTab');
    var allIndLBTab = document.querySelector('.allInd__lbTab');
    var dailyBtn = document.querySelector('.dailyBtn');
    var weeklyBtn = document.querySelector('.weeklyBtn');
    var monthlyBtn = document.querySelector('.monthlyBtn');
    var leaderBoardList = document.getElementById('leaderBoardList');
    var institutionRanks="";
    var allIndiaRank="";
    var instituteTab=true;

    function getDailyRank(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="prepjoy" action="getPrepjoyDailyRanks" params="'rankDate='+currentDate+'&siteId='+siteId" onSuccess="rankUI(data)" />
    }
    function getWeeklyRank(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="prepjoy" action="getPrepjoyWeeklyRanks" params="'rankDate='+currentDate+'&siteId='+siteId" onSuccess="rankUI(data)" />
    }
    function getMonthlyRank(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        <g:remoteFunction controller="prepjoy" action="getPrepjoyMonthlyRanks" params="'rankDate='+currentDate+'&siteId='+siteId" onSuccess="rankUI(data)" />
    }

    getDailyRank();

    dailyBtn.addEventListener('click',function (){
        dailyBtn.classList.add('activeDate');
        weeklyBtn.classList.remove('activeDate');
        monthlyBtn.classList.remove('activeDate');
        instituteTab = instLBTab.classList.contains('activeTab');
        getDailyRank();
    })
    weeklyBtn.addEventListener('click',function (){
        weeklyBtn.classList.add('activeDate');
        monthlyBtn.classList.remove('activeDate');
        dailyBtn.classList.remove('activeDate');
        instituteTab = instLBTab.classList.contains('activeTab');
        getWeeklyRank();
    })
    monthlyBtn.addEventListener('click',function (){
        monthlyBtn.classList.add('activeDate');
        dailyBtn.classList.remove('activeDate');
        weeklyBtn.classList.remove('activeDate');
        instituteTab = instLBTab.classList.contains('activeTab');
        getMonthlyRank();
    })

    instLBTab.addEventListener('click',function (){
        instLBTab.classList.add('activeTab');
        allIndLBTab.classList.remove('activeTab');
        updateRankUI(institutionRanks)
    });

    allIndLBTab.addEventListener('click',function (){
        instLBTab.classList.remove('activeTab');
        allIndLBTab.classList.add('activeTab');
        updateRankUI(allIndiaRank)
    })

    function rankUI(data){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        var showRank=data!="No Ranks"?JSON.parse(data):"";
        updateRankUI(showRank);
    }

    function updateRankUI(showRank){
        var lbHtml = "";

        if(showRank !=""){
            for(var i=0;i<showRank.length;i++){
                lbHtml +="<tr>"+
                    "<td style='padding-left:18px '><strong>"+showRank[i].rank+"</strong></td>"+
                    "<td class='user-info'>"+
                    "<div class='d-flex align-items-center' style='gap: 7px'>"+
                    "<div class='lb-userImg'>";
                if(showRank[i].profilePic !=null && showRank[i].profilePic !="" && showRank[i].profilePic != undefined){
                    lbHtml +="<img src='/funlearn/showProfileImage?id="+showRank[i].userId+"&fileName="+showRank[i].profilePic+"&type=user&imgType=passport'/>";
                }else {
                    lbHtml += "<img src='${assetPath(src:'landingpageImages/img_avatar3.png')}'/>";
                }
                lbHtml +="</div>"+
                    "<div>"+
                    "<p class='name'>"+showRank[i].name+"</p>";
                if (showRank[i].state!="" && showRank[i].state!=undefined){
                    lbHtml +="<p class='place' >"+showRank[i].state+"</p>";
                }
                lbHtml +="</div>"+
                    "</div>"+
                    "</td>"+
                    "<td style='text-align: end;padding-right:25px '><strong>"+showRank[i].userPoints+"</strong></td>"+
                    "</tr>";
            }
        }else{
            lbHtml +="<tr>"+
                "<td></td>"+
                "<td>No users found</td>" +
                "</tr>";
        }

        leaderBoardList.innerHTML = lbHtml;
    }

    var scrollRight = document.getElementById('scrollRight');
    var scrollLeft = document.getElementById('scrollLeft');
    var datesCardList = document.getElementById('datesCardList');
    var scrollLength = 200

    scrollRight.addEventListener('click',function (){
        datesCardList.scrollLeft += scrollLength;
    })
    scrollLeft.addEventListener('click',function (){
        datesCardList.scrollLeft -= scrollLength;
    })

    if (!prepjoySite){
        $('.analyticsDiv').removeClass('d-none');
        $('#ebooksLink').attr('href','/ebooks')
    }else {
        $('#ebooksLink').attr('href','/prepjoy/eBooks');
    }
    <%if("true".equals(session["prepjoySite"])){%>
    function loginOpen(){
        $('#prepjoySignupModal').modal('show');
    }
    <% }%>
</script>
