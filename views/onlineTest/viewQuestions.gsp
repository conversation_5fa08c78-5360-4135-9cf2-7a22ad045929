<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
<script>
    var loggedIn=false;
</script>
<style>
.table-bordered th,td {
    padding: 10px;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
.form-group a {
    color: white;
}

/* Question styling */
.question-text {
    font-weight: bold;
    margin-bottom: 10px;
}
.options {
    margin-left: 20px;
    margin-bottom: 10px;
}
.correct-answer {
    color: green;
    font-weight: bold;
}
.answer-description {
    font-style: italic;
    color: #666;
    margin-top: 5px;
    border-left: 3px solid #ccc;
    padding-left: 10px;
}
.difficulty {
    font-size: 0.9em;
    color: #888;
}
.difficulty-easy {
    color: green;
}
.difficulty-medium {
    color: orange;
}
.difficulty-hard {
    color: red;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="container">
                        <h2 class="text-center">Test Questions: ${testName}</h2>
                        <hr/>

                        <!-- Back button -->
                        <div class="mb-3">
                            <g:link action="listTests" class="btn btn-secondary">
                                <i class="fa fa-arrow-left"></i> Back to Tests
                            </g:link>
                        </div>

                        <!-- Questions Table -->
                        <div id="questions-container">
                            <div class="text-center p-5">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                                <p class="mt-2">Loading questions...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    document.addEventListener("DOMContentLoaded", function() {
        // Load questions via AJAX
        loadQuestions(${testId});
    });

    function loadQuestions(testId) {
        $.ajax({
            url: '${createLink(controller: "onlineTest", action: "getQuestionsJson")}',
            data: { testId: testId },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    displayQuestions(response.questions);
                } else {
                    showError(response.message || 'Failed to load questions');
                }
            },
            error: function(xhr, status, error) {
                showError('Error loading questions: ' + error);
            }
        });
    }

    function displayQuestions(questions) {
        const container = document.getElementById('questions-container');

        if (!questions || questions.length === 0) {
            container.innerHTML = "" +
                "<div class=\"alert alert-info\">" +
                    "No questions found for this test. This could be because:" +
                    "<ul>" +
                        "<li>The test has not been taken by any students yet</li>" +
                        "<li>The test questions are not accessible through the system</li>" +
                    "</ul>" +
                "</div>";
            return;
        }

        let html = "" +
            "<div class=\"table-responsive\">" +
                "<table class=\"table table-striped\" id=\"questionsTable\">" +
                    "<thead>" +
                        "<tr>" +
                            "<th>#</th>" +
                            "<th>Question</th>" +
                            "<th>Options</th>" +
                            "<th>Answer</th>" +
                            "<th>Difficulty</th>" +
                                    "</tr>" +
                    "</thead>" +
                    "<tbody>";


        questions.forEach((q, index) => {
            html += "" +
                "<tr>" +
                    "<td>" + (index + 1) + "</td>" +
                    "<td>" +
                        "<div class=\"question-text\">" + q.question + "</div>" +
                    "</td>" +
                    "<td>" +
                        "<div class=\"options\">" +
                            (q.option1 ? "A) " + q.option1 + "<br/>" : "") +
                            (q.option2 ? "B) " + q.option2 + "<br/>" : "") +
                            (q.option3 ? "C) " + q.option3 + "<br/>" : "") +
                            (q.option4 ? "D) " + q.option4 + "<br/>" : "") +
                            (q.option5 ? "E) " + q.option5 + "<br/>" : "") +
                        "</div>" +
                    "</td>" +
                    "<td>" +
                        "<div class=\"correct-answer\">" + (q.answer || "") + "</div>" +
                        (q.answerDescription ? "<div class=\"answer-description\">" + q.answerDescription + "</div>" : "") +
                    "</td>" +
                    "<td>" +
                        "<div class=\"difficulty difficulty-" + (q.difficultylevel || "").toLowerCase() + "\">" +
                            (q.difficultylevel || "Not specified") +
                        "</div>" +
                    "</td>" +
                "</tr>";
        });

        html += "" +
                    "</tbody>" +
                "</table>" +
            "</div>";

        container.innerHTML = html;

        // Render math formulas after content is loaded
        setTimeout(function() {
            try {
                console.log('Attempting to render math formulas');
                if (typeof renderMathInElement === 'function') {
                    renderMathInElement(document.getElementById('questionsTable'));
                    console.log('Math rendering completed with KaTeX');
                } else if (typeof MathJax !== 'undefined' && MathJax.Hub) {
                    MathJax.Hub.Queue(["Typeset", MathJax.Hub, document.getElementById('questionsTable')]);
                    console.log('Math rendering completed with MathJax');
                } else {
                    console.warn('Math rendering libraries not available');
                }
            } catch (e) {
                console.error('Error rendering math:', e);
            }
        }, 500); // Increased timeout to ensure content is fully loaded
    }

    function showError(message) {
        const container = document.getElementById('questions-container');
        container.innerHTML = "<div class=\"alert alert-danger\">" + message + "</div>";
    }
</script>

</body>
</html>
