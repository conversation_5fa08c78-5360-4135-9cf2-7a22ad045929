<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="groups/groupsStyle.css" async="true"/>

<style>
.user-info-img{
    width: 100%;
    height: 100%;
}
</style>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="admin-panel-header mt-4" id="admin-header">
    <div class="container">
        <div class="header-wrapper d-flex align-items-center justify-content-between">
            <a href="javascript:window.location.replace('/groups/groupDtl?groupId=${params.groupId}')" class="back-btn d-flex">
                <img src="${assetPath(src: 'groups/pink-back.svg')}" class="mr-2"> Back
            </a>
        </div>
        <div class="header-title mt-3 d-flex align-items-center">
            <img src="${assetPath(src: 'groups/memberreq.svg')}" class="mr-2">
            <h4 class="mb-0">Member Request</h4>
        </div>
    </div>
</section>

<section class="reqList mt-4 container" id="reqList">
    <div id="groupRequestList">

    </div>
</section>

<div id="successMsg" class="alert-message hide"></div>
<div id="errorMsg" class="alert-message hide"></div>

<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>
    var groupId = '${params.groupId}';
    var dataObj;
    var imageSrc;

    function getGroupJoinRequests(){
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="groups" action="fetchGroupPendingRequests" params="'groupId='+groupId" onSuccess='showGroupJoinRequests(data);'/>
    }

    function showGroupJoinRequests(data) {
        $(".loading-icon").addClass("hidden");
        var list = data.groupsRequest;
        var htmlStr = "";
        htmlStr += "<div class=\"apr-btns d-flex justify-content-between mb-5 width-size\">" +
                "<button onclick=\"javascript:rejectAllJoinRequests();\" class=\"btn btn-outline-primary btn-block mr-3 w-50\">Reject All</button>" +
                "<button onclick=\"javascript:acceptAllJoinRequests();\" class=\"btn btn-primary btn-block mt-0 w-50\">Approve All</button>" +
            "</div>";
        if(list=="no records found") {
            document.getElementById("groupRequestList").innerHTML = "<p class='mt-5 text-center py-4 card shadow-sm border-light'>No requests found!</p>";
        } else {
            htmlStr += "<ul class='pl-0'>";
            for(var i=0;i<list.length;i++){
                htmlStr += "<li class=\"d-flex align-items-center\">" +
                    "<div class=\"user-dp mr-3 d-flex align-items-center justify-content-center\">";
                if (list[i].profilepic != null){
                    imageSrc = "/funlearn/showProfileImage?id="+list[i].userId+"&fileName="+list[i].profilepic+"&type=user&imgType=passport";
                    htmlStr +=  "<img src='"+imageSrc+"' class=\"user-info-img\">";
                } else {
                    htmlStr += "<img src=\"${assetPath(src: 'groups/bear-1.svg')}\">";
                }
                htmlStr += "</div>" +
                    "<div class=\"user-apr\">" +
                    "<div class=\"usrn mb-3\">" +
                    "<p class=\"mb-0\">"+list[i].name+"</p>" +
                    "<p class='mb-0'>"+list[i].reason+"</p>"+
                    "</div>" +
                    "<div class=\"bt d-flex align-items-center\">" +
                    "<button onclick=\"javascript:rejectJoinRequest('"+list[i].username+"')\" class=\"btn btn-outline-primary mr-2\">Reject</button>" +
                    "<button onclick=\"javascript:acceptJoinRequest('"+list[i].username+"')\" class=\"btn btn-primary mt-0\">Approve</button>" +
                    "</div>" +
                    "</div>" +
                    "</li>";
            }
            htmlStr += "</ul>";
            document.getElementById("groupRequestList").innerHTML = htmlStr;
        }
    }

    getGroupJoinRequests();

    function rejectJoinRequest(username) {
        $(".loading-icon").removeClass("hidden");
        dataObj = {
            groupId: groupId,
            username: username,
        };
        ajaxCall('/groups/deleteJoinRequest','Request rejected!',dataObj);
    }

    function rejectAllJoinRequests() {
        $(".loading-icon").removeClass("hidden");
        dataObj = {
            groupId: groupId,
        };
        ajaxCall('/groups/rejectAllGroupRequest','All requests are rejected!',dataObj);
    }

    function acceptJoinRequest(username) {
        $(".loading-icon").removeClass("hidden");
        dataObj = {
            groupId: groupId,
            username: username,
        };
        ajaxCall('/groups/acceptJoinRequest','Request accepted!',dataObj);
    }

    function acceptAllJoinRequests() {
        $(".loading-icon").removeClass("hidden");
        dataObj = {
            groupId: groupId,
        };
        ajaxCall('/groups/approveAllGroupRequest','All requests are accepted!',dataObj);
    }

    function ajaxCall(url,message,dataObj) {
        $.ajax({
            url: url,
            type: "POST",
            contentType: "application/json",
            dataType:"json",
            data: JSON.stringify(dataObj),
            success: function (data) {
                if (data.status == "OK" || data.status == "Deleted") {
                    $(".loading-icon").addClass("hidden");
                    getGroupJoinRequests();
                }
            },
        });
    }

</script>