<%@ page import="java.text.SimpleDateFormat" %>

<!doctype html>
<html lang="en">
<script>
    // Define global functions to ensure they're available
    window.getBookAnalytics = window.getBookAnalytics || function() {
        console.log('getBookAnalytics called from global fallback');
        // This is a fallback implementation that will be overridden by the actual implementation
        // in the _aiOptions.gsp template
    };
</script>
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <meta http-equiv="Content-Security-Policy" content="media-src https: data:;">
    <title>${title}</title>
    <!-- Favicon -->
    <link rel="shortcut icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon"/>
    <link rel="icon"  href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" type="image/x-icon">
    <link rel="android-touch-icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}"/>
    <link rel="windows-touch-icon" href="/privatelabel/showPrivatelabelImage?siteId=${session["siteId"]}&fileName=${session["favicon"]}" />

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Manrope:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Lexend:wght@100..900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lucide-static@0.294.0/font/lucide.min.css">
    <script src="https://cdn.jsdelivr.net/npm/lucide@0.294.0/dist/umd/lucide.min.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" referrerpolicy="no-referrer" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp&display=swap" async>
    <link rel="stylesheet" href="/assets/bookgpt/bookgpt.css">
    <style>
    .highlight {
        background-color: yellow;
        transition: background-color 0.3s ease;
        transform: translateZ(0);
        will-change: background-color;
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #e4e8f0 100%) !important;
        min-height: 100vh !important;
        font-family: 'Inter', sans-serif !important;
        font-weight: 400 !important;
    }

    /* Make the left panel scrollable */
    #bookGPTReader {
        height: calc(100vh - 64px);
        overflow-y: auto;
        overflow-x: hidden;
        padding-right: 5px;
    }

    /* Aggressive scrollbar styling - forcing thin grey scrollbars */
    #bookGPTReader::-webkit-scrollbar,
    .ai-section::-webkit-scrollbar,
    .accordion-content::-webkit-scrollbar,
    .advanced-tools-content::-webkit-scrollbar,
    body::-webkit-scrollbar,
    div::-webkit-scrollbar,
    ::-webkit-scrollbar {
        width: 4px !important;
        height: 4px !important;
    }

    #bookGPTReader::-webkit-scrollbar-track,
    .ai-section::-webkit-scrollbar-track,
    .accordion-content::-webkit-scrollbar-track,
    .advanced-tools-content::-webkit-scrollbar-track,
    body::-webkit-scrollbar-track,
    div::-webkit-scrollbar-track,
    ::-webkit-scrollbar-track {
        background: #f1f1f1 !important;
        border-radius: 10px !important;
    }

    #bookGPTReader::-webkit-scrollbar-thumb,
    .ai-section::-webkit-scrollbar-thumb,
    .accordion-content::-webkit-scrollbar-thumb,
    .advanced-tools-content::-webkit-scrollbar-thumb,
    body::-webkit-scrollbar-thumb,
    div::-webkit-scrollbar-thumb,
    ::-webkit-scrollbar-thumb {
        background: #c1c1c1 !important;
        border-radius: 10px !important;
        border: none !important;
    }

    #bookGPTReader::-webkit-scrollbar-thumb:hover,
    .ai-section::-webkit-scrollbar-thumb:hover,
    .accordion-content::-webkit-scrollbar-thumb:hover,
    .advanced-tools-content::-webkit-scrollbar-thumb:hover,
    body::-webkit-scrollbar-thumb:hover,
    div::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8 !important;
    }

    /* Firefox scrollbar styling */
    #bookGPTReader,
    .ai-section,
    .accordion-content,
    .advanced-tools-content,
    body,
    div,
    * {
        scrollbar-width: thin !important;
        scrollbar-color: #c1c1c1 #f1f1f1 !important;
    }

    /* Additional override for any element with a scrollbar */
    [style*="overflow"],
    [style*="overflow-y"],
    [style*="overflow: auto"],
    [style*="overflow-y: auto"],
    [style*="overflow: scroll"],
    [style*="overflow-y: scroll"] {
        scrollbar-width: thin !important;
        scrollbar-color: #c1c1c1 #f1f1f1 !important;
    }

    [style*="overflow"]::-webkit-scrollbar,
    [style*="overflow-y"]::-webkit-scrollbar,
    [style*="overflow: auto"]::-webkit-scrollbar,
    [style*="overflow-y: auto"]::-webkit-scrollbar,
    [style*="overflow: scroll"]::-webkit-scrollbar,
    [style*="overflow-y: scroll"]::-webkit-scrollbar {
        width: 4px !important;
        height: 4px !important;
    }

    [style*="overflow"]::-webkit-scrollbar-thumb,
    [style*="overflow-y"]::-webkit-scrollbar-thumb,
    [style*="overflow: auto"]::-webkit-scrollbar-thumb,
    [style*="overflow-y: auto"]::-webkit-scrollbar-thumb,
    [style*="overflow: scroll"]::-webkit-scrollbar-thumb,
    [style*="overflow-y: scroll"]::-webkit-scrollbar-thumb {
        background: #c1c1c1 !important;
        border-radius: 10px !important;
    }

    .chapterWrap {
        display: flex;
        align-items: center;
    }

    /* AI Assistant Header Styles */
    .ai-assistant-header {
        padding: 15px 20px;
        border-radius: 8px 8px 0 0;
        margin: 0 20px 2px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    /* Soft Blue Header */
    .color-option-1 {
        background-color: #e3f2fd;
        border-top: 3px solid #64b5f6;
    }
    .color-option-1 .ai-assistant-title i,
    .color-option-1 .ai-assistant-title h2 {
        color: #1976d2;
        text-shadow: none;
    }

    .ai-assistant-header {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 15px;
    }

    .ai-assistant-title {
        display: flex;
        align-items: center;
    }

    .ai-assistant-title i {
        font-size: 24px;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        width: 24px;
        height: 24px;
        display: flex;
        align-items: center;
        justify-content: center;
        stroke-width: 1.5px;
    }

    /* Icon container for AI Assistant header */
    .ai-assistant-title .icon-container {
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .ai-assistant-title h2 {
        font-size: 20px;
        font-weight: 600;
        color: white;
        margin: 0;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        font-family: 'Lexend', sans-serif;
        letter-spacing: 0.5px;
    }

    /* Command Palette Shortcut - Hidden */
    .command-palette-shortcut {
        display: none;
    }

    /* Chapter navigation styles */
    .chapter-navigation {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        background-color: #ffffff;
        border: 1px solid #e9ecef;
        border-top: none;
        margin: 0 20px 20px;
        border-radius: 0 0 8px 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
    }

    .chapter-select-container {
        flex: 0 0 auto;
        width: 50%;
    }

    .chapter-select-container label {
        font-weight: 600;
        margin-bottom: 5px;
        color: #495057;
        display: block;
    }

    .chapter-select {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        background-color: white;
        font-size: 14px;
        color: #495057;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .chapter-select:focus {
        border-color: #80bdff;
        outline: 0;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }

    .book-view-container {
        margin-left: auto;
        flex: 0 0 auto;
    }

    .book-view-btn {
        background-color: #4285f4; /* Strong brand blue reserved for primary CTA */
        color: white;
        border: none;
        padding: 12px 25px;
        border-radius: 30px; /* Pill shape */
        font-weight: 600;
        font-size: 16px;
        font-family: 'Inter', sans-serif;
        display: inline-flex !important;
        align-items: center !important;
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        transition: all 0.2s ease-in-out;
        box-shadow: 0 4px 8px rgba(66, 133, 244, 0.3);
        letter-spacing: 0.5px;
        white-space: nowrap !important;
        line-height: 1 !important;
    }

    /* Add this to ensure text is properly aligned */
    .book-view-btn span {
        display: inline-block !important;
        vertical-align: middle !important;
    }

    .book-view-btn i {
        font-size: 20px;
        width: 20px;
        height: 20px;
        display: inline-flex !important;
        align-items: center;
        justify-content: center;
        stroke-width: 1.5px;
        color: white;
        vertical-align: middle !important;
        float: none !important;
    }

    /* For Lucide icons */
    .book-view-btn .lucide {
        width: 20px;
        height: 20px;
        stroke-width: 1.5px;
        color: white;
        display: inline-block !important;
        vertical-align: middle !important;
        float: none !important;
    }

    /* Icon container for Book View button */
    .book-view-btn .icon-container {
        width: 32px;
        height: 32px;
        display: inline-flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 50%;
        margin-right: 12px;
        flex-shrink: 0;
        background-color: rgba(255, 255, 255, 0.2);
        float: none !important;
        vertical-align: middle !important;
    }

    .book-view-btn:hover {
        background-color: #3367d6;
        color: white;
        transform: translateY(-2px);
        box-shadow: 0 6px 12px rgba(66, 133, 244, 0.4);
    }

    .book-view-btn:active {
        transform: translateY(1px);
        box-shadow: 0 2px 4px rgba(66, 133, 244, 0.2);
    }

    /* Pulsing animation for Book View button */
    @keyframes pulse {
        0% {
            box-shadow: 0 0 0 0 rgba(66, 133, 244, 0.7);
        }
        70% {
            box-shadow: 0 0 0 10px rgba(66, 133, 244, 0);
        }
        100% {
            box-shadow: 0 0 0 0 rgba(66, 133, 244, 0);
        }
    }

    .pulse-animation {
        animation: pulse 2s infinite;
    }

    /* AI Section Styles */
    .ai-section {
        margin: 0 20px 30px;
    }

    /* Section divider */
    .section-divider {
        margin: 10px 20px 40px;
        border: 0;
        height: 1px;
        background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.15), rgba(0, 0, 0, 0));
    }

    /* Command Palette Styles */
    .command-palette {
        position: fixed !important;
        top: 20% !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        width: 90% !important;
        max-width: 600px !important;
        background-color: white !important;
        border-radius: 12px !important;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2) !important;
        z-index: 1100 !important;
        overflow: hidden !important;
        animation: fadeIn 0.2s ease !important;
        border: 1px solid rgba(0, 0, 0, 0.1) !important;
        color: #333 !important;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -10px); }
        to { opacity: 1; transform: translate(-50%, 0); }
    }

    .command-search-container {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        background-color: #f8f9fa;
    }

    .command-search-container i {
        color: #666;
        font-size: 16px;
        margin-right: 10px;
    }

    .command-search {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 16px;
        outline: none;
        padding: 5px 0;
    }

    .keyboard-shortcut {
        display: flex;
        align-items: center;
        margin-left: 10px;
        color: #666;
        font-size: 12px;
    }

    .keyboard-shortcut span {
        display: inline-block;
        padding: 2px 6px;
        background-color: #f1f3f5;
        border-radius: 4px;
        margin-right: 5px;
        font-weight: 600;
    }

    .command-results {
        padding: 10px 0;
    }

    .result-item {
        display: flex;
        align-items: center;
        padding: 10px 20px;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .result-item:hover, .result-item.active {
        background-color: rgba(66, 133, 244, 0.1);
    }

    .result-icon {
        margin-right: 15px;
    }

    .result-icon i {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: white;
        font-size: 14px;
    }

    .result-label {
        font-size: 14px;
        font-weight: 500;
        color: #333;
    }

    .no-results {
        padding: 20px;
        text-align: center;
        color: #666;
        font-style: italic;
    }

    /* Command Palette Styles */
    .command-palette {
        position: fixed;
        top: 20%;
        left: 50%;
        transform: translateX(-50%);
        width: 90%;
        max-width: 600px;
        background-color: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 12px;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        z-index: 1100;
        overflow: hidden;
        animation: fadeIn 0.2s ease;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translate(-50%, -10px); }
        to { opacity: 1; transform: translate(-50%, 0); }
    }

    .command-search-container {
        display: flex;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        background-color: rgba(248, 249, 250, 0.9);
    }

    .command-search-container i {
        color: #666;
        font-size: 16px;
        margin-right: 10px;
    }

    .command-search {
        flex: 1;
        border: none;
        background: transparent;
        font-size: 16px;
        outline: none;
        padding: 5px 0;
    }

    .keyboard-shortcut {
        display: flex;
        align-items: center;
        margin-left: 10px;
        color: #666;
        font-size: 12px;
    }

    .keyboard-shortcut span {
        display: inline-block;
        padding: 2px 6px;
        background-color: #f1f3f5;
        border-radius: 4px;
        margin-right: 5px;
        font-weight: 600;
    }

    .command-results {
        padding: 10px 0;
        background-color: white;
    }

    .result-item {
        display: flex !important;
        align-items: center !important;
        padding: 10px 20px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        background-color: white !important;
        border-bottom: 1px solid #f5f5f5 !important;
        color: #333 !important;
        visibility: visible !important;
    }

    .result-item:hover, .result-item.active {
        background-color: rgba(66, 133, 244, 0.1);
    }

    .result-icon {
        margin-right: 0 !important;
        flex-shrink: 0 !important;
        width: 30px !important;
        height: 30px !important;
    }

    .result-icon i {
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        color: white;
        font-size: 14px;
        background-color: #4285f4; /* Default background color if none is set */
    }

    .result-label {
        font-size: 14px !important;
        font-weight: 500 !important;
        color: #333 !important;
        flex: 1 !important;
        white-space: nowrap !important;
        overflow: hidden !important;
        text-overflow: ellipsis !important;
        visibility: visible !important;
        display: block !important;
        margin-left: 10px !important;
    }

    .no-results {
        padding: 20px;
        text-align: center;
        color: #666;
        font-style: italic;
    }

    .ai-section-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        padding: 14px 16px;
        border-radius: 12px;
        background-color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border: 1px solid rgba(0, 0, 0, 0.1);
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    .ai-section-header:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        background-color: rgba(255, 255, 255, 0.9);
    }

    .header-left {
        display: flex;
        align-items: center;
    }

    .ai-section-header i:not(.section-chevron) {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.7); /* Darker text for better contrast */
        margin-right: 12px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        stroke-width: 1.5px;
        border-radius: 50%;
        background-color: rgba(208, 228, 247, 0.9); /* Soft blue background */
    }

    /* For Lucide icons */
    .ai-section-header .lucide {
        width: 20px;
        height: 20px;
        stroke-width: 1.5px;
        color: rgba(0, 0, 0, 0.7);
        background-color: transparent;
    }

    /* Icon container for section headers */
    .ai-section-header .icon-container {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .ai-section-header h3 {
        font-size: 16px;
        font-weight: 600;
        color: #333;
        margin: 0;
        font-family: 'Inter', sans-serif;
    }

    /* Section Toggle Styles */
    .section-toggle {
        cursor: pointer;
        display: flex;
        justify-content: space-between;
    }

    .section-chevron {
        font-size: 14px;
        color: #777;
        transition: transform 0.3s ease;
    }

    .section-chevron.active {
        transform: rotate(180deg);
    }

    /* Advanced Tools Content Styles */
    .advanced-tools-content {
        display: none;
        padding-top: 10px;
    }

    .advanced-tools-content.active {
        display: block;
    }

    /* Remove inner scrollbar on Advanced accordion */
    .accordion-content {
        overflow-y: visible !important;
        max-height: none !important;
    }

    /* Accordion Styles */
    .accordion-section {
        margin-bottom: 30px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.7);
    }

    .accordion-section:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .accordion-header {
        display: flex;
        align-items: center;
        padding: 12px;
        background-color: white !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        border-radius: 10px !important;
        cursor: pointer;
        position: relative;
        min-height: 48px;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
        margin-bottom: 15px !important;
        border-bottom: none !important;
    }

    .accordion-header:hover {
        background-color: rgba(255, 255, 255, 0.9);
    }

    .accordion-header i:first-child {
        font-size: 20px;
        margin-right: 12px;
        color: rgba(0, 0, 0, 0.7); /* Darker text for better contrast */
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        stroke-width: 1.5px;
        border-radius: 50%;
    }

    /* Icon container for accordion headers */
    .accordion-header .icon-container {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 12px;
        flex-shrink: 0;
    }

    /* For Lucide icons */
    .accordion-header .lucide {
        width: 20px;
        height: 20px;
        stroke-width: 1.5px;
        color: rgba(0, 0, 0, 0.7);
        background-color: transparent;
    }

    .accordion-header h4 {
        font-size: 15px;
        font-weight: 600;
        color: #333;
        margin: 0;
        flex-grow: 1;
        font-family: 'Inter', sans-serif;
    }

    .accordion-icon {
        font-size: 14px;
        color: #777;
        transition: transform 0.3s ease;
    }

    .accordion-icon.active {
        transform: rotate(180deg);
    }

    .accordion-content {
        padding: 0 !important;
        background-color: transparent !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
        border-top: none !important;
        overflow: visible !important;
        max-height: none !important;
        margin-bottom: 10px !important;
    }

    /* Soft pastel colors for section headers */
    /* Accordion section styles - matching Start Here section */
    .accordion-section {
        margin-bottom: 15px !important;
        border-radius: 12px !important;
        padding: 15px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    }

    /* Ensure page background remains white */
    body, html, .bookgpt, .sections, .pdfViewer, #bookGPTReader, .advanced-tools-content {
        background-color: white !important;
    }

    /* Color variations for different sections - matching Start Here blue background */
    .accordion-section[style*="border-color: #90caf9"],
    .accordion-section[style*="border-color: #81c784"],
    .accordion-section[style*="border-color: #ba68c8"],
    .accordion-section[style*="border-color: #ef9a9a"],
    .accordion-section[style*="border-color: #ffcc80"] {
        background-color: rgba(66, 133, 244, 0.1) !important;
        border: 1px solid rgba(66, 133, 244, 0.2) !important;
    }

    .accordion-content .ai-prompt-buttons {
        padding: 0 !important;
        box-shadow: none !important;
        border: none !important;
        background-color: transparent !important;
        backdrop-filter: none !important;
        -webkit-backdrop-filter: none !important;
        margin-bottom: 0 !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 16px !important;
    }

    .accordion-content .ai-prompt-btn {
        background-color: white !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        border-radius: 10px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
        padding: 12px 16px !important;
        transition: all 0.2s ease-in-out !important;
        flex: 1 !important;
        min-width: 150px !important;
        max-width: 220px !important;
        display: flex !important;
        align-items: center !important;
    }

    .accordion-content .ai-prompt-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        border-color: rgba(66, 133, 244, 0.3) !important;
    }

    /* Add colored circle backgrounds to icons in accordion sections */
    .accordion-content .ai-prompt-btn i {
        width: 32px !important;
        height: 32px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 50% !important;
        margin-right: 12px !important;
        flex-shrink: 0 !important;
        color: white !important;
    }

    /* Apply the same color variations as in Start Here section */
    .accordion-content .ai-prompt-btn[onclick*="Lesson Plan"] i,
    .accordion-content .ai-prompt-btn[onclick*="Learning"] i,
    .accordion-content .ai-prompt-btn[onclick*="Teach"] i {
        background-color: rgba(66, 133, 244, 0.8) !important; /* Blue */
    }

    .accordion-content .ai-prompt-btn[onclick*="Summary"] i,
    .accordion-content .ai-prompt-btn[onclick*="Explain"] i,
    .accordion-content .ai-prompt-btn[onclick*="Simplify"] i {
        background-color: rgba(52, 168, 83, 0.8) !important; /* Green */
    }

    .accordion-content .ai-prompt-btn[onclick*="Quiz"] i,
    .accordion-content .ai-prompt-btn[onclick*="Test"] i,
    .accordion-content .ai-prompt-btn[onclick*="Question"] i,
    .accordion-content .ai-prompt-btn[onclick*="Practice"] i {
        background-color: rgba(234, 67, 53, 0.8) !important; /* Red */
    }

    .accordion-content .ai-prompt-btn[onclick*="Concept"] i,
    .accordion-content .ai-prompt-btn[onclick*="Definition"] i,
    .accordion-content .ai-prompt-btn[onclick*="Key Point"] i {
        background-color: rgba(161, 66, 244, 0.8) !important; /* Purple */
    }

    .accordion-content .ai-prompt-btn[onclick*="Example"] i,
    .accordion-content .ai-prompt-btn[onclick*="Illustration"] i,
    .accordion-content .ai-prompt-btn[onclick*="Demonstrate"] i {
        background-color: rgba(251, 140, 0, 0.8) !important; /* Orange */
    }

    /* Additional color variations */
    .accordion-content .ai-prompt-btn[onclick*="Highlight"] i,
    .accordion-content .ai-prompt-btn[onclick*="Note"] i {
        background-color: rgba(255, 193, 7, 0.8) !important; /* Yellow */
    }

    .accordion-content .ai-prompt-btn[onclick*="Formula"] i,
    .accordion-content .ai-prompt-btn[onclick*="Equation"] i {
        background-color: rgba(0, 150, 136, 0.8) !important; /* Teal */
    }

    .accordion-content .ai-prompt-btn[onclick*="Mistake"] i,
    .accordion-content .ai-prompt-btn[onclick*="Error"] i {
        background-color: rgba(233, 30, 99, 0.8) !important; /* Pink */
    }

    .accordion-content .ai-prompt-btn[onclick*="Analytic"] i,
    .accordion-content .ai-prompt-btn[onclick*="Chart"] i {
        background-color: rgba(63, 81, 181, 0.8) !important; /* Indigo */
    }

    /* Default color for any remaining icons */
    .accordion-content .ai-prompt-btn i:not([style*="background-color"]) {
        background-color: rgba(158, 158, 158, 0.8) !important; /* Grey - default fallback */
    }

    /* AI Prompt Buttons */
    .ai-prompt-buttons {
        padding: 20px;
        background-color: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 12px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.5);
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-bottom: 24px;
        transition: all 0.3s ease;
    }

    .ai-prompt-buttons:hover {
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .ai-prompt-btn {
        flex: 1;
        min-width: 150px;
        max-width: 220px;
        padding: 14px 16px;
        background-color: rgba(255, 255, 255, 0.8);
        color: #333;
        border: 1px solid rgba(0, 0, 0, 0.1);
        border-radius: 12px;
        font-weight: 500;
        font-size: 14px;
        font-family: 'Inter', sans-serif;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: auto;
        min-height: 48px;
        touch-action: manipulation;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }

    .ai-prompt-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        background-color: rgba(255, 255, 255, 0.9);
    }

    .ai-prompt-btn i {
        font-size: 20px;
        margin-right: 12px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: rgba(0, 0, 0, 0.7); /* Darker text for better contrast */
        flex-shrink: 0;
        stroke-width: 1.5px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.7);
    }

    /* For Lucide icons */
    .ai-prompt-btn .lucide {
        width: 20px;
        height: 20px;
        stroke-width: 1.5px;
        margin-right: 12px;
        color: rgba(0, 0, 0, 0.7);
        background-color: transparent;
    }

    /* Icon container for Lucide icons */
    .ai-prompt-btn .icon-container {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        margin-right: 12px;
        flex-shrink: 0;
    }

    .ai-prompt-btn span {
        font-size: 13px;
        font-weight: 500;
        white-space: normal;
        line-height: 1.3;
        overflow-wrap: break-word;
        word-wrap: break-word;
        hyphens: auto;
    }

    /* Icon specific colors */
    .ai-prompt-btn.lesson-plan i {
        background-color: #3498db;
    }

    .ai-prompt-btn.create-test i {
        background-color: #2ecc71;
    }

    .ai-prompt-btn.chapter-summary i {
        background-color: #9b59b6;
    }

    .ai-prompt-btn.analytics i {
        background-color: #e74c3c;
    }

    .ai-prompt-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        border-color: #ccc;
    }

    .ai-prompt-btn.lesson-plan:hover {
        border-color: #3498db;
    }

    .ai-prompt-btn.create-test:hover {
        border-color: #2ecc71;
    }

    .ai-prompt-btn.chapter-summary:hover {
        border-color: #9b59b6;
    }

    .ai-prompt-btn.analytics:hover {
        border-color: #e74c3c;
    }

    .ai-prompt-btn:active {
        transform: translateY(1px);
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }

    /* Loading Indicator */
    #ai-loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.7);
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    }

    #ai-loading-indicator p {
        color: white;
        font-size: 18px;
        margin-top: 20px;
        font-weight: 500;
    }

    .spinner {
        width: 50px;
        height: 50px;
        border: 5px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
    }

    @keyframes spin {
        to { transform: rotate(360deg); }
    }

    /* AI Options styles */
    .ai-options-container {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        padding: 0 20px 20px;
    }

    .ai-option-btn {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        padding: 15px;
        min-width: 100px;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .ai-option-btn:hover {
        background-color: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .ai-option-btn i {
        font-size: 24px;
        margin-bottom: 8px;
        color: #007bff;
    }

    .ai-option-btn span {
        font-size: 14px;
        font-weight: 500;
        color: #495057;
    }

    /* Toggle Switch Styles */
    .toggle-switch {
        position: relative;
        display: inline-flex;
        align-items: center;
        cursor: pointer;
        margin: 10px;
    }

    .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
    }

    .toggle-slider {
        position: relative;
        display: inline-block;
        width: 56px;
        height: 28px;
        background-color: #e0e0e0;
        border-radius: 34px;
        transition: .3s;
        margin-right: 12px;
        box-shadow: inset 0 1px 3px rgba(0,0,0,0.1);
    }

    .toggle-slider:before {
        position: absolute;
        content: "";
        height: 22px;
        width: 22px;
        left: 3px;
        bottom: 3px;
        background-color: white;
        border-radius: 50%;
        transition: .3s;
        box-shadow: 0 1px 3px rgba(0,0,0,0.15);
    }

    input:checked + .toggle-slider {
        background-color: #4285f4;
    }

    input:focus + .toggle-slider {
        box-shadow: 0 0 0 2px rgba(66, 133, 244, 0.25);
    }

    input:checked + .toggle-slider:before {
        transform: translateX(28px);
    }

    .toggle-label {
        font-size: 15px;
        font-weight: 600;
        color: #4285f4;
        letter-spacing: 0.3px;
    }

    @media (max-width: 768px) {
        .ai-assistant-header {
            margin: 0 10px 2px;
            padding: 12px 15px;
        }

        .ai-assistant-title i {
            font-size: 20px;
        }

        .ai-assistant-title h2 {
            font-size: 18px;
        }

        .command-palette-shortcut {
            padding: 3px 8px;
        }

        .shortcut-text {
            display: none;
        }

        .chapter-navigation {
            flex-direction: column;
            align-items: stretch;
            padding: 10px;
            margin: 0 10px 15px;
        }

        .chapter-select-container {
            width: 100%;
            margin-bottom: 15px;
        }

        .book-view-container {
            margin-left: 0;
            margin-top: 15px;
            width: 100%;
        }

        .book-view-btn {
            width: 100%;
            justify-content: center !important;
            padding: 12px 20px;
            flex-direction: row !important;
            flex-wrap: nowrap !important;
            white-space: nowrap !important;
            display: inline-flex !important;
            align-items: center !important;
            line-height: 1 !important;
        }

        .ai-options-container {
            justify-content: center;
            padding: 0 10px 15px;
        }

        .ai-option-btn {
            min-width: 80px;
            padding: 10px;
        }

        .ai-section {
            margin: 0 10px 15px;
        }

        .section-divider {
            margin: 10px 10px 30px;
        }

        .ai-section-header {
            padding: 0 5px;
        }

        /* Mobile styles for Advanced Tools */
        .accordion-header {
            padding: 10px 12px;
        }

        .accordion-header h4 {
            font-size: 14px;
        }

        .accordion-content {
            padding: 10px;
        }

        .ai-prompt-buttons {
            padding: 12px 10px;
            justify-content: center;
        }

        .ai-prompt-btn {
            min-width: 120px;
            max-width: none;
            padding: 8px;
            min-height: 45px;
            height: auto;
            flex-basis: calc(50% - 10px);
        }

        .ai-prompt-btn i {
            font-size: 14px;
            width: 28px;
            height: 28px;
            margin-right: 8px;
        }

        .ai-prompt-btn span {
            font-size: 12px;
        }
    }

    .bookgpt .sections .pdfViewer {
        background: #f8f9fa !important
    }

    #defaultPromptPanel {
        background: #f8f9fa !important
    }

    .bookgpt .sections .chatViewer .chatOptions {
        background: #f8f9fa !important
    }

    .testseriesDisclaimer {
        color: #999;
        font-size: 12px;
        display: none;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
    }

    .botMessage img {
        width: 100%;
        overflow: scroll;
        object-fit: contain;
    }

    .book-title {
        margin: 0 0 0 15px;
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        font-family: 'Lexend', sans-serif;
    }

    /* Splitter style - subtle resize handle */
    .splitter {
        width: 2px !important;
        background-color: rgba(237, 97, 20, 0.4) !important; /* #ED6114 with 40% opacity */
        cursor: col-resize !important;
    }

    /* Fix for lucida.css adding additional icons */
    .icon-container:before,
    .icon-container:after,
    [class*="icon-"]:before,
    [class*="icon-"]:after,
    [data-lucide]:before,
    [data-lucide]:after {
        content: none !important;
        display: none !important;
        opacity: 0 !important;
        visibility: hidden !important;
        width: 0 !important;
        height: 0 !important;
        position: absolute !important;
        pointer-events: none !important;
    }

    /* Improve icon alignment and backgrounds */
    .icon-container {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        /* No default background color - each icon will have its own */
    }

    /* Ensure icons are centered */
    .icon-container i,
    .icon-container .lucide {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        margin: auto !important;
    }

    /* Ensure Advanced tools section icons have colored backgrounds */
    .accordion-content .ai-prompt-btn .icon-container {
        background-color: rgba(66, 133, 244, 0.8) !important; /* Default blue */
    }

    /* Specific colors for different types of buttons */
    /* Blue variants */
    .ai-prompt-btn[onclick*="Lesson Plan"] .icon-container,
    .ai-prompt-btn[onclick*="Learning"] .icon-container,
    .ai-prompt-btn[onclick*="Teach"] .icon-container {
        background-color: rgba(66, 133, 244, 0.8) !important; /* Blue */
    }

    /* Green variants */
    .ai-prompt-btn[onclick*="Summary"] .icon-container,
    .ai-prompt-btn[onclick*="Explain"] .icon-container,
    .ai-prompt-btn[onclick*="Simplify"] .icon-container {
        background-color: rgba(52, 168, 83, 0.8) !important; /* Green */
    }

    /* Red variants */
    .ai-prompt-btn[onclick*="Quiz"] .icon-container,
    .ai-prompt-btn[onclick*="Test"] .icon-container,
    .ai-prompt-btn[onclick*="Question"] .icon-container,
    .ai-prompt-btn[onclick*="Practice"] .icon-container {
        background-color: rgba(234, 67, 53, 0.8) !important; /* Red */
    }

    /* Purple variants */
    .ai-prompt-btn[onclick*="Concept"] .icon-container,
    .ai-prompt-btn[onclick*="Definition"] .icon-container,
    .ai-prompt-btn[onclick*="Key Point"] .icon-container {
        background-color: rgba(161, 66, 244, 0.8) !important; /* Purple */
    }

    /* Orange variants */
    .ai-prompt-btn[onclick*="Example"] .icon-container,
    .ai-prompt-btn[onclick*="Illustration"] .icon-container,
    .ai-prompt-btn[onclick*="Demonstrate"] .icon-container {
        background-color: rgba(251, 140, 0, 0.8) !important; /* Orange */
    }

    /* Teal variants */
    .ai-prompt-btn[onclick*="Compare"] .icon-container,
    .ai-prompt-btn[onclick*="Contrast"] .icon-container,
    .ai-prompt-btn[onclick*="Difference"] .icon-container {
        background-color: rgba(0, 150, 136, 0.8) !important; /* Teal */
    }

    /* Pink variants */
    .ai-prompt-btn[onclick*="Analyze"] .icon-container,
    .ai-prompt-btn[onclick*="Evaluate"] .icon-container,
    .ai-prompt-btn[onclick*="Critique"] .icon-container {
        background-color: rgba(233, 30, 99, 0.8) !important; /* Pink */
    }

    /* Yellow variants */
    .ai-prompt-btn[onclick*="Note"] .icon-container,
    .ai-prompt-btn[onclick*="Highlight"] .icon-container,
    .ai-prompt-btn[onclick*="Important"] .icon-container {
        background-color: rgba(255, 193, 7, 0.8) !important; /* Yellow */
    }

    /* Indigo variants */
    .ai-prompt-btn[onclick*="Discuss"] .icon-container,
    .ai-prompt-btn[onclick*="Debate"] .icon-container,
    .ai-prompt-btn[onclick*="Argument"] .icon-container {
        background-color: rgba(63, 81, 181, 0.8) !important; /* Indigo */
    }

    /* Brown variants */
    .ai-prompt-btn[onclick*="History"] .icon-container,
    .ai-prompt-btn[onclick*="Background"] .icon-container,
    .ai-prompt-btn[onclick*="Context"] .icon-container {
        background-color: rgba(121, 85, 72, 0.8) !important; /* Brown */
    }

    /* Default color for any remaining icons */
    .ai-prompt-btn .icon-container:not([style*="background-color"]) {
        background-color: rgba(158, 158, 158, 0.8) !important; /* Grey - default fallback */
    }

    /* Styles for Start Here section */
    .start-here-section {
        margin-top: 10px !important;
        margin-bottom: 20px !important;
        background-color: rgba(66, 133, 244, 0.1) !important;
        border: 1px solid rgba(66, 133, 244, 0.2) !important;
        border-radius: 12px !important;
        padding: 15px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
    }

    .start-here-section .ai-section-header {
        margin-bottom: 15px !important;
        cursor: default !important;
        pointer-events: none !important;
        background-color: white !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        border-radius: 10px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
        padding: 12px !important;
    }

    .start-here-section .section-chevron {
        display: none !important;
    }

    /* Fix for the buttons layout */
    .start-here-section .ai-prompt-buttons {
        padding: 0 !important;
        margin-top: 0 !important;
        margin-bottom: 0 !important;
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 16px !important;
        background-color: transparent !important;
        border: none !important;
        box-shadow: none !important;
    }

    /* Ensure buttons have proper sizing */
    .start-here-section .ai-prompt-btn {
        flex: 1 !important;
        min-width: 150px !important;
        max-width: 220px !important;
        display: flex !important;
        align-items: center !important;
        transition: all 0.2s ease-in-out !important;
        background-color: white !important;
        border-radius: 10px !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
        border: 1px solid rgba(0, 0, 0, 0.05) !important;
        padding: 12px 16px !important;
    }

    .start-here-section .ai-prompt-btn:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
        border-color: rgba(66, 133, 244, 0.3) !important;
        background-color: white !important;
    }

    @media (max-width: 768px) {
        .testseriesDisclaimer {
            font-size: 13px;
        }

        .book-title {
            font-size: 1.2rem;
            margin-left: 10px;
            max-width: 60%;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
    }
    </style>
    <asset:stylesheet href="wonderslate/vendors.min.css" async="true" media="all"/>
    <script src="https://code.jquery.com/jquery-1.12.4.min.js" crossorigin="anonymous"></script>
    <script src="/assets/wonderslate/vendors.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js" defer></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/styles/default.min.css">
    <script src="https://unpkg.com/@dotlottie/player-component@latest/dist/dotlottie-player.mjs" type="module"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>
    <script src="/assets/bookGPTScripts/resizeScreens.js"></script>
    <script src="https://www.youtube.com/iframe_api"></script>
    <script src="/assets/fixAccordions.js"></script>
    <script>
        // Override the default accordion behavior for the Start Here section
        document.addEventListener('DOMContentLoaded', function() {
            // Get the Start Here section toggle
            const startHereToggle = document.querySelector('.start-here-section .section-toggle');

            if (startHereToggle) {
                const content = startHereToggle.closest('.ai-section').querySelector('.ai-prompt-buttons');

                // Make sure content is always visible
                if (content) {
                    content.style.display = 'flex';
                }

                // Override the click event
                startHereToggle.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }, true);

                console.log('Start Here section initialized');
            }

            // Apply colored backgrounds to icons in accordion sections
            const colorIcons = function() {
                // Define color mapping based on keywords
                const colorMap = [
                    { keywords: ['Lesson Plan', 'Learning', 'Teach'], color: 'rgba(66, 133, 244, 0.8)' }, // Blue
                    { keywords: ['Summary', 'Explain', 'Simplify'], color: 'rgba(52, 168, 83, 0.8)' }, // Green
                    { keywords: ['Quiz', 'Test', 'Question', 'Practice'], color: 'rgba(234, 67, 53, 0.8)' }, // Red
                    { keywords: ['Concept', 'Definition', 'Key Point'], color: 'rgba(161, 66, 244, 0.8)' }, // Purple
                    { keywords: ['Example', 'Illustration', 'Demonstrate'], color: 'rgba(251, 140, 0, 0.8)' }, // Orange
                    { keywords: ['Highlight', 'Note', 'Important'], color: 'rgba(255, 193, 7, 0.8)' }, // Yellow
                    { keywords: ['Formula', 'Equation'], color: 'rgba(0, 150, 136, 0.8)' }, // Teal
                    { keywords: ['Mistake', 'Error'], color: 'rgba(233, 30, 99, 0.8)' }, // Pink
                    { keywords: ['Analytic', 'Chart'], color: 'rgba(63, 81, 181, 0.8)' }, // Indigo
                    { keywords: ['Homework', 'Assignment'], color: 'rgba(121, 85, 72, 0.8)' }, // Brown
                    { keywords: ['Video', 'Watch'], color: 'rgba(244, 67, 54, 0.8)' }, // Red
                    { keywords: ['Real-World', 'Globe'], color: 'rgba(33, 150, 243, 0.8)' }, // Blue
                    { keywords: ['Flashcard', 'Card'], color: 'rgba(255, 152, 0, 0.8)' }, // Orange
                    { keywords: ['Trick', 'Tip'], color: 'rgba(156, 39, 176, 0.8)' }, // Purple
                    { keywords: ['Revision', 'Review'], color: 'rgba(76, 175, 80, 0.8)' } // Green
                ];

                // Get all buttons in accordion sections
                const buttons = document.querySelectorAll('.accordion-content .ai-prompt-btn');

                buttons.forEach(button => {
                    const buttonText = button.textContent.trim();
                    const icon = button.querySelector('i');

                    if (icon) {
                        // Default color (grey)
                        let bgColor = 'rgba(158, 158, 158, 0.8)';

                        // Check if button text contains any keywords
                        for (const mapping of colorMap) {
                            if (mapping.keywords.some(keyword => buttonText.includes(keyword))) {
                                bgColor = mapping.color;
                                break;
                            }
                        }

                        // Apply the background color
                        icon.style.backgroundColor = bgColor;
                    }
                });
            };

            // Run the color function
            colorIcons();

            // Additional function to ensure scrollbars are styled correctly
            const fixScrollbars = function() {
                // Create a style element
                const style = document.createElement('style');
                style.textContent = `
                    ::-webkit-scrollbar {
                        width: 4px !important;
                        height: 4px !important;
                    }
                    ::-webkit-scrollbar-track {
                        background: #f1f1f1 !important;
                        border-radius: 10px !important;
                    }
                    ::-webkit-scrollbar-thumb {
                        background: #c1c1c1 !important;
                        border-radius: 10px !important;
                        border: none !important;
                    }
                    ::-webkit-scrollbar-thumb:hover {
                        background: #a8a8a8 !important;
                    }
                    * {
                        scrollbar-width: thin !important;
                        scrollbar-color: #c1c1c1 #f1f1f1 !important;
                    }
                `;

                // Append the style element to the head
                document.head.appendChild(style);

                // Force a repaint to apply the scrollbar styles
                document.body.style.display = 'none';
                setTimeout(() => {
                    document.body.style.display = '';
                }, 10);
            };

            // Run the scrollbar fix
            fixScrollbars();
        });
    </script>
    <script>
        var loggedIn = false;
    </script>

    <sec:ifLoggedIn>
        <script>
            loggedIn = true;
        </script>
    </sec:ifLoggedIn>
    <script>
        var gptSiteId =
        ${session["siteId"]}
        var bookType = "bookgpt"
        let basePrompts = []
        let loggedInUser = false;
        var isTestSeries = false;
        let suggestedVideos = []
        var isTeacher = false;
        let isContentsLocked = false;
        <sec:ifLoggedIn>
        loggedInUser = true;
        </sec:ifLoggedIn>

        let mobileView = false;
        var gptResId;
        var gptChapterId;
        var gptBookId='${params.bookId}';
        let profilePic = ''
        let freeTokenCount=99999
        let username=''
        let userId=''
        let namespace;
        let isOffline = false;
        let siteName="";
        let gptcustomloader = "${gptcustomloader}"
        let gptloaderpath = "${gptloaderpath}"
        let gptloaderName = "${gptloaderName}"
        const bookTitle = '${bookTitle}'
        <%if("true".equals(session["prepjoySite"]) || "true".equals(session["commonWhiteLabel"])){%>
        siteName="${session['siteName']}";
        <%}else{%>
        siteName="${session['entryController']}";
        <% }%>
        <%if(session["userdetails"]!=null){%>
        username = '${session["userdetails"].username}'
        username = username.replace("&#64;",'@')
        profilePic = '${session["userdetails"].profilepic}'
        userId = '${session["userdetails"].id}'
        <%}%>

        function isMobile() {
            return window.innerWidth <= 768;
        }

        if (isMobile()) {
            mobileView = true;
        }

        function isIOS() {
            const ua = navigator.userAgent || navigator.vendor || window.opera;
            return /iPad|iPhone|iPod/.test(ua) && !window.MSStream;
        }
    </script>
</head>
<body>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<div class="warning-dialog">
    <i class="fa-solid fa-xmark warning-close" onclick="closeWarningDialog()"></i>
    <i class="fa-solid fa-triangle-exclamation warning-icon"></i>

    <p class="warning-text">Unstable internet connection detected.</p>
</div>

<div class="bookgpt">
    <div class="header">
        <div class="chapterWrap">
            <button class="backButton" id="backButton"><i class="fa-solid fa-chevron-left"></i></button>
            <% if (isMobile) { %>
            <button class="readBookBtn" id="readBookBtn" style="display:none;"><i
                    class="fa-solid fa-chevron-left"></i> Back</button>
            <% } %>
            <h2 class="book-title">${bookTitle}</h2>
        </div>

        <% if (previewMode && !hasBookAccess) { %>
        <div class="gptBuyNowBtnWrap" style="display:flex;align-items: center">
            <sec:ifNotLoggedIn>
                <a href="javascript:openLoginModal()" style="font-size: 12px;margin-right: 5px;">Login</a>
            </sec:ifNotLoggedIn>
            <button class="gptBuyNowBtn" id="gptBuyNowBtn" onclick="openBookDtlPage()">Buy Now</button>
        </div>
        <% } %>
    </div>

    <div class="sections resizable-x">

        <% if (!isMobile) { %>
        <div class="panel-container">
            <div class="panel-left">
                <div class="pdfViewer" style="flex: 70%;">

                    <div class="pdfOpts" style="justify-content: flex-end">
                        <label class="toggle-switch" title="Turn on GPT Sir chat">
                            <input type="checkbox" id="fullScreenToggle" class="fullScreenBtn" data-chat="open">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">AI Chat</span>
                        </label>
                    </div>

                    <div id="bookGPTReader">
                        <g:render template="/wpmain/aiOptions"></g:render>
                    </div>
                </div>
            </div>

            <div class="splitter" title="HOLD AND MOVE LEFT OR RIGHT TO ADJUST THE SCREEN.">
            </div>

            <div class="panel-right">
                <g:render template="/prompt/bookgptChatViewer"></g:render>
            </div>
        </div>
        <% } else { %>
        <div class="pdfViewer mobileViewer" id="mobilePdfViewer">

            <div id="bookGPTReader">
                <g:render template="/wpmain/aiOptions" model="[bookId: bookId, userRoles: userRoles, chaptersList: chaptersList]"></g:render>
            </div>
            <button class="cta-button" id="openBookGPT">
                <span class="logo">AI<span class="logoHighlight">&nbsp;Tutor</span>
                </span>
                <i class="fa-solid fa-right-long gptBtnArrow"></i>
            </button>
        </div>
        <g:render template="/prompt/bookgptChatViewerMobile"></g:render>
        <% } %>
    </div>

    <div id="feedbackModal" class="feedbackModal">
        <div class="modal-content">
            <div id="feedbackContent"></div>
        </div>
    </div>

    <div id="videoModal" class="videoModal">
        <div class="videoModal-modal-content">
            <span class="videoModal-closeModalBtn" id="videoModal-closeModalBtn" onclick=closeytMdal()>&times;</span>

            <div class="video-wrapper">
                <iframe id="youtubeVideo" src="" frameborder="0" allow="autoplay; encrypted-media"
                        allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>

<div id="selection-box"></div>

<div id="printableDiv"></div>
<script src="/assets/bookGPTScripts/languageDetection.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    // Ensure global functions are accessible
    document.addEventListener('DOMContentLoaded', function() {
        console.log('Checking if getBookAnalytics is defined:', typeof window.getBookAnalytics);
    });
</script>
<script src="/assets/bookGPTScripts/bookGPTChatViewer.js"></script>
<script src="/assets/bookGPTScripts/testSeriesChat.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', (e) => {

        if (document.querySelector('.fullScreenBtn')) {
            document.querySelector('.fullScreenBtn').style.display = "none";
        }

        //ignore if it is coming from page if the url contains /bookgpt
        if (!document.referrer.includes("/bookgpt")) {
            if (document.referrer == "" && loggedInUser) {
                localStorage.setItem('referrerBookAI', window.location.origin + "/wsLibrary/myLibrary")
            } else if (document.referrer == "" && !loggedInUser) {
                localStorage.setItem('referrerBookAI', window.location.origin + "/")
            } else {
                localStorage.setItem('referrerBookAI', document.referrer)
            }
        }


    })


    function openBookDtlPage() {
        let bookRedirectionURL = ""
        if (bookPriceDtls.length > 1) {
            const hasEbookGPTUpgrade = bookPriceDtls.some(price => price.bookType === "ebookGPTUpgrade");
            if (isContentsLocked && hasEbookGPTUpgrade) {
                bookRedirectionURL = "/" + bookTitle.replaceAll(' ', '-') + "/ebook-details?siteName=" + siteName + "&bookId=" + gptBookId + "&preview=true&bookType=ebookGPTUpgrade&addToCart=true"
            } else {
                bookRedirectionURL = "/" + bookTitle.replaceAll(' ', '-') + "/ebook-details?siteName=" + siteName + "&bookId=" + gptBookId + "&preview=true"
            }
        } else {
            const bookType = bookPriceDtls[0].bookType
            bookRedirectionURL = "/" + bookTitle.replaceAll(' ', '-') + "/ebook-details?siteName=" + siteName + "&bookId=" + gptBookId + "&preview=true&bookType=" + bookType + "&addToCart=true"
        }
        window.open(bookRedirectionURL, '_blank');
    }


    function goBack() {
        const refLink = localStorage.getItem('referrerBookAI')
        if (refLink && refLink != "") {
            if (refLink.includes("ebook-details")) {
                window.close();
            } else {
                window.location.href = refLink;
            }
        }
    }

    document.getElementById('backButton').addEventListener('click', goBack)

    function addTextToDoubts(str) {
        document.getElementById('chatInput').value = str
        if (mobileView) {
            showMobileChat()
        } else {
            document.getElementById('chatInput').focus()
        }
    }


    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }

    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }


    window.addEventListener('beforeunload', function (event) {
       const pdfFlex = document.querySelector('.panel-left')
        if (pdfFlex) {
            const width = pdfFlex.offsetWidth;
            setCookie('pdfViewerWidth', width)
        }
    });


    if (!mobileView) {
        const fullScreenToggle = document.getElementById('fullScreenToggle')

        // Set initial state - checked means chat is visible
        fullScreenToggle.checked = true;

        fullScreenToggle.addEventListener('change', (e) => {
            const panelLeftDiv = document.querySelector('.panel-left')
            if (panelLeftDiv) {
                if (!fullScreenToggle.checked) {
                    // Toggle is off - hide chat (full screen mode)
                    hideChatWindow(fullScreenToggle, panelLeftDiv)
                } else {
                    // Toggle is on - show chat
                    showChatWindow(fullScreenToggle, panelLeftDiv)
                }
            }
        })
    }

    function hideChatWindow(fullScreenToggle, panelLeftDiv) {
        if (!isMobile()) {
            panelLeftDiv.style.width = "100%";
            panelLeftDiv.style.maxWidth = "100%";
            // Update toggle state without triggering the change event
            if (fullScreenToggle && fullScreenToggle.checked) {
                fullScreenToggle.checked = false;
            }
            // Update the data-chat attribute for compatibility with other functions
            if (fullScreenToggle) {
                fullScreenToggle.setAttribute("data-chat", "close");
            }
        } else {
            hideMobileChat()
        }

        document.getElementById('showClearChatBtn').style.display = 'none';
    }

    function showChatWindow(fullScreenToggle, panelLeftDiv) {
        if (!isMobile()) {
            panelLeftDiv.style.width = "70%";
            panelLeftDiv.style.maxWidth = "95%";
            // Update toggle state without triggering the change event
            if (fullScreenToggle && !fullScreenToggle.checked) {
                fullScreenToggle.checked = true;
            }
            // Update the data-chat attribute for compatibility with other functions
            if (fullScreenToggle) {
                fullScreenToggle.setAttribute("data-chat", "open");
            }
        } else {
            showMobileChat()
        }
        document.getElementById('showClearChatBtn').style.display = 'block';
    }

    async function showChatWindowCB(actionObj, actionType, actionId, language, languageVal) {
        const fullScreenToggle = document.getElementById('fullScreenToggle')
        const panelLeftDiv = document.querySelector('.panel-left')
        const openBookGPTBtnTemp = document.getElementById('openBookGPT')

        showChatWindow(fullScreenToggle, panelLeftDiv)
                pageNo = 0;


    }

    function hideChatWindowCB() {
        const fullScreen = document.querySelector('.fullScreenBtn')
        const panelLeftDiv = document.querySelector('.panel-left')
        hideChatWindow(fullScreen, panelLeftDiv)
        if (controller) {
            controller.abort();
            controller = null;
        }
    }

    const showOfflineDialog = (msg) => {
        const warningDialog = document.querySelector('.warning-dialog')
        const warningIcon = document.querySelector('.warning-icon')
        const warningText = document.querySelector('.warning-text')
        if (mobileView) {
            warningDialog.style.top = '45px'
        } else {
            warningDialog.style.top = '33px'
        }
        warningText.textContent = msg
        warningIcon.classList.add('fa-triangle-exclamation')
        warningIcon.classList.remove('fa-bolt')
        warningDialog.style.background = "#ED6114"
    }

    const showOnlineDialog = () => {
        const warningDialog = document.querySelector('.warning-dialog')
        const warningIcon = document.querySelector('.warning-icon')
        const warningText = document.querySelector('.warning-text')
        warningText.textContent = "You're back online!"
        warningIcon.classList.add('fa-bolt')
        warningIcon.classList.remove('fa-triangle-exclamation')
        warningDialog.style.background = "#06c44c"
        setTimeout(() => {
            warningDialog.style.top = '-150%'
        }, 5000)
    }

    const closeWarningDialog = () => {
        const warningDialog = document.querySelector('.warning-dialog')
        warningDialog.style.top = '-150%'
    }
    window.addEventListener('online', () => {
        isOffline = false;
        showOnlineDialog()
    })
    window.addEventListener('offline', () => {
        isOffline = true;
        showOfflineDialog("You're offline. Please check your internet.")
    });

    if ('connection' in navigator) {
        const connection = navigator.connection || navigator.mozConnection || navigator.webkitConnection;
        if (connection.effectiveType === '2g' || connection.effectiveType === '3g' || connection.saveData || connection.effectiveType.includes('2g') || connection.effectiveType.includes('3g')) {
            showOfflineDialog("Your internet connection is slow.")
        } else {
            showOnlineDialog()
        }

        connection.addEventListener('change', () => {
            if (connection.effectiveType === '2g' || connection.effectiveType === '3g' || connection.saveData || connection.effectiveType.includes('2g') || connection.effectiveType.includes('3g')) {
                showOfflineDialog("Your internet connection is slow.")
            } else {
                if (!isOffline) {
                    showOnlineDialog()
                }
            }
        });
    } else {
        console.log("Network Information API is not supported by this browser.");
    }



</script>
<script>
    <% if (!isMobile) { %>
    $(".panel-left").resizable({
        handleSelector: ".splitter",
        resizeHeight: false
    });
    <%}%>
    <%if(!showAIWindow){%>
    hideChatWindowCB()
    <%}%>

    function closeNotice(){
        modal.style.display = 'none';
        modal.classList.remove('fade-out');
        isResponseComplete = true
        isTyping.style.display = 'none';
    }
</script>

</body>
</html>
