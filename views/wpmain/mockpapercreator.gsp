<%@ page contentType="text/html;charset=UTF-8" %>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<html>
<head>
    <title>Create Mock Paper - ${bookTitle}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .mock-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 160px);
        }
        
        .mock-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .mock-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .mock-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }
        
        .mock-form {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }
        
        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            color: #333;
            background-color: #fff;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .chapter-selection {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }
        
        .chapter-toggle {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .chapter-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-weight: 500;
        }
        
        .chapter-option input[type="radio"] {
            margin: 0;
        }
        
        .chapters-list {
            display: none;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            background: white;
        }

        .chapter-controls {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
            padding: 8px;
            background: #f8f9fa;
            border-radius: 4px;
        }

        .chapter-control-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .chapter-control-btn:hover {
            background: #0056b3;
        }

        .chapter-control-btn.secondary {
            background: #6c757d;
        }

        .chapter-control-btn.secondary:hover {
            background: #545b62;
        }
        
        .chapters-list.show {
            display: block;
        }
        
        .chapter-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 0;
            font-size: 0.9rem;
        }
        
        .question-row {
            display: flex;
            gap: 15px;
            align-items: end;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 2px solid #e9ecef;
        }
        
        .question-field {
            flex: 1;
        }
        
        .question-field label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #555;
            margin-bottom: 5px;
            display: block;
        }
        
        .question-field select,
        .question-field input {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            width: 100%;
        }
        
        .remove-row {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 12px;
            cursor: pointer;
            font-size: 0.9rem;
            height: 40px;
        }
        
        .remove-row:hover {
            background: #c82333;
        }
        
        .add-row-btn {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 25px;
        }
        
        .add-row-btn:hover {
            background: #218838;
        }
        
        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: #667eea;
            color: white;
        }
        
        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 768px) {
            .mock-container {
                padding: 15px;
            }
            
            .question-row {
                flex-direction: column;
                gap: 10px;
            }
            
            .btn-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="mock-container">
        <!-- Header -->
        <div class="mock-header">
            <h1><i class="fas fa-file-alt"></i> Create Mock Paper</h1>
            <p>Quick and easy mock paper creation for ${bookTitle}</p>
        </div>

        <!-- Form -->
        <div class="mock-form">
            <div class="form-group">
                <label class="form-label" for="paperName">Mock Paper Name</label>
                <input type="text" id="paperName" class="form-control" value="Mock Paper #${nextPaperNumber}" placeholder="Enter mock paper name">
            </div>
            
            <div class="form-group">
                <label class="form-label">Chapter Selection</label>
                <div class="chapter-selection">
                    <div class="chapter-toggle">
                        <label class="chapter-option">
                            <input type="radio" name="chapterMode" value="all" checked onchange="toggleChapterMode()">
                            <span>All Chapters</span>
                        </label>
                        <label class="chapter-option">
                            <input type="radio" name="chapterMode" value="custom" onchange="toggleChapterMode()">
                            <span>Custom Selection</span>
                        </label>
                    </div>
                    <div class="chapters-list" id="chaptersList">
                        <div class="chapter-controls">
                            <button type="button" class="chapter-control-btn" onclick="selectAllChapters()">Select All</button>
                            <button type="button" class="chapter-control-btn secondary" onclick="unselectAllChapters()">Unselect All</button>
                        </div>
                        <g:each in="${chaptersList}" var="chapter">
                            <div class="chapter-item">
                                <input type="checkbox" class="chapter-checkbox" id="chapter_${chapter.id}" value="${chapter.id}" checked>
                                <label for="chapter_${chapter.id}">${chapter.name}</label>
                            </div>
                        </g:each>
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label class="form-label">Question Configuration</label>
                <div id="questionRows">
                    <!-- Initial row will be added by JavaScript -->
                </div>
                <button type="button" class="add-row-btn" onclick="addQuestionRow()">
                    <i class="fas fa-plus"></i> Add Another Question Type
                </button>
            </div>
            
            <div class="btn-group">
                <a href="/wpmain/mockpaperlist?bookId=${bookId}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Mock Papers
                </a>
                <button type="button" class="btn btn-primary" onclick="createMockPaper()">
                    <i class="fas fa-magic"></i> Create Mock Paper
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>Creating Your Mock Paper...</h3>
            <p>Please wait while we generate your questions</p>
        </div>
    </div>

    <script>
        var bookId = "${bookId}";
        var availableQuestionCounts = {};
        var questionTypeOrder = [
            "Question and Answers",
            "LongAnswer",
            "ShortAnswer",
            "VeryShortAnswer",
            "AssertionReason",
            "Problem",
            "Multiple Choice Questions",
            "FillBlank",
            "TrueFalse",
            "MatchFollowing",
            "ArrangeSequence",
            "Mixed Objectives"
        ];
        var rowCounter = 0;

        // Load available question counts on page load
        document.addEventListener("DOMContentLoaded", function() {
            loadAvailableQuestions();
            // Add initial row - it will be populated when question counts are loaded
            addQuestionRow();
        });

        function toggleChapterMode() {
            var mode = document.querySelector('input[name="chapterMode"]:checked').value;
            var chaptersList = document.getElementById("chaptersList");

            if (mode === "custom") {
                chaptersList.classList.add("show");
            } else {
                chaptersList.classList.remove("show");
                // Check all chapters when switching to "All Chapters"
                var checkboxes = document.querySelectorAll(".chapter-checkbox");
                checkboxes.forEach(function(cb) {
                    cb.checked = true;
                });
            }
        }

        function selectAllChapters() {
            var checkboxes = document.querySelectorAll(".chapter-checkbox");
            checkboxes.forEach(function(cb) {
                cb.checked = true;
            });
        }

        function unselectAllChapters() {
            var checkboxes = document.querySelectorAll(".chapter-checkbox");
            checkboxes.forEach(function(cb) {
                cb.checked = false;
            });
        }

        function loadAvailableQuestions() {
            var chapterIds = [];
            <g:each in="${chaptersList}" var="chapter">
                chapterIds.push("${chapter.id}");
            </g:each>

            fetch("/wpmain/getAvailableQuestionCounts?chapterIds=" + chapterIds.join(","))
                .then(function(response) { return response.json(); })
                .then(function(data) {
                    console.log("Received question counts data:", data);
                    if (data.success) {
                        availableQuestionCounts = data.counts;
                        console.log("Available question counts:", availableQuestionCounts);

                        // Update all existing dropdowns
                        updateAllQuestionTypeDropdowns();
                    } else {
                        console.error("Failed to load question counts:", data.message);
                    }
                })
                .catch(function(error) {
                    console.error("Error loading question counts:", error);
                });
        }

        function addQuestionRow() {
            rowCounter++;
            var container = document.getElementById("questionRows");
            console.log("Adding row " + rowCounter);
           //print the values of row 1
            if(rowCounter>1) {
                console.log("question type of row 1:" + document.getElementById("type1").value);
                console.log("question count of row 1:" + document.getElementById("count1").value);
                console.log("question difficulty of row 1:" + document.getElementById("difficulty1").value);
            }

            var rowHtml = "<div class=\"question-field\">" +
                    "<label>Question Type</label>" +
                    "<select id=\"type" + rowCounter + "\" onchange=\"updateQuestionCount(" + rowCounter + ")\">" +
                        "<option value=\"\">Select Type</option>" +
                    "</select>" +
                "</div>" +
                "<div class=\"question-field\">" +
                    "<label>Number of Questions</label>" +
                    "<input type=\"number\" id=\"count" + rowCounter + "\" min=\"1\" max=\"50\" placeholder=\"e.g., 10\">" +
                "</div>" +
                "<div class=\"question-field\">" +
                    "<label>Difficulty Level</label>" +
                    "<select id=\"difficulty" + rowCounter + "\">" +
                        "<option value=\"All\">Mixed</option>" +
                        "<option value=\"Easy\">Easy</option>" +
                        "<option value=\"Medium\">Medium</option>" +
                        "<option value=\"Difficult\">Difficult</option>" +
                    "</select>" +
                "</div>";

            if (rowCounter > 1) {
                rowHtml += "<button type=\"button\" class=\"remove-row\" onclick=\"removeQuestionRow(" + rowCounter + ")\">Remove</button>";
            }

            // Create a new div element and append it (preserves existing DOM elements)
            var newRow = document.createElement("div");
            newRow.className = "question-row";
            newRow.id = "row" + rowCounter;
            newRow.innerHTML = rowHtml;
            container.appendChild(newRow);
            //print row 1 values
            if(rowCounter>1) console.log("row 1 values after adding row:", document.getElementById("type1").value, document.getElementById("count1").value, document.getElementById("difficulty1").value);
            // Only populate the newly added row's dropdown
            var newTypeSelect = document.getElementById("type" + rowCounter);
            if (newTypeSelect && availableQuestionCounts) {
                newTypeSelect.innerHTML = "<option value=\"\">Select Type</option>" + getQuestionTypeOptions();
            }
            if(rowCounter>1)  console.log("row 1 values after adding row:", document.getElementById("type1").value, document.getElementById("count1").value, document.getElementById("difficulty1").value);
        }

        function updateAllQuestionTypeDropdowns() {
            console.log("Updating all question type dropdowns...");
            var questionRows = document.querySelectorAll(".question-row");
            questionRows.forEach(function(row) {
                var rowId = row.id.replace("row", "");
                var typeSelect = document.getElementById("type" + rowId);
                if (typeSelect) {
                    var currentValue = typeSelect.value;
                    // Only update if dropdown is empty (no selection made)
                    if (!currentValue || currentValue === "") {
                        var newOptionsHtml = "<option value=\"\">Select Type</option>" + getQuestionTypeOptions();
                        typeSelect.innerHTML = newOptionsHtml;
                    }
                }
            });
        }

        function removeQuestionRow(rowId) {
            var row = document.getElementById("row" + rowId);
            if (row) {
                row.remove();
            }
        }

        function getQuestionTypeOptions() {
            var options = "";
            questionTypeOrder.forEach(function(type) {
                var count = availableQuestionCounts[type] || 0;
                if (count > 0) {
                    options += "<option value=\"" + type + "\">" + type + " (" + count + " available)</option>";
                }
            });
            return options;
        }

        function updateQuestionCount(rowId) {
            var typeSelect = document.getElementById("type" + rowId);
            var countInput = document.getElementById("count" + rowId);
            var selectedType = typeSelect.value;

            if (selectedType && availableQuestionCounts[selectedType]) {
                var maxCount = availableQuestionCounts[selectedType];
                countInput.setAttribute("max", maxCount);
                countInput.placeholder = "Max: " + maxCount;

                // Add validation event listener
                countInput.addEventListener("input", function() {
                    var enteredValue = parseInt(this.value);
                    if (enteredValue > maxCount) {
                        this.value = maxCount;
                        alert("Maximum " + maxCount + " questions available for " + selectedType);
                    }
                });

                // Also validate on blur
                countInput.addEventListener("blur", function() {
                    var enteredValue = parseInt(this.value);
                    if (enteredValue > maxCount) {
                        this.value = maxCount;
                        alert("Maximum " + maxCount + " questions available for " + selectedType);
                    }
                });
            }
        }

        function createMockPaper() {
            if (!validateForm()) {
                return;
            }

            // Show loading
            document.getElementById("loadingOverlay").style.display = "flex";

            // Prepare form data
            var formData = new FormData();
            formData.append("paperName", document.getElementById("paperName").value);
            formData.append("paperHeader", document.getElementById("paperName").value);
            formData.append("bookId", bookId);

            // Get selected chapters
            var chapterMode = document.querySelector('input[name="chapterMode"]:checked').value;
            var chapterIds = [];
            
            if (chapterMode === "all") {
                <g:each in="${chaptersList}" var="chapter">
                    chapterIds.push("${chapter.id}");
                </g:each>
            } else {
                var selectedChapters = document.querySelectorAll(".chapter-checkbox:checked");
                chapterIds = Array.from(selectedChapters).map(function(cb) {
                    return cb.value;
                });
            }
            formData.append("chapterIds", chapterIds.join(","));

            // Add section data
            var questionRows = document.querySelectorAll(".question-row");
            var sectionIndex = 1;

            questionRows.forEach(function(row) {
                var rowId = row.id.replace("row", "");
                var type = document.getElementById("type" + rowId).value;
                var count = document.getElementById("count" + rowId).value;
                var difficulty = document.getElementById("difficulty" + rowId).value;

                if (type && count) {
                    formData.append("section" + sectionIndex + "Header", "Section " + sectionIndex);
                    formData.append("section" + sectionIndex + "Marks", getDefaultMarks(type));
                    formData.append("section" + sectionIndex + "Questions", count);
                    formData.append("section" + sectionIndex + "Type", type);
                    formData.append("section" + sectionIndex + "Difficulty", difficulty);
                    sectionIndex++;
                }
            });

            // Submit form
            fetch("/wpmain/generateQuestionPaper", {
                method: "POST",
                body: formData
            })
            .then(function(response) { return response.json(); })
            .then(function(data) {
                if (data.success) {
                    window.location.href = "/wpmain/qpview?id=" + data.questionPaperId + "&bookId=" + bookId + "&fromMock=true";
                } else {
                    alert("Error creating mock paper: " + data.message);
                    document.getElementById("loadingOverlay").style.display = "none";
                }
            })
            .catch(function(error) {
                console.error("Error:", error);
                alert("Error creating mock paper");
                document.getElementById("loadingOverlay").style.display = "none";
            });
        }

        function getDefaultMarks(questionType) {
            var marksMap = {
                "Question and Answers": 3,
                "LongAnswer": 5,
                "ShortAnswer": 3,
                "VeryShortAnswer": 2,
                "AssertionReason": 2,
                "Problem": 5,
                "Multiple Choice Questions": 1,
                "FillBlank": 1,
                "TrueFalse": 1,
                "MatchFollowing": 2,
                "ArrangeSequence": 2,
                "Mixed Objectives": 1
            };
            return marksMap[questionType] || 2;
        }

        function validateForm() {
            var paperName = document.getElementById("paperName").value.trim();
            if (!paperName) {
                alert("Please enter a mock paper name");
                return false;
            }

            // Check if at least one chapter is selected
            var chapterMode = document.querySelector('input[name="chapterMode"]:checked').value;
            if (chapterMode === "custom") {
                var selectedChapters = document.querySelectorAll(".chapter-checkbox:checked");
                if (selectedChapters.length === 0) {
                    alert("Please select at least one chapter");
                    return false;
                }
            }

            // Check if at least one question row is filled and validate question counts
            var questionRows = document.querySelectorAll(".question-row");
            var hasValidRow = false;

            for (var i = 0; i < questionRows.length; i++) {
                var row = questionRows[i];
                var rowId = row.id.replace("row", "");
                var type = document.getElementById("type" + rowId).value;
                var count = document.getElementById("count" + rowId).value;

                if (type && count && count > 0) {
                    // Validate that the requested count doesn't exceed available questions
                    var maxAvailable = availableQuestionCounts[type] || 0;
                    if (parseInt(count) > maxAvailable) {
                        alert("Row " + rowId + ": You requested " + count + " questions for " + type + ", but only " + maxAvailable + " are available.");
                        return false;
                    }
                    hasValidRow = true;
                }
            }

            if (!hasValidRow) {
                alert("Please configure at least one question type");
                return false;
            }

            return true;
        }
    </script>
</body>
</html>

<g:render template="/${session['entryController']}/footer_new"></g:render>
