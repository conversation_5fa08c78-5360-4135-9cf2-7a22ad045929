<%@ page contentType="text/html;charset=UTF-8" %>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<html>
<head>
    <title>Question Paper Creator - ${bookTitle}</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .qp-container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            min-height: calc(100vh - 160px);
        }

        .qp-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            margin-bottom: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .qp-header h1 {
            margin: 0 0 10px 0;
            font-size: 2rem;
            font-weight: 600;
        }

        .qp-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .qp-form {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .templates-section {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            border: 2px solid #e9ecef;
        }

        .templates-header {
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            font-size: 1.1rem;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .template-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .template-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .template-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .template-card.selected {
            border-color: #667eea;
            background: #f0f4ff;
        }

        .template-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        .template-description {
            font-size: 0.9rem;
            color: #666;
            line-height: 1.4;
        }

        .form-group {
            margin-bottom: 25px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-size: 1rem;
        }

        .form-control {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
            color: #333;
            background-color: #fff;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .chapter-selection {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 2px solid #e9ecef;
        }

        .chapter-toggle {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }

        .chapter-option {
            display: flex;
            align-items: center;
            gap: 8px;
            cursor: pointer;
            font-weight: 500;
        }

        .chapter-option input[type="radio"] {
            margin: 0;
        }

        .chapters-list {
            display: none;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 10px;
            background: white;
        }

        .chapters-list.show {
            display: block;
        }

        .chapter-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 5px 0;
            font-size: 0.9rem;
        }

        .question-row {
            display: flex;
            gap: 15px;
            align-items: end;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 2px solid #e9ecef;
        }

        .question-field {
            flex: 1;
        }

        .question-field label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #555;
            margin-bottom: 5px;
            display: block;
        }

        .question-field select,
        .question-field input {
            padding: 10px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 0.9rem;
            width: 100%;
        }

        .marks-field {
            flex: 0.8;
        }

        .remove-row {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 12px;
            cursor: pointer;
            font-size: 0.9rem;
            height: 40px;
        }

        .remove-row:hover {
            background: #c82333;
        }

        .add-row-btn {
            background: #28a745;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 25px;
        }

        .add-row-btn:hover {
            background: #218838;
        }

        .advanced-toggle {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .advanced-toggle:hover {
            background: #e9ecef;
        }

        .advanced-toggle-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-weight: 600;
            color: #333;
        }

        .advanced-content {
            display: none;
            margin-top: 15px;
            padding-top: 15px;
            border-top: 1px solid #dee2e6;
        }

        .advanced-content.show {
            display: block;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none !important;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #545b62;
        }

        .btn-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
        }

        .loading-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .loading-content {
            background: white;
            padding: 30px;
            border-radius: 12px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .preview-section {
            background: #e8f4fd;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
            display: none;
        }

        .preview-section.show {
            display: block;
        }

        .preview-title {
            font-weight: 600;
            color: #0c5460;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .preview-content {
            color: #0c5460;
            font-size: 0.9rem;
            line-height: 1.5;
        }

        @media (max-width: 768px) {
            .qp-container {
                padding: 15px;
            }

            .template-options {
                grid-template-columns: 1fr;
            }

            .question-row {
                flex-direction: column;
                gap: 10px;
            }

            .question-field,
            .marks-field {
                flex: 1;
            }

            .btn-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="qp-container">
        <!-- Header -->
        <div class="qp-header">
            <h1><i class="fas fa-file-alt"></i> Question Paper Creator</h1>
            <p>Create professional question papers with ease for ${bookTitle}</p>
        </div>

        <!-- Main Form -->
        <div class="qp-form">
            <!-- Template Selection -->
            <div class="templates-section">
                <div class="templates-header">
                    <i class="fas fa-magic"></i> Quick Templates
                </div>
                <div class="template-options">
                    <div class="template-card" onclick="selectTemplate('quick')">
                        <div class="template-title">Quick Test</div>
                        <div class="template-description">20 MCQs (1 mark) + 5 Short (3 marks)<br>Perfect for quick assessments</div>
                    </div>
                    <div class="template-card" onclick="selectTemplate('practice')">
                        <div class="template-title">Practice Paper</div>
                        <div class="template-description">30 MCQs + 10 Short + 5 Long<br>Marks customizable per section</div>
                    </div>
                    <div class="template-card" onclick="selectTemplate('exam')">
                        <div class="template-title">Full Exam</div>
                        <div class="template-description">50 MCQs + 15 Short + 10 Long<br>Professional exam format</div>
                    </div>
                    <div class="template-card selected" onclick="selectTemplate('custom')">
                        <div class="template-title">Custom</div>
                        <div class="template-description">Configure questions & marks<br>Complete control over paper</div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="paperName">Question Paper Name</label>
                <input type="text" id="paperName" class="form-control" placeholder="Enter question paper name">
            </div>

            <div class="form-group">
                <label class="form-label">Chapter Selection</label>
                <div class="chapter-selection">
                    <div class="chapter-toggle">
                        <label class="chapter-option">
                            <input type="radio" name="chapterMode" value="all" checked onchange="toggleChapterMode()">
                            <span>All Chapters</span>
                        </label>
                        <label class="chapter-option">
                            <input type="radio" name="chapterMode" value="custom" onchange="toggleChapterMode()">
                            <span>Custom Selection</span>
                        </label>
                    </div>
                    <div class="chapters-list" id="chaptersList">
                        <g:each in="${chaptersList}" var="chapter">
                            <div class="chapter-item">
                                <input type="checkbox" class="chapter-checkbox" id="chapter_${chapter.id}" value="${chapter.id}" checked>
                                <label for="chapter_${chapter.id}">${chapter.name}</label>
                            </div>
                        </g:each>
                    </div>
                </div>
            </div>

            <div class="form-group" id="customQuestionConfig">
                <label class="form-label">Question Configuration</label>
                <div id="questionRows">
                    <!-- Question rows will be added by JavaScript -->
                </div>
                <button type="button" class="add-row-btn" onclick="addQuestionRow()">
                    <i class="fas fa-plus"></i> Add Another Question Type
                </button>
            </div>

            <!-- Advanced Settings -->
            <div class="advanced-toggle" onclick="toggleAdvancedSettings()">
                <div class="advanced-toggle-header">
                    <span><i class="fas fa-cog"></i> Advanced Settings</span>
                    <i class="fas fa-chevron-down" id="advancedChevron"></i>
                </div>
                <div class="advanced-content" id="advancedContent">
                    <div class="form-group">
                        <label class="form-label" for="paperHeader">Custom Paper Header</label>
                        <input type="text" id="paperHeader" class="form-control" placeholder="Leave blank for auto-generated header">
                    </div>
                </div>
            </div>

            <!-- Preview Section -->
            <div class="preview-section" id="previewSection">
                <div class="preview-title">
                    <i class="fas fa-eye"></i> Paper Preview
                </div>
                <div class="preview-content" id="previewContent">
                    <!-- Preview will be populated by JavaScript -->
                </div>
            </div>

            <div class="btn-group">
                <a href="/wpmain/qplist?bookId=${bookId}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Question Papers
                </a>
                <button type="button" class="btn btn-primary" onclick="createQuestionPaper()">
                    <i class="fas fa-magic"></i> Create Question Paper
                </button>
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-content">
            <div class="spinner"></div>
            <h3>Creating Your Question Paper...</h3>
            <p>Please wait while we generate your questions</p>
        </div>
    </div>

    <script>
        var bookId = "${bookId}";
        var availableQuestionCounts = {};
        var questionTypeOrder = [
            "Question and Answers",
            "LongAnswer",
            "ShortAnswer",
            "VeryShortAnswer",
            "AssertionReason",
            "Problem",
            "Multiple Choice Questions",
            "FillBlank",
            "TrueFalse",
            "MatchFollowing",
            "ArrangeSequence",
            "Mixed Objectives"
        ];
        var rowCounter = 0;
        var selectedTemplate = 'custom';

        // Templates configuration
        var templates = {
            quick: [
                { type: "Multiple Choice Questions", count: 20, difficulty: "All", marks: 1 },
                { type: "ShortAnswer", count: 5, difficulty: "All", marks: 3 }
            ],
            practice: [
                { type: "Multiple Choice Questions", count: 30, difficulty: "All", marks: 1 },
                { type: "ShortAnswer", count: 10, difficulty: "All", marks: 3 },
                { type: "LongAnswer", count: 5, difficulty: "All", marks: 5 }
            ],
            exam: [
                { type: "Multiple Choice Questions", count: 50, difficulty: "All", marks: 1 },
                { type: "ShortAnswer", count: 15, difficulty: "All", marks: 3 },
                { type: "LongAnswer", count: 10, difficulty: "All", marks: 5 }
            ]
        };

        // Load available question counts on page load
        document.addEventListener("DOMContentLoaded", function() {
            loadAvailableQuestions();
            generatePaperName();
            // Add initial row for custom template
            addQuestionRow();
        });

        function generatePaperName() {
            var today = new Date();
            var dateStr = today.getDate() + "-" + (today.getMonth() + 1) + "-" + today.getFullYear();
            document.getElementById("paperName").value = "${bookTitle} - Question Paper #${nextPaperNumber ?: 1} - " + dateStr;
        }

        function selectTemplate(templateName) {
            selectedTemplate = templateName;

            // Update UI
            var cards = document.querySelectorAll(".template-card");
            cards.forEach(function(card) {
                card.classList.remove("selected");
            });
            event.target.closest(".template-card").classList.add("selected");

            // Clear existing question rows
            document.getElementById("questionRows").innerHTML = "";
            rowCounter = 0;

            if (templateName === 'custom') {
                // Show custom configuration
                document.getElementById("customQuestionConfig").style.display = "block";
                addQuestionRow();
            } else {
                // Hide custom configuration and apply template
                document.getElementById("customQuestionConfig").style.display = "none";
                applyTemplate(templateName);
            }

            updatePreview();
        }

        function applyTemplate(templateName) {
            var templateConfig = templates[templateName];
            if (!templateConfig) return;

            templateConfig.forEach(function(config) {
                addQuestionRowWithConfig(config);
            });
        }

        function toggleChapterMode() {
            var mode = document.querySelector('input[name="chapterMode"]:checked').value;
            var chaptersList = document.getElementById("chaptersList");

            if (mode === "custom") {
                chaptersList.classList.add("show");
            } else {
                chaptersList.classList.remove("show");
                // Check all chapters when switching to "All Chapters"
                var checkboxes = document.querySelectorAll(".chapter-checkbox");
                checkboxes.forEach(function(cb) {
                    cb.checked = true;
                });
            }
            updatePreview();
        }

        function toggleAdvancedSettings() {
            var content = document.getElementById("advancedContent");
            var chevron = document.getElementById("advancedChevron");

            if (content.classList.contains("show")) {
                content.classList.remove("show");
                chevron.classList.remove("fa-chevron-up");
                chevron.classList.add("fa-chevron-down");
            } else {
                content.classList.add("show");
                chevron.classList.remove("fa-chevron-down");
                chevron.classList.add("fa-chevron-up");
            }
        }

        function loadAvailableQuestions() {
            var chapterIds = [];
            <g:each in="${chaptersList}" var="chapter">
                chapterIds.push("${chapter.id}");
            </g:each>

            fetch("/wpmain/getAvailableQuestionCounts?chapterIds=" + chapterIds.join(","))
                .then(function(response) { return response.json(); })
                .then(function(data) {
                    if (data.success) {
                        availableQuestionCounts = data.counts;
                        updateAllQuestionTypeDropdowns();
                    } else {
                        console.error("Failed to load question counts:", data.message);
                    }
                })
                .catch(function(error) {
                    console.error("Error loading question counts:", error);
                });
        }

        function addQuestionRow() {
            addQuestionRowWithConfig({});
        }

        function addQuestionRowWithConfig(config) {
            rowCounter++;
            var container = document.getElementById("questionRows");

            var rowHtml = "<div class=\"question-field\">" +
                    "<label>Question Type</label>" +
                    "<select id=\"type" + rowCounter + "\" onchange=\"updateQuestionCount(" + rowCounter + "); updateMarksField(" + rowCounter + "); updatePreview();\">" +
                        "<option value=\"\">Select Type</option>" +
                    "</select>" +
                "</div>" +
                "<div class=\"question-field\">" +
                    "<label>Number of Questions</label>" +
                    "<input type=\"number\" id=\"count" + rowCounter + "\" min=\"1\" max=\"50\" placeholder=\"e.g., 10\" onchange=\"updatePreview()\">" +
                "</div>" +
                "<div class=\"question-field marks-field\">" +
                    "<label>Marks Each</label>" +
                    "<input type=\"number\" id=\"marks" + rowCounter + "\" min=\"0.5\" max=\"20\" step=\"0.5\" placeholder=\"e.g., 2\" onchange=\"updatePreview()\">" +
                "</div>" +
                "<div class=\"question-field\">" +
                    "<label>Difficulty Level</label>" +
                    "<select id=\"difficulty" + rowCounter + "\" onchange=\"updatePreview()\">" +
                        "<option value=\"All\">Mixed</option>" +
                        "<option value=\"Easy\">Easy</option>" +
                        "<option value=\"Medium\">Medium</option>" +
                        "<option value=\"Difficult\">Difficult</option>" +
                    "</select>" +
                "</div>";

            if (rowCounter > 1 || selectedTemplate === 'custom') {
                rowHtml += "<button type=\"button\" class=\"remove-row\" onclick=\"removeQuestionRow(" + rowCounter + ")\">Remove</button>";
            }

            var newRow = document.createElement("div");
            newRow.className = "question-row";
            newRow.id = "row" + rowCounter;
            newRow.innerHTML = rowHtml;
            container.appendChild(newRow);

            // Populate dropdown and set config values
            var newTypeSelect = document.getElementById("type" + rowCounter);
            if (newTypeSelect && availableQuestionCounts) {
                newTypeSelect.innerHTML = "<option value=\"\">Select Type</option>" + getQuestionTypeOptions();

                // Apply configuration if provided
                if (config.type) {
                    newTypeSelect.value = config.type;
                    updateQuestionCount(rowCounter);
                    updateMarksField(rowCounter);
                }
                if (config.count) {
                    document.getElementById("count" + rowCounter).value = config.count;
                }
                if (config.marks) {
                    document.getElementById("marks" + rowCounter).value = config.marks;
                }
                if (config.difficulty) {
                    document.getElementById("difficulty" + rowCounter).value = config.difficulty;
                }
            }
        }

        function getQuestionTypeOptions() {
            var options = "";
            questionTypeOrder.forEach(function(type) {
                var count = availableQuestionCounts[type] || 0;
                if (count > 0) {
                    options += "<option value=\"" + type + "\">" + type + " (" + count + " available)</option>";
                }
            });
            return options;
        }

        function updateAllQuestionTypeDropdowns() {
            var questionRows = document.querySelectorAll(".question-row");
            questionRows.forEach(function(row) {
                var rowId = row.id.replace("row", "");
                var typeSelect = document.getElementById("type" + rowId);
                if (typeSelect) {
                    var currentValue = typeSelect.value;
                    if (!currentValue || currentValue === "") {
                        var newOptionsHtml = "<option value=\"\">Select Type</option>" + getQuestionTypeOptions();
                        typeSelect.innerHTML = newOptionsHtml;
                    }
                }
            });
        }

        function removeQuestionRow(rowId) {
            var row = document.getElementById("row" + rowId);
            if (row) {
                row.remove();
                updatePreview();
            }
        }

        function updateQuestionCount(rowId) {
            var typeSelect = document.getElementById("type" + rowId);
            var countInput = document.getElementById("count" + rowId);
            var selectedType = typeSelect.value;

            if (selectedType && availableQuestionCounts[selectedType]) {
                var maxCount = availableQuestionCounts[selectedType];
                countInput.setAttribute("max", maxCount);
                countInput.placeholder = "Max: " + maxCount;

                countInput.addEventListener("input", function() {
                    var enteredValue = parseInt(this.value);
                    if (enteredValue > maxCount) {
                        this.value = maxCount;
                        alert("Maximum " + maxCount + " questions available for " + selectedType);
                    }
                });
            }
        }

        function updateMarksField(rowId) {
            var typeSelect = document.getElementById("type" + rowId);
            var marksInput = document.getElementById("marks" + rowId);
            var selectedType = typeSelect.value;

            if (selectedType && !marksInput.value) {
                // Only set default if field is empty (user hasn't set custom value)
                var defaultMarks = getDefaultMarks(selectedType);
                marksInput.value = defaultMarks;
                marksInput.placeholder = "Default: " + defaultMarks;
            }
        }

        function updatePreview() {
            var previewSection = document.getElementById("previewSection");
            var previewContent = document.getElementById("previewContent");

            var questionRows = document.querySelectorAll(".question-row");
            var hasValidConfig = false;
            var totalQuestions = 0;
            var totalMarks = 0;
            var sections = [];

            questionRows.forEach(function(row) {
                var rowId = row.id.replace("row", "");
                var type = document.getElementById("type" + rowId).value;
                var count = parseInt(document.getElementById("count" + rowId).value) || 0;
                var marks = parseFloat(document.getElementById("marks" + rowId).value) || getDefaultMarks(type);
                var difficulty = document.getElementById("difficulty" + rowId).value;

                if (type && count > 0) {
                    hasValidConfig = true;
                    totalQuestions += count;
                    totalMarks += (count * marks);
                    sections.push({
                        type: type,
                        count: count,
                        marks: marks,
                        difficulty: difficulty
                    });
                }
            });

            if (hasValidConfig) {
                var previewHtml = "<strong>Total Questions:</strong> " + totalQuestions +
                                "<br><strong>Total Marks:</strong> " + totalMarks +
                                "<br><strong>Sections:</strong><ul>";

                sections.forEach(function(section, index) {
                    previewHtml += "<li>Section " + (index + 1) + ": " + section.count + " " +
                                  section.type + " (" + section.marks + " marks each, " +
                                  section.difficulty + " difficulty)</li>";
                });

                previewHtml += "</ul>";
                previewContent.innerHTML = previewHtml;
                previewSection.classList.add("show");
            } else {
                previewSection.classList.remove("show");
            }
        }
        function getDefaultMarks(questionType) {
            var marksMap = {
                "Question and Answers": 3,
                "LongAnswer": 5,
                "ShortAnswer": 3,
                "VeryShortAnswer": 2,
                "AssertionReason": 2,
                "Problem": 5,
                "Multiple Choice Questions": 1,
                "FillBlank": 1,
                "TrueFalse": 1,
                "MatchFollowing": 2,
                "ArrangeSequence": 2,
                "Mixed Objectives": 1
            };
            return marksMap[questionType] || 2;
        }

        function createQuestionPaper() {
            if (!validateForm()) {
                return;
            }

            // Show loading
            document.getElementById("loadingOverlay").style.display = "flex";

            // Prepare form data
            var formData = new FormData();
            var paperName = document.getElementById("paperName").value;
            var paperHeader = document.getElementById("paperHeader").value.trim();

            formData.append("paperName", paperName);
            formData.append("paperHeader", paperHeader || (paperName + " - " + new Date().toLocaleDateString()));
            formData.append("bookId", bookId);

            // Get selected chapters
            var chapterMode = document.querySelector('input[name="chapterMode"]:checked').value;
            var chapterIds = [];

            if (chapterMode === "all") {
                <g:each in="${chaptersList}" var="chapter">
                    chapterIds.push("${chapter.id}");
                </g:each>
            } else {
                var selectedChapters = document.querySelectorAll(".chapter-checkbox:checked");
                chapterIds = Array.from(selectedChapters).map(function(cb) {
                    return cb.value;
                });
            }
            formData.append("chapterIds", chapterIds.join(","));

            // Add section data
            var questionRows = document.querySelectorAll(".question-row");
            var sectionIndex = 1;

            questionRows.forEach(function(row) {
                var rowId = row.id.replace("row", "");
                var type = document.getElementById("type" + rowId).value;
                var count = document.getElementById("count" + rowId).value;
                var marks = document.getElementById("marks" + rowId).value || getDefaultMarks(type);
                var difficulty = document.getElementById("difficulty" + rowId).value;

                if (type && count) {
                    formData.append("section" + sectionIndex + "Header", "Section " + sectionIndex);
                    formData.append("section" + sectionIndex + "Marks", marks);
                    formData.append("section" + sectionIndex + "Questions", count);
                    formData.append("section" + sectionIndex + "Type", type);
                    formData.append("section" + sectionIndex + "Difficulty", difficulty);
                    sectionIndex++;
                }
            });

            // Submit form
            fetch("/wpmain/generateQuestionPaper", {
                method: "POST",
                body: formData
            })
            .then(function(response) { return response.json(); })
            .then(function(data) {
                if (data.success) {
                    window.location.href = "/wpmain/qpview?id=" + data.questionPaperId + "&bookId=" + bookId;
                } else {
                    alert("Error creating question paper: " + data.message);
                    document.getElementById("loadingOverlay").style.display = "none";
                }
            })
            .catch(function(error) {
                console.error("Error:", error);
                alert("Error creating question paper");
                document.getElementById("loadingOverlay").style.display = "none";
            });
        }

        function validateForm() {
            var paperName = document.getElementById("paperName").value.trim();
            if (!paperName) {
                alert("Please enter a question paper name");
                return false;
            }

            // Check if at least one chapter is selected
            var chapterMode = document.querySelector('input[name="chapterMode"]:checked').value;
            if (chapterMode === "custom") {
                var selectedChapters = document.querySelectorAll(".chapter-checkbox:checked");
                if (selectedChapters.length === 0) {
                    alert("Please select at least one chapter");
                    return false;
                }
            }

            // Check if at least one question row is filled and validate question counts
            var questionRows = document.querySelectorAll(".question-row");
            var hasValidRow = false;

            for (var i = 0; i < questionRows.length; i++) {
                var row = questionRows[i];
                var rowId = row.id.replace("row", "");
                var type = document.getElementById("type" + rowId).value;
                var count = document.getElementById("count" + rowId).value;

                if (type && count && count > 0) {
                    // Validate that the requested count doesn't exceed available questions
                    var maxAvailable = availableQuestionCounts[type] || 0;
                    if (parseInt(count) > maxAvailable) {
                        alert("Row " + rowId + ": You requested " + count + " questions for " + type + ", but only " + maxAvailable + " are available.");
                        return false;
                    }
                    hasValidRow = true;
                }
            }

            if (!hasValidRow) {
                alert("Please configure at least one question type");
                return false;
            }

            return true;
        }
    </script>
</body>
</html>

<g:render template="/${session['entryController']}/footer_new"></g:render>
