<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" crossorigin="anonymous">
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" crossorigin="anonymous"></script>
<script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" crossorigin="anonymous"></script>

<link rel="stylesheet" href="/assets/chat/chat.css">
<link rel="stylesheet" href="/assets/chat/chatInput.css">

<!-- Font Awesome for icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<style>
    /* Global Styles */
    * {
        box-sizing: border-box;
    }

    html{
        overflow: hidden;
    }
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f8f9fa;
        line-height: 1.6;
    }
    .footer-menus,
    .mobile-footer-nav{
        display: none !important;
    }
    .menu-main-min-height{
        min-height: 75px !important;
    }
    @media (max-width: 768px){
        .panels-container .left-panel .panel-content{
            padding: 0;
        }
        .ebook-container{
            padding: 0 !important;
        }
        .ebook-layout{
            padding: 0 !important;
        }
        .content-area{
            padding: 0 !important;
        }
    }

    /* Back button styling */
    .back-btn {
        background: #667eea;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.2s ease;
    }

    .back-btn:hover {
        background: #5a6fd8;
    }

    .back-btn i {
        font-size: 12px;
    }

    /* Chapter Content Heading Styles - Match theoryChapter.gsp styling */
    #chapterContentContainer h1 {
        font-size: 2.5em;
        font-weight: bold;
        margin: 1em 0;
        text-align: center;
        color: #1a237e;
        border-bottom: 3px solid #3498db;
        padding-bottom: 0.5em;
    }

    #chapterContentContainer h2 {
        font-size: 1.8em;
        font-weight: bold;
        margin-top: 2em;
        margin-bottom: 1em;
        color: #1a237e;
        border-bottom: 2px solid #3498db;
        padding-bottom: 0.3em;
    }

    #chapterContentContainer h3 {
        font-size: 1.5em;
        font-weight: bold;
        margin-top: 1.5em;
        margin-bottom: 1em;
        color: #1a237e;
        border-bottom: 1px solid #3498db;
        padding-bottom: 0.3em;
    }

    #chapterContentContainer h4 {
        font-size: 1.3em;
        font-weight: bold;
        margin-top: 1.2em;
        margin-bottom: 0.8em;
        color: #1a237e;
    }

    #chapterContentContainer h5 {
        font-size: 1.1em;
        font-weight: bold;
        margin-top: 1em;
        margin-bottom: 0.6em;
        color: #1a237e;
    }

    #chapterContentContainer h6 {
        font-size: 1em;
        font-weight: bold;
        margin-top: 0.8em;
        margin-bottom: 0.5em;
        color: #1a237e;
    }

    /* AI Tutor Highlighting for First-time Users */
    .ai-tutor-highlight {
        position: relative;
        animation: aiTutorPulse 2s infinite;
    }

    .ai-tutor-highlight::before {
        content: '';
        position: absolute;
        top: -8px;
        left: -8px;
        right: -8px;
        bottom: -8px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        border-radius: 12px;
        z-index: -1;
        animation: aiTutorGlow 2s infinite;
    }

    .ai-tutor-highlight::after {
        content: 'NEW! Try AI Tutor';
        position: absolute;
        top: -35px;
        left: 50%;
        transform: translateX(-50%);
        background: #ff4757;
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 11px;
        font-weight: bold;
        white-space: nowrap;
        z-index: 10;
        animation: aiTutorBounce 1s infinite alternate;
    }

    @keyframes aiTutorPulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }

    @keyframes aiTutorGlow {
        0%, 100% { opacity: 0.7; }
        50% { opacity: 1; }
    }

    @keyframes aiTutorBounce {
        0% { transform: translateX(-50%) translateY(0); }
        100% { transform: translateX(-50%) translateY(-3px); }
    }

    /* Hide highlighting after interaction */
    .ai-tutor-highlight.interacted {
        animation: none;
    }

    .ai-tutor-highlight.interacted::before,
    .ai-tutor-highlight.interacted::after {
        display: none;
    }
</style>

<!-- Main Chat Container -->
<div id="chat-app" class="chat-app">
    <!-- Top Header with Toggle -->
    <div class="app-header">
        <div class="header-content">
            <g:if test="${params.chapterOnly == 'true' && params.chapterId}">
                <button class="back-btn" onclick="goBack()" style="display: block;">
                    <i class="fas fa-arrow-left"></i> <span>Back</span>
                </button>
            </g:if>
            <g:else>
                <button class="back-btn" style="display: none;">
                    <span>Back</span>
                </button>
            </g:else>
            <g:if test="${previewMode}">
                <div class="header-buy-now-section">
                    <button class="gptBuyNowBtn header-buy-btn" onclick="addToCart('${booksMst.id}', '${bookPriceDtl.bookType}')">Buy Now</button>
                </div>
            </g:if>
            <div class="toggle-wrapper">
                <small class="toggle-label">AI Chat</small>
                <button id="toggle-btn" class="toggle-btn" title="Toggle AI Chat"></button>
            </div>
        </div>
    </div>

    <!-- Panels Container -->
    <div id="panels-container" class="panels-container">
        <!-- Left Panel -->
        <div id="left-panel" class="chat-panel left-panel">
            <div class="panel-content">
                <!-- Left Panel Content -->
                <g:render template="/wpmain/aiContent"></g:render>
            </div>
        </div>

        <!-- Resizable Divider -->
        <div id="divider" class="divider">
            <div class="divider-handle">
                <i class="fas fa-grip-lines-vertical"></i>
            </div>
        </div>

        <!-- Right Panel (Chat Content) -->
        <div id="right-panel" class="chat-panel right-panel">
            <div class="panel-content">
                <g:render template="/chat/rightContent"></g:render>
            </div>
        </div>
    </div>
</div>

<!-- Include cart scripts for Buy Now functionality -->
<g:render template="/wsshop/cartScripts"></g:render>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/marked/13.0.1/marked.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.4.0/highlight.min.js"></script>

<script src="/assets/chat/ChatApp.js"></script>
<script src="/assets/chat/ResizableDivider.js"></script>
<script>

    document.addEventListener('DOMContentLoaded', function() {

        const chatApp = new ChatApp();
        window.chatApp = chatApp;
        const chatMessages = document.getElementById('chat-messages');

        // Initialize the ResizableDivider
        const resizableDivider = new ResizableDivider({
            dividerId: 'divider',
            leftPanelId: 'left-panel',
            rightPanelId: 'right-panel',
            containerId: 'panels-container',
            minPanelSize: 50,
            enabled: true
        });

        // Add event listener for toggle button
        document.getElementById('toggle-btn').addEventListener('click', () => {
            chatApp.toggle('toggle-btn')();
            // Update divider state after toggle
            setTimeout(() => {
                resizableDivider.updateState();
            }, 50);
        });

        document.querySelector('.back-btn').addEventListener('click', () => {
            chatApp.openToggle('toggle-btn')();
        });

        // Initialize AI Tutor highlighting for first-time users
        initializeAITutorHighlighting();
    });

    // AI Tutor highlighting functionality
    function initializeAITutorHighlighting() {
        // Check if user has seen the AI Tutor before
        const hasSeenAITutor = localStorage.getItem('hasSeenAITutor');

        if (!hasSeenAITutor) {
            // Add highlighting to the toggle wrapper
            const toggleWrapper = document.querySelector('.toggle-wrapper');
            if (toggleWrapper) {
                toggleWrapper.classList.add('ai-tutor-highlight');

                // Remove highlighting after 10 seconds or when user interacts
                const removeHighlighting = () => {
                    toggleWrapper.classList.add('interacted');
                    localStorage.setItem('hasSeenAITutor', 'true');

                    // Clean up event listeners
                    toggleWrapper.removeEventListener('click', removeHighlighting);
                    toggleWrapper.removeEventListener('mouseenter', removeHighlighting);
                };

                // Remove highlighting on interaction
                toggleWrapper.addEventListener('click', removeHighlighting);
                toggleWrapper.addEventListener('mouseenter', removeHighlighting);

                // Auto-remove after 10 seconds
                setTimeout(() => {
                    if (toggleWrapper.classList.contains('ai-tutor-highlight') &&
                        !toggleWrapper.classList.contains('interacted')) {
                        removeHighlighting();
                    }
                }, 10000);
            }
        }
    }

    // Function to handle back navigation
    function goBack() {
        // Check if there's a referrer, otherwise go to a default page
        if (document.referrer && document.referrer !== window.location.href) {
            window.history.back();
        } else {
            // Fallback: redirect to book details or library
            window.location.href = '/wpmain/aiBookDtl?bookId=${params.bookId}';
        }
    }
</script>

