<%@ page contentType="text/html"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <title>Welcome</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,400i,500,600,700|Work+Sans:300,400,500,600,700" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Exo:300,400" rel="stylesheet">

    <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap');
    .download-apps table td a {
        text-decoration: none;
    }
    @media only screen and (max-width: 767px) {
        .main-wrapper {
            padding: 20px !important;
        }
        .main-wrapper .email-content p {
            font-size: 14px !important;
        }
        .main-wrapper .email-content .notes-bg {
            padding: 1rem !important;
        }
        .main-wrapper .email-content .notes-bg iframe {
            height: auto !important;
        }
        .main-wrapper .email-content ol li.note-point {
            line-height: 20px !important;
            padding-bottom: 7px !important;
            font-size: 14px !important;
        }
        .main-wrapper table {
            width: 100% !important;
        }
        .email-logo tr td img.libwonder-logo-img {
            width: 200px !important;
        }
        .download-apps {
            display: block !important;
        }
        .download-apps table.app-links td {
            padding-top: 0 !important;
        }
        .download-apps table td p {
            font-size: 14px !important;
        }
        .download-apps table td a {
            margin-bottom: 7px !important;
            display: inline-block;
        }
        .download-apps table {
            width: 100% !important;
        }
        .download-apps table br {
            display: none !important;
        }
        .seperator {
            border-bottom: 1px solid #eeeeee !important;
        }
        .seperator img {
            display: none !important;
        }
    }
    @media only screen and (max-width: 330px) {
        .main-wrapper .email-content p {
            font-size: 13px !important;
        }
    }
    </style>

</head>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())
%>
<body>
<div style="background: #f1f1f1; padding: 30px 15px;">
    <div class="main-wrapper" style="max-width:700px;margin:0 auto;font-family: 'Poppins', sans-serif;font-size: 16px;color: #000;background: #ffffff;padding: 40px;border-radius: 10px;">
        <div>
            <table class="email-logo" style="width: 100%;">
                <tr>
                    <td>
                        <img src="${serverURL}/assets/ws/wslogo_email.png" alt="wonderslate-logo" width="160" height="auto" style="width: 160px;height: auto;margin-right: 8px;">
                    </td>
                </tr>
            </table>
            <table class="email-content" style="width: 100%;margin-top: 0;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 18px; margin-bottom: 1.5rem;margin-top: 0;">
                            Hello <span style="color: #212529;font-weight: 600;">${name},</span>
                        </p>
                        <p style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 15px; margin-bottom: 1rem;">
                            You have successfully purchased the Books.
                        </p>

                        <p style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 15px; margin-bottom: 1rem;">
                            Purchase Details:
                        </p>
                        <p style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 25px; font-size: 15px; margin-bottom: 1rem;">
                            Book(s) - <span style="color: #8B2BA3;font-weight: 600;">${cartBooks}</span><br>
                            Price - <span style="color: #8B2BA3;font-weight: 600;">Rs. ${amount}</span><br>
                            Payment Id - <span style="color: #8B2BA3;font-weight: 600;">${paymentId}</span>
                        </p>
                    </td>
                </tr>
            </table>
            <table class="email-content" style="width: 100%;">
                <tr>
                    <td class="notes-bg" style="padding: 1.5rem;border: 1px solid #ffcf96;border-radius: 10px;background-color: #fffcf8;">
                        <p style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: 600; line-height: 20px; font-size: 15px; margin-bottom: 1rem; margin-top:0;">
                            To access your Books, follow these instructions.
                        </p>
                        <ol style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 25px; font-size: 15px; margin-top: 0;padding-left: 15px;">
                            <li class="note-point">Go to <a href="https://www.wonderslate.com" target="_blank" style="color: #007BFF;font-weight: 600;">www.wonderslate.com</a></li>
                            <li class="note-point">Login using your register id <span style="color: #212529;font-weight: 600;">${loginId}</span>. Login using this id.</li>
                            <li class="note-point">Click on <span style="color: #212529;font-weight: 600;">My Home</span> and then on <span style="color: #212529;font-weight: 600;">My Books</span>.</li>
                            <li class="note-point">All the Books purchased will be available from the My Books section.</li>
                        </ol>
                    </td>
                </tr>
            </table>
            <table class="email-content" style="width: 100%;">
                <tr>
                    <td>
                        <p style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 22px; font-size: 15px; margin-bottom: .5rem;">
                            If you face any issues in accessing your Book, click on the following link for help. <br>
                            <a href="${serverURL}/creation/userBookPurchase?paymentId=${paymentId}" target="_blank" style="color: #007BFF;font-weight: 600;">
                                ${serverURL}/creation/userBookPurchase?paymentId=${paymentId}
                            </a>
                        </p>
                    </td>
                </tr>
            </table>
            <table class="seperator" style="width: 100%;">
                <tr>
                    <td>
                        <img src="${serverURL}/assets/ws/horizontal-separator-line.png" width="100%" height="2" style="width: 100%;height: 2px;">
                    </td>
                </tr>
            </table>
            <div class="download-apps" style="display: flex; align-items: center;">
                <table style="width: 50%;">
                    <tr>
                        <td style="width:100%;padding-top: 1rem;display: flex;">
                            <p style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 15px; margin-bottom: 1rem;">
                                You can also download our app for better experience.
                            </p>
                        </td>
                    </tr>
                </table>
                <table class="app-links" style="width: 50%;">
                    <tr>
                        <td style="width:50%;text-align: center; padding-top: 1.5rem;">
                            <a href="https://bit.ly/WonderslateIOS" target="_blank">
                                <img src="${serverURL}/assets/ws/icon-mail-appstore.png" width="120" height="auto" style="width: 130px;height: auto;">
                            </a>
                            <a href="https://bit.ly/WonderslateApp" target="_blank">
                                <img src="${serverURL}/assets/ws/icon-mail-playstore.png" width="120" height="auto" style="width: 130px;height: auto;">
                            </a>
                        </td>
                    </tr>
                </table>
            </div>
            <table class="email-content" style="width: 100%;">
                <tr>
                    <td>
                        <p style="color: rgba(68, 68, 68, 0.8);font-family: 'Poppins', sans-serif; font-weight: normal; line-height: 20px; font-size: 15px; margin-bottom: .5rem;">
                            Thanks & Regards,<br>
                            Team Wonderslate
                        </p>
                    </td>
                </tr>
            </table>
            <table style="width: 100%;margin: 0.5rem auto 0;">
                <tr>
                    <td style="padding-top: 1rem;">
                        <p style="color: rgba(68, 68, 68, 0.48);font-family: 'Poppins', sans-serif; font-weight: normal; text-align: center; line-height: 16px; font-size: 10px; margin-top: 1rem; margin-bottom: 0;">
                            This email has been sent to you by Wonderslate Technologies Pvt Ltd.<br> No 401 Fourth Floor, Sapthagiri Apartments, No. 30, 10th Cross, 15th Main Rd, Raj Mahal Vilas Extension, Sadashivanagar, Bengaluru, 560080.
                        </p>
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>
</body>
</html>
