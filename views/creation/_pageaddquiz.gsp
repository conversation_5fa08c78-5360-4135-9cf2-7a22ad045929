<%--
  Created by IntelliJ IDEA.
  User: Anand
  Date: 25-Sep-15
  Time: 11:37 AM
--%>

<%@ page import="com.wonderslate.data.SyllabusMst" contentType="text/html;charset=UTF-8" %>
<html>
<head>
    <title></title>

</head>
<script language="JavaScript">


    function checkAddContent()
    {
        if(document.contentForm.syllabus.selectedIndex==0){
            alert("Please select Syllabus");
            document.contentForm.syllabus.focus();
            return false;
        }
        else if(document.contentForm.grade.selectedIndex==0){
            alert("Please select Grade");
            document.contentForm.grade.focus();
            return false;
        }
        else if(document.contentForm.subject.selectedIndex==0){
            alert("Please select Subject");
            document.contentForm.subject.focus();
            return false;
        }
        else if(document.contentForm.resourceType.selectedIndex==0){
            alert("Please select resource type");
            document.contentForm.resourceType.focus();
            return false;
        }

        else  if (document.contentForm.resourceName.value == '') {
            alert("Please enter the  name");
            document.contentForm.resourceName.focus();
            return false;
        }

        return true;

    }


</script>
<body>


<g:uploadForm name="contentForm" controller="resourceCreator" action="addQuiz" onsubmit="return checkAddContent()">

    <p><b>Upload Quiz</b></p>



    <table width="500" border="1" cellspacing="0">
        <tr>
            <td><b>Syllabus</b></td>
            <td><b>Grades</b></td>
            <td><b>Subjects</b></td>
            <td><b>Topics</b></td>
            <td><b>Resource Type</b></td>
            <td>Name</td>
            <td></td>
        </tr>
        <tr>
            <td> <g:select name="syllabus" from="${com.wonderslate.data.SyllabusMst.list()}" optionKey="name" optionValue="name"
                           noSelection="['':'Choose Syllabus']"  onchange="syllabusChanged(this.value);" /></td>
            <td><span id="grades"></span></td>
            <td> <span id="subjects"></span></td>
            <td> <span id="topics"></span></td>
            <td> <g:select name="resourceType" from="${com.wonderslate.data.ResourceType.findAllByUseTypeNotInList(["file","link"])}" optionKey="resourceType" optionValue="resourceType"
                           noSelection="['':'Choose']"  /></td>
            <td><input type="text" size="30" maxlength="255" name="resourceName"> </td>
            <td><input type="file" name="file" /></td>
        </tr>
    </table>


    <g:submitButton name="upload"  value="Add" />

</g:uploadForm>
<g:javascript src="jquery-1.11.2.min.js"/>
<g:javascript src="bootstrap.min.js"/>
</body>
</html>
