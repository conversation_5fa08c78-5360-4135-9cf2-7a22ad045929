<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Wonderslate</title>
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1 maximum-scale=1, minimum-scale=1, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="mobile-web-app-capable" content="yes">
    <link rel="icon" sizes="192x192" href="${assetPath(src: 'app-icon-4x.png')}">
    <link rel="manifest" href="manifest.json">
    <link rel="icon" href="${assetPath(src: 'favicon.ico')}" type="image/x-icon" />
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link href='https://fonts.googleapis.com/css?family=Exo' rel='stylesheet' type='text/css'>
    <asset:stylesheet href="bootstrap.css"/>
    <asset:stylesheet href="style.css"/>
    <asset:stylesheet href="login.css"/>
    <asset:stylesheet href="font-awesome.min.css"/>
</head>

<body>
<div class="header-and-topic-select">
    <div class="container">
        <div class="header">
            <div class="row">
                <div class="col-md-6 col-sm-6">
                    <div class="brand">
                        <a  href="#"><img src="${assetPath(src: 'logo-ws.png')}">WONDERSLATE</a>
                    </div>
                </div>
                <div class="col-md-6 col-sm-6 hidden-xs text-right">
                    <div class="social-links">
                        <ul class="list-inline">
                            <li><a href="https://www.facebook.com/wonderslate/"><img src="${assetPath(src: 'icon-fb.png')}"></a></li>
                            <li><a href="https://twitter.com/wonderslate"><img src="${assetPath(src: 'icon-twitter.png')}"></a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
<div class="container " >
    <div class="row">
        <div class="Absolute-Center is-Responsive">

            <div class="col-sm-12 col-md-10 col-md-offset-1">
                <form method="POST" action="${resource(file: 'j_spring_security_check')}">
                    <div class="form-group input-group">
                        <span class="input-group-addon"><i class="fa fa-user"></i></span>
                        <input class="form-control" type="text" name='j_username' placeholder="username"/>
                    </div>
                    <div class="form-group input-group">
                        <span class="input-group-addon"><i class="fa fa-lock"></i></span>
                        <input class="form-control" type="password" name='j_password' placeholder="password"/>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" name="_spring_security_remember_me"> Remember me
                        </label>
                    </div>
                    <div class="form-group">
                        <button type="submit" class="btn btn-def btn-block">Login</button>
                    </div>

                </form>
            </div>
        </div>
    </div>
</div>
    </div>
<g:render template="/funlearn/footer"></g:render>
</body>