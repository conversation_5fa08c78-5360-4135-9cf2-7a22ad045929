<%--
  Created by IntelliJ IDEA.
  User: Anand
  Date: 25-Sep-15
  Time: 11:37 AM
--%>

<%@ page import="com.wonderslate.data.SyllabusMst" contentType="text/html;charset=UTF-8" %>
<html>
<head>
    <title></title>

</head>
<script language="JavaScript">
    function submitTopic()
    {
        if(document.topicForm.syllabus.selectedIndex==0){
            alert("Please select Syllabus");
            document.topicForm.syllabus.focus();
            return false;
        }
        else if(document.topicForm.grade.selectedIndex==0){
            alert("Please select Grade");
            document.topicForm.grade.focus();
            return false;
        }
        else if(document.topicForm.subject.selectedIndex==0){
            alert("Please select Subject");
            document.topicForm.subject.focus();
            return false;
        }
        else  if (document.topicForm.topic.value == '') {
            alert("Please enter the topic name");
            document.topicForm.topic.focus();
            return false;
        }
        return true;
    }



</script>
<body>
<g:if test="${flash.error}">
    <div class="alert alert-error" style="display: block">${flash.error}</div>
</g:if>
<g:if test="${flash.message}">
    <div class="message" style="display: block">${flash.message}</div>
</g:if>
<g:form name="topicForm" action="topiccreation" onsubmit="return submitTopic()">

    <b>Add Chapter/Topic name:</b><br>
    <g:select name="syllabus" from="${com.wonderslate.data.SyllabusMst.findAllBySyllabusType(session['syllabusType'])}" optionKey="name" optionValue="name"
              noSelection="['':'Choose Syllabus']" />
    <g:select name="grade" from="${1..10}"
              noSelection="['':'-Grade-']"/>
    <g:select name="subject" from="${com.wonderslate.data.SubjectMst.list()}" optionKey="name" optionValue="name"
              noSelection="['':'Choose Subject']" />
    Chapter/Topic Name: <g:textField name="topic"/>

    <br>
    <g:submitButton name="upload"  value="Create" />

</g:form>
<br><br>

<g:javascript src="jquery-1.11.2.min.js"/>
<g:javascript src="bootstrap.min.js"/>
</body>
</html>