<div class="modal fade" data-backdrop="static" data-keyboard="false" id="welcomeModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" id="registerModalContent">
            <div class="modal-body">
                <div>


                    <g:form name="welcomeForm" url="[action:'basicProfileUpdate',controller:'creation']"  method="post">
                        <input type="hidden" name="actionType">
                        <div class="row">
                            <div class="col-md-2"><img src="${assetPath(src: 'logo-ws.png')}"></div>
                            <div class="col-md-9">

                                <h3>${session['userdetails'].name},<br> welcome to Wonderslate.</h3>
                                <p>Kindly provide us the following information to serve you better.</p>
                                <p><b>You are </b></p>
                                <div class="red" style="display: none" id="sex">** Please select Female or Male.</div>
                                <div class="form-group">
                                    <input type="radio" name="sex" value="female">&nbsp;Female&nbsp;&nbsp;&nbsp;&nbsp;<input type="radio" name="sex" value="male">&nbsp;Male
                                </div>
                                    <div class="red" style="display: none" id="usertype">** Please select a user type</div>
                                    <div class="form-group">
                                        <input type="checkbox" name="student" >&nbsp;Student&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="teacher">&nbsp;Teacher&nbsp;&nbsp;&nbsp;&nbsp;<input type="checkbox" name="parent">&nbsp;Parent

                                    </div>


                                <div class="red" style="display: none" id="syllabus">** Please select the syllabus.</div>
                                <div class="red" style="display: none" id="grade" >** Please enter the grade.</div>
                                <div class="form-group">
                                    <g:select name="syllabus" from="${com.wonderslate.data.SyllabusMst.findAllBySyllabusType(session['syllabusType'])}" optionKey="name" optionValue="name"
                                              noSelection="['':'Choose Syllabus']" /> &nbsp;<input type="text" name="grade"  size="20" placeholder="Grade/Class">
                                </div>

                                <div class="red" style="display: none" id="qualification" >** Please enter your qualification.</div>
                                <div class="form-group"><input type="text" name="qualification"  size="20" placeholder="Qualification"></div>
                                <div class="red" style="display: none" id="city" >** Please enter your place of residence.</div>
                                <div class="form-group"><input type="text" name="city"  size="30" placeholder="Place(City/Town/Village)"></div>



                                    <br>
                                    <button class="btn  btn-primary " type="button" onclick="javascript:formSubmitWelcome('profile')">Complete your profile</button>&nbsp;&nbsp;&nbsp;<b>or</b>&nbsp;&nbsp;&nbsp;
                                    <button class="btn  btn-primary " type="button" onclick="javascript:formSubmitWelcome('tour')">Take a tour</button>&nbsp;&nbsp;&nbsp;<b>or</b>&nbsp;&nbsp;&nbsp;
                                    <button class="btn  btn-primary " type="button" onclick="javascript:formSubmitWelcome('start')">Let me start using Wonderslate</button>
                           </div>

                        </div>
                        </g:form>

                </div>
            </div>
        </div>
    </div>
</div>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script>
    function formSubmitWelcome(actionType){
        var allValid =  true;
        document.getElementById('usertype').style.display = 'none';
        document.getElementById('sex').style.display = 'none';
        document.getElementById('syllabus').style.display = 'none';
        document.getElementById('grade').style.display = 'none';
        document.getElementById('qualification').style.display = 'none';
        document.getElementById('city').style.display = 'none';
        if(!(document.welcomeForm.student.checked||document.welcomeForm.teacher.checked||document.welcomeForm.parent.checked)){
            document.getElementById('usertype').style.display = 'block';
            allValid =  false;

        }
        if(!(document.welcomeForm.sex[0].checked||document.welcomeForm.sex[1].checked)){
            document.getElementById('sex').style.display = 'block';
            allValid =  false;

        }

        if(document.welcomeForm.student.checked || document.welcomeForm.teacher.checked) {
                if (document.welcomeForm.syllabus.selectedIndex == 0) {
                    document.getElementById('syllabus').style.display = 'block';
                    allValid =  false;
                }
                if (document.welcomeForm.grade.value == '') {
                    document.getElementById('grade').style.display = 'block';
                    allValid =  false;
                }
        }
        if(document.welcomeForm.parent.checked && document.welcomeForm.qualification.value == ''){
                document.getElementById('qualification').style.display = 'block';
                allValid =  false;
        }
        if (document.welcomeForm.city.value == '') {
            document.getElementById('city').style.display = 'block';
            allValid =  false;
        }



    if(allValid) {
            document.welcomeForm.actionType.value = actionType;
            $("#welcomeModal").modal("hide");
            document.welcomeForm.submit();
        }
    }
</script>
