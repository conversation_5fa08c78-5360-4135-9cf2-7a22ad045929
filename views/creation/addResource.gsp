<%--
  Created by IntelliJ IDEA.
  User: Anand
  Date: 25-Sep-15
  Time: 11:37 AM
--%>

<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Wonderslate</title>
    <link href='https://fonts.googleapis.com/css?family=Roboto:300,700,900' rel='stylesheet' type='text/css'>
    <link rel="stylesheet" href="${request.contextPath}/css/bootstrap.min.css">
    <link rel="stylesheet" href="${request.contextPath}/css/style.css">
</head>
<>


<script language="JavaScript">


    function syllabusChanged(syllabus) {
        <g:remoteFunction controller="creation" action="syllabusChanged"
                    update="grades"
                    params="'syllabus='+syllabus"/>
    }

    function gradeChanged(grade) {
        <g:remoteFunction controller="creation" action="gradeChanged"
                    update="subjects"
                    params="'grade='+grade+'&syllabus='+document.contentForm.syllabus[document.contentForm.syllabus.selectedIndex].value"/>
    }

    function subjectChanged(subject) {
        <g:remoteFunction controller="creation" action="subjectChanged"
                    update="topics"
                    params="'subject='+subject+'&syllabus='+document.contentForm.syllabus[document.contentForm.syllabus.selectedIndex].value+'&grade='+document.contentForm.grade[document.contentForm.grade.selectedIndex].value"/>
    }
</script>

<g:render template="addTopic"></g:render>
<br><br>
<g:if test="${contentType == 'quiz'}">
    <g:render template="pageaddquiz"></g:render>
</g:if>
<g:if test="${contentType == 'link'}">
    <g:render template="pageaddlink"></g:render>
</g:if>
<g:if test="${contentType == 'file'}">
    <g:render template="pageupload"></g:render>
</g:if>
<g:if test="${chapterDetails!=null}">
    <div class="container">
             <!-- Header -->
         <div class="header">
             <div class="row ">
                 <div class="col-md-12">
                    Details
                </div>
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-bordered">
                <thead>
                <tr>
                    <th>Type</th>
                    <th>Name</th>
                    <th>Created By</th>
                    <th>Link</th>
                </tr>
                </thead>

               <g:each in="${chapterDetails}" var="p" status="i">
                <tr>
                    <td>${p.resType}</td>
                    <td>${p.resourceName}</td>
                    <td>${p.createdBy}</td>
                    <td>${p.resLink}</td>

                </tr>
                </g:each>
                </tbody>
            </table>
        </div>
    </div>
</g:if>
<g:else>
    doesnt have any content
</g:else>

<g:if test="${cookie(name: 'creationSyllabus')}">
 <script>
     syllabusChanged("${cookie(name: 'creationSyllabus')}");
 </script>
</g:if>
<g:cookie name="creationSyllabus" />
<asset:javascript src="jquery-1.11.2.min.js"/>

</body>
</html>