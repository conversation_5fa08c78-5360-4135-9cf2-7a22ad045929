<%@ page contentType="text/html"%>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html>
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Welcome</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <link href="https://fonts.googleapis.com/css?family=Roboto:400,500,700,900" rel="stylesheet">
</head>
<%
    String serverURL = request.getScheme()+"://"+request.getServerName()+
            ("http".equals(request.getScheme()) && request.getServerPort() == 80 ||
                    "https".equals(request.getScheme()) && request.getServerPort() == 443 ? "" : ":" +
                    request.getServerPort())

%>
<body>
<div style="max-width: 800px;padding:1rem;margin:0 auto;border:2px solid #000;font-family: 'Roboto', sans-serif; font-size: 14px; padding-bottom: 2rem">
    <!--header starts-->
    <table cellpadding="20" style="width: 100%;">
        <tr>
            <td>
                <h1 style="text-align: center; font-weight: 900;font-size: 18px;margin:0;padding-bottom:10px;padding-top: 10px;">TAX INVOICE</h1>
            </td>
        </tr>
    </table>
    <!--header finishes-->

    <!--Address section-->
    <table style="width: 100%;margin-top: 1rem;">
        <tr>
            <td style="width: 70%;">
                <p style="font-family: 'Roboto', sans-serif;font-weight: 500;margin: 0 0 10px; font-size: 20px;line-height: 20px;">Wonderslate Technologies Private Limited.</p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">No 401 Fourth Floor, Sapthagiri Apartments,</p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">No. 30, 10th Cross, 15th Main Rd, Raj Mahal Vilas Extension,</p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">Sadashivanagar, Bengaluru, 560080</p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">GST No. : 29AABCW7019D1ZC</p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">E-mail : <EMAIL></p>
                <p style="font-family: 'Roboto', sans-serif;font-weight: normal;margin: 0;font-size: 14px;line-height: 16px;">Website : https://oswalpublishers.com/ </p>
            </td>
            <td style="width: 50%;text-align: right;">
                <img src='${serverURL}/assets/oswalpublisher/logo.png' width="100%" height="auto" style="height: auto;float: right;"/>
            </td>
        </tr>
    </table>
    <%


        Float igst = 0
        //Total Amount Is 50
        //Taxable Amount= 100/118*50(Book’s Total Price)= 42.37

        //Igst= 18% Of Taxable Amount If gstPercentage=18(if book has isbn) else 5%
        Float tamount=0
        if(gstPercentage==5) {
            tamount = (Float)((new Float(amount))*(100/105)).trunc(2)
        } else {
            tamount = (Float)((new Float(amount))*(100/118)).trunc(2)
        }
        if(gstPercentage==5) {
            igst = (Float)((5*tamount)/100)
        } else {
            igst = (Float)((18*tamount)/100)
        }
    %>
    <div style="margin-top:2rem;border-top: 1px solid #000; padding-top: 1rem;">
        <table cellpadding="10" style="margin-top:5px;float: right;">
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;">Date :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${createdAt}</span>
                </td>
            </tr>
        </table>
        <table cellpadding="10" style="margin-top:5px;">
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Order No. :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${poNo}</span>
                </td>
            </tr>
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Name :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${name}</span>
                </td>
            </tr>
            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Contact No. :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${mobile}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> E-mail :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${account}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Address :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${state}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Payment Mode :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${method}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;"> Customer IP :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${ipAddress}</span>
                </td>
            </tr>

            <tr style="position: relative;">
                <td style="padding: 0px;">
                    <h2  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 15px;margin:0;line-height: 15px;">Bank Ref. :</h2></td>
                <td style="padding: 0px;">
                    <span style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;margin-right: 4px;line-height: 16px;">${paymentId}</span>
                </td>
            </tr>
        </table>
    </div>

    <div style="margin-top: 10px;">
        <table cellpadding="10" style="border-collapse: collapse; width: 100%;margin-top: 4px;">
            <tr>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse; line-height: 16px;">Sl No.</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Book Title</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Price</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Discount</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Taxable Amount</th>
                <th colspan="2"  style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Tax</th>
                <th rowspan="2" style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;border:1px solid #000;border-collapse: collapse;line-height: 16px;">Total Amount</th>
            </tr>
            <tr>
                <th style="font-family: 'Roboto', sans-serif;border:1px solid #000;border-collapse: collapse;line-height: 16px;">IGST 5%</th>
                <th style="font-family: 'Roboto', sans-serif;border:1px solid #000;border-collapse: collapse; line-height: 16px;">IGST 18%</th>
            </tr>
            <% if(shoppingCartOrdersDtl!=null){
                for(int i=0;i<shoppingCartOrdersDtl.size();i++){
            %>

            <%
                    igst = 0
                    tamount=0
                    if(shoppingCartOrdersDtl[i].gstPercentage==5) {
                        tamount = (Float)((new Float(shoppingCartOrdersDtl[i].amount))*(100/105)).trunc(2)
                    } else {
                        tamount = (Float)((new Float(shoppingCartOrdersDtl[i].amount))*(100/118)).trunc(2)
                    }
                    if(shoppingCartOrdersDtl[i].gstPercentage==5) {
                        igst = (Float)((5*tamount)/100)
                    } else {
                        igst = (Float)((18*tamount)/100)
                    }
            %>
            <tr>

                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">${i+1}</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse;line-height: 16px;">${shoppingCartOrdersDtl[i].title}</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${shoppingCartOrdersDtl[i].bookPrice}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${shoppingCartOrdersDtl[i].discountAmount}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${tamount}" type="number" maxFractionDigits="2" /></td>
                <%if(shoppingCartOrdersDtl[i].gstPercentage==5) {%>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${igst}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">0</td>
                <%} else {%>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">0</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${igst}" type="number" maxFractionDigits="2" /></td>
                <%}%>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${shoppingCartOrdersDtl[i].amount}" type="number" maxFractionDigits="2" /></td>
            </tr>

            <%}%>
            <tr style="border:none;">
                <td colspan="4" style="font-family: 'Roboto', sans-serif;text-align: center;font-size: 16px;color:black;font-weight: 900;"><div style="display: flex;justify-content: center;align-items: center;padding: 0.5rem;">IN WORDS:<span style="text-transform: uppercase;font-weight: 500;">&nbsp;${amtStr}</span></div></td>
                <td  colspan="3" style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;">Grand Total (INR)</td>
                <td  style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;"><g:formatNumber number="${totalAmount}" type="number" maxFractionDigits="2" /></td>
            </tr>
            <%}else{%>
            <tr>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">1</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse;line-height: 16px;">${title}</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${price}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${discountAmount}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${tamount}" type="number" maxFractionDigits="2" /></td>
                <%if(gstPercentage==5) {%>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${igst}" type="number" maxFractionDigits="2" /></td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">0</td>
                <%} else {%>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;">0</td>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${igst}" type="number" maxFractionDigits="2" /></td>
                <%}%>
                <td style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 500;border:1px solid #000;border-collapse: collapse; line-height: 16px;text-align: center;"><g:formatNumber number="${amount}" type="number" maxFractionDigits="2" /></td>
            </tr>
            <tr style="border:none;">
                <td colspan="4" style="font-family: 'Roboto', sans-serif;text-align: center;font-size: 16px;color:black;font-weight: 900;"><div style="display: flex;justify-content: center;align-items: center;padding: 0.5rem;">IN WORDS:<span style="text-transform: uppercase;font-weight: 500;">&nbsp;${amtStr}</span></div></td>
                <td  colspan="3" style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;">Grand Total(INR)</td>
                <td  style="font-family: 'Roboto', sans-serif;font-size: 16px;color:black;font-weight: 900;border:1px solid #000;border-collapse: collapse; padding: 5px;text-align: center;"><g:formatNumber number="${amount}" type="number" maxFractionDigits="2" /></td>
            </tr>

            <%}%>


        </table>
    </div>
    <div style="display: flex;margin-top: 4rem;">
        <table cellpadding="10" style="width: 100%;">
            <tr>
                <td style="width: 60%;">
                    <h1 style="font-family: 'Roboto', sans-serif;font-weight: 900;font-size: 16px;margin:0;margin-bottom: 20px;line-height: 16px;">Terms & Conditions:</h1>
                    <p style="font-family: 'Roboto', sans-serif;font-size: 14px;margin:0;color:#444444;line-height: 16px;">
                    <p style="font-family: 'Roboto', sans-serif;font-size: 14px;margin:0;color:#444444;line-height: 16px;">
                        (a) The terms of this Agreement shall be binding for any further goods/services supplied by Company to Client.
                        (b) Upon execution of this Agreement, Client is agreeing to pay to Company, the full amount of the Fee.
                        (c) If client does not cancel attendance at, or participation in, the Program for any reason whatsoever,
                        Client will not be entitled to receive a refund.(d) Amount is inclusive of Tax.(e) All disputes are subjected to Bangalore, Karnataka Jurisdriction.


                    </p>
                </td>
            </tr>
        </table>
    </div>
    <div style="font-family: 'Roboto', sans-serif;margin-top: 1rem;">
        <table cellpadding="10" style="width: 100%; border-top:1px solid #000;border-bottom: 1px solid #000;">
            <tr>
                <td align="center">
                    <h1 style="font-family: 'Roboto', sans-serif;font-weight: 700;font-size: 16px;margin:0;line-height: 16px;">This is a computer generated Invoice hence Stamp and Signature not required.</h1>
                </td>
            </tr>
        </table>
    </div>
</div>
</body>
</html>
