
<g:render template="/etexts/navheader_new"></g:render>
<style>
@media (max-width: 350px) {
    #gRecaptcha {
        transform:scale(0.77);
        -webkit-transform:scale(0.77);
        transform-origin:0 0;
        -webkit-transform-origin:0 0;
        margin-bottom: -15px;
    }
}
</style>
<section class="contactus">
    <div class="container card shadow p-4 p-md-5 my-md-5">
        <h1>Contact Us</h1>
        <hr class="mb-4">
        <p>Please feel free to get in touch with us with your questions and suggestions.</p>
        <p>Write to us at <a href="mailto:<EMAIL>"><EMAIL></a> or fill out the form below. We will get in touch with you on priority.</p>
        <div class="contact_form col-12 col-md-6 px-0 mt-4">
            <form class="contact-form" method="post" action="/log/addContactForm" novalidate>
                <div class="form-group">
                    <label>Name <span class="text-danger">*</span> </label>
                    <input type="text" name="name" class="form-control" required>
                    <div class="invalid-feedback">
                        Please enter name.
                    </div>
                </div>
                <div class="form-group">
                    <label>Email <span class="text-danger">*</span> </label>
                    <input type="email" name="email" class="form-control"
                           pattern="^[a-zA-Z0-9]+(\.[_a-zA-Z0-9]+)*@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*(\.[a-zA-Z]{2,15})$" required>
                    <div class="invalid-feedback">
                        Please enter valid email address.
                    </div>
                </div>
                <div class="form-group">
                    <label>Phone Number <span class="text-danger">*</span> </label>
                    <input type="tel" class="form-control" name="phone" id="phoneNumber" minlength="10" maxlength="10" oninput="numberOnly(this.id);"
                           pattern="^\(?([0-9]{3})\)?[-. ]?([0-9]{3})[-. ]?([0-9]{4})$" required>
                    <div class="invalid-feedback">
                        Please enter valid phone number.
                    </div>
                </div>
                <div class="form-group">
                    <label>Comment <span class="text-danger">*</span> </label>
                    <textarea class="form-control" name="comment" required></textarea>
                    <div class="invalid-feedback">
                        Please enter your comment.
                    </div>
                </div>
                <div class="form-group form-check">
                    <label class="form-check-label"><input type="checkbox" name="copy" class="form-check-input"> Send me a copy of this submission.</label>
                </div>
                <div class="form-group">
                    <div id="gRecaptcha"></div>
                    <div class="invalid-feedback invalid-contact-captcha" style="display: none;">
                        Please verify that you are not a robot.
                    </div>
                </div>
                <button type="submit" class="btn btn-primary col-md-3 mt-3">Submit</button>
            </form>
        </div>
    </div>
</section>

<g:render template="/etexts/footer_new"></g:render>

<script src="https://www.google.com/recaptcha/api.js?onload=recaptchaContactCallback&render=explicit" async defer></script>
<script>
    var contactId;
    (function() {
        'use strict';
        window.addEventListener('load', function() {
            var forms = document.getElementsByClassName('contact-form');
            var validation = Array.prototype.filter.call(forms, function(form) {
                form.addEventListener('submit', function(event) {
                    if($("div#gRecaptcha").length > 0) {
                        if (form.checkValidity() === false) {
                            event.preventDefault();
                            event.stopPropagation();
                        } else if(grecaptcha.getResponse(contactId) == "") {
                            event.preventDefault();
                            event.stopPropagation();
                            $(".invalid-contact-captcha").css('display', 'block');
                        } else {
                            setTimeout(function(){
                                alert('Submitted successfully!');
                            },100);
                        }
                        form.classList.add('was-validated');
                    } else {
                        $(".invalid-contact-captcha").css('display', 'none');
                        if ($('.contact-form .captchaError').length > 0) {
                            document.querySelector(".contact-form .captchaError").innerHTML = "<div class='alert alert-danger p-2 border-0 m-0 d-flex align-items-center'>" +
                                "<span class='mr-2'>X</span>" +
                                "Something went wrong with reCAPTCHA." +
                                "</div>";
                        } else {
                            var error_div = document.createElement("div");
                            error_div.className = 'captchaError';
                            error_div.innerHTML = "<div class='alert alert-danger p-2 border-0 m-0 d-flex align-items-center'>" +
                                "<span class='mr-2'>X</span>" +
                                "Something went wrong with reCAPTCHA." +
                                "</div>";
                            var append_div = document.querySelector(".contact-form .form-check");
                            append_div.parentNode.insertBefore(error_div, append_div.nextSibling);
                        }
                        event.preventDefault();
                        event.stopPropagation();
                    }
                }, false);
            });
        }, false);
    })();

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    var recaptchaContactCallback = function() {
        contactId = grecaptcha.render('gRecaptcha', {
            'sitekey' : '6LcL_ooaAAAAAJH2vf_Tt-FtUDGFTzVJMEQbC3IG'
        });
    };

    document.addEventListener('contextmenu', function(e) {
        e.preventDefault();
    });
    document.onkeydown = function(e) {
        if(e.keyCode == 123) {
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'I'.charCodeAt(0)) {
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'C'.charCodeAt(0)) {
            return false;
        }
        if(e.ctrlKey && e.shiftKey && e.keyCode == 'J'.charCodeAt(0)) {
            return false;
        }
        if(e.ctrlKey && e.keyCode == 'U'.charCodeAt(0)) {
            return false;
        }
    }
</script>
