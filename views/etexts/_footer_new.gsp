<g:render template="/evidya/commonfooter_new"></g:render>
<asset:javascript src="moment.min.js"/>

<style>
@media (max-width:400px){
    #loginLimitModal .modal-body p {
        font-size: 14px !important;
    }
    #loginLimitModal .modal-body p br {
        display: none;
    }
}
</style>

<%if(!isBookPage){%>
<footer class="footer-menu">
    <div class="row w-100 align-items-center">
        <div class="col-8">
            <ul class="d-flex flex-wrap mb-0">
                <%if(session["userdetails"]==null){%>
                <li><a href="javascript:" id="createProfile">Create Profile</a> </li>
                <%}else{%>
                <li><a href="/creation/userProfile">Create Profile</a> </li>
                <%}%>
                <li><a href="/etexts/feedback">Feedback</a> </li>
                <li><a href="/etexts/terms">Terms of Use</a> </li>
                <li><a href="/etexts/privacy">Privacy Policy</a> </li>
                <li><a href="/etexts/contact">Contact Us</a> </li>
                <li><a href="/etexts/help">Help</a> </li>
            </ul>

        </div>

        <div class="col-4 text-center">
            <p class="text-center mb-0">Contact us for an offline conversation about how we can help you</br>create a more digitally engaged classroom.</br>
            Write to us: <a href="mailto:<EMAIL>"><EMAIL></a></p>
            <p class="copyright-footer"></p>
        </div>
    </div>
</footer>

<% } %>
<div class="modal" id="loginLimitModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center mb-0 py-3" style="font-size: 16px;font-family: 'HelveticaNeue-Roman';">OOPS! Exceeded maximum concurrent sessions </br>for this account. Please try after sometime.</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-sm btn-primary col-4 col-md-3" data-dismiss="modal" style="font-family: 'HelveticaNeue-Roman';">Okay</button>
            </div>

        </div>
    </div>
</div>
<div class="modal" id="signUpLimitModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center mb-0 py-3" style="font-size: 16px;font-family: 'HelveticaNeue-Roman';">This Email ID is already taken. Please sign in</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-sm btn-primary col-4 col-md-3" data-dismiss="modal" style="font-family: 'HelveticaNeue-Roman';">Okay</button>
            </div>

        </div>
    </div>
</div>
<asset:javascript src="landingpage/popper.min.js" async="true"/>
<asset:javascript src="landingpage/bootstrap.min.js" async="true"/>
%{--<script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0-rc.2/js/materialize.min.js"></script>--}%

<asset:javascript src="landingpage/jquery.shorten.js" async="true"/>
<asset:javascript src="landingpage/slick.js" async="true"/>
<asset:javascript src="landingpage/index.js" async="true"/>

<sec:ifNotLoggedIn>
    <g:render   template="/creation/register"></g:render>
</sec:ifNotLoggedIn>


<script>

    //Dynamic Year in Footer
    var strDate = new Date();
    var shortYear = strDate.getFullYear();
    $('.copyright-footer').html(shortYear +' SAGE Publications India Pvt.Ltd.');


    $( "#evidyaLogin, #evidyaLogin1" ).click(function() {
        document.getElementById('loginFailedEvidya').style.display = 'none';
        $( ".evidyaLogin" ).toggle();
        $( ".browseMenu" ).hide();
        $('.forgotPassword,#reset-password-completed,#reset-google-paswd,.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
        $('body').removeClass('profile-popup-open');
    });
    $( ".browse").click(function() {
        $( ".browseMenu" ).toggle();
        $( ".evidyaLogin" ).hide();
        $('body').removeClass('profile-popup-open');
    });

    $(function(){
        $(".img-wrapper").hover(function(){
                $(this).find(".zoomImage").fadeIn();
            }
            ,function(){
                $(this).find(".zoomImage").fadeOut();
            }
        );
    });
</script>
<script>

    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailedEvidya').style.display = 'none';
        }
        else{
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }

    }

    var simultaneosCookie = getCookie("SimulError");
    var loginErrorCheck="${params.loginFailed}";
    if("Fail"==simultaneosCookie) {
        var loginClicked= localStorage.getItem('loginClicked');
        if(loginClicked != undefined && loginClicked != null && loginClicked == '1' && loginErrorCheck!="true") {
            localStorage.setItem('loginClicked','0');
            $(window).load(function () {
                $("#loginLimitModal").modal("show");
            });
            //alert("Log-in limits exceeded. Please try after sometime.");
        }
    }

    <%if("true".equals(params.loginFailed)){%>
    $( ".evidyaLogin" ).toggle();
    $( ".browseMenu" ).hide();
    $( "#loginFailedEvidya" ).show();

    <%}%>

    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";
    function getTopicsMap(mode){
        console.log("coming here and what is happening");
        <g:remoteFunction controller="funlearn" action="topicsMap"  onSuccess='initializeDataIndex(data);'
                params="'country='+country+'&mode='+mode" />
    }
    <%if("true".equals(showDiscover)||"true".equals(homeDiscover)){%>
    getTopicsMap('topic');
    <%}%>





    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }



    // checkNewMessages();
</script>


<script>
    function openSignup(){
        $('.evidyaLogin').show();
        createAccount();
    }
</script>
<script>
    function evidyaloginOpen() {
        $('#loginBenifits,#popupLogin').modal('hide');
        console.log("calling this");
        $('.evidyaLogin').show();
        console.log("but not working");
    }
    function evidyabackLogin() {
        $('.forgotPassword,#reset-password-completed,#reset-google-paswd').hide();
        $('.evidyaloginWrapper').show();
    }
</script>
<script>
    $(document).click(function(){
        $(".browseMenu,.evidyaLogin").hide();
        $('body').removeClass('profile-popup-open');

    });


    $(".browseMenu,.evidyaLogin,.browse,#evidyaLogin, #evidyaLogin1, #createProfile, #helpForgotPassword, .cmn-login,.logOfflibrary,input").click(function(e){
        e.stopPropagation();
        //$('body').removeClass('profile-popup-open');
    });

    $( "#createProfile" ).click(function() {
        document.getElementById('loginFailedEvidya').style.display = 'none';
        $( ".evidyaLogin" ).toggle();
        $( ".browseMenu" ).hide();
        $('.forgotPassword,#reset-password-completed,#reset-google-paswd,.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
        $('body').addClass('profile-popup-open');
    });

    $( "#helpForgotPassword" ).click(function() {
        document.getElementById('loginFailedEvidya').style.display = 'none';
        $( ".evidyaLogin" ).toggle();
        $( ".browseMenu" ).hide();
        $('.evidyaloginWrapper,#reset-password-completed,#reset-google-paswd,.evidyaSignup').hide();
        $('.forgotPassword').show();
        $('body').addClass('profile-popup-open');
    });

</script>
<%if("true".equals(session["successModal"])){
    session["successModal"] = null
%>
<script>
    $('#signInSuccess').modal('show');
</script>
<%}%>

<script>
    $( document ).ready( function () {
        $( '.dropdown-menu a.dropdown-toggle' ).on( 'click', function ( e ) {
            var $el = $( this );
            var $parent = $( this ).offsetParent( ".dropdown-menu" );
            if ( !$( this ).next().hasClass( 'show' ) ) {
                $( this ).parents( '.dropdown-menu' ).first().find( '.show' ).removeClass( "show" );
            }
            var $subMenu = $( this ).next( ".dropdown-menu" );
            $subMenu.toggleClass( 'show' );
            $( this ).parent( "li" ).toggleClass( 'show' );
            $( this ).parents( 'li.nav-item.dropdown.show' ).on( 'hidden.bs.dropdown', function ( e ) {
                $( '.dropdown-menu .show' ).removeClass( "show" );
            } );
            if ( !$parent.parent().hasClass( 'navbar-nav' ) ) {
                $el.next().css( { "top": $el[0].offsetTop, "left": $parent.outerWidth() - 4 } );
            }
            return false;
        } );
    } );


    $('#toggle').on('click', function(){
        $(this).toggleClass('open');
        $('body').toggleClass('active');
        $('body').removeClass('profile-popup-open');
    });
</script>

