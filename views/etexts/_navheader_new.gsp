<%@ page import="com.wonderslate.institute.InstituteIpAddress; com.wonderslate.data.UtilService; com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"SAGE | TEXTS"%></title>
    <meta name="description" content="SAGE | TEXTS">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'etexts/favicon.ico')}" type="image/x-icon">

    <meta name="theme-color" content="#2EBAC6" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    %{--<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0-rc.2/css/materialize.min.css">--}%
    <asset:stylesheet href="landingpage/bootstrap.min.css"/>
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Montserrat&display=swap">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp&display=swap" async>
    <asset:stylesheet href="landingpage/sageEvidya.css"/>
    %{--<asset:stylesheet href="landingpage/sageStyleEvidya.css" async="true"/>
    <asset:stylesheet href="landingpage/sageStyle.css" async="true"/>--}%
    <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
    <asset:stylesheet href="landingpage/sageEtexts.css"/>
    <script src="https://code.jquery.com/jquery-3.6.0.slim.min.js" integrity="sha256-u7e5khyithlIdTpu22PHhENmPcRdFiHRjhAuHcs05RI=" crossorigin="anonymous"></script>
    <style>
    .btco-hover-menu .show > .dropdown-toggle::after{
        transform: rotate(-90deg);
    }
    .btco-hover-menu ul li {
        position:relative;
        border-right: 1px solid gray;
    }
    .btco-hover-menu ul li:last-child {
        border-right: none;
    }
    .btco-hover-menu ul li:nth-last-child(3) {
        border-right: none;
    }
    .btco-hover-menu ul ul li {
        position:relative;
    }
    .btco-hover-menu ul ul li:hover> ul {
        display:block;
    }
    .btco-hover-menu ul ul ul {
        position:absolute;
        top:0;
        left:-100% !important;
        min-width:220px;
        display:none;
    }
    .btco-hover-menu ul li.login_button {
        border-right: none;
    }

    #gRecaptchaSignup {
        transform:scale(0.77);
        -webkit-transform:scale(0.77);
        transform-origin:0 0;
        -webkit-transform-origin:0 0;
        margin-bottom: -1rem;
    }
    .invalid-captcha p {
        color: #B72319;
        font-size: 12px;
        margin: 0;
    }
    .captchaError {
        margin-top: 0.75rem;
    }
    .captchaError .alert {
        font-size: 13px;
    }
    .captchaError .alert span {
        width: 22px;
        height: 22px;
        background: #d9534f;
        padding: 2px;
        vertical-align: middle;
        text-align: center;
        border-radius: 30px;
        color: #eee;
        font-weight: 500;
    }
    #signupDummy{
        width: 100%;
        background: #e18b1e;
        color: #ffffff;
    }
    </style>

</head>
<%session['siteId'] = new Integer(23);%>


<body class="eutkarsh evidya etexts" data-spy="scroll" data-target=".ws-header" data-offset="50">

<sec:ifNotLoggedIn>
%{--<g:render template="/funlearn/signupNew"></g:render>--}%
</sec:ifNotLoggedIn>

<header class="sageEvidya">
    <%if(showHeader==null||"true".equals(showHeader)){%>
    <div class="ws-menu-start py-3 px-4">
        <nav class="ws-header navbar navbar-expand-md navbar-default btco-hover-menu">
            <a href="/etexts/index"><span class="logo"><img  src="${assetPath(src: 'etexts/logo.png')}" alt="SAGE TEXTS"></span></a>
            <div class="d-none mobile-profile justify-content-center">
                <%if(session["userdetails"]==null){%>
                <li class="nav-item">
                    <a class="nav-link login" id="evidyaLogin1">LOGIN & SIGN UP</a>
                </li>
                <%}else{%>

                <li class="nav-item">
                    <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                        <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">
                        <%}%>
                    </a>
                    <div class="dropdown-menu dropdown-menu-right">
                        <div class="media p-3">
                            <a href="/creation/userProfile">
                                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                <%}%>
                                <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                            </a>
                            <div class="media-body">
                                <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                            </div>
                        </div>
                        <a class="dropdown-item order-pr d-none" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                        %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                        <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                    </div>
                </li>
                <%}%>
                <li class="nav-item right">
                    <div id="toggle" class="right">
                        <span></span><span></span><span></span><span></span>
                    </div>
                    <div id="menu">
                        <ul id="menu-left-menu" class="menu">
                            <li><a href="/etexts/store">Browse</a></li>

                            <% if(showLibrary){%>
                            <li><a href="/etexts/library">Library</a></li>
                            <%}%>
                            <li><a href="/etexts/packages">Packages</a></li>
                        </ul>
                    </div>
                </li>
            </div>
            %{--<a class="mobile-toggle d-md-none" onclick="openNav()"><i class="material-icons">menu</i></a>--}%
            %{--<ul class="navbar-nav mr-auto d-none d-md-flex">
                <li class="nav-item">
                    <a class="nav-link estore" href="/evidya/store">Store</a>
                </li>

                <% if(session['userdetails']!=null||(showLibrary)){%>
                <li class="nav-item">
                    <a class="nav-link estore" href="/evidya/library">Library</a>
                </li>
                <%}%>
                <sec:ifLoggedIn>
                    <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                                Publishing
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                                <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tabs</a>
                                <a class="dropdown-item" href="/wonderpublish/manageExams">Manage Exams</a>
                                --}%%{--<a class="dropdown-item" href="/institute/isbnKeyword">Manage Isbn/Keywords</a>--}%%{--
                                --}%%{--                     <a href="/institute/isbnKeyword">Manage Isbn/Keywords</a>--}%%{--


                            </div>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_DIGITAL_MARKETER">
                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="" data-toggle="dropdown">
                                Digital Marketing
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/header/index">Header Management</a>
                                <a class="dropdown-item" href="/wonderpublish/pubDesk?mode=print">Print books</a>
                            </div>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_FINANCE">
                        <li class="nav-item">
                            <a href="/publishing-sales" class="nav-link">Sales</a>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_MASTER_LIBRARY_ADMIN,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN">

                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                                Admin
                            </a>
                            <div class="dropdown-menu">
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                                    <a class="dropdown-item" href="/institute/admin">Institute Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/libAdmin">Institute/Library Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/userManagement">User Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                                    <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
                                </sec:ifAllGranted>
                            </div>
                        </li>
                    </sec:ifAnyGranted>
                    <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">

                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="ad1" data-toggle="dropdown">
                                Report
                            </a>
                            <ul class="dropdown-menu">

                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <li><a class="dropdown-item dropdown-toggle">Usage Report</a>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="/institute/usageReport">Institute Report</a></li>
                                            <li><a class="dropdown-item" href="/institute/registeredUserReport?report=usagebookview">Books Report</a></li>
                                        </ul>
                                    </li>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <li><a class="dropdown-item" href="/institute/registeredUserReport?report=login">Users Report</a></li>
                                </sec:ifAllGranted>

                            </ul>
                        </li>
                    </sec:ifAnyGranted>

                </sec:ifLoggedIn>

            </ul>--}%
            <ul class="navbar-nav right-menu d-flex d-md-flex align-items-center mt-2 mt-md-0 ml-auto">
                %{--<li class="nav-item">--}%
                %{--<a class="nav-link" href="https://play.google.com/store/apps/details?id=com.utkarshnew.android" target="_blank">DOWNLOAD APP</a>--}%
                %{--</li>
                <li class="nav-item">
                    <a class="nav-link">Wishlist</a>
                </li>--}%
                %{--<li class="nav-item browse">
                    <a class="nav-link" href="#">Browse</a>
                    <div class="browseMenu">
                        <div class="row">
                            <div class="col-6">
                                <h4>Browse by Subject</h4>

                                <div class="mt-3 d-flex align-items-center subHeader">
                                    <div class="circle1">
                                        <img src="${assetPath(src: 'sage/Management_Icon.svg')}" class="img-responsive" alt="">
                                    </div>
                                    <h4><a href="/evidya/store?grade=Business&homepage=filter"> Management </a></h4>

                                </div>

                                <div class="mt-3 d-flex align-items-center subHeader">
                                    <div class="circle1">
                                        <img src="${assetPath(src: 'sage/Social_Sciences_Icon.svg')}" class="img-responsive" alt="">
                                    </div>
                                    <h4> <a href="/evidya/store?grade=Social Science&homepage=filter">Social Science</a> </h4>

                                </div>
                                <div class="mt-3 d-flex align-items-center subHeader">
                                    <div class="circle1">
                                        <img src="${assetPath(src: 'sage/Non-fiction.svg')}" class="img-responsive" alt="">
                                    </div>
                                    <h4> <a href="/evidya/store?grade=Non-Fiction&homepage=filter">Non-Fiction</a> </h4>

                                </div>
                            </div>
                            <div class="col-6">
                                <h4>Browse by Language</h4>
                                <ul class="language">
                                    <li><a href="/evidya/store?language=English&homepage=filter">English</a></li>
                                    <li><a href="/evidya/store?language=Hindi&homepage=filter">Hindi</a></li>
                                    <li><a href="/evidya/store?language=Marathi&homepage=filter">Marathi</a></li>
                                    <li><a href="/evidya/store?language=Bengali&homepage=filter">Bengali</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </li>--}%

                <li class="nav-item">
                    <a class="nav-link estore" href="/etexts/store">Browse</a>
                </li>

            <% if(showLibrary){%>
                <li class="nav-item">
                    <a class="nav-link estore" href="/etexts/library">Library</a>
                </li>
                <%}%>
                <li class="nav-item">
                    <a class="nav-link" href="/etexts/packages">Packages</a>
                </li>
                <sec:ifLoggedIn>
                    <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                                Publishing
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                                <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tabs</a>
                                <a class="dropdown-item" href="/wonderpublish/manageExams">Manage Exams</a>
                                %{--<a class="dropdown-item" href="/institute/isbnKeyword">Manage Isbn/Keywords</a>--}%
                                %{--                     <a href="/institute/isbnKeyword">Manage Isbn/Keywords</a>--}%


                            </div>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_DIGITAL_MARKETER">
                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="" data-toggle="dropdown">
                                Digital Marketing
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/header/index">Header Management</a>
                                <a class="dropdown-item" href="/wonderpublish/pubDesk?mode=print">Print books</a>
                            </div>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAllGranted roles="ROLE_FINANCE">
                        <li class="nav-item">
                            <a href="/publishing-sales" class="nav-link">Sales</a>
                        </li>
                    </sec:ifAllGranted>
                    <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_MASTER_LIBRARY_ADMIN,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN">

                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                                Admin
                            </a>
                            <div class="dropdown-menu">
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                                    <a class="dropdown-item" href="/institute/admin">Institute Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/libAdmin">Institute/Library Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/userManagement">User Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                                    <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
                                </sec:ifAllGranted>
                            </div>
                        </li>
                    </sec:ifAnyGranted>
                    <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">

                        <li class="nav-item dropdown active">
                            <a class="nav-link dropdown-toggle" href="#" id="ad1" data-toggle="dropdown">
                                Report
                            </a>
                            <ul class="dropdown-menu">

                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <li class="border-0"><a class="dropdown-item dropdown-toggle">Usage Report</a>
                                        <ul class="dropdown-menu">
                                            <li class="border-0"><a class="dropdown-item border-bottom" href="/institute/usageReport">Institute Report</a></li>
                                            <li><a class="dropdown-item" href="/institute/registeredUserReport?report=usagebookview">Books Report</a></li>
                                            <li><a class="dropdown-item" href="/institute/downloadUsageReport">Books Download Report</a></li>
                                        </ul>
                                    </li>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <li><a class="dropdown-item" href="/institute/registeredUserReport?report=login">Users Report</a></li>
                                </sec:ifAllGranted>

                            </ul>
                        </li>
                    </sec:ifAnyGranted>

                </sec:ifLoggedIn>

                <sec:ifNotLoggedIn>

                    <li class="nav-item login_button">
                        <a class="nav-link login-btn py-2" id="evidyaLogin">Login</a>
                    </li>
                    <div class="evidyaLogin">
                        <div class="evidyaloginWrapper" >
                            <p>Log-in to access your personalized dashboard for additional features. </p>
                            <form method="post" action="/login/authenticate" name="signin">
                                <input type="hidden" name="username">
                                <label>Email</label>
                                <input type="text" class="form-control" id="email" name="username_temp" required>
                                <label class="mt-2">Password</label>
                                <input class="form-control" type="password" id="password" name="password" required>
                                <a href="javascript:showForgetPassword();" class="mt-4 frgtPassword">Forgot Password?</a>

                                <input type="button" id="sign-in" class="btn btn-login" value="Log In" onclick="submitSignIn()">
                            </form>
                            <div id="loginFailedEvidya" style="display:none; color: #F05A2A; margin-top: 15px;">Login failed. Please try again!</div>
                            <button class="btn btn-create" onclick="javascript:createAccount();">Create a new account</button>
                            %{--<button type="button" class="googleLogin sign-google" id="googleSignInButton"  onclick="googleSignInCalled();">Login with Google</button>--}%


                        </div>
                        <div class="evidyaSignup" style="display: none;">
                            <h3 class="text-center">Sign Up </h3>
                            <g:form name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
                                <label>Name</label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <label class="mt-2">Phone Number</label>
                                <input class="form-control" type="text" name="mobile" id="mobile" required maxlength="10" minlength="10" oninput="numberOnly(this.id);">
                                <div class="input-error-tooltip" style="display: none;">
                                    <div class="input-error-tooltip-inner">Please enter 10 digit mobile number.</div>
                                </div>
                                <label class="mt-2">Email</label>
                                <input class="form-control" type="email" id="username" name="username" required>
                                <div class="email-error" id="emailexists" style="display: none;">
                                    <p class="email-error-text">This email is already taken. Please sign in.</p>
                                </div>
                                <div class="email-error" id="loginerror" style="display: none;">
                                    <p class="email-error-text"></p>
                                </div>
                                <label class="mt-2">Password</label>
                                <input class="form-control" type="password" id="signup-password" name="password" required>
                                <div class="email-error" id="passworderror" style="display: none;">
                                    <p class="email-error-text"></p>
                                </div>
                                <label class="mt-2">State</label>
                                <select class="form-control" name="state" id="state" required>
                                    <option value="">Select</option>
                                    <option value="Andaman and Nicobar Islands">Andaman and Nicobar Islands</option>
                                    <option value="Andhra Pradesh">Andhra Pradesh</option>
                                    <option value="Arunachal Pradesh">Arunachal Pradesh</option>
                                    <option value="Assam">Assam</option>
                                    <option value="Bihar">Bihar</option>
                                    <option value="Chandigarh">Chandigarh</option>
                                    <option value="Chhattisgarh">Chhattisgarh</option>
                                    <option value="Dadra and Nagar Haveli">Dadra and Nagar Haveli</option>
                                    <option value="Goa">Goa</option>
                                    <option value="Gujarat">Gujarat</option>
                                    <option value="Haryana">Haryana</option>
                                    <option value="Himachal">Himachal Pradesh</option>
                                    <option value="Jammu & Kashmir">Jammu & Kashmir</option>
                                    <option value="Jharkhand">Jharkhand</option>
                                    <option value="Karnataka">Karnataka</option>
                                    <option value="Kerala">Kerala</option>
                                    <option value="Lakshadweep">Lakshadweep</option>
                                    <option value="Madhya Pradesh">Madhya Pradesh</option>
                                    <option value="Maharashtra">Maharashtra</option>
                                    <option value="Manipur">Manipur</option>
                                    <option value="Meghalaya">Meghalaya</option>
                                    <option value="Mizoram">Mizoram</option>
                                    <option value="Nagaland">Nagaland</option>
                                    <option value="Odisha">Odisha</option>
                                    <option value="Puducherry">Puducherry</option>
                                    <option value="Punjab">Punjab</option>
                                    <option value="Rajasthan">Rajasthan</option>
                                    <option value="Sikkim">Sikkim</option>
                                    <option value="Tamil Nadu">Tamil Nadu</option>
                                    <option value="Telangana">Telangana</option>
                                    <option value="The Government of NCT of Delhi">The Government of NCT of Delhi</option>
                                    <option value="Tripura">Tripura</option>
                                    <option value="Uttarakhand">Uttarakhand</option>
                                    <option value="Uttar Pradesh">Uttar Pradesh</option>
                                    <option value="West Bengal">West Bengal</option>
                                </select>
                                <div class="mt-3 d-flex align-items-center justify-content-around">
                                    <input class="" type="checkbox" id="termsCondition" name="termsCondition" required>
                                    <label for="termsCondition" class="mb-0">I have read and agree to the  <a class="mt-2" href="/etexts/termsCondition" target="_blank">Terms of Service</a></label>
                                </div>
                                <div class="tcEtext-error" id="v" style="display: none;">
                                    <p class="tcEtext-error-text"></p>
                                </div>
                                <div id="gRecaptchaSignup" class="mt-3"></div>
                                <div class="invalid-captcha" style="display: none;"><p>Please verify that you are not a robot.</p></div>
                                <input type="hidden" name="email">
                                <input type="hidden" name="otp_finished" id="otp_finished">
                                <div id="continueButtonWrapper"></div>
                                <input type="button" id="signupDummy" class="btn btn-lg continue mt-3 signupDummy" value="Continue" disabled>
                                <a onclick="javascript:loginContinue();" class="mt-3 loginContinue text-center" style="font-size: 15px;">Already have an Account?Click here to login</a>
                            </g:form>
                        </div>
                        <div class="forgotPassword" style="display: none;">
                            <h3>Reset Password</h3>
                            <p class="mt-4">To recieve a link to reset your password, please enter your account email address.</p>
                            <g:form name="forgotpassword" class="form-horizontal mt-4" method="post" autocomplete="off">
                                <label class="mt-2">Email</label>
                                <input class="form-control"  type="text" name="username" id="fPemail" required>
                                <span id="showEmailerror" style="color:red;
                                display: block;
                                margin: 7px;"></span>
                                <input type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="btn btn-lg reset mt-2" value="Send reset link">
                                <a id="back-login" class="btn btn-back mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </g:form>
                        </div>
                        <div id="reset-password-completed" style="display: none;">
                            <div class="text-center">
                                <p>Password reset link sent.</p>
                                <p>We’ve sent instructions on how to reset your password to <span id="fp-user-email"></span>. If you haven’t received an email from e-Text within a couple of minutes, please check your spam folder.</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </div>
                        </div>
                        <div id="reset-google-paswd" style="display: none">
                            <div class="text-center">
                                <p>Password reset not possible!</p>
                                <p>We see that you have registered using Google, so it will not possible to change the password for your account (<span id="fp-user-email1"></span>) with us. <br><br>Kindly continue to login using Google</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackLogin();">Back to Login</a>
                            </div>
                        </div>
                        <div id="account-exists" style="display: none">
                            <div class="text-center" style="margin-top: 6rem;">
                                <p class="notRegister">This Email is not registered.<br> Please signup!</p>
                                <a id="back-login" class="btn mt-4" onclick="javascript:evidyabackSingup();">Back to Signup</a>
                            </div>
                        </div>
                    </div>

                </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <li class="nav-item notification">
                        <a class="nav-link" onclick=""><i class="material-icons">notifications</i></a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link p-0" class="dropdown-toggle" data-toggle="dropdown">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle">
                            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">
                            <%}%>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <div class="media p-3">
                                <a href="/creation/userProfile">
                                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                    <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                    <%}%>
                                    <span class="edit-btn"><i class="material-icons">edit</i></span>
                                </a>
                                <div class="media-body">
                                    <p class="user-name">Hello <span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span>,</p>
                                    <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                                </div>
                            </div>
                            <a class="dropdown-item order-pr d-none" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                            %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                            <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                        </div>
                    </li>
                </sec:ifLoggedIn>
            </ul>
        </nav>

    </div>
    <%}%>
</header>



<% if (!hideBottomIcons) { %>
<ul class="mobile-bottom-menu-wrapper hidden-md hidden-sm hidden-lg">
    <sec:ifNotLoggedIn>
        <li class="mobile-bottom-menu-item">
            <a href="" class="estore mobile-bottom-menu-item-link mobile-store">Store</a>
            Store
        </li>
        <li class="mobile-bottom-menu-item">
            <a href="javascript:showSignUpModal();" class="mobile-bottom-menu-item-link mobile-library">Library</a>
            Library
        </li>

    </sec:ifNotLoggedIn>

    <sec:ifLoggedIn>
        <li class="mobile-bottom-menu-item">
            <a href="" class="mobile-bottom-menu-item-link mobile-store">Store</a>
            Store
        </li>
        <li class="mobile-bottom-menu-item">
            <a href="/etexts/library" class="mobile-bottom-menu-item-link mobile-library">Library</a>
            Library
        </li>

    </sec:ifLoggedIn>
</ul>
<% } %>
<div class="modal" id="signInSuccess">
    <div class="modal-dialog  modal-sm modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">


            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <p class="text-center" style="color:#233982;font-size: 16px;">Signed in Successfully</p>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer justify-content-center">
                %{--                <button type="button" class="btn btn-danger" data-dismiss="modal">Continue</button>--}%
            </div>

        </div>
    </div>
</div>



<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";


    function createAccount(){
        $('.evidyaloginWrapper').hide();
        $('.evidyaSignup').show();
    }
    function loginContinue() {
        $('.evidyaSignup').hide();
        $('.evidyaloginWrapper').show();
    }
</script>

<script src="https://apis.google.com/js/api:client.js"></script>
<script src="https://www.google.com/recaptcha/api.js?onload=recaptchaCallback&render=explicit" async defer></script>
<script>
    var otpReg = "${otpReg}";

    var signUpId;
    var flds = new Array (
        'name',
        'username',
        'signup-password',
        'mobile',
        'state'
    );

    var flds1 = new Array (
        'name',
        'username',
        'signup-password'
    );

    function userSignIn() {
        if($('#email').val()=="" || $('#password').val()=="") {
            $('#loginSignup').modal('show');
            $('#signInError').show();
        } else {
            $('#sign-in-div').hide();
            $('#connecting-div').show();
        }
    }

    function validateEmail(email)
    {
        var re = /\S+@\S+\.\S+/;
        return re.test(email);
    }


    function validateUpdateSignUp()
    {

        var allFilled=true;
        document.getElementById('emailexists').style.display = 'none';
        document.getElementById('passworderror').style.display = 'none';
        document.getElementById('loginerror').style.display = 'none';

        for (i=0; i<flds1.length; i++) {
            if( !$("#"+flds1[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds1[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds1[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                // $("#"+flds[i]).css('border', 'none');

            }
        }

        if($("div#gRecaptchaSignup").length > 0) {
            if (grecaptcha.getResponse(signUpId) == "") {
                $(".invalid-captcha").css('display', 'block');
                allFilled = false;
            }
        } else {
            $(".invalid-captcha").css('display', 'none');
            if ($('.evidyaSignup .captchaError').length > 0) {
                document.querySelector(".evidyaSignup .captchaError").innerHTML = "<div class='alert alert-danger p-2 border-0 m-0 d-flex align-items-center'>" +
                    "<span class='mr-2'>X</span>" +
                    "Something went wrong with reCAPTCHA." +
                    "</div>";
            } else {
                var error_div = document.createElement("div");
                error_div.className = 'captchaError';
                error_div.innerHTML = "<div class='alert alert-danger p-2 border-0 m-0 d-flex align-items-center'>" +
                    "<span class='mr-2'>X</span>" +
                    "Something went wrong with reCAPTCHA." +
                    "</div>";
                var append_div = document.querySelector(".evidyaSignup #adduser #state");
                append_div.parentNode.insertBefore(error_div, append_div.nextSibling);
            }
            allFilled = false;
        }

        if(!allFilled) {
            $('.alert').show();
        } else {
            var email = $("#username").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                $("#username").css('border', '1px solid rgba(240, 90, 40, 0.5)');
                return false;
            }else{
                $("#username").css('border', '1px solid #ced4da');
            }
        }
        return allFilled;
    }

    function formSubmit() {
        if(validateUpdateSignUp() && updatesignup){
            // method=updateUser
            // controller = institute
            if(!document.getElementById('signup-password').value){
                $("#passworderror p:first").text("Password cannot be empty.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            if( document.getElementById('signup-password').value.length>64){
                $("#passworderror p:first").text("Password cannot be more than 64 characters.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            if( document.getElementById('signup-password').value.length<8){
                $("#passworderror p:first").text("Password cannot be less than 8 characters.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            if( document.getElementById('signup-password').value.toString().indexOf(document.getElementById('name').value)>-1){
                $("#passworderror p:first").text("Password cannot contain your name.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            var regex = new RegExp("^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$")
            if( !regex.test(document.getElementById('signup-password').value)){
                $("#passworderror p:first").text("Password must have a upper case letter, a lower case letter and a number.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            var regex1 = new RegExp("^(?=.*[!@#$%^&*()_+=\"\"''~`{}|:;?<>.-])")
            if( regex1.test(document.getElementById('signup-password').value)){
                $("#passworderror p:first").text("Password cannot contain any special character.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            // params?.password
            // def mobile = params?.mobile
            // def state = params?.state
            // def name = params?.userName
            // also need userId
            var password=document.getElementById('signup-password').value;
            var mobile=document.getElementById('mobile').value;
            var state=document.getElementById('state').value;
            var name=document.getElementById('name').value;
            var userId=updateUserId;
            var termsCondition=document.getElementById('termsCondition');
            if (termsCondition.checked){
                termsCondition='true';
            }else{
                termsCondition="";
            }
            <g:remoteFunction controller="institute" action="updateUser"  onSuccess='updateLogin(data);' params="'password='+password+'&mobile='+mobile+'&state='+state+'&userName='+name+'&userId='+userId+'&termsCondition='+termsCondition" />

        }
         if (!updatesignup && validateSignUp()) {
            document.adduser.username.value = document.adduser.username.value.toLowerCase();
            document.adduser.email.value = document.adduser.username.value.toLowerCase();
            if(!document.getElementById('signup-password').value){
                $("#passworderror p:first").text("Password cannot be empty.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            if( document.getElementById('signup-password').value.length>64){
                $("#passworderror p:first").text("Password cannot be more than 64 characters.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            if( document.getElementById('signup-password').value.length<8){
                $("#passworderror p:first").text("Password cannot be less than 8 characters.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            if( document.getElementById('signup-password').value.toString().indexOf(document.getElementById('name').value)>-1){
                $("#passworderror p:first").text("Password cannot contain your name.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            var regex = new RegExp("^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$")
            if( !regex.test(document.getElementById('signup-password').value)){
                $("#passworderror p:first").text("Password must have a upper case letter, a lower case letter and a number.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            var regex1 = new RegExp("^(?=.*[!@#$%^&*()_+=\"\"''~`{}|:;?<>.-])")
            if( regex1.test(document.getElementById('signup-password').value)){
                $("#passworderror p:first").text("Password cannot contain any special character.");
                document.getElementById('passworderror').style.display = 'block';
                return
            }
            checkUsernameExists();
        }
    }

    function updateLogin(data){
        if(data.status=="Success")
        {
            document.signin.username.value= "${session["siteId"]}_"+document.getElementById('username').value;
            document.signin.password.value=document.getElementById('signup-password').value
            document.signin.submit();
            localStorage.setItem('loginClicked','1');
        }
        else{
            $("#loginerror p:first").text("This Email ID is already taken. Please sign in");
            document.getElementById('loginerror').style.display = 'block';
        }
    }

    function validateSignUp(){
        var allFilled=true;
        document.getElementById('emailexists').style.display = 'none';
        document.getElementById('loginerror').style.display = 'none';

        for (i=0; i<flds.length; i++) {
            if( !$("#"+flds[i]).val() ) {
                //actual code to check all fields needs to be entered. use the array of fields
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
                allFilled = false;
            } else {
                // $("#"+flds[i]).css('border', 'none');

            }
        }

        if(!validateEmail(htmlDecode(document.getElementById('username').value))) {
            $("#loginerror p:first").text("Please enter valid email");
            document.getElementById('loginerror').style.display = 'block';
            $("#username").css('border', '1px solid rgba(240, 90, 40, 0.5)');
            allFilled = false;
        }

        if($("div#gRecaptchaSignup").length > 0) {
            if (grecaptcha.getResponse(signUpId) == "") {
                $(".invalid-captcha").css('display', 'block');
                allFilled = false;
            }
        } else {
            $(".invalid-captcha").css('display', 'none');
            if ($('.evidyaSignup .captchaError').length > 0) {
                document.querySelector(".evidyaSignup .captchaError").innerHTML = "<div class='alert alert-danger p-2 border-0 m-0 d-flex align-items-center'>" +
                    "<span class='mr-2'>X</span>" +
                    "Something went wrong with reCAPTCHA." +
                    "</div>";
            } else {
                var error_div = document.createElement("div");
                error_div.className = 'captchaError';
                error_div.innerHTML = "<div class='alert alert-danger p-2 border-0 m-0 d-flex align-items-center'>" +
                    "<span class='mr-2'>X</span>" +
                    "Something went wrong with reCAPTCHA." +
                    "</div>";
                var append_div = document.querySelector(".evidyaSignup #adduser #state");
                append_div.parentNode.insertBefore(error_div, append_div.nextSibling);
            }
            allFilled = false;
        }

        if(!allFilled) {
            $('.alert').show();
        } else {
            var email = $("#username").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
                $("#username").css('border', '1px solid rgba(240, 90, 40, 0.5)');
                return false;
            }else{
                $("#username").css('border', '1px solid #ced4da');
            }
        }

        return allFilled;
    }

    function checkUsernameExists() {
        var username = $("#username").val();
        <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
    }

    function userNameExistsResult(data) {
        if(data=="0") {
            $('#connecting-div').show();

            if(otpReg=="true") {
                $('#loginSignup').modal('hide');
                $('#otp-mobile').val($('#mobile').val());
                $('#otp-email-txt').text($('#username').val());
                $('#otp-email-txt1').text($('#username').val());
                $('#otp-mobile1').text($('#otp-mobile').val());
                $('#otp-next').modal('show');
            } else {
                document.adduser.submit();
            }

            $('#sign-up-div').hide();
        } else {
            $('#loginSignup').modal('show');
            document.getElementById('emailexists').style.display = 'block';
        }
    }

    function formFPSubmit() {
        $("#emailidnf").hide();

        if( !$("#fPemail").val() ) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {
            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {

                if(siteId==23){
                    document.getElementById('showEmailerror').innerHTML='Please enter valid Email address';
                }
                else {
                    return false;
                }
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if("OK"==data.status) {
            // $('#loginSignup').modal('show');
            // $('#loginSignup').attr('data-page', 'reset-completed');
            $('.forgotPassword').hide();
            $('#reset-password-completed').show();
            $('#fp-user-email').html("“"+userEmail+"�?");
        } else if("Google"==data.status) {
            $('.forgotPassword').hide();
            $('#reset-google-paswd').hide();
            $('#fp-user-email1').html("“"+userEmail+"�?");
        }
        else if("Fail"==data.status){
            $('.forgotPassword').hide();
            $('#account-exists').show();

        }
    }

    <%  if(params.loginFailed=="true"&&!"23".equals(""+session["siteId"])) {%>
    $(window).on('load',function() {
        $('#loginSignup').modal('show');
    });
    <%  } %>
    function showForgetPassword() {
        $('.evidyaloginWrapper').hide();
        $('.forgotPassword').show();
    }
    function evidyabackSingup(){
        $('#account-exists').hide();
        $('.evidyaSignup').show();

    }

    function logoutClicked() {
        document.cookie = '';
        document.cookie = 'currentUrl' + "=" + window.location.href + "; path=/";
        window.location.href ="/logoff";
    }


    function submitSignIn(){
        document.signin.username.value= "${session["siteId"]}_"+document.signin.username_temp.value;
        document.signin.submit();
        localStorage.setItem('loginClicked','1');
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    var recaptchaCallback = function() {
        <sec:ifNotLoggedIn>
            signUpId = grecaptcha.render('gRecaptchaSignup', {'sitekey' : '6LcL_ooaAAAAAJH2vf_Tt-FtUDGFTzVJMEQbC3IG'});
        </sec:ifNotLoggedIn>
    };
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };
    $('#termsCondition').on('change', function() {
        var checked = this.checked
        if (checked){
            $("#continueButtonWrapper").html("<input type='button' id='signup' onclick='javascript:formSubmit();' class='btn btn-lg continue mt-3' value='Continue'>")
            $("#signupDummy").hide();
        }else{
            $("#continueButtonWrapper").html("")
            $("#signupDummy").show().attr("disabled","disabled");
        }
    });
</script>

