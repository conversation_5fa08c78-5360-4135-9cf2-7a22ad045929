<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<section class="page-main-wrapper mdl-js pb-5 pt-0 nextexam_section">
    <div class="container store1_topbanner pt-4 pt-md-5 mt-3 mt-md-0">
        <div class="row page_title d-flex pb-3 pb-lg-4 px-3 mb-0 align-items-center">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <h3 class="mr-2"><strong>Monthly Quiz</strong></h3>
            <h4><small>(100 questions each)</small></h4>
        </div>
        <p>Take monthly quiz and keep your current affairs knowledge upto date.</p>
        <p>** New questions are created every time you take the test. More number times you take test, better would be your practice</p>

    </div>

    <div class="store1_index store1_index_accordion mt-3">
        <div class="container px-3">
            <div class="accordion">
                <div class="card border-0 mb-3 bg-transparent">
                    <div id="collapse0" class="collapse show" aria-labelledby="heading0">
                        <div class="card-body row justify-content-start align-items-center mx-0 px-0" id="card0">
                        </div>
                        <span class='show-all'>Show all..</span>
                        <span class='show-less'>Show less..</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
%{--//model window for questions--}%

    <div class="modal modal-modifier fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLongTitle" aria-hidden="true">
        <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom" role="document">
            <div class="modal-content modal-content-modifier">

                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">x</span>
                </button>

                <div class="modal-body modal-body-modifier text-center">
                    <h5 class="mt-3">Select number of questions</h5>
                    <div class="row">
                        <div class="col-12 parent">
                            <div class="btn-wrapper mt-4">
                                    <a class="select-number" >10</a>
                                    <a class="select-number" >25</a>
                                    <a class="select-number" >50</a>
                                    <a class="select-number" >100</a>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-end col-12 py-3 mt-3">
                        <button type="button" class="btn btn-lg btn-outline-secondary btn-outline-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col-4 col-md-3" data-dismiss="modal" aria-label="Close">Cancel</button>
                    </div>

                </div>

            </div>
        </div>
    </div>

</section>

<div class="books-list">
    <g:render template="/resources/relatedBooks"></g:render>
</div>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<g:render template="/books/footer_new"></g:render>
<script>

    function getMonthlyBooks(){
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="testgenerator" action="nextExamMonthlyQuiz"  onSuccess='displayMonthlyBooks(data);'/>
    }

    function displayMonthlyBooks(data){
        var allBooks =  JSON.parse(data.nextExamBooks);
        var htmlStr = "";
        for(var i=0;i<allBooks.length;i++){
            htmlStr += "<div class='col-6 col-md-3 col-lg-2 px-2 mb-3 name_of_chapter'>" +
                "<a onclick=limitBooks("+allBooks[i].bookId+",100) >" + allBooks[i].month+" "+allBooks[i].year + "</a>" +
                "</div>";
        }
        document.getElementById("card0").innerHTML=htmlStr;
        $('.loading-icon').addClass('hidden');

        var list = $('#card0 .name_of_chapter').size();

        if($(window).width() >= 992) {
            if(list > 18){
                $('.name_of_chapter').hide();
                $('#card0').find('.name_of_chapter:lt(18)').show();
                $('.show-less').hide();
                $('.show-all').click(function(ev) {
                    $(ev.currentTarget).parent().find('.name_of_chapter').show();
                    $('.show-less').show();
                    $(this).hide();
                });
                $('.show-less').click(function(ev) {
                    $(ev.currentTarget).parent().find('.name_of_chapter').not(':lt(18)').hide();
                    $('.show-all').show();
                    $(this).hide();
                    $('html, body').animate({scrollTop:0}, 'slow');
                });
            } else {
                $('.show-all, .show-less').hide();
            }
        } else {
            if(list > 12){
                $('.name_of_chapter').hide();
                $('#card0').find('.name_of_chapter:lt(12)').show();
                $('.show-less').hide();
                $('.show-all').click(function(ev) {
                    $(ev.currentTarget).parent().find('.name_of_chapter').show();
                    $('.show-less').show();
                    $(this).hide();
                });
                $('.show-less').click(function(ev) {
                    $(ev.currentTarget).parent().find('.name_of_chapter').not(':lt(12)').hide();
                    $('.show-all').show();
                    $(this).hide();
                    $('html, body').animate({scrollTop:0}, 'slow');
                });
            } else {
                $('.show-all, .show-less').hide();
            }
        }
    }
    // function to get bookid and noOfQues and get selected number of question
 function limitBooks(bookId , noOfQues){
    $('#myModal').modal('show');
     $(".btn-wrapper a").on("click",function(){
         var getinnerText = $(this).text();
         window.location.href="/funlearn/quiz?mode=bookTest&bookId="+bookId+"&noOfQuestions="+getinnerText;
     })
 }
   getMonthlyBooks();
</script>

</body>
</html>