<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.css" integrity="sha512-wR4oNhLBHf7smjy0K4oqzdWumd+r5/+6QO/vDda76MW5iug4PT7v86FoEkySIJft3XA0Ae6axhIvHrqwm793Nw==" crossorigin="anonymous" referrerpolicy="no-referrer" />
<link href="https://fonts.cdnfonts.com/css/righteous" rel="stylesheet">
<asset:stylesheet href="prepJoy/currentAffairsDaily.css" async="true"/>
<title><%= title!=null?title:"Current Affairs - Wonderslate"%></title>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container">
    <div class="button-wrap">
       <a href="javascript:goToPrevious()" class="previous">&#8592; Previous</a>
       <a href="javascript:goToNext()" class="next" >Next &#8594;</a>
    </div>
    <div class="row heading">
        <div class="col-12">
           <p>Current Affairs</p>
        </div>
        <div class="switch toggleSwitch"><span class="public-text">Change Language</span>
            <a class="language" onclick="javascript:changeLanguage();" id="changeLanguage" ><img class="language"  src="${assetPath(src: 'mobile/language.svg')}" alt="PrepJoy"></a>
        </div>

    </div>
    <div class="row first-row mcqRead">
        <div class="col-12">
            <div class="button-wrapper">
                <div class="col-10 first-block">
                    <span>MCQs</span>
                </div>
                <div class="col-2 second-block">
                    <img class="arrow"  src="${assetPath(src: 'prepJoy/slick-arrow.svg')}" alt="PrepJoy">
                </div>
            </div>
        </div>
    </div>

     <div class="slider-parent hindi">
        <p>Watch</p>
         <div class="slider-video">
       </div>
        </div>
    <div class="slider-parent English">
        <p>Watch</p>
        <div class="slider-video">
        </div>
    </div>

    <div class="readingMaterialParent">
        <p>Read</p>
        <p class="telegram-para text-left">PDF available at<a class="tel-link" href="https://t.me/prepjoy" target="blank"><img class="navbar-logo" src="${assetPath(src: 'prepJoy/telegramLoco.svg')}" alt="PrepJoy" /></a></p>
        <div class="d-flex flex-wrap readingMaterialcards">
        </div>
    </div>
    <div class="row first-row mcqRead">
        <div class="col-12">
            <div class="button-wrapper">
                <div class="col-10 first-block">
                    <span>TAKE TEST</span>
                </div>
                <div class="col-2 second-block">
                    <img class="arrow"  src="${assetPath(src: 'prepJoy/slick-arrow.svg')}" alt="PrepJoy">
                </div>
            </div>
        </div>
    </div>


    <div class="modal modal-modifier fade" id="videoModal" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-dialog-modifier modal-lg modal-dialog-centered">
            <div class="modal-content modal-content-modifier">
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close" id="youtubeclose">
                    <span aria-hidden="true">x</span>
                </button>
                <div class="modal-body modal-body-modifier">
                    <iframe width="100%" height="450px" id="vidIf" src="" frameborder="0" allowfullscreen allow="autoplay"></iframe>
                </div>
            </div>
        </div>
    </div>
</div>



<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.js" integrity="sha512-XtmMtDEcNz2j7ekrtHvOVR4iwwaD6o/FUJe6+Zq+HgcCsk3kj4uSQQR8weQ2QVj1o0Pk6PwYLohm206ZzNfubg==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>

<script>
    var localLanguage;
   var mcqRead='';
   var mcqVideo='';
   var mcqVideo1='';
   var mcqsTest='';
   var language;
   var nothingpresent=false;
    var latestdate;
    var startdate;

    var myParam =location.search.split('date=')[1] ? location.search.split('date=')[1] : 'myDefaultValue';
    var urlSeparate=location.search.split('&');
    latestdate = urlSeparate[0].split('=')[1] ? urlSeparate[0].split('=')[1] : 'myDefaultValue';
    startdate = urlSeparate[1].split('=')[1] ? urlSeparate[1].split('=')[1] : 'myDefaultValue';

    var latestdate1 = moment.utc([latestdate],'Do MM YYYY').local().format("Do MMM YYYY");
    var startdate1= moment.utc([startdate],'Do MM YYYY').local().format("Do MMM YYYY");
    var datePassed = decodeURIComponent(myParam);
    var date =moment.utc([datePassed],'Do MMM YYYY').local().format("DD-MM-YYYY");
   var date1 =moment.utc([datePassed],'Do MMM YYYY').local().format("MMM D YYYY");
    function hidenextPrevious() {
        if (startdate1 == datePassed) {
            $('.previous').hide();

        }
        else if(latestdate1 == datePassed)
        {
            $('.next').hide();
        }
    }
    hidenextPrevious();

   function goToPrevious(){
       var nextDay1 = new Date(date1);
       var nextDay = new Date(nextDay1);
       nextDay.setDate(nextDay.getDate()-1);
       var newDate =moment.utc([convert(nextDay)],'Do MM YYYY').local().format("Do MMM YYYY");
       window.location.href="./currentAffairsDaily?latestDate=" +latestdate+"&startingDate="+startdate+ "&date=" +newDate;

       hidenextPrevious();
   }
   function convert(dd) {
       var date = new Date(dd),
           mnth = ("0" + (date.getMonth() + 1)).slice(-2),
           day = ("0" + date.getDate()).slice(-2);
       return [day, mnth, date.getFullYear()].join("-");
   }
   function goToNext(){
       var nextDay1 = new Date(date1);
       var nextDay = new Date(nextDay1);
       nextDay.setDate(nextDay.getDate() + 1);
       var newDate =moment.utc([convert(nextDay)],'Do MM YYYY').local().format("Do MMM YYYY");
       window.location.href="./currentAffairsDaily?latestDate=" +latestdate+"&startingDate="+startdate+ "&date=" +newDate;
       hidenextPrevious();
   }
   var htmlstr=""
    $(window).on('load',function(){
        htmlstr += "<p>" +
            datePassed +
            "</p>"
        $(".col-10.first-block").append(htmlstr);
        $(".row.heading .col-12").append(htmlstr);
        $(" .last-row ").append(htmlstr);


    })
   function testDom() {
       $(".validation-msg").empty();
       if (mcqVideo.length == 0 && mcqRead.length == 0 && mcqsTest == '-1' ) {
           $(".validation-msg").empty();
           $(".container .row.heading").after($("<div class='validation-msg'> <img class='empty-image'  src='${assetPath(src: "ws/boxempty.svg")}' alt='PrepJoy'><h4>There are no current affairs available for the current date </h4><button class='btn-primary' onclick='goBack()'>Back</button></div>"));
       }
       if(nothingpresent==true){
           $(".slick-slider").hide();
           $('.slider-parent').hide();
       }
   }
    function slickInitialize() {
        $('.slider-video').slick({
            centerMode: true,
            centerPadding: '60px',
            slidesToShow: 3,
            responsive: [
                {
                    breakpoint: 768,
                    settings: {
                        arrows: true,
                        centerMode: true,
                        centerPadding: '60px',
                        slidesToShow: 3,
                        cssEase: 'linear',
                    }
                },
                {
                    breakpoint: 480,
                    settings: {
                        arrows: true,
                        centerMode: true,
                        centerPadding: '60px',
                        slidesToShow: 3,
                        cssEase: 'linear',
                    }
                }
            ]
        });
    };

   $(window).on('load',function(){
       $(".loading-icon").removeClass("hidden");
       <g:remoteFunction controller="admin" action="getCurrentAffairsVideos" params="'inputDate='+date" onSuccess='showcurrentAffairsVideo(data);' />
       hidenextPrevious()


   })

       function showcurrentAffairsVideo(data)
       {
           var videos = JSON.parse(data.videos);
           var videos1 = JSON.parse(data.videos);
           var htmlstrr='';
           var htmlstr = '';
           var englishCounter=0
           var hindiCounter=0
           for(var i=0;i<videos.length;i++) {
               var videolinks = videos[i].videoLink;
               var link = videolinks.split("=")[1];
               language = videos[i].tag.language;
               videolinks = videolinks.replace(/watch\?v\=/g, "embed/")
               var videolinks1 = '\"' + videolinks + "?autoplay=1;rel=0" + '\"';
               var imgSrc = "https://i.ytimg.com/vi/" + link + "/mqdefault.jpg";
               if (language == 'Hindi') {
                   htmlstrr += "<div class='slider-card hindi'>" +
                       "<div class='video-card' >" +
                       "        <img width='100%' height='100%'   src='" + imgSrc + "'>" +
                       "<div class='play-parent'>" +
                       " <div class='play' onclick='javascript:loadVideo(" + videolinks1 + ")'>" +
                       " </div>" +
                       "</div>" +
                       "</div>" +
                       "</div>"
                   hindiCounter+=1
               }
                if (language == 'English') {
                   htmlstr += "<div class='slider-card English'>" +
                       "<div class='video-card' id='English'>" +
                       "        <img width='100%' height='100%'   src='" + imgSrc + "'>" +
                       "<div class='play-parent'>" +
                       " <div class='play' onclick='javascript:loadVideo(" + videolinks1 + ")'>" +
                       " </div>" +
                       "</div>" +
                       "</div>" +
                       "</div>"
                    englishCounter+=1
               }
           }
           if(hindiCounter <4 && hindiCounter>0) {
                while(hindiCounter<5) {
                    for (var i = 0; i < videos.length; i++) {
                        var videolinks = videos[i].videoLink;
                        var link = videolinks.split("=")[1];
                        language = videos[i].tag.language;
                        videolinks = videolinks.replace(/watch\?v\=/g, "embed/")
                        var videolinks1 = '\"' + videolinks + "?autoplay=1;rel=0" + '\"';
                        var imgSrc = "https://i.ytimg.com/vi/" + link + "/mqdefault.jpg";
                        if (language == 'Hindi') {
                            htmlstrr += "<div class='slider-card hindi'>" +
                                "<div class='video-card' >" +
                                "        <img width='100%' height='100%'   src='" + imgSrc + "'>" +
                                "<div class='play-parent'>" +
                                " <div class='play' onclick='javascript:loadVideo(" + videolinks1 + ")'>" +
                                " </div>" +
                                "</div>" +
                                "</div>" +
                                "</div>"
                            hindiCounter += 1
                        }
                    }
                }
           }
           if(englishCounter <4 && englishCounter>0){
               while(englishCounter<5) {
                   for (var i = 0; i < videos.length; i++) {
                       var videolinks = videos[i].videoLink;
                       var link = videolinks.split("=")[1];
                       language = videos[i].tag.language;
                       videolinks = videolinks.replace(/watch\?v\=/g, "embed/")
                       var videolinks1 = '\"' + videolinks + "?autoplay=1;rel=0" + '\"';
                       var imgSrc = "https://i.ytimg.com/vi/" + link + "/mqdefault.jpg";
                       if (language == 'English') {
                           htmlstr += "<div class='slider-card English'>" +
                               "<div class='video-card' id='English'>" +
                               "        <img width='100%' height='100%'   src='" + imgSrc + "'>" +
                               "<div class='play-parent'>" +
                               " <div class='play' onclick='javascript:loadVideo(" + videolinks1 + ")'>" +
                               " </div>" +
                               "</div>" +
                               "</div>" +
                               "</div>"
                           englishCounter+=1
                       }
                   }
               }
           }

           $('.loading-icon').addClass('hidden');
           $(".hindi .slider-video ").append(htmlstrr);
           $(".English .slider-video").append(htmlstr);
           slickInitialize();
           var slickcard = $('.English .slick-track .slick-slide');
           var sliderCard = $('.hindi .slick-track .slick-slide');
           mcqVideo=slickcard;
           mcqVideo1=sliderCard;
           if (slickcard.length== 0 || sliderCard.length==0) {
               $(".slick-slider").hide();
               $('.slider-parent').hide();
               var slickcard = $('.slick-track .slick-slide');
               nothingpresent=true;
           }
           testDom();
           $("button.slick-next.slick-arrow").append("<img id='theImg' src='${assetPath(src: "prepJoy/slick-arrow.svg")}'/>");
           $("button.slick-prev.slick-arrow").append("<img id='theImg' src='${assetPath(src: "prepJoy/slick-arrow.svg")}'/>");
       }
    function loadVideo(videoLink){
        document.getElementById("vidIf").src=videoLink

        $('#videoModal').modal('show');
    }
    $('#videoModal').on('hidden.bs.modal', function () {
        $("#videoModal iframe").attr("src", $("#videoModal iframe").attr("src",''));
    });
    $(window).on('load',function(){
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="admin" action="getCurrentAffairsReadingMaterials" params="'inputDate='+date" onSuccess='showcurrentAffairsReadingMaterial(data);' />

    });

    function showcurrentAffairsReadingMaterial(data) {
        var readingMaterial = JSON.parse(data.readingMaterials);
        var htmlst="";
        var readingmaterialLink;
        var readingmaterialImage;
        var readingmaterialPlaindescription;
        var readingmaterialTitle;
        var img1;
        for(var i =0 ;i<readingMaterial.length;i++) {
            readingmaterialLink = readingMaterial[i].referenceLink;
            readingmaterialImage = readingMaterial[i].description;
            img1 = $(readingmaterialImage).find("img:first").attr("src");
            readingmaterialPlaindescription = readingMaterial[i].tag.plainDescription;
            readingmaterialTitle = readingMaterial[i].title;
            language = readingMaterial[i].tag.language;
            if (language == 'Hindi') {

                htmlst += "<div class='col-12 col-md-6 col-lg-6 mt-3 pl-md-0 readingMaterialcard hindi'>" +
                    "<div class='d-flex  resource-set'>" +
                    "<div class='readingcard'>" +
                    "<h5>" + readingmaterialTitle + "</h5>" +
                    "<img width='100%' height='100%'   src=" + img1 + ">" +
                    "<p>" + readingmaterialPlaindescription + "</p>" +
                    "<a href=" + readingmaterialLink + " target='blank'><strong>Read more: </strong>" + readingmaterialLink + " </a>" +
                    "</div>" +
                    "</div>" +
                    "</div>"

            }
            if (language == 'English') {
                htmlst += "<div class='col-12 col-md-6 col-lg-6 mt-3 pl-md-0 readingMaterialcard English'>" +
                    "<div class='d-flex  resource-set'>" +
                    "<div class='readingcard'>" +
                    "<h5>" + readingmaterialTitle + "</h5>" +
                    "<img width='100%' height='100%'   src=" + img1 + ">" +
                    "<p>" + readingmaterialPlaindescription + "</p>" +
                    "<a href=" + readingmaterialLink + " target='blank'><strong>Read more: </strong>" + readingmaterialLink + " </a>" +
                    "</div>" +
                    "</div>" +
                    "</div>"
            }
        }
        $('.loading-icon').addClass('hidden');
        $(".readingMaterialcards ").append(htmlst);
        changeLanguage();
        checkLanguage();

        var readingcard = $('.readingMaterialParent .col-12.readingMaterialcard');
        mcqRead=readingcard;
        if (readingcard.length== 0)
        {
            $('.readingMaterialParent').hide();
            // $(".readingMaterialParent p").after($("<h4>There are no reading materials available for the current date </h4>"));
        }
        $('img[src="undefined"]').hide();
        testDom()
    }


    $('.mcqRead .col-12').on('click',function() {
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="admin" action="getCurrentAffairsQuizId" params="'inputDate='+date" onSuccess='showcurrentAffairsFordate(data);' />
    })
    function showcurrentAffairsFordate(data) {
        $('.loading-icon').addClass('hidden');
        var resId = data.resId;
        if(resId != '-1') {
            window.location.href = "/funlearn/quiz?fromMode=book&quizMode=practice&fromTab=all&viewedFrom=quiz&resId=" + resId+"&currentAffairs=true";
        }
    }
    $(window).on('load',function() {
        <g:remoteFunction controller="admin" action="getCurrentAffairsQuizId" params="'inputDate='+date" onSuccess='hidecurrentAffairsFordate(data);' />
    })
    function hidecurrentAffairsFordate(data)
    {
        $('.loading-icon').addClass('hidden');
        var resId1 = data;
        mcqsTest = resId1;
        if(resId1 == '-1')
        {
            $('.row.first-row.mcqRead').hide();

        }
        testDom();
    }
   function changeLanguage() {
$('.language').toggleClass('rotate');
       var getclass= $('.switch.toggleSwitch a');
       if($(getclass).hasClass( "rotate" ))
       {
           $('.switch.toggleSwitch  img').attr('src', '${assetPath(src: 'mobile/language.svg')}');
       }
       else{
           $('.switch.toggleSwitch  img').attr('src', '${assetPath(src: 'mobile/language1.svg')}');

       }
       localLanguage=localLanguage=="English"?"Hindi":"English";
       checkLanguage()
   }
    $(window).on('load',function(){
       localLanguage  = localStorage.getItem("language");
   })
    function checkLanguage(){
        if(localLanguage == 'Hindi')
        {
            $('.English').show();
            $('.hindi').hide();
            $('.switch.toggleSwitch  img').attr('src', '${assetPath(src: 'mobile/language.svg')}');
            localStorage.setItem("language", "English");

        }
        else if(localLanguage == 'English')
        {
            $('.hindi').show();
            $('.English').hide();
            $('.switch.toggleSwitch  img').attr('src', '${assetPath(src: 'mobile/language1.svg')}');
            localStorage.setItem("language", "Hindi");
        }
       testDom();
   }
   function goBack(){
       testDom();
       window.history.back();
   }
</script>