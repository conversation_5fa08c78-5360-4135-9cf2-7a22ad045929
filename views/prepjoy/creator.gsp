<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width">
    <title>PR ws Creator</title>
    <asset:stylesheet href="prepJoy/style.css" async="true"/>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.0.0/dist/css/bootstrap.min.css" integrity="sha384-Gn5384xqQ1aoWXA+058RXPxPg6fy4IWvTNh0E263XmFcJlSAwiGgFAW/dAiS6JXm" crossorigin="anonymous">
</head>
<body>
<div class="container">
    <h1 class="mt-2">Create Room</h1>

    <br>
    <div>
        <div>
            <div id="receiver-id" style="font-weight: bold;" title="Copy this ID to the input on send.html.">ID:</div>
        </div>
        <p>Copy Above Id and paste in joiner room</p>
        <div>
            <br>
            <div class="form-inline">
                <input type="text" class="form-control" id="sendMessageBox" placeholder="Enter a message..." autofocus="true" />
                <button type="button" class="btn btn-primary ml-2" id="sendButton">Send</button>
                <button type="button" class="btn btn-secondary ml-2" id="clearMsgsButton">Clear</button>
            </div>
        </div>
    </div>
    <br>
    <div>

        <div>
            <div class="title">Status:</div>
            <div id="status" class="status"></div>
        </div>
        <div>
            <div class="title">Messages:</div>
            <div class="message" id="message"></div>
        </div>
    </div>

</div>
<div class="meet-controls-bar">
    <button class="btn btn-default" onclick="startScreenShare()">Screen Share</button>
</div>

<div class="d-none">
    <p id="notification" hidden></p>
    <div class="entry-modal" id="entry-modal">
        <p>Create or Join Meeting</p>
        <input id="room-input" class="room-input" placeholder="Enter Room ID">
        <div>
            <button onclick="createRoom()">Create Room</button>
            <button onclick="joinRoom()">Join Room</button>
        </div>
    </div>


    <div class="meet-area">
        <!-- Remote Video Element-->
        <video id="remote-video"></video>

        <!-- Local Video Element-->
        <video id="local-video"></video>

    </div>
</div>
<script src="https://unpkg.com/peerjs@1.3.2/dist/peerjs.min.js"></script>
<script type="text/javascript">
    (function () {

        var lastPeerId = null;
        var peer = null; // Own peer object
        var peerId = null;
        var conn = null;
        var recvId = document.getElementById("receiver-id");
        var status = document.getElementById("status");
        var message = document.getElementById("message");
        // var standbyBox = document.getElementById("standby");
        // var goBox = document.getElementById("go");
        // var fadeBox = document.getElementById("fade");
        // var offBox = document.getElementById("off");
        var sendMessageBox = document.getElementById("sendMessageBox");
        var sendButton = document.getElementById("sendButton");
        var audioButton=document.getElementById("audioButton");
        var clearMsgsButton = document.getElementById("clearMsgsButton");

        /**
         * Create the Peer object for our end of the connection.
         *
         * Sets up callbacks that handle any events related to our
         * peer object.
         */
        function initialize() {
            // Create own peer object with connection to shared PeerJS server
            peer = new Peer(null, {
                debug: 2
            });

            peer.on('open', function (id) {
                // Workaround for peer.reconnect deleting previous id
                if (peer.id === null) {
                    console.log('Received null id from peer open');
                    peer.id = lastPeerId;
                } else {
                    lastPeerId = peer.id;
                }

                console.log('ID: ' + peer.id);
                recvId.innerHTML = "ID: " + peer.id;
                status.innerHTML = "waiting for connection...";
            });
            peer.on('connection', function (c) {
                // Allow only a single connection
                if (conn && conn.open) {
                    c.on('open', function() {
                        c.send("Already connected to another client");
                        setTimeout(function() { c.close(); }, 500);
                    });
                    return;
                }

                conn = c;
                console.log("Connected to: " + conn.peer);
                status.innerHTML = "Connected";
                ready();
                createRoom();
            });
            peer.on('disconnected', function () {
                status.innerHTML = "Connection lost. Please reconnect";
                console.log('Connection lost. Please reconnect');

                // Workaround for peer.reconnect deleting previous id
                peer.id = lastPeerId;
                peer._lastServerId = lastPeerId;
                peer.reconnect();
            });
            peer.on('close', function() {
                conn = null;
                status.innerHTML = "Connection desdivoyed. Please refresh";
                console.log('Connection desdivoyed');
            });
            peer.on('error', function (err) {
                console.log(err);
                alert('' + err);
            });


        };


        /**
         * diviggered once a connection has been achieved.
         * Defines callbacks to handle incoming data and connection events.
         */
        function ready() {
            conn.on('data', function (data) {

                console.log("Data recieved");
                var cueSdiving = "<span class=\"cueMsg\">Cue: </span>";
                switch (data) {
                    case 'Reset':
                        reset();
                        addMessage(cueSdiving + data);
                        break;
                    default:
                        addMessage("<span class=\"peerMsg\">UserA: </span>" + data);
                        break;
                };
            });
            conn.on('close', function () {
                status.innerHTML = "Connection reset<br>wait for connection...";
                conn = null;
            });
            conn.on('open', (id) => {
                console.log("Peer Connected with ID: ", id);
                hideModal()
                getUserMedia({ video: false, audio: true }, (stream) => {
                    local_stream = stream;
                    setLocalStream(local_stream)
                }, (err) => {
                    console.log(err)
                })
                notify("Waiting for peer to join.")
            })
            conn.on('call', (call) => {
                call.answer(local_stream);
                call.on('stream', (stream) => {
                    setRemoteStream(stream)
                })
                currentPeer = call;
            })
        }



        function reset() {

            return;
        };

        function addMessage(msg) {

            message.innerHTML = "<br>" + msg + message.innerHTML;
        }

        function clearMessages() {
            message.innerHTML = "";
            addMessage("Msgs cleared");
        }

        // Listen for enter in message box
        sendMessageBox.addEventListener('keypress', function (e) {
            var event = e || window.event;
            var char = event.which || event.keyCode;
            if (char == '13')
                sendButton.click();
        });
        // Send message
        sendButton.addEventListener('click', function () {
            if (conn && conn.open) {
                var msg = sendMessageBox.value;
                sendMessageBox.value = "";
                conn.send(msg);
                console.log("Sent: " + msg)
                addMessage("<span class=\"selfMsg\">userB: </span>" + msg);
            } else {
                console.log('Connection is closed');
            }
        });


        // Clear messages box
        clearMsgsButton.addEventListener('click', clearMessages);

        initialize();
    })();




</script>
<script src="/assets/prepjoy/script.js"></script>

</body>
</html>
