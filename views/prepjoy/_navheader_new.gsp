<%@ page import="javax.servlet.http.Cookie" %>
<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title><%= title!=null?title:"All-India Online Test Series and Best books for CBSE | ICSE\n" +
            "ISC | NCERT | CUET | JEE | Olympiads | Competitive\n" +
            "Exams"%></title>
    <%if(seoDesc!=null){%>
    <meta name="description" content="${seoDesc}">
    <%}else{%>
    <meta name="description" content="Buy best school and competitive Exams books and online test series. Best way to prepare for your exams. Find the books from Arihant , MTG, Oswaal, Nirali p<PERSON> and many more.">
    <%}%>
    <%if(keywords!=null){%>
    <meta name="keywords" content="${keywords}"/>
    <%}else{%>
    <meta name="keywords" content="Government exams test series, banking exams test series, SSC exams test series, Civil services exam test series, engineering entrance test series, medical exams test series, computer and information technology exams test series, management exams test series, graduate engineering exams test series, railway test series, law arts commerce test series, insurance test series, school level test series, foreign education test series, defence and police test series, skill aptitude and language test series, teaching test series, fashion test series">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'prepJoy/faviconPrepJoy.ico')}" type="image/x-icon">
    <link rel="windows-touch-icon" href="icon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0,maximum-scale=1.0, user-scalable=0" />
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Righteous&display=swap" rel="stylesheet">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" referrerpolicy="no-referrer"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js" integrity="sha384-ApNbgh9B+Y1QKtv3Rn7W3mgPxhU9K/ScQsAP7hUibX39j7fakFPskvXusvfa0b4Q" crossorigin="anonymous"></script>
    <script src="https://code.jquery.com/jquery-migrate-1.4.1.min.js"></script>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Material+Icons|Material+Icons+Outlined|Material+Icons+Two+Tone|Material+Icons+Round|Material+Icons+Sharp" async>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <asset:stylesheet href="wonderslate/material.css" async="true"/>
    <asset:stylesheet href="landingpage/bootstrap.min.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.min.css" async="true"/>
    <asset:stylesheet href="landingpage/slick.theme.css" async="true"/>
    <script src="https://unpkg.com/sweetalert/dist/sweetalert.min.js"></script>

    <asset:stylesheet href="prepJoy/prepjoyWebsites/prepjoyTemplate.css" async="true"/>
    <asset:stylesheet href="prepJoy/gptBookDtl.css" async="true"/>
    <link rel="stylesheet" href="/assets/katex.min.css">
    <style>
    .mobile-back-button{
        display: none;
    }
    .fixed{
        position: fixed !important;
        transition: all 0.4s ease;
    }

    .prepjoy-profile{
        position: absolute;
        top:0;
    }
    .hideProfile{
        right: -110%;
    }
    .showProfile{
        right: 0;
        position: fixed;
    }
    @media screen and (max-width: 767px){
        .navbar{
            display: none;
        }
        .header-wrapper__regBtn{
            display: none;
        }
    }
    @media screen and (max-width: 860px){
        .navbar{
            display: none;
        }
        .header-wrapper__regBtn{
            display: none;
        }
    }

    #salesData{
        color: #fff !important;
    }
    thead tr{
        background: #E83500 !important;
    }
    #search-btn,#download-btn{
        background: #E83500 !important;
    }
    .bg-primary{
        background:#E83500 !important;
    }
    .paginate_button page-item active{
        background: #E83500 !important;
    }
    #salesData_length{
        color: #fff !important;
    }
    #content-books{
        color: #fff !important;
    }
    #view-more{
        background: #E83500 !important;
        margin-top: 20px !important;
        color: #fff !important;
        border-color:#E83500 !important;
    }
    .modal.fade .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(.5);transform: translate(0,0)scale(.5);}
    .modal.show .modal-dialog.modal-dialog-zoom {-webkit-transform: translate(0,0)scale(1);transform: translate(0,0)scale(1);}

    </style>

    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-27Y4MB206N"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());

        gtag('config', 'G-27Y4MB206N');
        gtag('config', 'G-2Y9D9ELSN2');  //WS GA code
    </script>
</head>

<body>


<% boolean isInstitutePublisher = false %>
<%if(session.getAttribute("userdetails")!=null&&session.getAttribute("userdetails").publisherId!=null){

    isInstitutePublisher = session["isInstitutePublisher"]

}%>

<%if(params.tokenId==null&&session["appType"]==null){%>

<header class="header">
    <div class="container-fluid header-wrapper">
        <!----  item-1    --->
        <div class="header-wrapper__logo">
            <a href="/prepjoy" id="siteNavigation" class="d-flex align-items-center"><img src="${assetPath(src:'prepJoy/prepjoy.svg')}" /></a>
            <div class="d-flex align-items-center" style="gap: 10px">
                <sec:ifNotLoggedIn>
                    <div class="header-wrapper__regBtn-mob">
                        <button class="btn btn-danger" id="prepjoyRegBtnMob" style="padding: 3px 10px;">Register or Login</button>
                    </div >
                </sec:ifNotLoggedIn>
                <div class="prepjoy-header__hamburger text-white ml-auto" style="font-size: 1.6rem;margin-top: -25px;">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>

        </div>
        <!----  item-2    --->
        <nav class="navbar header-wrapper__navbar">
            <ul class="navbar-list">
                    <li><a href="/prepjoy/eBooks">Store</a></li>
                <sec:ifLoggedIn>
                    <li><a href="/wsLibrary/myLibrary">My Books</a></li>
                </sec:ifLoggedIn>
                    <li><a href="/books/leaderboard">LeaderBoard</a></li>
                  <sec:ifLoggedIn>
                    <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                        <li><a href="/log/notification">Notification</a></li>
                    </sec:ifAllGranted>
                    <li><a href="/usermanagement/orders">Orders History</a></li>
                </sec:ifLoggedIn>
            </ul>
            <a href="/wsshop/cart" class="mobile_cart_icon"><i class="material-icons-outlined">shopping_cart</i><span class="cart_count" id="navbarCartCount">0</span></a>


        </nav>
        <!----  item-3    --->
        <form class="searchForm header-wrapper__searchbar">
            <div class="search-bar__wrapper d-flex">
                <input type="text" name="search" class="searchbar border-0 typeahead shadow-sm" autocomplete="off" id="search-book-header" placeholder="Search title, subject, author, ISBN, language etc." >
                <button type="button" class="srchIcon" onclick="submitSearchHeader()"><i class="fa-solid fa-magnifying-glass"></i></button>
            </div>
            <div class="profile header-wrapper__userProfile">
            <sec:ifLoggedIn>
                <a href="javascript:openProfile()" class="profile-wrapper" style="margin-left: auto" id="userImageNav">
                    <span>
                        <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                        <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle" style="width: 50px;height: 50px">
                        <%} else { %> <img src="${assetPath(src: 'wonderslate/puser.svg')}" alt="" class="rounded-circle" style="width: 50px;">
                        <%}%>
                    </span>
                </a>
            </sec:ifLoggedIn>
            </div >
        </form>
        <!----  item-4    --->
<sec:ifNotLoggedIn>
        <div class="header-wrapper__regBtn">
            <button class="btn btn-danger" id="prepjoyRegBtn">Register or Login</button>
        </div >
</sec:ifNotLoggedIn>
    </div>
</header>

<!-- User Profile -->
<section class="prepjoy-profile hideProfile">
    <div class="d-flex align-items-center prepjoy-profile__content">
        <!----- profile picture ----->
        <div style="margin-left: auto">
            <a class="text-white" href="javascript:closeProfile()" id="closeProfileSlider" style="font-size: 1.5rem"> <i class="fa fa-times"></i></a>
        </div>

        <div class=" mr-sm-0" id="userImage">
            <span class="avatar rounded-circle" style="width: 100px;">
                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="rounded-circle" style="width: 60px;height: 60px">
                <%} else { %> <img src="${assetPath(src: 'wonderslate/puser.svg')}" alt="" class="rounded-circle" style="width: 80px;">
                <%}%>
            </span>
        </div>
        <!----- User credentials ----->
        <div class="mt-3 mt-sm-4">
<sec:ifLoggedIn>
            <h5 class="text-center text-white"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></h5>
            <div class="d-flex align-items-center justify-content-center" id="userBadge">
                <lottie-player src="https://assets9.lottiefiles.com/packages/lf20_BCXrjU.json"  background="transparent"  speed="1"  style="width: 35px; height: 35px;position: relative;top: 0" loop autoplay></lottie-player>
                <h1 class="text-center" id="userCurBadge" style="margin-bottom: 10px"></h1>
            </div>

            <h5 class="text-white text-center">Total Points</h5>
            <div class="d-flex align-items-center justify-content-center text-center" style="margin-right: 20px">
                <lottie-player src="https://assets6.lottiefiles.com/packages/lf20_vttg4obf.json"  background="transparent"  speed="1"  style="width: 35px; height: 35px;position: relative;top: 0"  loop  autoplay></lottie-player>
                <h1 id="userTolPoints"></h1>
            </div>
            <h5 class="text-white text-center mt-5">Medals</h5>
            <h5 class="text-white text-center d-none" id="noMedal"></h5>
            <div class="d-flex">
                <div class="d-flex align-items-center" id="mBronze">
                    <lottie-player src="https://assets5.lottiefiles.com/packages/lf20_wya7d5mr.json"  background="transparent" id="bMedal" speed="1"  style="width: 50px; height: 50px;position: relative;top: 0"  loop autoplay></lottie-player>
                    <h3 id="bronze"></h3>
                </div>
                <div class="d-flex align-items-center" id="mSilver">
                    <lottie-player src="https://assets2.lottiefiles.com/packages/lf20_EWJbSj.json"  background="transparent" id="sMedal" speed="1"  style="width: 50px; height: 50px;position: relative;top: 0"  loop  autoplay></lottie-player>
                    <h3 id="silver"></h3>
                </div>
                <div class="d-flex align-items-center" id="mGold" >
                    <lottie-player src="https://assets9.lottiefiles.com/packages/lf20_qc3pdcqf.json"  background="transparent" id="gMedal" speed="1"  style="width: 50px; height: 50px;position: relative;top: 0"  loop autoplay></lottie-player>
                    <h3 id="gold"></h3>
                </div>
            </div>
</sec:ifLoggedIn>
<sec:ifNotLoggedIn>
    <h3>Please Login</h3>
</sec:ifNotLoggedIn>
        </div>
<sec:ifLoggedIn>
        <div class="logoutDiv text-center">
            <div>
                <a href="/creation/userProfile" id="editProfile" class="logout">Edit Profile</a>
            </div>
            <a href="/logoff" class="logout">Logout</a>
        </div>
</sec:ifLoggedIn>
    </div>
</section>

<%}%>

<!-- Modal for  prepjoy -->
<div class="modal modal-modifier fade" id="soonModal" >
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-zoom">
        <div class="modal-content modal-content-modifier" style="background: #0F0839">
            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-left" id="soonModalBodys">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>
                <div style="width: 100%">
                    <img src="${assetPath(src: 'prepJoy/prepjoy-website/cmsoon.jpg')}" style="width: 100%" class="">
                </div>
            </div>
        </div>
    </div>
</div>

<g:render template="/prepjoy/prepjoySignup" />
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<asset:javascript src="whitelabel/bootstrap.min.js"/>
<asset:javascript src="whitelabel/jquery-ui.min.js"/>
<asset:javascript src="landingpage/slick.js"/>

<script>


    //CATEGORY LIST
    var level1 = {"level":"Competitive Exams",}
    var level2 = {"level":"College"}
    var level3 = {"level": "Engineering Entrances"}
    var level4 = {"level": "General"}
    var level5 = {"level": "Government Recruitments",}
    var level6 = {"level": "Medical Entrances",}
    var level7 = {"level": "Magazine",}
    var level8 = {"level": "School",}
    var level9 = {"level": "ITI & Polytechnic",}

    //USER DETAILS
    <%if(session['userdetails']!=null){%>
    var username = "${session['userdetails'].username}";
    <%} else {%>
    username = ''
    <%} %>
    var currentBadge;
    var totalPoints;
    var bronze;
    var gold;
    var silver;
    var totalMedals;
    var todaysPoints;

    var siteName = "${session['siteName']}";
    var siteId = "${session['siteId']}";
    var userNameSiteId;
    var appHeaderName;

    //ADDING CATEGORY LIST
    var categoriesList = [];
    categoriesList.push(level1,level2,level3,level4,level5,level6,level7,level8,level9);
    JSON.stringify(categoriesList)
    var activeCategories = categoriesList;

    //OPENING MENU LIST
    $('.prepjoy-header__hamburger').on('click',function (){
        $(this).toggleClass('open');
        $('.header-wrapper__navbar').slideToggle('show');
    })

    //OPENING USER PROFILE
    function openProfile(){
        $('.prepjoy-profile').toggleClass('hideProfile').toggleClass('showProfile');

        if ($('.prepjoy-profile').hasClass('hideProfile')){
            $('body').css('position','relative');
        }else if ($('.prepjoy-profile').hasClass('showProfile')){
            $('body').css('position','fixed');
        }
    }

    //CLOSING USER PROFILE
    function closeProfile(){
        $('.prepjoy-profile').toggleClass('hideProfile').toggleClass('showProfile');
        if ($('.prepjoy-profile').hasClass('hideProfile')){
            $('body').css('position','relative');
        }else if ($('.prepjoy-profile').hasClass('showProfile')){
            $('body').css('position','fixed');
        }
    }

    //GETTING USER DETAILS
  function getUserDetails(){
        <g:remoteFunction controller="prepjoy" action="getUserPrepJoyDetails" params="'username='+username" onSuccess="showUserDetails(data)" />
  }
  if (username !=null){
      getUserDetails();
  }

    //SHOWING USER DETAILS IN USER PROFILE
    function showUserDetails(data){
        var userDetails = data.details;
        userDetails = userDetails.split(",").reduce(function (obj, str, index) {
            var strParts = str.split(":");
            if (strParts[0] && strParts[1]) { //<-- Make sure the key & value are not undefined
                obj[strParts[0].replace(/\s+/g, '')] = strParts[1].trim(); //<-- Get rid of extra spaces at beginning of value strings
            }
            return obj;
        }, {});

        currentBadge = userDetails.currentBadge;
        totalPoints = userDetails.totalPoints;
        bronze = userDetails.bronze;
        silver = userDetails.silver;
        gold = userDetails.gold;
        totalMedals = userDetails.totalMedals;
        todaysPoints = userDetails.todaysPoints;

        //UPDATING UI
        if (currentBadge !='' || currentBadge != null || currentBadge != undefined || currentBadge != 'undefined'){
            $('#userCurBadge').text(currentBadge);
        }
        if (totalPoints !='' || totalPoints != null || totalPoints != undefined || totalPoints != 'undefined'){
            $('#userTolPoints').text(totalPoints);
        }

        if (typeof gold == "undefined"){
            $('#gold').text(0);
        }else {
            $('#gold').text(gold);
        }
        if (typeof silver == "undefined"){
            $('#silver').text(0);
        }else {
            $('#silver').text(silver);
        }

        if (typeof bronze == "undefined"){
            $('#bronze').text(0);
        }else {
            $('#bronze').text(bronze);
        }
    }

    //LINK PATHS FOR DIFFERENT SITES
    var linkPath;
    var link = document.querySelector('#storeLink');
    siteName = siteName.toLowerCase().replaceAll(' ','');

    checkPrepjoySiteName(siteName);

    //METHOD FOR HREF LINKS
    function checkPrepjoySiteName(siteName){
        if (siteName.includes('currentaffairs')){
            linkPath = '/currentaffairs/eBooks';
            link.href = linkPath;
        }else if(siteName.includes('karnataka')){
            linkPath = '/karnataka/eBooks';
            link.href = linkPath;
        }else if(siteName.includes('neet')){
            linkPath = '/neet/eBooks';
            link.href = linkPath;
        }else if(siteName.includes('enggentrances')){
            linkPath = '/enggentrances/eBooks';
            link.href = linkPath;
        }else if(siteName.includes('cacscma')){
            linkPath = '/cacscma/eBooks';
            link.href = linkPath;
        }else if(siteName.includes('ctet')){
            linkPath = '/ctet/eBooks';
            link.href = linkPath;
        }
    }

    //OPENING APP LINK METHOD
    function openAppLink(){
        var appUrl;
        var prepjoyBookOpenLink = "${session['siteName']}";

        if (prepjoyBookOpenLink.includes('currentaffairs')){
            appUrl = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy';
            window.location.href = appUrl;
        }else if(prepjoyBookOpenLink.includes('ctet')){
            appUrl = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.karnataka&hl=en-GB';
            window.location.href = appUrl;
        }else if(prepjoyBookOpenLink.includes('neet')){
            appUrl = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.karnataka&hl=en-GB';
            window.location.href = appUrl;
        }else if(prepjoyBookOpenLink.includes('enggentrances')){
            appUrl = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.karnataka&hl=en-GB';
            window.location.href = appUrl;
        }else if(prepjoyBookOpenLink.includes('karnataka')){
            appUrl = 'https://play.google.com/store/apps/details?id=com.wonderslate.prepjoy.karnataka&hl=en-GB';
            window.location.href = appUrl;
        }
    }


    //UPDATING BROWSER TITLE
    if (siteName.includes('currentaffairs')){
        appHeaderName = 'Current Affairs';
        $('#siteNavigation').attr('href','/prepjoy/currentaffairs')
        document.title = "Prepjoy - Current Affairs";
    }else if (siteName.includes('karnataka')){
        appHeaderName = 'Karnataka';
        $('#siteNavigation').attr('href','/prepjoy/karnataka')
        document.title = "Prepjoy - Karnataka";
    }else if (siteName.includes('neet')){
        appHeaderName = 'NEET'
        $('#siteNavigation').attr('href','/prepjoy/neet')
        document.title = "Prepjoy - NEET";
    }else if (siteName.includes('enggentrances')){
        appHeaderName = 'Engg Entrances'
        $('#siteNavigation').attr('href','/prepjoy/enggentrances');
        document.title = "Prepjoy - Engg Entrances";
    }else if (siteName.includes('cacscma')){
        appHeaderName = 'CA CS CMA'
        $('#siteNavigation').attr('href','/prepjoy/cacscma');
        document.title = "Prepjoy - CA CS CMA";
    }else if (siteName.includes('ctet')){
        appHeaderName = 'Teaching Job Exams'
        $('#siteNavigation').attr('href','/prepjoy/ctet');
        document.title = "Prepjoy - Teaching Job Exams";
    }
    //UPDATING APP NAME IN HEADER
    $('.prepjoySiteName').text(" - "+appHeaderName);

</script>
