<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>
    var completeSearchData=""
    $('#search-print-book-store').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList?printSearchType=true',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    completeSearchData = data;
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitPrintSearch("search-print-book-store");
        }
    });

    $('#search-print-book-store-navbar').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList?printSearchType=true',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks(data) {
                    completeSearchData = data;
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitPrintSearch("search-print-book-store-navbar");
        }
    });
    function submitPrintSearch(searchField){
        var searchString = document.getElementById(searchField).value;
        var tempSearchList = completeSearchData.searchList;
        var searchIndex=-1;
        currentPageNo=0;
        $(".loading-icon").removeClass("hidden");
        for(var i=0;i<tempSearchList.length;i++){
            if(tempSearchList[i]==searchString) searchIndex = i;
        }

        if(searchIndex>-1) {
            var searchValues = completeSearchData.searchValues[searchIndex];

            if ("book" == searchValues.type) {
                var bookUrl = "/" + (searchValues.title).replaceAll('?', '').replaceAll(',', '-').replaceAll(' ', '-').replaceAll("&", "-").replaceAll("'", "-").replaceAll("--", "-").replaceAll(":", "-").replaceAll("/", "-").toLowerCase() + "/ebook-details?siteName=${session['siteName']}&pBookId=" + searchValues.bookId+"&baseCategory="+searchValues.baseCategory;
                window.location.href = bookUrl;
            } else if ("publisher" == searchValues.type) {
                categoryId = "all";
                publisherId = searchValues.pubId;
                <g:remoteFunction controller="printbooks" action="getPrintBooks"  onSuccess='displayBooks(data);'
                params="'pageNo=0&categoryId=all&publisherId='+publisherId" />
            } else if ("category" == searchValues.type) {
                categoryId = searchValues.categId;
                <g:remoteFunction controller="printbooks" action="getPrintBooks"  onSuccess='displayBooks(data);'
                params="'pageNo=0&categoryId='+categoryId+'&baseCategory='+searchValues.baseCategory" />
            }
        }else {
            document.getElementById("content-data-books-ebooks").innerHTML = "";
            $('#emptyState').removeClass('d-none');
            $(".loading-icon").addClass("hidden");
        }
    }

    $(document).on("keypress", "#search-print-book-store", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitPrintSearch();
            }
        }
    });
</script>
