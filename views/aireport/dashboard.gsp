<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

        .dashboard-container {
            padding: 20px;
        }
        .dashboard-header {
            margin-bottom: 20px;
        }
        .filter-section {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .dashboard-section {
            background-color: white;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
        }
        .section-header {
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            margin-bottom: 20px;
        }
        .data-table {
            width: 100%;
            margin-top: 20px;
        }
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        .error-message {
            color: #dc3545;
            padding: 10px;
            background-color: #f8d7da;
            border-radius: 5px;
            margin-bottom: 15px;
            display: none;
        }
        .no-data-message {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #6c757d;
            font-size: 16px;
            background-color: rgba(255, 255, 255, 0.8);
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            z-index: 10;
        }
        .no-data-section-message {
            margin: 20px 0;
            padding: 30px;
            border-radius: 5px;
        }
        .no-data-section-message .alert {
            font-size: 18px;
            padding: 20px;
        }
        .breadcrumb-navigation {
            margin-bottom: 20px;
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .breadcrumb-item {
            display: inline-block;
            margin-right: 5px;
        }
        .breadcrumb-item:not(:last-child):after {
            content: '>';
            margin-left: 5px;
            color: #6c757d;
        }
        .breadcrumb-item a {
            color: #007bff;
            text-decoration: none;
        }
        .breadcrumb-item.active {
            color: #6c757d;
            font-weight: bold;
        }
        .clickable {
            cursor: pointer;
            color: #007bff;
        }
        .clickable:hover {
            text-decoration: underline;
        }
        .clickable-row {
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .clickable-row:hover {
            background-color: rgba(0, 123, 255, 0.1) !important;
        }
        .clickable-row td:first-child {
            color: #007bff;
            position: relative;
            padding-left: 20px;
        }
        .clickable-row td:first-child::before {
            content: '\f0da';
            font-family: 'Font Awesome 5 Free';
            font-weight: 900;
            position: absolute;
            left: 5px;
            color: #007bff;
        }
        .back-button {
            margin-bottom: 15px;
        }
        /* Demo mode toggle switch styles */
        .form-check-input {
            width: 3em;
            height: 1.5em;
            margin-top: 0.25em;
            vertical-align: top;
            background-color: #fff;
            background-repeat: no-repeat;
            background-position: center;
            background-size: contain;
            border: 1px solid rgba(0,0,0,.25);
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            -webkit-print-color-adjust: exact;
            color-adjust: exact;
            transition: background-color .15s ease-in-out,background-position .15s ease-in-out,border-color .15s ease-in-out;
        }
        .form-check-input:checked {
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .form-check-input[type=checkbox] {
            border-radius: 0.5em;
        }
        .form-check-input[type=checkbox]:checked {
            background-position: right center;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23fff'/%3e%3c/svg%3e");
        }
        .form-switch .form-check-input {
            width: 2em;
            margin-left: -2.5em;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
            background-position: left center;
            border-radius: 2em;
            transition: background-position .15s ease-in-out;
        }
    </style>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
    <div class="dashboard-container">
        <div class="dashboard-header">
            <h1>Analytics Dashboard</h1>
            <p>View and analyze user interactions and practice metrics</p>
        </div>

        <!-- Breadcrumb Navigation -->
        <div id="breadcrumbNavigation" class="breadcrumb-navigation">
            <div class="breadcrumb-item active">Dashboard</div>
        </div>

        <!-- Error Message -->
        <div id="errorMessage" class="alert alert-danger" style="display: none;"></div>

        <!-- Filters Section -->
        <div class="filter-section">
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="dateRange">Date Range</label>
                        <select id="dateRange" class="form-control">
                            <option value="7">Last 7 days</option>
                            <option value="30">Last 30 days</option>
                            <option value="365">Last 1 year</option>
                            <option value="custom">Custom Range</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-4" id="customDateRange" style="display: none;">
                    <div class="form-group">
                        <label for="fromDate">From</label>
                        <input type="date" id="fromDate" class="form-control" value="${fromDate}">
                    </div>
                </div>
                <div class="col-md-4" id="customDateRangeTo" style="display: none;">
                    <div class="form-group">
                        <label for="toDate">To</label>
                        <input type="date" id="toDate" class="form-control" value="${toDate}">
                    </div>
                </div>

                <!-- Batch Filter (Only for Managers and Instructors) -->
                <g:if test="${userRole == 'Manager' || userRole == 'Instructor'}">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="batchFilter">Batch</label>
                            <select id="batchFilter" class="form-control">
                                <g:each in="${batches}" var="batch">
                                    <option value="${batch.id}">${batch.name}</option>
                                </g:each>
                            </select>
                        </div>
                    </div>
                </g:if>

                <div class="col-md-4">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button id="applyFilters" class="btn btn-primary form-control">Apply Filters</button>
                    </div>
                </div>

                <!-- Demo Mode Toggle -->
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="demoModeToggle">Demo Mode</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="demoModeToggle" ${isDemoMode ? 'checked' : ''}>
                            <label class="form-check-label" for="demoModeToggle">Show demo data</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Message Display -->
        <div id="errorMessage" class="error-message"></div>

        <!-- Main Dashboard Section -->
        <div id="mainDashboard">
            <!-- Interaction Dashboard Section -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Interaction Dashboard</h2>
                    <p>Analysis of user interactions with AI features</p>
                </div>

                <!-- No Data Message for Interaction Dashboard -->
                <div id="noInteractionDataMsg" class="no-data-section-message" style="display: none;">
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle mr-2"></i> No interaction data available for the selected period.
                    </div>
                </div>

            <!-- Charts and Tables -->
            <div id="interactionChartsContainer" class="row">
                <!-- Subject-wise Interaction Count -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="subjectChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="subjectChart" title="Click on a subject to see more details"></canvas>
                        <div class="text-muted small text-center mt-2"><i class="fas fa-info-circle"></i> Click on a subject to see more details</div>
                    </div>
                    <div class="table-responsive">
                        <table id="subjectTable" class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-mouse-pointer text-primary small mr-1"></i> Subject (clickable)</th>
                                    <th>Interaction Count</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- PromptType Distribution -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="promptTypeChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="promptTypeChart"></canvas>
                    </div>
                    <div class="table-responsive">
                        <table id="promptTypeTable" class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th>Prompt Type</th>
                                    <th>Count</th>
                                    <th>Percentage</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

            <!-- Practice Dashboard Section -->
            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Practice Dashboard</h2>
                    <p>Analysis of user practice and quiz performance</p>
                </div>

                <!-- No Data Message for Practice Dashboard -->
                <div id="practiceNoDataMessage" class="no-data-section-message" style="display: none;">
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle"></i> No practice data available for the selected period.
                    </div>
                </div>

                <div id="practiceChartsRow" class="row">
                <!-- Subject-wise Practice Metrics -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="practiceChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="practiceChart" title="Click on a subject to see more details"></canvas>
                        <div class="text-muted small text-center mt-2"><i class="fas fa-info-circle"></i> Click on a subject to see more details</div>
                    </div>
                    <div class="table-responsive">
                        <table id="practiceTable" class="table table-striped data-table">
                            <thead>
                                <tr>
                                    <th><i class="fas fa-mouse-pointer text-primary small mr-1"></i> Subject (clickable)</th>
                                    <th>Correct</th>
                                    <th>Incorrect</th>
                                    <th>Skipped</th>
                                    <th>Accuracy</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Performance Trends Over Time -->
                <div class="col-md-6">
                    <div class="chart-container">
                        <div id="trendsChartLoading" class="loading-overlay">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                        </div>
                        <canvas id="trendsChart"></canvas>
                    </div>
                </div>
                </div>
            </div>
        </div>

        <!-- Books Dashboard Section (Hidden initially) -->
        <div id="booksDashboard" style="display: none;">
            <button id="backToMainDashboard" class="btn btn-secondary back-button">
                <i class="fa fa-arrow-left"></i> Back to Dashboard
            </button>

            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Books for <span id="selectedSubject"></span></h2>
                    <p>Analysis of books for the selected subject</p>
                </div>

                <!-- No Data Message for Books Dashboard -->
                <div id="noBooksDataMsg" class="no-data-section-message" style="display: none;">
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle"></i> No books data available for the selected subject.
                    </div>
                </div>

                <div class="row">
                    <!-- Books Chart -->
                    <div class="col-md-6">
                        <div class="chart-container">
                            <div id="booksChartLoading" class="loading-overlay">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                            <canvas id="booksChart"></canvas>
                        </div>
                    </div>

                    <!-- Books Table -->
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table id="booksTable" class="table table-striped data-table">
                                <thead>
                                    <tr>
                                        <th>Book Title</th>
                                        <th>Interaction Count</th>
                                        <th>Percentage</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chapters Dashboard Section (Hidden initially) -->
        <div id="chaptersDashboard" style="display: none;">
            <button id="backToBooksView" class="btn btn-secondary back-button">
                <i class="fa fa-arrow-left"></i> Back to Books
            </button>

            <div class="dashboard-section">
                <div class="section-header">
                    <h2>Chapters for <span id="selectedBook"></span></h2>
                    <p>Analysis of chapters for the selected book</p>
                </div>

                <!-- No Data Message for Chapters Dashboard -->
                <div id="noChaptersDataMsg" class="no-data-section-message" style="display: none;">
                    <div class="alert alert-info text-center">
                        <i class="fa fa-info-circle"></i> No chapters data available for the selected book.
                    </div>
                </div>

                <div class="row">
                    <!-- Chapters Chart -->
                    <div class="col-md-6">
                        <div class="chart-container">
                            <div id="chaptersChartLoading" class="loading-overlay">
                                <div class="spinner-border text-primary" role="status">
                                    <span class="sr-only">Loading...</span>
                                </div>
                            </div>
                            <canvas id="chaptersChart"></canvas>
                        </div>
                    </div>

                    <!-- Chapters Table -->
                    <div class="col-md-6">
                        <div class="table-responsive">
                            <table id="chaptersTable" class="table table-striped data-table">
                                <thead>
                                    <tr>
                                        <th>Chapter Name</th>
                                        <th>Interaction Count</th>
                                        <th>Percentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Learning Progress Dashboard -->
    <div id="progressDashboard" class="dashboard-section">
        <div class="section-header">
            <h3>Your Learning Progress</h3>
            <p>Compare your performance with the previous period</p>
        </div>

        <div class="row">
            <div class="col-md-12">
                <div class="form-group">
                    <label for="progressPeriod">Time Period</label>
                    <select id="progressPeriod" class="form-control">
                        <option value="7">Last 7 days vs previous 7 days</option>
                        <option value="30" selected>Last 30 days vs previous 30 days</option>
                        <option value="90">Last 90 days vs previous 90 days</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Overall Progress Card -->
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Overall Accuracy</h5>
                    </div>
                    <div class="card-body text-center d-flex flex-column justify-content-center" style="height: 75px; padding: 0.5rem;">
                        <div id="accuracyGauge" style="height: 40px;"></div>
                        <div id="accuracyChange" class="mt-0"></div>
                    </div>
                </div>
            </div>

            <!-- Activity Change Card -->
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Activity Level</h5>
                    </div>
                    <div class="card-body text-center d-flex flex-column justify-content-center" style="height: 75px; padding: 0.5rem;">
                        <div id="activityChart" style="height: 40px;"></div>
                        <div id="activityChange" class="mt-0"></div>
                    </div>
                </div>
            </div>

            <!-- Learning Streak Card -->
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5>Learning Summary</h5>
                    </div>
                    <div class="card-body">
                        <div id="learningSummary"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- Subject Progress Table -->
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Subject Progress</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table id="subjectProgressTable" class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Subject</th>
                                        <th>Current Accuracy</th>
                                        <th>Previous Accuracy</th>
                                        <th>Change</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <!-- Will be populated by JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
            </div>
        </div>
        </div>
        </div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>v
    <!-- JavaScript Libraries -->
    <asset:javascript src="jquery-1.11.2.min.js"/>
    <script>
        // Wait for jQuery to load
        window.addEventListener('load', function() {
            // Now load Bootstrap and Chart.js
            var bootstrapScript = document.createElement('script');
            bootstrapScript.src = '<asset:assetPath src="bootstrap.min.js"/>';
            document.body.appendChild(bootstrapScript);

            var chartScript = document.createElement('script');
            chartScript.src = 'https://cdn.jsdelivr.net/npm/chart.js';
            document.body.appendChild(chartScript);

            // Initialize the dashboard after all scripts are loaded
            chartScript.onload = initializeDashboard;
        });
    </script>

    <script>
        // Global chart instances and data
        let subjectChart, promptTypeChart, practiceChart, trendsChart, booksChart, chaptersChart;

        // Global variables for drill-down
        let currentSubject = '';
        let currentBookId = 0;
        let currentBookTitle = '';
        let currentBookDetails = [];

        // Global variables for practice drill-down
        let currentPracticeSubject = '';
        let currentPracticeBookId = 0;
        let currentPracticeBookTitle = '';
        let currentPracticeBookDetails = [];

        // Initialize data from controller
        let subjectData = { labels: [], data: [], total: 0 };
        let promptTypeData = { labels: [], data: [], total: 0 };
        let practiceData = [];
        let trendsData = { labels: [], datasets: [] };

        // Function to initialize the dashboard
        function initializeDashboard() {
            // Set up date range selector
            $('#dateRange').change(function() {
                if ($(this).val() === 'custom') {
                    $('#customDateRange, #customDateRangeTo').show();
                } else {
                    $('#customDateRange, #customDateRangeTo').hide();

                    // Calculate dates based on selection
                    const today = new Date();
                    const days = parseInt($(this).val());
                    const fromDate = new Date();
                    fromDate.setDate(today.getDate() - days);

                    // Format dates for input fields
                    $('#fromDate').val(formatDate(fromDate));
                    $('#toDate').val(formatDate(today));
                }
            });

            // Apply filters button click
            $('#applyFilters').click(function() {
                loadDashboardData();
            });

            // Demo mode toggle click
            $('#demoModeToggle').change(function() {
                loadDashboardData();
                loadLearningProgressData();
            });

            // Initial data load
            loadDashboardData();

            // Load learning progress data
            loadLearningProgressData();

            // Add event listener for progress period change
            $('#progressPeriod').change(function() {
                loadLearningProgressData();
            });

            // Set up back buttons
            $('#backToMainDashboard').click(function() {
                $('#booksDashboard').hide();
                $('#mainDashboard').show();
            });

            $('#backToBooksView').click(function() {
                $('#chaptersDashboard').hide();
                $('#booksDashboard').show();
            });
        }

        // Format date as YYYY-MM-DD
        function formatDate(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return year + '-' + month + '-' + day;
        }

        // Show error message
        function showError(message) {
            $('#errorMessage').text(message).show();
            console.error(message);
        }

        // Load dashboard data based on filters
        function loadDashboardData() {
            // Show loading overlays
            $('.loading-overlay').show();

            // Hide any previous error messages
            $('#errorMessage').hide();

            // Get filter values
            const fromDate = $('#fromDate').val();
            const toDate = $('#toDate').val();
            const batchId = $('#batchFilter').val() || '';
            const isDemoMode = $('#demoModeToggle').is(':checked');

            // Load interaction data
            $.ajax({
                url: '<g:createLink controller="aireport" action="getInteractionData"/>',
                data: {
                    fromDate: fromDate,
                    toDate: toDate,
                    batchId: batchId,
                    showReport: isDemoMode ? 'demo' : null
                },
                success: function(response) {
                    if (response.status === 'success') {
                        updateInteractionDashboard(response.data);
                    } else {
                        showError(response.message || 'Failed to load interaction data');
                    }
                    $('#subjectChartLoading, #promptTypeChartLoading').hide();
                },
                error: function(xhr, status, error) {
                    showError('Error loading interaction data: ' + error);
                    $('#subjectChartLoading, #promptTypeChartLoading').hide();
                }
            });

            // Load practice data
            $.ajax({
                url: '<g:createLink controller="aireport" action="getPracticeData"/>',
                data: {
                    fromDate: fromDate,
                    toDate: toDate,
                    batchId: batchId,
                    showReport: isDemoMode ? 'demo' : null
                },
                success: function(response) {
                    if (response.status === 'success') {
                        updatePracticeDashboard(response.data);
                    } else {
                        showError(response.message || 'Failed to load practice data');
                    }
                    $('#practiceChartLoading, #trendsChartLoading').hide();
                },
                error: function(xhr, status, error) {
                    showError('Error loading practice data: ' + error);
                    $('#practiceChartLoading, #trendsChartLoading').hide();
                }
            });
        }

        // Show error message
        function showError(message) {
            $('#errorMessage').text(message).show();
        }

        // Update Interaction Dashboard with new data
        function updateInteractionDashboard(data) {
            // Check if there's any data to display
            if (!data || !data.subjectData || !data.subjectData.labels || data.subjectData.labels.length === 0) {
                // Hide charts container and tables
                $('#interactionChartsContainer').hide();
                $('#subjectTable').closest('.table-responsive').hide();
                $('#promptTypeTable').closest('.table-responsive').hide();
                // Show no data message
                $('#noInteractionDataMsg').show();
                return;
            } else {
                // Show charts container and tables
                $('#interactionChartsContainer').show();
                $('#subjectTable').closest('.table-responsive').show();
                $('#promptTypeTable').closest('.table-responsive').show();
                // Hide no data message
                $('#noInteractionDataMsg').hide();
            }

            // Update Subject Chart
            updateSubjectChart(data.subjectData);

            // Update Prompt Type Chart
            updatePromptTypeChart(data.promptTypeData);
        }

        // Update Subject Chart
        function updateSubjectChart(subjectData) {
            const ctx = document.getElementById('subjectChart').getContext('2d');

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (subjectChart && typeof subjectChart.destroy === 'function') {
                subjectChart.destroy();
            }

            // Check if data is available
            if (!subjectData || !subjectData.labels || !subjectData.data || subjectData.labels.length === 0) {
                return;
            }

            // Create new chart
            subjectChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: subjectData.labels,
                    datasets: [{
                        data: subjectData.data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Subjects You\'ve Explored',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = subjectData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements && elements.length > 0) {
                            const index = elements[0].index;
                            const subject = subjectData.labels[index];
                            drillDownToBooks(subject);
                        }
                    }
                }
            });

            // Update subject table
            const tableBody = $('#subjectTable tbody');
            tableBody.empty();

            if (subjectData && subjectData.labels) {
                for (let i = 0; i < subjectData.labels.length; i++) {
                    const percentage = Math.round((subjectData.data[i] / subjectData.total) * 100);
                    const row = document.createElement('tr');
                    const subject = subjectData.labels[i];

                    // Make the row clickable and visually indicate it's clickable
                    row.className = 'clickable-row';
                    row.title = 'Click to see books for ' + subject;
                    row.addEventListener('click', function() {
                        drillDownToBooks(subject);
                    });

                    const labelCell = document.createElement('td');
                    labelCell.innerHTML = '<span class="text-primary">' + subject + '</span> <i class="fas fa-chevron-right text-muted small ml-1"></i>';
                    row.appendChild(labelCell);

                    const dataCell = document.createElement('td');
                    dataCell.textContent = subjectData.data[i];
                    row.appendChild(dataCell);

                    const percentageCell = document.createElement('td');
                    percentageCell.textContent = percentage + '%';
                    row.appendChild(percentageCell);

                    tableBody.append(row);
                }
            }
        }

        // Update Prompt Type Chart
        function updatePromptTypeChart(promptTypeData) {
            const ctx = document.getElementById('promptTypeChart').getContext('2d');

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (promptTypeChart && typeof promptTypeChart.destroy === 'function') {
                promptTypeChart.destroy();
            }

            // Check if data is available
            if (!promptTypeData || !promptTypeData.labels || !promptTypeData.data || promptTypeData.labels.length === 0) {
                return;
            }

            // Create new chart
            promptTypeChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: promptTypeData.labels,
                    datasets: [{
                        label: 'Count',
                        data: promptTypeData.data,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Types of Questions Asked',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw || 0;
                                    const total = promptTypeData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return 'Count: ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        }
                    }
                }
            });

            // Update prompt type table
            const tableBody = $('#promptTypeTable tbody');
            tableBody.empty();

            if (promptTypeData && promptTypeData.labels) {
                for (let i = 0; i < promptTypeData.labels.length; i++) {
                    const percentage = Math.round((promptTypeData.data[i] / promptTypeData.total) * 100);
                    const row = document.createElement('tr');

                    const labelCell = document.createElement('td');
                    labelCell.textContent = promptTypeData.labels[i];
                    row.appendChild(labelCell);

                    const dataCell = document.createElement('td');
                    dataCell.textContent = promptTypeData.data[i];
                    row.appendChild(dataCell);

                    const percentageCell = document.createElement('td');
                    percentageCell.textContent = percentage + '%';
                    row.appendChild(percentageCell);

                    tableBody.append(row);
                }
            }
        }

        // Update Practice Dashboard with new data
        function updatePracticeDashboard(data) {
            // Check if there's any data to display
            if (!data || !data.subjectPerformance || !data.subjectPerformance.length) {
                // Hide charts container and tables
                $('#practiceChartsRow').hide();
                $('#practiceTable').closest('.table-responsive').hide();
                // Show no data message
                $('#practiceNoDataMessage').show();
                return;
            } else {
                // Show charts container and tables
                $('#practiceChartsRow').show();
                $('#practiceTable').closest('.table-responsive').show();
                // Hide no data message
                $('#practiceNoDataMessage').hide();
            }

            // Update Subject Performance Chart
            updatePracticeChart(data.subjectPerformance);

            // Update Performance Trends Chart
            updateTrendsChart(data.timeSeriesData);
        }

        // Update Practice Chart
        function updatePracticeChart(practiceData) {
            const ctx = document.getElementById('practiceChart').getContext('2d');

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (practiceChart && typeof practiceChart.destroy === 'function') {
                practiceChart.destroy();
            }

            // Check if data is available
            if (!practiceData || !practiceData.length) {
                return;
            }

            // Prepare data for stacked bar chart
            const subjectLabels = practiceData.map(item => item.subject);
            const correctData = practiceData.map(item => item.correct);
            const incorrectData = practiceData.map(item => item.incorrect);
            const skippedData = practiceData.map(item => item.skipped);

            // Create new chart
            practiceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: subjectLabels,
                    datasets: [
                        {
                            label: 'Correct',
                            data: correctData,
                            backgroundColor: 'rgba(75, 192, 192, 0.7)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Incorrect',
                            data: incorrectData,
                            backgroundColor: 'rgba(255, 99, 132, 0.7)',
                            borderColor: 'rgba(255, 99, 132, 1)',
                            borderWidth: 1
                        },
                        {
                            label: 'Skipped',
                            data: skippedData,
                            backgroundColor: 'rgba(255, 206, 86, 0.7)',
                            borderColor: 'rgba(255, 206, 86, 1)',
                            borderWidth: 1
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Your Performance by Subject',
                            font: {
                                size: 16
                            }
                        }
                    },
                    scales: {
                        x: {
                            stacked: true
                        },
                        y: {
                            stacked: true,
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements && elements.length > 0) {
                            const index = elements[0].index;
                            // Get the subject from the chart data
                            const subject = subjectLabels[index];
                            console.log('Clicked on subject:', subject);
                            drillDownToPracticeBooks(subject);
                        }
                    }
                }
            });

            // Update practice table
            const tableBody = $('#practiceTable tbody');
            tableBody.empty();

            if (practiceData && practiceData.length) {
                practiceData.forEach(function(item) {
                    const row = document.createElement('tr');
                    const subject = item.subject;

                    // Make the row clickable and visually indicate it's clickable
                    row.className = 'clickable-row';
                    row.title = 'Click to see details for ' + subject;
                    row.addEventListener('click', function() {
                        drillDownToPracticeBooks(subject);
                    });

                    const subjectCell = document.createElement('td');
                    subjectCell.innerHTML = '<span class="text-primary">' + subject + '</span> <i class="fas fa-chevron-right text-muted small ml-1"></i>';
                    row.appendChild(subjectCell);

                    const correctCell = document.createElement('td');
                    correctCell.textContent = item.correct;
                    row.appendChild(correctCell);

                    const incorrectCell = document.createElement('td');
                    incorrectCell.textContent = item.incorrect;
                    row.appendChild(incorrectCell);

                    const skippedCell = document.createElement('td');
                    skippedCell.textContent = item.skipped;
                    row.appendChild(skippedCell);

                    const accuracyCell = document.createElement('td');
                    // Format accuracy to 2 decimal places
                    const formattedAccuracy = parseFloat(item.accuracy).toFixed(2);
                    accuracyCell.textContent = formattedAccuracy + '%';
                    row.appendChild(accuracyCell);

                    tableBody.append(row);
                });
            }
        }

        // Update Trends Chart
        function updateTrendsChart(trendsData) {
            const ctx = document.getElementById('trendsChart').getContext('2d');

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (trendsChart && typeof trendsChart.destroy === 'function') {
                trendsChart.destroy();
            }

            // Check if data is available
            if (!trendsData || !trendsData.labels || !trendsData.datasets || trendsData.labels.length === 0) {
                return;
            }

            // Create new chart
            trendsChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: trendsData.labels,
                    datasets: trendsData.datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Your Progress Over Time',
                            font: {
                                size: 16
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        }
                    }
                }
            });
        }

        // Drill down from subject to books
        function drillDownToBooks(subject) {
            // Save the current subject for reference
            currentSubject = subject;

            // Update the header in the books dashboard
            $('#selectedSubject').text(subject);

            // Show loading overlay
            $('#booksChartLoading').show();

            // Get date range and batch ID from filters
            const fromDate = $('#fromDate').val();
            const toDate = $('#toDate').val();
            const batchId = $('#batchFilter').val();

            // Make AJAX call to get books data
            $.ajax({
                url: '<g:createLink controller="aireport" action="getBooksBySubject"/>',
                data: {
                    fromDate: fromDate,
                    toDate: toDate,
                    batchId: batchId,
                    subject: subject,
                    showReport: $('#demoModeToggle').is(':checked') ? 'demo' : null
                },
                success: function(response) {
                    if (response.status === 'success') {
                        updateBooksView(response.data, subject);
                    } else {
                        showError(response.message || 'Failed to load books data');
                    }
                    $('#booksChartLoading').hide();
                },
                error: function(xhr, status, error) {
                    showError('Error loading books data: ' + error);
                    $('#booksChartLoading').hide();
                }
            });

            // Hide main dashboard and show books dashboard
            $('#mainDashboard').hide();
            $('#booksDashboard').show();
        }

        // Update Books View with data
        function updateBooksView(data, subject) {
            // Check if there's any data to display
            if (!data || !data.bookData || !data.bookData.labels || data.bookData.labels.length === 0) {
                // Hide chart and table
                $('#booksChart').closest('.chart-container').hide();
                $('#booksTable').closest('.table-responsive').hide();
                // Show no data message
                $('#noBooksDataMsg').show();
                return;
            } else {
                // Show chart and table
                $('#booksChart').closest('.chart-container').show();
                $('#booksTable').closest('.table-responsive').show();
                // Hide no data message
                $('#noBooksDataMsg').hide();
            }

            // Store book details for chart click handling
            currentBookDetails = data.bookDetails;

            // Update Books Chart
            updateBooksChart(data.bookData);

            // Update Books Table
            updateBooksTable(data.bookDetails);
        }

        // Update Books Chart
        function updateBooksChart(bookData) {
            const ctx = document.getElementById('booksChart').getContext('2d');

            // Destroy existing chart if it exists
            if (booksChart) {
                booksChart.destroy();
            }

            // Create new chart
            booksChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: bookData.labels,
                    datasets: [{
                        data: bookData.data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Books You\'ve Explored',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = bookData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements && elements.length > 0) {
                            const index = elements[0].index;
                            const bookId = currentBookDetails[index].id;
                            const bookTitle = bookData.labels[index];
                            drillDownToChapters(bookId, bookTitle);
                        }
                    }
                }
            });
        }

        // Update Books Table
        function updateBooksTable(bookDetails) {
            const tableBody = $('#booksTable tbody');
            tableBody.empty();

            if (bookDetails && bookDetails.length > 0) {
                const total = bookDetails.reduce((sum, book) => sum + book.count, 0);

                bookDetails.forEach(book => {
                    const percentage = Math.round((book.count / total) * 100);
                    const row = document.createElement('tr');

                    // Make the row clickable
                    row.style.cursor = 'pointer';

                    const titleCell = document.createElement('td');
                    titleCell.textContent = book.title;
                    row.appendChild(titleCell);

                    const countCell = document.createElement('td');
                    countCell.textContent = book.count;
                    row.appendChild(countCell);

                    const percentageCell = document.createElement('td');
                    percentageCell.textContent = percentage + '%';
                    row.appendChild(percentageCell);

                    const actionCell = document.createElement('td');
                    const viewButton = document.createElement('button');
                    viewButton.className = 'btn btn-sm btn-primary';
                    viewButton.textContent = 'View Chapters';
                    viewButton.addEventListener('click', function() {
                        drillDownToChapters(book.id, book.title);
                    });
                    actionCell.appendChild(viewButton);
                    row.appendChild(actionCell);

                    // Add click event to the row
                    row.addEventListener('click', function(e) {
                        // Only trigger if the click wasn't on the button
                        if (e.target.tagName !== 'BUTTON') {
                            drillDownToChapters(book.id, book.title);
                        }
                    });

                    tableBody.append(row);
                });
            }
        }

        // Drill down from book to chapters
        function drillDownToChapters(bookId, bookTitle) {
            // Save the current book for reference
            currentBookId = bookId;
            currentBookTitle = bookTitle;

            // Update the header in the chapters dashboard
            $('#selectedBook').text(bookTitle);

            // Show loading overlay
            $('#chaptersChartLoading').show();

            // Get date range and batch ID from filters
            const fromDate = $('#fromDate').val();
            const toDate = $('#toDate').val();
            const batchId = $('#batchFilter').val();

            // Make AJAX call to get chapters data
            $.ajax({
                url: '<g:createLink controller="aireport" action="getChaptersByBook"/>',
                data: {
                    fromDate: fromDate,
                    toDate: toDate,
                    batchId: batchId,
                    bookId: bookId,
                    showReport: $('#demoModeToggle').is(':checked') ? 'demo' : null
                },
                success: function(response) {
                    if (response.status === 'success') {
                        updateChaptersView(response.data);
                    } else {
                        showError(response.message || 'Failed to load chapters data');
                    }
                    $('#chaptersChartLoading').hide();
                },
                error: function(xhr, status, error) {
                    showError('Error loading chapters data: ' + error);
                    $('#chaptersChartLoading').hide();
                }
            });

            // Hide books dashboard and show chapters dashboard
            $('#booksDashboard').hide();
            $('#chaptersDashboard').show();
        }

        // Update Chapters View with data
        function updateChaptersView(data) {
            // Check if there's any data to display
            if (!data || !data.chapterData || !data.chapterData.labels || data.chapterData.labels.length === 0) {
                // Hide chart and table
                $('#chaptersChart').closest('.chart-container').hide();
                $('#chaptersTable').closest('.table-responsive').hide();
                // Show no data message
                $('#noChaptersDataMsg').show();
                return;
            } else {
                // Show chart and table
                $('#chaptersChart').closest('.chart-container').show();
                $('#chaptersTable').closest('.table-responsive').show();
                // Hide no data message
                $('#noChaptersDataMsg').hide();
            }

            // Update Chapters Chart
            updateChaptersChart(data.chapterData);

            // Update Chapters Table
            updateChaptersTable(data.chapterDetails);
        }

        // Update Chapters Chart
        function updateChaptersChart(chapterData) {
            const ctx = document.getElementById('chaptersChart').getContext('2d');

            // Destroy existing chart if it exists
            if (chaptersChart) {
                chaptersChart.destroy();
            }

            // Create new chart
            chaptersChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: chapterData.labels,
                    datasets: [{
                        data: chapterData.data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Chapters You\'ve Explored',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = chapterData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Update Chapters Table
        function updateChaptersTable(chapterDetails) {
            const tableBody = $('#chaptersTable tbody');
            tableBody.empty();

            if (chapterDetails && chapterDetails.length > 0) {
                const total = chapterDetails.reduce((sum, chapter) => sum + chapter.count, 0);

                chapterDetails.forEach(chapter => {
                    const percentage = Math.round((chapter.count / total) * 100);
                    const row = document.createElement('tr');

                    const nameCell = document.createElement('td');
                    nameCell.textContent = chapter.name;
                    row.appendChild(nameCell);

                    const countCell = document.createElement('td');
                    countCell.textContent = chapter.count;
                    row.appendChild(countCell);

                    const percentageCell = document.createElement('td');
                    percentageCell.textContent = percentage + '%';
                    row.appendChild(percentageCell);

                    tableBody.append(row);
                });
            }
        }

        // Drill down from subject to books for practice data
        function drillDownToPracticeBooks(subject) {
            console.log('drillDownToPracticeBooks called with subject:', subject);

            // Save the current subject for reference
            currentPracticeSubject = subject;

            // Update the header in the books dashboard
            $('#selectedSubject').text(subject);

            // Show loading overlay
            $('#booksChartLoading').show();

            // Show debugging info
            console.log('Making AJAX call to get practice books data for subject:', subject);

            // Get date range and batch ID from filters
            const fromDate = $('#fromDate').val();
            const toDate = $('#toDate').val();
            const batchId = $('#batchFilter').val();

            // Make AJAX call to get practice books data
            const url = '<g:createLink controller="aireport" action="getPracticeBooksBySubject"/>';
            console.log('AJAX URL:', url);

            const ajaxData = {
                fromDate: fromDate,
                toDate: toDate,
                batchId: batchId,
                subject: subject,
                showReport: $('#demoModeToggle').is(':checked') ? 'demo' : null
            };
            console.log('AJAX data:', ajaxData);

            $.ajax({
                url: url,
                data: ajaxData,
                success: function(response) {
                    console.log('AJAX success response:', response);
                    if (response.status === 'success') {
                        updatePracticeBooksView(response.data, subject);
                    } else {
                        showError(response.message || 'Failed to load practice books data');
                    }
                    $('#booksChartLoading').hide();
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error:', xhr, status, error);
                    showError('Error loading practice books data: ' + error);
                    $('#booksChartLoading').hide();
                }
            });

            // Hide main dashboard and show books dashboard
            $('#mainDashboard').hide();
            $('#booksDashboard').show();
        }

        // Update Practice Books View with data
        function updatePracticeBooksView(data, subject) {
            // Check if there's any data to display
            if (!data || !data.bookData || !data.bookData.labels || data.bookData.labels.length === 0) {
                // Hide chart and table
                $('#booksChart').closest('.chart-container').hide();
                $('#booksTable').closest('.table-responsive').hide();
                // Show no data message
                $('#noBooksDataMsg').show();
                return;
            } else {
                // Show chart and table
                $('#booksChart').closest('.chart-container').show();
                $('#booksTable').closest('.table-responsive').show();
                // Hide no data message
                $('#noBooksDataMsg').hide();
            }

            // Store book details for chart click handling
            currentPracticeBookDetails = data.bookDetails;

            // Update Books Chart
            updatePracticeBooksChart(data.bookData);

            // Update Books Table
            updatePracticeBooksTable(data.bookDetails);
        }

        // Update Practice Books Chart
        function updatePracticeBooksChart(bookData) {
            const ctx = document.getElementById('booksChart').getContext('2d');

            // Destroy existing chart if it exists
            if (booksChart) {
                booksChart.destroy();
            }

            // Create new chart
            booksChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: bookData.labels,
                    datasets: [{
                        data: bookData.data,
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Your Performance by Book',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = bookData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    onClick: function(event, elements) {
                        if (elements && elements.length > 0) {
                            const index = elements[0].index;
                            const bookId = currentPracticeBookDetails[index].id;
                            const bookTitle = bookData.labels[index];
                            drillDownToPracticeChapters(bookId, bookTitle);
                        }
                    }
                }
            });
        }

        // Update Practice Books Table
        function updatePracticeBooksTable(bookDetails) {
            const tableBody = $('#booksTable tbody');
            tableBody.empty();

            if (bookDetails && bookDetails.length > 0) {
                const total = bookDetails.reduce((sum, book) => sum + book.count, 0);

                bookDetails.forEach(book => {
                    const percentage = Math.round((book.count / total) * 100);
                    const row = document.createElement('tr');

                    // Make the row clickable
                    row.style.cursor = 'pointer';

                    const titleCell = document.createElement('td');
                    titleCell.textContent = book.title;
                    row.appendChild(titleCell);

                    const countCell = document.createElement('td');
                    countCell.textContent = book.count;
                    row.appendChild(countCell);

                    const percentageCell = document.createElement('td');
                    percentageCell.textContent = percentage + '%';
                    row.appendChild(percentageCell);

                    const actionCell = document.createElement('td');
                    const viewButton = document.createElement('button');
                    viewButton.className = 'btn btn-sm btn-primary';
                    viewButton.textContent = 'View Chapters';
                    viewButton.addEventListener('click', function() {
                        drillDownToPracticeChapters(book.id, book.title);
                    });
                    actionCell.appendChild(viewButton);
                    row.appendChild(actionCell);

                    // Add click event to the row
                    row.addEventListener('click', function(e) {
                        // Only trigger if the click wasn't on the button
                        if (e.target.tagName !== 'BUTTON') {
                            drillDownToPracticeChapters(book.id, book.title);
                        }
                    });

                    tableBody.append(row);
                });
            }
        }

        // Drill down from book to chapters for practice data
        function drillDownToPracticeChapters(bookId, bookTitle) {
            // Save the current book for reference
            currentPracticeBookId = bookId;
            currentPracticeBookTitle = bookTitle;

            // Update the header in the chapters dashboard
            $('#selectedBook').text(bookTitle);

            // Show loading overlay
            $('#chaptersChartLoading').show();

            // Get date range and batch ID from filters
            const fromDate = $('#fromDate').val();
            const toDate = $('#toDate').val();
            const batchId = $('#batchFilter').val();

            // Make AJAX call to get practice chapters data
            const url = '<g:createLink controller="aireport" action="getPracticeChaptersByBook"/>';
            console.log('AJAX URL for chapters:', url);

            const ajaxData = {
                fromDate: fromDate,
                toDate: toDate,
                batchId: batchId,
                bookId: bookId
            };
            console.log('AJAX data for chapters:', ajaxData);

            $.ajax({
                url: url,
                data: ajaxData,
                success: function(response) {
                    console.log('AJAX success response for chapters:', response);
                    if (response.status === 'success') {
                        updatePracticeChaptersView(response.data);
                    } else {
                        showError(response.message || 'Failed to load practice chapters data');
                    }
                    $('#chaptersChartLoading').hide();
                },
                error: function(xhr, status, error) {
                    console.error('AJAX error for chapters:', xhr, status, error);
                    showError('Error loading practice chapters data: ' + error);
                    $('#chaptersChartLoading').hide();
                }
            });

            // Hide books dashboard and show chapters dashboard
            $('#booksDashboard').hide();
            $('#chaptersDashboard').show();
        }

        // Update Practice Chapters View with data
        function updatePracticeChaptersView(data) {
            // Check if there's any data to display
            if (!data || !data.chapterData || !data.chapterData.labels || data.chapterData.labels.length === 0) {
                // Hide chart and table
                $('#chaptersChart').closest('.chart-container').hide();
                $('#chaptersTable').closest('.table-responsive').hide();
                // Show no data message
                $('#noChaptersDataMsg').show();
                return;
            } else {
                // Show chart and table
                $('#chaptersChart').closest('.chart-container').show();
                $('#chaptersTable').closest('.table-responsive').show();
                // Hide no data message
                $('#noChaptersDataMsg').hide();
            }

            // Update Chapters Chart
            updatePracticeChaptersChart(data.chapterData);

            // Update Chapters Table
            updatePracticeChaptersTable(data.chapterDetails);
        }

        // Update Practice Chapters Chart
        function updatePracticeChaptersChart(chapterData) {
            const ctx = document.getElementById('chaptersChart').getContext('2d');

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (chaptersChart && typeof chaptersChart.destroy === 'function') {
                chaptersChart.destroy();
            }

            // Create new chart
            chaptersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: chapterData.labels,
                    datasets: [{
                        label: 'Performance',
                        data: chapterData.data,
                        backgroundColor: 'rgba(75, 192, 192, 0.7)',
                        borderColor: 'rgba(75, 192, 192, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Your Performance by Chapter',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw || 0;
                                    const total = chapterData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return 'Performance: ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Score'
                            }
                        }
                    }
                }
            });
        }

        // Update Practice Chapters Table
        function updatePracticeChaptersTable(chapterDetails) {
            const tableBody = $('#chaptersTable tbody');
            tableBody.empty();

            if (chapterDetails && chapterDetails.length > 0) {
                const total = chapterDetails.reduce((sum, chapter) => sum + chapter.count, 0);

                chapterDetails.forEach(chapter => {
                    const percentage = Math.round((chapter.count / total) * 100);
                    const row = document.createElement('tr');

                    const nameCell = document.createElement('td');
                    nameCell.textContent = chapter.name;
                    row.appendChild(nameCell);

                    const countCell = document.createElement('td');
                    countCell.textContent = chapter.count;
                    row.appendChild(countCell);

                    const percentageCell = document.createElement('td');
                    percentageCell.textContent = percentage + '%';
                    row.appendChild(percentageCell);

                    tableBody.append(row);
                });
            }
        }

        // End of dashboard script

        // Function to show books for a specific subject
        function showBooksBySubject(subject) {
            // Just call our drill-down function
            drillDownToBooks(subject);
        }

        // Function to show chapters for a specific book
        function showChaptersByBook(bookId, bookTitle) {
            // Just call our drill-down function
            drillDownToChapters(bookId, bookTitle);
        }

        // Function to update the books view
        function updateBooksView(data) {
            // Check if there's any data to display
            if (!data || !data.bookData || !data.bookData.labels || data.bookData.labels.length === 0) {
                // Hide charts container
                $('#booksDashboard .row').hide();
                // Show no data message
                $('#noBooksDataMsg').show();
                return;
            } else {
                // Show charts container
                $('#booksDashboard .row').show();
                // Hide no data message
                $('#noBooksDataMsg').hide();
            }

            // Update books chart
            updateBooksChart(data.bookData);

            // Update books table
            updateBooksTable(data.bookDetails);
        }

        // Function to update the chapters view
        function updateChaptersView(data) {
            // Check if there's any data to display
            if (!data || !data.chapterData || !data.chapterData.labels || data.chapterData.labels.length === 0) {
                // Hide charts container
                $('#chaptersDashboard .row').hide();
                // Show no data message
                $('#noChaptersDataMsg').show();
                return;
            } else {
                // Show charts container
                $('#chaptersDashboard .row').show();
                // Hide no data message
                $('#noChaptersDataMsg').hide();
            }

            // Update chapters chart
            updateChaptersChart(data.chapterData);

            // Update chapters table
            updateChaptersTable(data.chapterDetails);
        }

        // Function to update the books chart
        function updateBooksChart(bookData) {
            const ctx = document.getElementById('booksChart').getContext('2d');

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (booksChart && typeof booksChart.destroy === 'function') {
                booksChart.destroy();
            }

            // Create new chart
            booksChart = new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: bookData.labels,
                    datasets: [{
                        data: bookData.data,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(255, 206, 86, 0.7)',
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(153, 102, 255, 0.7)',
                            'rgba(255, 159, 64, 0.7)',
                            'rgba(199, 199, 199, 0.7)',
                            'rgba(83, 102, 255, 0.7)',
                            'rgba(40, 159, 64, 0.7)',
                            'rgba(210, 199, 199, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Books You\'ve Explored',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            position: 'right'
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = bookData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    }
                }
            });
        }

        // Function to update the chapters chart
        function updateChaptersChart(chapterData) {
            const ctx = document.getElementById('chaptersChart').getContext('2d');

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (chaptersChart && typeof chaptersChart.destroy === 'function') {
                chaptersChart.destroy();
            }

            // Create new chart
            chaptersChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: chapterData.labels,
                    datasets: [{
                        label: 'Interactions',
                        data: chapterData.data,
                        backgroundColor: 'rgba(54, 162, 235, 0.7)',
                        borderColor: 'rgba(54, 162, 235, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Chapters You\'ve Explored',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const value = context.raw || 0;
                                    const total = chapterData.total;
                                    const percentage = Math.round((value / total) * 100);
                                    return 'Count: ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        }
                    }
                }
            });
        }

        // Function to update the books table
        function updateBooksTable(bookDetails) {
            const tableBody = $('#booksTable tbody');
            tableBody.empty();

            bookDetails.forEach(function(book) {
                const row = document.createElement('tr');

                const titleCell = document.createElement('td');
                titleCell.textContent = book.title;
                row.appendChild(titleCell);

                const countCell = document.createElement('td');
                countCell.textContent = book.count;
                row.appendChild(countCell);

                const percentageCell = document.createElement('td');
                const percentage = Math.round((book.count / bookDetails.reduce((sum, b) => sum + b.count, 0)) * 100);
                percentageCell.textContent = percentage + '%';
                row.appendChild(percentageCell);

                const actionCell = document.createElement('td');
                const viewButton = document.createElement('button');
                viewButton.className = 'btn btn-sm btn-primary';
                viewButton.textContent = 'View Chapters';
                viewButton.onclick = function() {
                    showChaptersByBook(book.id, book.title);
                };
                actionCell.appendChild(viewButton);
                row.appendChild(actionCell);

                tableBody.append(row);
            });
        }

        // Function to update the chapters table
        function updateChaptersTable(chapterDetails) {
            const tableBody = $('#chaptersTable tbody');
            tableBody.empty();

            chapterDetails.forEach(function(chapter) {
                const row = document.createElement('tr');

                const nameCell = document.createElement('td');
                nameCell.textContent = chapter.name;
                row.appendChild(nameCell);

                const countCell = document.createElement('td');
                countCell.textContent = chapter.count;
                row.appendChild(countCell);

                const percentageCell = document.createElement('td');
                const percentage = Math.round((chapter.count / chapterDetails.reduce((sum, c) => sum + c.count, 0)) * 100);
                percentageCell.textContent = percentage + '%';
                row.appendChild(percentageCell);

                tableBody.append(row);
            });
        }

        // Function to update the breadcrumb navigation
        function updateBreadcrumb(type, value) {
            if (type === 'subject') {
                $('#breadcrumbNavigation').html(
                    '<div class="breadcrumb-item"><a href="#" id="breadcrumbHome">Dashboard</a></div>' +
                    '<div class="breadcrumb-item active">Subject: ' + value + '</div>'
                );

                // Add click handler for breadcrumb home
                $('#breadcrumbHome').click(function(e) {
                    e.preventDefault();
                    $('#booksDashboard').hide();
                    $('#mainDashboard').show();
                    $('#breadcrumbNavigation').html('<div class="breadcrumb-item active">Dashboard</div>');
                });
            } else if (type === 'book') {
                $('#breadcrumbNavigation').html(
                    '<div class="breadcrumb-item"><a href="#" id="breadcrumbHome">Dashboard</a></div>' +
                    '<div class="breadcrumb-item"><a href="#" id="breadcrumbSubject">Subject: ' + currentSubject + '</a></div>' +
                    '<div class="breadcrumb-item active">Book: ' + value + '</div>'
                );

                // Add click handlers for breadcrumb links
                $('#breadcrumbHome').click(function(e) {
                    e.preventDefault();
                    $('#chaptersDashboard').hide();
                    $('#booksDashboard').hide();
                    $('#mainDashboard').show();
                    $('#breadcrumbNavigation').html('<div class="breadcrumb-item active">Dashboard</div>');
                });

                $('#breadcrumbSubject').click(function(e) {
                    e.preventDefault();
                    $('#chaptersDashboard').hide();
                    $('#booksDashboard').show();
                    updateBreadcrumb('subject', currentSubject);
                });
            }
        }

        // Make subject cells clickable in the subject table
        function makeSubjectCellsClickable() {
            const subjectTable = $('#subjectTable tbody');

            // Remove existing click handlers
            subjectTable.find('tr').off('click');

            // Add click handlers to each row
            subjectTable.find('tr').each(function() {
                const subject = $(this).find('td:first').text();
                $(this).find('td:first').addClass('clickable').data('subject', subject);
                $(this).find('td:first').click(function() {
                    showBooksBySubject(subject);
                });
            });
        }

        // Call this function after updating the subject table
        $(document).on('DOMSubtreeModified', '#subjectTable tbody', function() {
            makeSubjectCellsClickable();
        });

        // Load learning progress data
        function loadLearningProgressData() {
            // Show loading overlay
            $('.loading-overlay').show();

            // Hide any previous error messages
            $('#errorMessage').hide();

            // Get filter values
            const toDate = $('#toDate').val();
            const days = $('#progressPeriod').val();
            const batchId = $('#batchFilter').val() || '';

            // Load progress data
            $.ajax({
                url: '<g:createLink controller="aireport" action="getLearningProgress"/>',
                data: {
                    toDate: toDate,
                    days: days,
                    batchId: batchId,
                    showReport: $('#demoModeToggle').is(':checked') ? 'demo' : null
                },
                success: function(response) {
                    if (response.status === 'success') {
                        updateProgressDashboard(response.data);
                    } else {
                        showError(response.message || 'Failed to load progress data');
                    }
                    $('.loading-overlay').hide();
                },
                error: function(xhr, status, error) {
                    showError('Error loading progress data: ' + error);
                    $('.loading-overlay').hide();
                }
            });
        }

        // Update Progress Dashboard
        function updateProgressDashboard(progressData) {
            // Check if we have the new structure (demo data) or old structure
            if (progressData.overallAccuracy) {
                // New structure (demo data)
                // Update accuracy gauge
                updateAccuracyGauge(
                    progressData.overallAccuracy.current,
                    progressData.overallAccuracy.previous,
                    progressData.overallAccuracy.change
                );

                // Update activity chart
                updateActivityChart({
                    currentPeriodCount: progressData.activityLevel.current,
                    previousPeriodCount: progressData.activityLevel.previous,
                    percentChange: progressData.activityLevel.percentChange,
                    trend: progressData.activityLevel.trend
                });

                // Update learning summary
                updateLearningSummary({
                    subjectProgress: progressData.subjectProgress
                });

                // Update subject progress table
                updateSubjectProgressTable(progressData.subjectProgress);
            } else {
                // Old structure (real data)
                // Update accuracy gauge
                updateAccuracyGauge(progressData.currentAccuracy, progressData.previousAccuracy, progressData.accuracyChange);

                // Update activity chart
                updateActivityChart(progressData.activityChange);

                // Update learning summary
                updateLearningSummary(progressData);

                // Update subject progress table
                updateSubjectProgressTable(progressData.subjectProgress);
            }
        }

        // Update Accuracy Gauge
        function updateAccuracyGauge(currentAccuracy, previousAccuracy, change) {
            const gaugeElement = document.getElementById('accuracyGauge');

            // Handle null values and ensure they are numbers
            let safeCurrentAccuracy = currentAccuracy != null ? Number(currentAccuracy) : 0;
            let safePreviousAccuracy = previousAccuracy != null ? Number(previousAccuracy) : 0;
            let safeChange = change != null ? Number(change) : 0;

            // Ensure they are valid numbers
            if (isNaN(safeCurrentAccuracy)) safeCurrentAccuracy = 0;
            if (isNaN(safePreviousAccuracy)) safePreviousAccuracy = 0;
            if (isNaN(safeChange)) safeChange = 0;

            // Create gauge chart
            const data = {
                labels: ['Current', 'Previous'],
                datasets: [{
                    data: [safeCurrentAccuracy, safePreviousAccuracy],
                    backgroundColor: ['rgba(75, 192, 192, 0.7)', 'rgba(201, 203, 207, 0.7)'],
                    borderWidth: 0
                }]
            };

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (window.accuracyGaugeChart && typeof window.accuracyGaugeChart.destroy === 'function') {
                window.accuracyGaugeChart.destroy();
            }

            // Create new chart
            window.accuracyGaugeChart = new Chart(gaugeElement, {
                type: 'doughnut',
                data: data,
                options: {
                    cutout: '70%',
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return context.label + ': ' + context.raw + '%';
                                }
                            }
                        }
                    }
                }
            });

            // Add center text
            const accuracyChangeElement = document.getElementById('accuracyChange');
            let changeClass = 'text-info';
            let changeIcon = '';

            if (safeChange > 0) {
                changeClass = 'text-success';
                changeIcon = '<i class="fas fa-arrow-up"></i> ';
            } else if (safeChange < 0) {
                changeClass = 'text-danger';
                changeIcon = '<i class="fas fa-arrow-down"></i> ';
            }

            // Use string concatenation instead of template literals to avoid GSP conflicts
            accuracyChangeElement.innerHTML =
                '<div class="h5 mb-0">' + safeCurrentAccuracy.toFixed(1) + '%</div>' +
                '<div class="' + changeClass + ' small">' +
                    changeIcon + Math.abs(safeChange).toFixed(1) + '%' +
                '</div>';
        }

        // Update Activity Chart
        function updateActivityChart(activityChange) {
            const chartElement = document.getElementById('activityChart');

            // Handle null values
            const safeActivityChange = activityChange || {
                currentPeriodCount: 0,
                previousPeriodCount: 0,
                percentChange: 0,
                trend: 'Stable'
            };

            // Ensure numeric values are valid numbers
            let safeCurrentPeriodCount = Number(safeActivityChange.currentPeriodCount || 0);
            let safePreviousPeriodCount = Number(safeActivityChange.previousPeriodCount || 0);
            let safePercentChange = Number(safeActivityChange.percentChange || 0);

            if (isNaN(safeCurrentPeriodCount)) safeCurrentPeriodCount = 0;
            if (isNaN(safePreviousPeriodCount)) safePreviousPeriodCount = 0;
            if (isNaN(safePercentChange)) safePercentChange = 0;

            // Create bar chart
            const data = {
                labels: ['Current Period', 'Previous Period'],
                datasets: [{
                    label: 'Questions Attempted',
                    data: [safeCurrentPeriodCount, safePreviousPeriodCount],
                    backgroundColor: ['rgba(54, 162, 235, 0.7)', 'rgba(201, 203, 207, 0.7)'],
                    borderWidth: 1
                }]
            };

            // Destroy existing chart if it exists and is a valid Chart.js instance
            if (window.activityChart && typeof window.activityChart.destroy === 'function') {
                window.activityChart.destroy();
            }

            // Create new chart
            window.activityChart = new Chart(chartElement, {
                type: 'bar',
                data: data,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Questions'
                            }
                        }
                    }
                }
            });

            // Update activity change text
            const activityChangeElement = document.getElementById('activityChange');
            let changeClass = 'text-info';
            let changeIcon = '';

            if (safePercentChange > 0) {
                changeClass = 'text-success';
                changeIcon = '<i class="fas fa-arrow-up"></i> ';
            } else if (safePercentChange < 0) {
                changeClass = 'text-danger';
                changeIcon = '<i class="fas fa-arrow-down"></i> ';
            }

            // Use string concatenation instead of template literals to avoid GSP conflicts
            activityChangeElement.innerHTML =
                '<div class="h5 mb-0">' + safeCurrentPeriodCount + '</div>' +
                '<div class="' + changeClass + ' small">' +
                    changeIcon + Math.abs(safePercentChange).toFixed(1) + '%' +
                '</div>';
        }

        // Update Learning Summary
        function updateLearningSummary(progressData) {
            const summaryElement = document.getElementById('learningSummary');

            // Handle null values
            const safeProgressData = progressData || { subjectProgress: [] };
            const safeSubjectProgress = safeProgressData.subjectProgress || [];

            // Count subjects by status
            const statusCounts = {
                'Significant Improvement': 0,
                'Improving': 0,
                'Stable': 0,
                'Needs Focus': 0,
                'New': 0
            };

            safeSubjectProgress.forEach(subject => {
                statusCounts[subject.status]++;
            });

            // Create summary HTML
            let summaryHTML = '<ul class="list-group list-group-flush">';

            if (statusCounts['Significant Improvement'] > 0) {
                summaryHTML +=
                    '<li class="list-group-item d-flex justify-content-between align-items-center">' +
                        'Subjects with significant improvement' +
                        '<span class="badge bg-success rounded-pill">' + statusCounts['Significant Improvement'] + '</span>' +
                    '</li>';
            }

            if (statusCounts['Improving'] > 0) {
                summaryHTML +=
                    '<li class="list-group-item d-flex justify-content-between align-items-center">' +
                        'Subjects showing improvement' +
                        '<span class="badge bg-info rounded-pill">' + statusCounts['Improving'] + '</span>' +
                    '</li>';
            }

            if (statusCounts['Stable'] > 0) {
                summaryHTML +=
                    '<li class="list-group-item d-flex justify-content-between align-items-center">' +
                        'Subjects with stable performance' +
                        '<span class="badge bg-secondary rounded-pill">' + statusCounts['Stable'] + '</span>' +
                    '</li>';
            }

            if (statusCounts['Needs Focus'] > 0) {
                summaryHTML +=
                    '<li class="list-group-item d-flex justify-content-between align-items-center">' +
                        'Subjects needing focus' +
                        '<span class="badge bg-danger rounded-pill">' + statusCounts['Needs Focus'] + '</span>' +
                    '</li>';
            }

            if (statusCounts['New'] > 0) {
                summaryHTML +=
                    '<li class="list-group-item d-flex justify-content-between align-items-center">' +
                        'Newly explored subjects' +
                        '<span class="badge bg-primary rounded-pill">' + statusCounts['New'] + '</span>' +
                    '</li>';
            }

            summaryHTML += '</ul>';
            summaryElement.innerHTML = summaryHTML;
        }

        // Update Subject Progress Table
        function updateSubjectProgressTable(subjectProgress) {
            const tableBody = $('#subjectProgressTable tbody');
            tableBody.empty();

            // Handle null values
            const safeSubjectProgress = subjectProgress || [];

            if (safeSubjectProgress.length > 0) {
                safeSubjectProgress.forEach(subject => {
                    const row = document.createElement('tr');

                    // Handle null values for subject properties
                    const safeSubject = subject || {};

                    // Ensure numeric values are valid numbers
                    let safeCurrentAccuracy = Number(safeSubject.currentAccuracy || 0);
                    let safePreviousAccuracy = Number(safeSubject.previousAccuracy || 0);
                    let safeImprovement = Number(safeSubject.improvement || 0);

                    if (isNaN(safeCurrentAccuracy)) safeCurrentAccuracy = 0;
                    if (isNaN(safePreviousAccuracy)) safePreviousAccuracy = 0;
                    if (isNaN(safeImprovement)) safeImprovement = 0;

                    const subjectCell = document.createElement('td');
                    subjectCell.textContent = safeSubject.subject || 'Unknown';
                    row.appendChild(subjectCell);

                    const currentAccuracyCell = document.createElement('td');
                    currentAccuracyCell.textContent = safeCurrentAccuracy.toFixed(1) + '%';
                    row.appendChild(currentAccuracyCell);

                    const previousAccuracyCell = document.createElement('td');
                    previousAccuracyCell.textContent = safePreviousAccuracy.toFixed(1) + '%';
                    row.appendChild(previousAccuracyCell);

                    const changeCell = document.createElement('td');
                    const change = safeImprovement;
                    let changeClass = '';
                    let changePrefix = '';

                    if (change > 0) {
                        changeClass = 'text-success';
                        changePrefix = '+';
                    } else if (change < 0) {
                        changeClass = 'text-danger';
                    }

                    changeCell.className = changeClass;
                    changeCell.textContent = changePrefix + change.toFixed(1) + '%';
                    row.appendChild(changeCell);

                    const statusCell = document.createElement('td');
                    let textColorClass = '';

                    switch (safeSubject.status || 'Stable') {
                        case 'Significant Improvement':
                            textColorClass = 'text-success';
                            break;
                        case 'Improving':
                            textColorClass = 'text-info';
                            break;
                        case 'Stable':
                            textColorClass = 'text-secondary';
                            break;
                        case 'Needs Focus':
                            textColorClass = 'text-danger';
                            break;
                        case 'New':
                            textColorClass = 'text-primary';
                            break;
                    }

                    statusCell.className = textColorClass;
                    statusCell.textContent = safeSubject.status || 'Stable';
                    row.appendChild(statusCell);

                    tableBody.append(row);
                });
            } else {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 5;
                cell.textContent = 'No subject progress data available';
                cell.className = 'text-center';
                row.appendChild(cell);
                tableBody.append(row);
            }
        }

        // Add event listener for progress period change
        $('#progressPeriod').change(function() {
            loadLearningProgressData();
        });

        // This function is called from the document ready handler above
    </script>
</body>
</html>
