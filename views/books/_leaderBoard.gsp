<div class="row mx-0">
    <div class="leaderboard-info col-12 col-lg-8 mt-3">

        <div id="leaderBoardTabs" class="d-none justify-content-center leaderboard-tabs col-12 mt-3">
            <a href="javascript:" class="btn btn-lg instituteTab active">INSTITUTE</a>
            <a href="javascript:" class="btn btn-lg allIndiaTab">ALL INDIA</a>
        </div>

        <div class="card card-modifier card-shadow border-0 p-3 p-md-4 p-lg-5">

            <div class="filter-wrapper mb-3">
                <div class="d-flex position-relative">
                    <a href="#" class="col daily-tab active">Daily</a>
                    <a href="#" class="col weekly-tab">Weekly</a>
                    <a href="#" class="col monthly-tab">Monthly</a>
                    <span class="active-tab-bg"></span>
                </div>
            </div>

            <div id="calendarFilter" class="calendar_wrapper days_calendar d-block">
                <div class="row m-0 justify-content-center" id="selectedCurrentDate">
                    <a href="javascript:selectMonth();" class="d-flex align-items-end pb-2 border-bottom">
                        <i class="material-icons-round pr-1">date_range</i>
                        <span id="selectedMonthYear"></span>
                        <i class="material-icons-round pl-1">keyboard_arrow_down</i>
                    </a>
                </div>
            </div>

            <div class="leaderboard-top mt-5">
                <div id="leaderboardTopList" class="d-none justify-content-center align-items-end col-12 col-md-10 col-lg-12 col-xl-10 px-0 mx-auto">
                    <div class="top-rank second col text-center">
                        <figure>
                            <img loading="lazy" id="secondImg" src="${assetPath(src: 'wonderslate/avatar.webp')}" width="100" height="100" alt="User Avatar Image" class="rounded-circle">
                            <span class="rank-badge"><img loading="lazy" src="${assetPath(src: 'wonderslate/rank-second.svg')}" width="40" height="40" alt="Rank Badge"></span>
                        </figure>
                        <figcaption id="secondUserDetail">
                            <p>Mahima</p>
                            <h5>1196</h5>
                        </figcaption>
                    </div>
                    <div class="top-rank first px-0 col text-center">
                        <figure>
                            <span class="crown-img"><img loading="lazy" src="${assetPath(src: 'wonderslate/crown.png')}" width="40" height="40" alt="Crown"></span>
                            <img loading="lazy" id="firstImg" src="${assetPath(src: 'wonderslate/avatar.webp')}" width="125" height="125" alt="User Avatar Image" class="rounded-circle">
                            <span class="rank-badge"><img loading="lazy" src="${assetPath(src: 'wonderslate/rank-first.svg')}" width="40" height="40" alt="Rank Badge"></span>
                        </figure>
                        <figcaption id="firstUserDetail">
                            <p>Ashutosh Kumar</p>
                            <h5>1970</h5>
                        </figcaption>
                    </div>
                    <div class="top-rank third col text-center">
                        <figure>
                            <img loading="lazy" id="thirdImg" src="${assetPath(src: 'wonderslate/avatar.webp')}" width="100" height="100" alt="User Avatar Image" class="rounded-circle">
                            <span class="rank-badge"><img loading="lazy" src="${assetPath(src: 'wonderslate/rank-third.svg')}" width="40" height="40" alt="Rank Badge"></span>
                        </figure>
                        <figcaption id="thirdUserDetail">
                            <p>Pradip Takur</p>
                            <h5>1010</h5>
                        </figcaption>
                    </div>
                </div>

                <div class="noRanksMsg text-center d-none">
                    <h4>Not many leaders?</h4>
                    <p>Play and top the leaderboard.</p>
                </div>

            </div>

            <hr>

            <div class="leaderboard-list d-none">
                <table class="table">
                    <thead class="text-white bg-dark">
                    <tr>
                        <th class="text-center">RANK</th>
                        <th>NAME</th>
                        <th class="text-center">POINTS</th>
                    </tr>
                    </thead>
                    <tbody id="leaderboardNextList">
                    <tr>
                        <td class="user-rank text-center"><strong>4</strong></td>
                        <td class="user-info d-flex align-items-center">
                            <span class="avatar"><img loading="lazy" src="${assetPath(src: 'wonderslate/avatar.webp')}" width="60" height="60" alt="User Avatar Image" class="rounded-circle"></span>
                            <span class="name col pl-2 pr-0">Aditya Pal <br> <small>Delhi (NCT)</small></span>
                        </td>
                        <td class="user-points text-center">811</td>
                    </tr>
                    <tr></tr>
                    <tr class="current-rank">
                        <td class="user-rank text-center"><strong>4</strong></td>
                        <td class="user-info d-flex align-items-center">
                            <span class="avatar"><img loading="lazy" src="${assetPath(src: 'wonderslate/avatar.webp')}" width="60" height="60" alt="User Avatar Image" class="rounded-circle"></span>
                            <span class="name col pl-2 pr-0">Aditya Pal <br> <small>Delhi (NCT)</small></span>
                        </td>
                        <td class="user-points text-center">811</td>
                    </tr>
                    <tr></tr>
                    <tr>
                        <td class="user-rank text-center"><strong>4</strong></td>
                        <td class="user-info d-flex align-items-center">
                            <span class="avatar"><img loading="lazy" src="${assetPath(src: 'wonderslate/avatar.webp')}" width="60" height="60" alt="User Avatar Image" class="rounded-circle"></span>
                            <span class="name col pl-2 pr-0">Aditya Pal <br> <small>Delhi (NCT)</small></span>
                        </td>
                        <td class="user-points text-center">811</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="leaderboard-sidebar col-12 col-lg-4">
        <h6>Take Daily Tests & Get on the Leaderboard</h6>
        <a href="/prepjoy/dailyTest" class="card card-modifier card-shadow p-3 mb-4 text-dark align-items-center flex-row">
            <span class="col pl-2">Daily Test <br><small>Competitive Exams, General, Engineering, etc.</small></span>
            <i class="material-icons-round">chevron_right</i>
        </a>
        <h6>Others</h6>
        <div class="d-flex d-lg-block others">
            <%if(!"true".equals(session["prepjoySite"])){%>
            <sec:ifLoggedIn>
                <a href="/prepjoy/quizAnalytics" class="card card-modifier card-shadow p-3 mb-3 text-dark align-items-center col flex-row">
            </sec:ifLoggedIn>
            <sec:ifNotLoggedIn>
                <a href="javascript:loginOpen();" class="card card-modifier card-shadow p-3 mb-3 text-dark align-items-center col flex-row">
            </sec:ifNotLoggedIn>
                    <i class="material-icons-round d-lg-none">bar_chart</i>
                    <span class="col pl-2">Analytics <br><small>All your quiz analytics in one place.</small></span>
                    <i class="material-icons-round d-none d-lg-block">chevron_right</i>
                </a>
            <sec:ifLoggedIn>
                <a href="/books/myActivity" class="card card-modifier card-shadow p-3 mb-3 text-dark align-items-center col flex-row">
            </sec:ifLoggedIn>
            <sec:ifNotLoggedIn>
                <a href="javascript:loginOpen();" class="card card-modifier card-shadow p-3 mb-3 text-dark align-items-center col flex-row">
            </sec:ifNotLoggedIn>
                    <i class="material-icons-round d-lg-none">timeline</i>
                    <span class="col pl-2">Learning History <br><small>All your learning history here.</small></span>
                    <i class="material-icons-round d-none d-lg-block">chevron_right</i>
                </a>
            <% }%>
            <a href="/ebooks" class="card card-modifier card-shadow p-3 mb-3 text-dark align-items-center col flex-row" id="lEbooks">
                <i class="material-icons-round d-lg-none">local_library</i>
                <span class="col pl-2">eBooks <br><small>World's No 1 smart eBooks store.</small></span>
                <i class="material-icons-round d-none d-lg-block">chevron_right</i>
            </a>
        </div>
    </div>
</div>

<div id="datePickerModal" class="modal modal-modifier fade">
    <div class="modal-dialog modal-dialog-centered modal-dialog-zoom" role="document">
        <div class="modal-content modal-content-modifier">
            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier text-center">
                <div class="calendar_wrapper">
                    <div id="datePicker"></div>
                </div>
            </div>

        </div>
    </div>
</div>
<%if("true".equals(session["prepjoySite"])){%>
<g:render template="/prepjoy/prepjoy-loader"></g:render>
<% } %>
<script>
    //REQUIRED VARIABLES
    var siteId = '${session['siteId']}';
    var prepjoySite = "${session['prepjoySite']}";

    var currentUserId ='';
    <sec:ifLoggedIn>
    currentUserId = "${session['userdetails'].id}";
    </sec:ifLoggedIn>

    var instituteId = "${session['instituteId']}";
    var currentDate = new Date().toISOString().split("T")[0];
    var allIndiaTabElement = document.querySelector('.allIndiaTab');
    var instituteTabElement = document.querySelector('.instituteTab');
    var dailyTabElement = document.querySelector('.daily-tab');
    var weeklyTabElement = document.querySelector('.weekly-tab');
    var monthlyTabElement = document.querySelector('.monthly-tab');
    var allIndiaTab = true;
    var currentTab;

    if(instituteId != "" && instituteId != null && instituteId != "null" && instituteId != undefined) {
        document.getElementById('leaderBoardTabs').classList.add('d-flex');
        document.getElementById('leaderBoardTabs').classList.remove('d-none');
        allIndiaTab = false;
    }

    allIndiaTabElement.addEventListener('click',function (e){
        e.preventDefault();
        allIndiaTab = true;
        if (dailyTabElement.classList.contains('active')){
            getDailyRank();
        }else if (weeklyTabElement.classList.contains('active')){
            getWeeklyRank();
        }else if (monthlyTabElement.classList.contains('active')){
            getMonthlyRank();
        }
    });

    instituteTabElement.addEventListener('click',function (e){
        e.preventDefault();
        allIndiaTab=false;
        if (dailyTabElement.classList.contains('active')){
            getDailyRank();
        }else if (weeklyTabElement.classList.contains('active')){
            getWeeklyRank();
        }else if (monthlyTabElement.classList.contains('active')){
            getMonthlyRank();
        }
    });


    dailyTabElement.addEventListener('click',function (e){
        e.preventDefault();
        document.getElementById("selectedMonthYear").innerHTML = today;
        document.getElementById('calendarFilter').classList.add('d-block');
        document.getElementById('calendarFilter').classList.remove('d-none');
        currentDate = new Date().toISOString().split("T")[0];
        getDailyRank();
    });
    weeklyTabElement.addEventListener('click',function (e){
        e.preventDefault();
        document.getElementById('calendarFilter').classList.remove('d-block');
        document.getElementById('calendarFilter').classList.add('d-none');
        currentDate = new Date().toISOString().split("T")[0];
        getWeeklyRank();
    });
    monthlyTabElement.addEventListener('click',function (e){
        e.preventDefault();
        document.getElementById('calendarFilter').classList.remove('d-block');
        document.getElementById('calendarFilter').classList.add('d-none');
        currentDate = new Date().toISOString().split("T")[0];
        getMonthlyRank();
    });



    function getDailyRank(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        var serviceAction;
        var serviceParams;

        if (allIndiaTab){
            serviceAction = 'getPrepjoyDailyRanks';
            serviceParams = 'rankDate='+currentDate+'&siteId='+siteId;
        }else{
            serviceAction = 'getPrepjoyDailyRanksInstitute';
            serviceParams = 'rankDate='+currentDate+'&instituteId='+instituteId;
        }

        <g:remoteFunction controller="prepjoy" action="'+serviceAction+'" params="serviceParams" onSuccess="rankUI(data)" />
    }
    function getWeeklyRank(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        var serviceAction;
        var serviceParams;

        if (allIndiaTab){
            serviceAction = 'getPrepjoyWeeklyRanks';
            serviceParams = 'rankDate='+currentDate+'&siteId='+siteId;
        }else{
            serviceAction = 'getPrepjoyWeeklyRanksInstitute';
            serviceParams = 'rankDate='+currentDate+'&instituteId='+instituteId;
        }

        <g:remoteFunction controller="prepjoy" action="'+serviceAction+'" params="serviceParams" onSuccess="rankUI(data)" />
    }
    function getMonthlyRank(){
        if (!prepjoySite){
            $('.loading-icon').removeClass('hidden');
        }else{
            $('#loading').show();
        }
        var serviceAction;
        var serviceParams;

        if (allIndiaTab){
            serviceAction = 'getPrepjoyMonthlyRanks';
            serviceParams = 'rankDate='+currentDate+'&siteId='+siteId;
        }else{
            serviceAction = 'getPrepjoyMonthlyRanksInstitute';
            serviceParams = 'rankDate='+currentDate+'&instituteId='+instituteId;
        }

        <g:remoteFunction controller="prepjoy" action="'+serviceAction+'" params="serviceParams" onSuccess="rankUI(data)" />
    }

    getDailyRank();
    function rankUI(data){
        if (!prepjoySite){
            $('.loading-icon').addClass('hidden');
        }else{
            $('#loading').hide();
        }
        if (allIndiaTab){
            if (data !="No Ranks"){
                var topRank = JSON.parse(data).slice(0,3);
                var nextRank = JSON.parse(data).slice(3,10);
            }else{
                var topRank = "";
                var nextRank = "";
            }
        }else{
            if (data.institutionranks !="No Ranks"){
                var topRank = JSON.parse(data.institutionranks).slice(0,3);
                var nextRank = JSON.parse(data.institutionranks).slice(3,10);
            }else{
                var topRank = "";
                var nextRank = "";
            }
        }



        var nextRankHtml = "";

        var [a,b,c] = topRank;

        var allRanks = topRank.concat(nextRank);

        if (topRank!="" && allRanks.length >= 4){
            if (a.profilePic !="" && a.profilePic!=undefined && a.profilePic!=null){
                document.getElementById('firstImg').setAttribute('src',"/funlearn/showProfileImage?id="+a.userId+"&fileName="+a.profilePic+"&type=user&imgType=passport");
            }else{
                document.getElementById('firstImg').setAttribute('src',"/assets/wonderslate/avatar.webp");
            }
            if (b.profilePic !="" && b.profilePic!=undefined && b.profilePic!=null){
                document.getElementById('secondImg').setAttribute('src',"/funlearn/showProfileImage?id="+b.userId+"&fileName="+b.profilePic+"&type=user&imgType=passport");
            }else{
                document.getElementById('secondImg').setAttribute('src',"/assets/wonderslate/avatar.webp");
            }
            if (c.profilePic !="" && c.profilePic!=undefined && c.profilePic!=null){
                document.getElementById('thirdImg').setAttribute('src',"/funlearn/showProfileImage?id="+c.userId+"&fileName="+c.profilePic+"&type=user&imgType=passport");
            }else{
                document.getElementById('thirdImg').setAttribute('src',"/assets/wonderslate/avatar.webp");
            }

            if (a.state != undefined && a.state != null && a.state != "null" && a.state != "") {
                document.getElementById('firstUserDetail').innerHTML = "<p>"+a.name+"</p><small>"+a.state+"</small><h5>"+a.userPoints+"</h5>";
            } else {
                document.getElementById('firstUserDetail').innerHTML = "<p>"+a.name+"</p><h5>"+a.userPoints+"</h5>";
            }

            if(b.state != undefined && b.state != null && b.state != "null" && b.state != "") {
                document.getElementById('secondUserDetail').innerHTML = "<p>"+b.name+"</p><small>"+b.state+"</small><h5>"+b.userPoints+"</h5>";
            } else {
                document.getElementById('secondUserDetail').innerHTML = "<p>"+b.name+"</p><h5>"+b.userPoints+"</h5>";
            }

            if(c.state != undefined && c.state != null && c.state != "null" && c.state != "") {
                document.getElementById('thirdUserDetail').innerHTML = "<p>"+c.name+"</p><small>"+c.state+"</small><h5>"+c.userPoints+"</h5>";
            } else {
                document.getElementById('thirdUserDetail').innerHTML = "<p>"+c.name+"</p><h5>"+c.userPoints+"</h5>";
            }


            document.querySelector('.leaderboard-list').classList.remove('d-none');
            for(var n=0;n<nextRank.length;n++){
                if (currentUserId==nextRank[n].userId){
                    nextRankHtml +="<tr class='current-rank'>";
                }else{
                    nextRankHtml +="<tr>";
                }
                nextRankHtml +=    "<td class='user-rank text-center'><strong>"+nextRank[n].rank+"</strong></td>"+
                    "<td class='user-info'><div class='d-flex align-items-center'>";
                if (nextRank[n].profilePic !="" && nextRank[n].profilePic!=undefined && nextRank[n].profilePic!=null){
                    nextRankHtml +=  "<span class='avatar'><img loading='lazy' src='/funlearn/showProfileImage?id="+nextRank[n].userId+"&fileName="+nextRank[n].profilePic+"&type=user&imgType=passport' width='60' height='60' alt='User Avatar Image' class='rounded-circle'></span>";
                }else{
                    nextRankHtml +=  "<span class='avatar'><img loading='lazy' src='${assetPath(src: 'wonderslate/avatar.webp')}' width='60' height='60' alt='User Avatar Image' class='rounded-circle'></span>";
                }
                nextRankHtml += "<span class='name col px-2'>"+nextRank[n].name+"<br>";
                if (nextRank[n].state!=undefined && nextRank[n].state!=null && nextRank[n].state!=""){
                    nextRankHtml +=    " <small>"+nextRank[n].state+"</small></span>";
                }
                nextRankHtml +=    "</div></td>"+
                    "<td class='user-points text-center'>"+nextRank[n].userPoints+"</td>"+
                    "</tr>"+
                    "<tr></tr>";
            }

            document.getElementById('leaderboardTopList').classList.add('d-flex');
            document.getElementById('leaderboardTopList').classList.remove('d-none');
            document.querySelector('.noRanksMsg').classList.add('d-none');
            document.querySelector('.noRanksMsg').classList.remove('d-block');
            document.getElementById('leaderboardNextList').innerHTML = nextRankHtml;
        }else{
            document.getElementById('leaderboardTopList').classList.remove('d-flex');
            document.getElementById('leaderboardTopList').classList.add('d-none');
            document.querySelector('.noRanksMsg').classList.remove('d-none');
            document.querySelector('.noRanksMsg').classList.add('d-block');

            document.querySelector('.leaderboard-list').classList.add('d-none');
        }

    }
</script>

<script>
    $(document).ready(function () {
        if(getCookie("firstTimeAccess") != 'true'){
            $('#dailyTestModal').modal('show');
            setCookie("firstTimeAccess",true);
        }

        $(".filter-wrapper a").on("click", function() {
            $(".filter-wrapper a.active").removeClass("active");
            $(this).addClass("active");
        });

        $(".leaderboard-tabs a").on("click", function() {
            $(".leaderboard-tabs a.active").removeClass("active");
            $(this).addClass("active");
        });

    });

    if (!prepjoySite){
        $('#lEbooks').attr('href','/ebooks')
    }else {
        $('#lEbooks').attr('href','/prepjoy/eBooks');
    }

</script>