<g:render template="/funlearn/pnavheader"></g:render>


<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>

<!--<div>-->
<div>
    <div class="container-fluid wplandingblueimage topnavblue">
        <div class="row">
            <div class="col-md-12 text-center">
                <h4 class="whitetext">MY LIBRARY</h4>
            </div>
        </div>

    </div>
    <div class="container-fluid" >
        <div  class='row ' id="bookdtl">
            <div class="col-md-2 wpsidebar"><br>
                <div class="hidden-xs">
                    <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadprofile" id="uploadprofile" action="/creation/uploadprofile" method="post">
                        <input type="hidden" name="source" value="home">
                        <input type="hidden" name="type" value="user">
                        <input type="hidden" name="sourceController" value="funlearn">
                        <div class="row"><div class="col-md-12 text-center">
                            <%if(session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="img-circle" height="150">
                            <%}else{%> <a href="#" ><i class="fa fa-user fa-5x"></i></a><%}%>

                        </div></div>
                        <a href='#' id='popover1' rel='popover1' data-content='Add your profile picture here.'></a>
                    </form>
                    <br>

                    <div></div>
                    <div class="row text-center"><div class="col-md-12">${session['userdetails'].name}</div></div>


                    <hr>

                </div>

            </div>

            <div class='col-md-9 main'>
                <div id="content-books">

                    <div class="row"><br>
                        <div class="col-md-10 col-md-offset-1">
                            <ul class="nav nav-tabs lefttitle">
                                <li class="active"><a data-toggle="tab" href="#subject"><i class="fa fa-tasks fa-x>"></i>&nbsp;&nbsp;ALL BOOKS</a></li>
                                <!--  <li ><a data-toggle="tab" href="#chapters">CHAPTERS</a></li> -->
                                <li><a data-toggle="tab" href="#year"><i class="fa fa-heart-o fa-x>"></i>&nbsp;&nbsp; FAVOURITES</a></li>

                            </ul>
                            <div class="tab-content">
                                <div id="subject" class="tab-pane fade in active"><br>





                                </div>
                                <!--    <div id="chapters" class="tab-pane fade">
                            chapters
                            </div> -->
                                <div id="year" class="tab-pane fade"><br>




                                </div>

                            </div>
                        </div>
                    </div><BR>

                    <br><br>



                    <br>
                </div>



            </div>

        </div>

    </div>
    <div class="push"></div>
</div>



<g:render template="bfooter"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script>

    function changeSem(){

        var htmlStr = "<div class='row'><div class='col-md-12 text-center'><span class='numberCircle'>1</span><span class='numberCircle'>2</span><span class='numberCircle'>3</span>" +
                "<span class='numberCircle selected'><a href=''>4</a></span></div></div> ";

        document.getElementById("semchange").innerHTML = htmlStr;
        $('#semchange').toggle(1000);
    }
    function getBooksList(){

        <g:remoteFunction controller="wonderpublish" action="getBooksListForUser"  onSuccess='mainDisplayBooks(data);'
                params="'status=demovtu'" />
    }

    function mainDisplayBooks(data){
        displayBooks(data,'subject');
        //displayBooks(data,'year');
    }
    function displayBooks(data,booksType){
        var books = data.books;

        var htmlStr="<br><br> <div class='row'>";
        var rowclosed=false;
        var columnCount=0;
        for(var i = 0; i <books.length; ++i){

                columnCount++;
                htmlStr += "     <div class='col-md-3 text-center'>" +
                        "                                    <div class='wpboxifychapter'>" +
                        "                                        <a href='/books/book?bookId="+books[i].id+"'><div class='wpboxifychapterbody'><img src='/funlearn/showProfileImage?id="+books[i].id+"&fileName="+books[i].coverImage+"&type=books&imgType=passport' width='170' height='220'></div></a>" +
                        "                                        <p class=\"robotoslab greytext\"><span class=\"orange\"> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-half-empty fa-x'></i><i class='fa fa-star-o fa-x'></i></span>(14)&nbsp;&nbsp;<b>&#8377; 109</b></p>" +
                        "                                    </div>" +
                        "                                </div>";
                // logic to close the row and create a new one
                if ((columnCount) % 4 == 0 && i < books.length) htmlStr += "</div><br><br> <div class='row'>";


        }
        htmlStr+="</div>";
     if(books.length>0)   document.getElementById("subject").innerHTML=htmlStr;
        //$("#content-books").css("display","block");
    }

   getBooksList();
    //getTopicsMap('topic');
</script>

<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js"/>
<% }%>

</body>
</html>