<%
    String syllabusType="";
    String country="";

    if(request.getRequestURL().toString().indexOf("success")>-1) syllabusType="success";
    else syllabusType="school";


    country=session.getAttribute("country")==null?"":session.getAttribute("country");%>

<footer class="dark4">
    <div class="container" id="footerid">



        <div class="row">
            <div class="col-md-12 text-center"><img src="${assetPath(src: 'apple.png')}" height="40">&nbsp;&nbsp;&nbsp;<img src="${assetPath(src: 'googleplay.png')}" height="40">
            </div>
        </div>

        <div class="row">
            <div class="col-md-12 text-center">
                <p class="white">&copy; Wonderslate 2016 - All Rights Reserved</p>
            </div>
        </div>
    </div>
    <div class="lmodal" style="display: none">
        <div class="lcenter">
            <img alt="" src="${assetPath(src: 'loading.gif')}" />
        </div>
    </div>


</footer>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifNotLoggedIn>
    <g:render   template="/creation/register"></g:render>
</sec:ifNotLoggedIn>




<script>
    var serverPath= "${request.contextPath}";

    function showregister(registerType){
        $("#registerModal").modal("show");
        document.getElementById('forgotPassword').style.display = 'none';
        document.getElementById('tandc').style.display = 'none';

        if("login"==registerType) {
            document.getElementById('signup').style.display = 'none';
            document.getElementById('login').style.display = 'block';
            document.getElementById('loginFailed').style.display = 'none';
        }
        else{
            document.getElementById('login').style.display = 'none';
            document.getElementById('signup').style.display = 'block';
        }

    }


    <%if("true".equals(params.loginFailed)){%>
    showregister('login');
    document.getElementById('loginFailed').style.display = 'block';
    <%}%>

    var syllabusType="${syllabusType}";

    var country ="${country}";
    var noOfNotifications=0;
    var messagetype="message";
    function getTopicsMap(mode){
        <g:remoteFunction controller="funlearn" action="topicsMap"  onSuccess='initializeDataIndex(data);'
                params="'country='+country+'&mode='+mode" />
    }
    <%if("true".equals(showDiscover)||"true".equals(homeDiscover)){%>
    getTopicsMap('topic');
    <%}%>



    $(document).ready(function(){
        $('[data-toggle="popover"]').popover();
    });

    function getLocalDate(dateStr){
        var newDateStr = dateStr.substr(0,dateStr.length-18)+" "+dateStr.substr(dateStr.length-5);
        return newDateStr;
    }

    function getCookie(cookiename){
        // Get name followed by anything except a semicolon
        var cookiestring=RegExp(""+cookiename+"[^;]+").exec(document.cookie);
        // Return everything after the equal sign
        return unescape(!!cookiestring ? cookiestring.toString().replace(/^[^=]+./,"") : "");
    }

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text.replace(/\W+/g, '') == valueToSet.replace(/\W+/g, '')) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }

    function updateQuizPoints(quizId,correctAnswers) {
        <g:remoteFunction controller="funlearn" action="addQuizPoints"
			params="'quizId='+quizId+'&correctAnswers='+correctAnswers"></g:remoteFunction>
    }
    function getStars(stars)
    { var starsString="";
        if(stars=="0") starsString="<span class='orange'> <i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="0.5") starsString="<span class='orange'> <i class='fa fa-star-half-empty fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="1"||stars=="1.0") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="1.5") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star-half-empty fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="2"||stars=="2.0") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="2.5") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-half-empty fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="3"||stars=="3.0") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-o fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="3.5") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-half-empty fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="4"||stars=="4.0") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-o fa-x'></i></span>";
        else if(stars=="4.5") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star-half-empty fa-x'></i></span>";
        else if(stars=="5"||stars=="5.0") starsString="<span class='orange'> <i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i><i class='fa fa-star fa-x'></i></span>";

        return starsString;
    }

</script>
