

<script>
    var pomodoroStartDate = new Date();
    var pomodoroCurrentDuration = 0;
    var daysDuration = 0;
    var pageActive = true;
    var oneSession = 1500; //1500 seconds
    var countDownMinutes, CountDownSeconds;
    var cookieDate = getCookie("cookieDate");

    //get previous date if present
    if(getCookie("cookieDate")) cookieDate = new Date(getCookie("cookieDate"));
    else {
        cookieDate = new Date();
        setCookie("cookieDate",cookieDate);
        daysDuration = 0;
    }


    // get pomodoro duration and day's duration
    if(getCookie("pomodoroCurrentDuration"))
        pomodoroCurrentDuration = getCookie("pomodoroCurrentDuration");
    else setCookie("pomodoroCurrentDuration",0);
    if(getCookie("daysDuration"))
        daysDuration = getCookie("daysDuration");
    else setCookie("daysDuration",0);

    pomodoroStartDate.setHours(0,0,0,0);
    cookieDate.setHours(0,0,0,0);

    if(cookieDate.getTime()==pomodoroStartDate.getTime()) {
        //nothing to do
    }
    else{
        //call and update the duration for a given date
        var logDate = cookieDate.getDate()+"/"+(cookieDate.getMonth()+1)+"/"+cookieDate.getFullYear();

        <g:remoteFunction controller="usermanagement" action="updateUserDurationLog" params="'logDate='+logDate+'&logDuration='+daysDuration" onSuccess='userSessionLogged(data);' />
        setCookie("cookieDate",new Date());
        daysDuration = 0;

    }
    var elapsedTime = 0;

    var focus = function() {
        pageActive = true;
        pomodoroStartDate = new Date();
    };

    var blur = function() {
        pageActive = false;

    };

    window.addEventListener('focus', focus);
    window.addEventListener('blur', blur);




    setInterval(function(){
        if(pageActive){
            pomodoroCurrentDuration = parseInt(pomodoroCurrentDuration)+1;
            daysDuration = parseInt(daysDuration)+1;
            //check if 25 minutes has passed 25 minutes = 1500 seconds
            if(parseInt(pomodoroCurrentDuration)==oneSession){
                //show the session complete thingy
                //sessionCompletionMessage();
                //send the information to server
                var logDate = pomodoroStartDate.getDate()+"/"+(pomodoroStartDate.getMonth()+1)+"/"+pomodoroStartDate.getFullYear();
                <g:remoteFunction controller="usermanagement" action="updateUserDurationLog" params="'logDate='+logDate+'&logDuration='+daysDuration" onSuccess='userSessionLogged(data);'/>
                <g:remoteFunction controller="usermanagement" action="updateNoOfTrees" onSuccess='userSessionLogged(data);'/>
                //reset the timer
                pomodoroCurrentDuration = 0;
                daysDuration = 0 ;
            }
            setCookie("pomodoroCurrentDuration",pomodoroCurrentDuration);
            setCookie("daysDuration",daysDuration);
            //logic for displaying count down timer
            minutes = parseInt((oneSession-pomodoroCurrentDuration) / 60, 10);
            seconds = parseInt((oneSession-pomodoroCurrentDuration) % 60, 10);
            minutes = minutes < 10 ? "0" + minutes : minutes;
            seconds = seconds < 10 ? "0" + seconds : seconds;
            document.getElementById("pomodoroTimer") ? document.getElementById("pomodoroTimer").innerText = minutes + ":" + seconds : '';


        }
    }, 1000);

    function userSessionLogged(data){
        console.log("logged="+data.status);
    }

</script>
