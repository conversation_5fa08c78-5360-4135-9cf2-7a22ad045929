<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/toppers.css" async="true"/>
<asset:stylesheet href="wonderslate/studentproblems.css" async="true"/>
<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section id="student-problems-section" class="student-problems">
    <div class="container hero-text">
        <h1>Student Problems</h1>
        <p class="text-center text-md-left">As a student and a young person you might facing a problem. And sometimes you might feel that these problems cannot be solved. But there is hope. Below section you will find information about the common problems faced by the students, possible solutions and how you can reach for help.</p>
    </div>
    <div class="container features">
        <ul class="row d-flex justify-content-center mx-1 p-0 px-md-3">
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <img src="${assetPath(src: 'ws/speak-up.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Speaking Up</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <img src="${assetPath(src: 'ws/new-school.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">New School</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <img src="${assetPath(src: 'ws/peer.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Peer Pressure</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
               <li><img src="${assetPath(src: 'ws/mental-health.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Mental Health</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <li><img src="${assetPath(src: 'ws/group-activity.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Group Activities</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
               <li> <img src="${assetPath(src: 'ws/bullying.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Bullying</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <li><img src="${assetPath(src: 'ws/teachers-fav.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Teacher
                    Favouritism</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <li><img src="${assetPath(src: 'ws/exam-stress.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Exam Stress</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
                <li><img src="${assetPath(src: 'ws/cyber-bullying.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">Cyber Bullying</h3>
                </span></li>
            </a>
            <a href='https://blog.wonderslate.com/category/student-problems${session.getAttribute("appType")!=null?"?tokenId=app":""}' class="card col-12 col-sm-12 col-md-3 flex-fill justify-content-end" <%= session.getAttribute("appType")==null?"target='_blank'":"" %> ><li>
               <li> <img src="${assetPath(src: 'ws/school-pressure.svg')}" />
                <span class="card-body">
                    <h3 class="card-title">School Pressures</h3>
                </span></li>
            </a>
        </ul>
    </div>
%{--    <div class="container doubts">--}%
%{--        <p class="doubts-title mb-3">Related Doubts</p>--}%
%{--        <div id="studentproblem-doubts" class="doubts-scroller row d-flex justify-content-start flex-md-nowrap justify-content-xs-start">--}%
%{--        </div>--}%
%{--        <div class="arrows d-none d-md-flex">--}%
%{--            <!-- <span class="left-arrow d-flex align-items-center justify-content-center"><i--}%
%{--                        class="fas fa-angle-left"></i></span> -->--}%
%{--            <span class="right-arrow d-flex align-item-center justify-content-center"> <i class="material-icons">keyboard_arrow_right</i></span>--}%
%{--        </div>--}%

%{--    </div>--}%
%{--    <div class="text-center mt-4">--}%
%{--        <button onclick="askDoubt()" class="btn btn-askQuestion mt-4" > Have a Question? Ask Here. </button>--}%
%{--    </div>--}%
<g:render template="/discussion/discussionBoardTemplate" model="[parentPageFilter:'page_studentProblems']"></g:render>
</section>

<section style="display: none;" id="doubts-section" class="student-problems">
    <div class="container hero-text">
    <div class="d-flex align-items-center">
                <button type="button" id="backToDoubts" onclick="javascript:showPageSection();" ><i class="material-icons">west</i> </button><h1>Discuss Your Problem</h1>
                </div>


    </div>
    <div class=" container posting-question" >
                    <div class="row col-12 col-lg-7 no-gutters">
                    <div class="card answer-card mt-4" id="queSave-modal">
                    </div>
                    <div style="display: none" class="card" id="image-preview">
                    <img id="q-image-preview" style="width: 100%;" src="#" alt="your image" />
                    <button type="button" class="close-img" onclick="removeSelectedQuestionImg()" > <i class="material-icons">cancel</i> </button>
                </div>
                    </div>
                </div>

    </div>
</section>

<div class="modal fade postmodals" id="image-modal">
    <div class="modal-dialog modal-dialog-centered modal-sm">
        <div class="modal-content">

            <!-- Modal body -->
            <div class="modal-body">
                <div id="image-modal-body" ></div>
            </div>

            <!-- Modal footer -->
            <div class="modal-footer">
                <button type="button" id="image-modal-close" class="btn btn-flashcard" data-dismiss="modal">Okay</button>
            </div>

        </div>
    </div>
</div>
<asset:javascript src="landingpage/slick.js"/>
<script>
    function slickInitialize() {
        if (window.matchMedia("(min-width:768px)").matches) {
            $('.posts-scroller').slick({
                slidesToShow: 3,
                slidesToScroll: 3,
                arrows: true,
                infinite: false,
                prevArrow: '.posts-arrows .left-arrow',
                nextArrow: '.posts-arrows .right-arrow'

            });

        }
    }
    window.addEventListener('resize', () => {
        if (window.matchMedia("(min-width:768px)").matches) {

            $(".posts-scroller").not('.slick-initialized').slick({
                slidesToShow: 3,
                slidesToScroll: 3,
                arrows: true,
                infinite: false,
                prevArrow: '.posts-arrows .left-arrow',
                nextArrow: '.posts-arrows .right-arrow'

            });

        } else {
            if($('.posts-scroller').hasClass('slick-initialized')) {
                $('.posts-scroller').slick('unslick');
            }
        }
    })

    $(document).ready(function(){
        slickInitialize()

        // When document is loaded this will based on the size of the device remove the d-none class from back button [ used for history ]
        if($(window).width() < 767){
            $('.backMenu').removeClass('d-none');
        }
    });
</script>

