
<g:render template="/funlearn/pnavheader"></g:render>
<link href='https://fonts.googleapis.com/css?family=Nunito' rel='stylesheet' type='text/css'>


<style>
.book {
    padding: 15px 0 0 0;
    margin: auto;
}
.shelf {
    border-bottom: 30px solid #A1A194;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    top: -15px;

}

</style>
<!--<div>-->

<div class="container-fluid wplandingblueimage topnavblue">
    <div class="row">
        <div class="col-md-12 text-center">
            <h4 class="whitetext">4 BOOKS IN YOUR CART</h4>
        </div>
    </div>
</div>
<div class="container-fluid wpbackgroundcolor" >
    <div  class='row maincontent'>
        <div class="col-md-2">
            <BR>
        </div>

        <div class='col-md-8 '>
            <div id="content-books">
           <BR>
                <div class="row">
                    <div class="col-md-12 text-center chaptertext whitetext">
                        Review & Checkout
                    </div>
                </div><br>
                <div id="content-data-books" class="row  row-centered">
                    <div class='col-md-3'>
                        <div class='boxifychapter'><div class='row'><div class='col-md-11 text-right  greytext'>&nbsp;</div></div>
                            <a href='bookdtl'><div class='boxifychapterbody'><img src='/funlearn/showProfileImage?id=1&fileName=book5.jpg&type=books&imgType=passport' height='170'></div></a>
                           </div>
                    </div>
                    <div class='col-md-3 '>
                        <div class='boxifychapter'><div class='row'><div class='col-md-11 text-right  greytext'>&nbsp;</div></div>
                            <a href='bookdtl'><div class='boxifychapterbody'><img src='/funlearn/showProfileImage?id=1&fileName=book6.jpg&type=books&imgType=passport' height='170'></div></a>
                        </div>
                    </div>
                    <div class='col-md-3 '>

                        <div class='boxifychapter'><div class='row'><div class='col-md-11 text-right  greytext'>&nbsp;</div></div>
                            <a href='bookdtl'><div class='boxifychapterbody'><img src='/funlearn/showProfileImage?id=1&fileName=book7.jpg&type=books&imgType=passport'  height='170'></div></a>
                         </div>
                    </div>
                    <div class='col-md-3 '>
                        <div class='boxifychapter'><div class='row'><div class='col-md-11 text-right  greytext'>&nbsp;</div></div>
                            <a href='bookdtl'><div class='boxifychapterbody'><img src='/funlearn/showProfileImage?id=1&fileName=book8.jpg&type=books&imgType=passport'  height='170'></div></a>
                        </div>
                    </div>

                </div><br>
                 <div class="row  row-centered">
                     <div class="col-md-12"><b><hr style="border-color: #0a001f"></b></div>
                 </div>

                <div class="row  row-centered greytext">
                    <div class="col-md-12">Items&nbsp;:&nbsp; <b>4</b>&nbsp;&nbsp;&nbsp;&nbsp;      Total&nbsp; :&nbsp; &#8377;<span class="red"> 1350</span></div>
                </div>

                <br>
                <div class="row  row-centered greytext">
                    <div class="col-md-12"><h4 class="greytext">SELECT PAYMENT METHOD</h4></div>
                </div>

                <br>
                <div class="row  row-centered">
                    <div class="col-md-2 col-md-offset-3 greytext"><i class="fa fa-credit-card fa-3x orange"></i><br>Card </div>
                    <div class="col-md-2 greytext "><i class="fa fa-institution fa-3x orange"></i><br>Net banking </div>
                    <div class="col-md-2 greytext "><i class="fa fa-credit-card-alt fa-3x orange"></i><br>Wallet </div>
                </div>
            </div><br><br>



        </div>

    </div>
</div>



<g:render template="bfooter"></g:render>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js" async></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<script>



</script>

<%
    if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1 && (user==null||!"Yes".equals(""+user.wonderSlateEmployee))){ %>

<asset:javascript src="analytics.js" async="true"/>
<% }%>


</body>
</html>