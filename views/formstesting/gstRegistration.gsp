<!DOCTYPE html>
<html>
<head>
    <title>Arivupro</title>
    <asset:stylesheet src="forms/material.css"/>
    <asset:stylesheet src="forms/main.css"/>
    <asset:stylesheet src="forms/index.css"/>
    <!-- Latest compiled and minified CSS -->

    <!-- <link rel="stylesheet" href="css/font-awesome.4.7.min.css"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

</head>
<body>
<div data-ng-controller="headerCtrl">
    <header class="main-header">
        <div class="skip">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <ul class="skip list-inline">
                            <li><a tabindex="-1" class="accessible" href="#">Skip to Main Content</a></li>
                            <li class="high-low"><i class="fa fa-adjust"></i></li>
                            <li class="fresize f-up">A<sup>+</sup></li>
                            <li class="fresize f-down">A<sup>-</sup></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="container">
            <div class="row branding">
                <div class="col-xs-12">
                    <a href="#" title="Goods and Services Tax Home">
                        <img class="logo" src="${assetPath(src: 'emblem.png')}" alt="Goods and Services Tax Home">
                    </a>
                    <h1 class="site-title"><a href="#">Goods and Services Tax</a></h1>
                    <ul class="list-inline mlinks">
                        <!----><li ng-if="!udata">
                        <a target="_self" href="#"><i class="fa fa-sign-in"></i> Login</a>
                    </li>
                        <li><a class="nav_home" href="formDocuments?quizId=${params.quizId}" target="_blank"> Documents</a></li><!---->
                    <!---->
                    <!---->
                    </ul>
                    <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main">
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="sr-only">Toggle navigation</span>
                    </button>
                </div>
            </div>
        </div>
    </header>
    <!----><nav class="navbar navbar-default collapsed" style="top: auto;">
    <div class="container">
        <div id="main" class="navbar-collapse collapse">
            <ul class="nav navbar-nav" link="">
                <li class="active">
                    <a class="nav_home" data-ng-show="!udata" data-ng-href="//www.gst.gov.in/" target="_self" href="//www.gst.gov.in/">Home</a>
                </li>
                <li class="dropdown drpdwn" data-ng-class="{'active': servers.NAV_COMPONENT == 'services'}">
                    <a data-ng-href="#" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false" target="_self" href="#">Services <span class="caret"></span></a>
                    <ul class="dropdown-menu smenu" role="menu">
                        <li class="has-sub">
                            <a data-ng-show="udata &amp;&amp; udata.role == 'login'" data-ng-href="//services.gst.gov.in/services/auth/quicklinks/registration" target="_self" href="//services.gst.gov.in/services/auth/quicklinks/registration" class="ng-hide">Registration</a>
                            <ul class="isubmenu serv">
                                <li data-ng-hide="udata.utype=='TR' || udata.utype=='UN'  || udata.utype == 'O'">
                                    <a data-ng-href="//reg.gst.gov.in/registration" target="_self" href="//reg.gst.gov.in/registration">New Registration</a>
                                </li>
                            </ul>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>
    </div>
</nav><!---->
<!----></ng-include>
</div>


<div class="content-wrapper" id="screen1" style="display: block;">
    <div class="container">
        <div class="mypage">
            <div class="row">
                <div class="col-xs-10">
                    <div>
                        <ol class="breadcrumb">
                            <li>
                                <a target="" href="#">Home</a>
                            </li>
                            <li>
                                <span>Registration</span>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="col-xs-2">
                    <div class="lang dropdown">
                        <span class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">English</span>
                        <ul class="dropdown-menu lang-dpdwn">
                            <li>
                                <a>English</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="content-pane" style="min-height: 568.2px;">
                <div>
                    <form method="POST" action="addForm" autocomplete="off" novalidate="" class="new-registration" name="form1">
                        <div class="row">
                            <div class="col-lg-offset-5 col-lg-4 col-md-offset-3 col-md-6 col-sm-offset-2 col-sm-8 col-xs-12">
                                <div class="contain">
                                    <ul class="progressbar">
                                        <li class="z1 step-on">
                                            <p><span class="circle on-page">1</span><span hidden="" class="circle"></span></p>
                                            <span>User Credentials</span>
                                        </li>
                                        <li class="z2">
                                            <p><span class="circle not-active">2</span><span hidden="" class="circle"></span></p>
                                            <span>OTP Verification</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-offset-2 col-md-offset-3 col-md-6 col-sm-8 col-xs-12">
                                <p></p>
                                <h4 >New Registration</h4>
                                <hr>
                                <p class="mand-text">indicates mandatory fields</p>
                                <div class="row">
                                    <div class="col-xs-12">
                                        <fieldset>
                                            <input type="radio" name="typ" id="radionew" checked="checked" onclick="javascript:newReg();">
                                            <label for="radionew">New Registration</label>
                                            <input type="radio" name="typ" id="radiotrn" onclick="javascript:trnReg();">
                                            <label for="radiotrn">Temporary Reference Number (TRN)</label>
                                        </fieldset>
                                    </div>
                                </div>
                                <fieldset id="new-registration">
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label class="reg m-cir" for="applnType">I am a</label>
                                            <input type="hidden" id="applnType_hidden" name="app_type_hidden" value="APLRG">
                                            <select class="form-control" id="applnType" name="app_type">
                                                <option value="">Select</option>
                                                <option value="APLRG">Taxpayer</option>
                                                <option value="RTTR1">GST Practitioner</option>
                                                <option value="APLTD">Tax Deductor</option>
                                                <option value="APLTC">Tax Collector (e-Commerce)</option>
                                                <option value="APLNR">Non Resident Taxable Person</option>
                                                <option value="APLUN">United Nation Body </option>
                                                <option value="APLEM">Consulate or Embassy of Foreign Country </option>
                                                <option value="APLOT">Other Notified Person</option>
                                                <option value="REGOI">Non-Resident Online Services Provider</option>
                                            </select>
                                            <span id="appintype_error"></span>
                                        </div>
                                    </div>
                                    <select class="brwoser-default" id="country"></select>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label for="applnState" class="reg m-cir">State / UT</label>
                                            <select class="form-control" id="region" name="applnState" required="required">
                                                <option value="">Select</option>
                                                <option value="kar">KARNATAKA</option>
                                                <option value="tamil">TAMIL NADU</option>
                                                <option value="kerala">KERALA</option>
                                            </select>
                                            <span id="appinstate_error"></span>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label class="reg" for="applnDistr">District</label>
                                            <select class="form-control" id="city" name="applnDistr" required="required">
                                                <option value="">Select</option>
                                                <option value="ban">BANGALORE</option>
                                                <option value="tamil">Mysore</option>
                                                <option value="kerala">BELLARI</option>
                                            </select>
                                            <span id="appindistr_error"></span>
                                        </div>
                                    </div>


                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label for="bnm" class="reg m-cir">
                                                <span>Legal Name of the Business</span>
                                                <span class="help">(As mentioned in PAN)</span>
                                            </label>
                                            <input type="hidden" id="pan_lgnmbzpann" name="lgnmbzpan_hidden" value="OLD BOYS ASSOCIATION SAINIK">
                                            <input type="text" id="bnm" name="lgnmbzpan" placeholder="Enter Legal Name of Business" class="form-control">
                                            <span id="bnm_error"></span>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label class="reg m-cir" for="pan_card">Permanent Account Number (PAN)</label>
                                            <div class="has-feedback">
                                                <input type="hidden" id="pan_card_hidden" name="pan_card_hidden" value="**********">
                                                <input class="form-control" id="pan_card" name="pan_card" placeholder="Enter Permanent Account Number (PAN)" type="text" maxlength="10" required="">
                                                <span id="pan_card_error"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label class="reg m-cir" for="email"><span>Email Address</span></label>
                                            <div class="input-group">
                                                <span class="input-group-addon" id="ba2"><i class="fa fa-envelope"></i></span>
                                                <input id="email" class="form-control" type="email" placeholder="Enter Email Address" name="email" required="">
                                            </div>
                                            <span id="ba2_error"></span>
                                            <span class="help-block"><i class="fa fa-info-circle"></i> <span>OTP will be sent to this Email Address</span></span>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label class="reg m-cir"><span>Mobile Number</span></label>
                                            <div class="input-group">
                                                <span class="input-group-addon" id="ba">+91</span>
                                                <input id="mobile" class="form-control" name="mobile" required="required">
                                            </div>
                                            <span id="ba_error"></span>
                                            <span class="help-block"><i class="fa fa-info-circle"></i> <span>Separate OTP will be sent to this mobile number</span></span>

                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label class="reg m-cir" for="chrs">Type the characters you see in the image below</label>
                                            <div class="has-feedback">
                                                <input class="user-text form-control" id="chrs" name="chrs" placeholder="Type the characters you see in the image below" type="text" maxlength="10" required="">
                                                <span id="chrs_error"></span>
                                                <p class="wrong info">Wrong!, please try again.</p>
                                            </div>
                                            <div id="captcha">
                                                <div class="controls">
                                                    <button class="refresh btn-common">
                                                        <!-- this image should be converted into inline svg -->
                                                        <img src="${assetPath(src: 'refresh_icon.png')}"  alt="refresh icon">
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                </fieldset>

                                <fieldset id="trn-registration">
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label for="trnm" class="reg m-cir">
                                                <span>Temporary Reference Number (TRN)</span>
                                            </label>
                                            <input type="text" id="trnm" name="trnm" placeholder="Temporary Reference Number (TRN)" class="form-control">
                                            <span id="trnm_error"></span>
                                        </div>
                                    </div>


                                    <input type="hidden" name="resId" value="${params.quizId}">
                                </fieldset>

                                <div class="row">
                                    <div class="col-xs-12">
                                        <a id="ctop" href="javascript:requestOtp();" class="btn validate btn-block btn-primary">Proceed</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content-wrapper" id="screen2" style="display: none;">
    <div class="container">
        <div class="mypage">
            <div class="row">
                <div class="col-xs-10">
                    <div>
                        <ol class="breadcrumb">
                            <li>
                                <a target="" href="#">Home</a>
                            </li>
                            <li>
                                <span>Registration</span>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="col-xs-2">
                    <div class="lang dropdown">
                        <span class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">English</span>
                        <ul class="dropdown-menu lang-dpdwn">
                            <li>
                                <a>English</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="content-pane" style="min-height: 568.2px;">
                <div>
                    <form method="POST" autocomplete="off" novalidate="" class="new-registration" action="trnCreated" name="form2">
                        <div class="row">
                            <div class="col-lg-offset-5 col-lg-4 col-md-offset-3 col-md-6 col-sm-offset-2 col-sm-8 col-xs-12">
                                <div class="contain">
                                    <ul class="progressbar">
                                        <li class="z1 step-done">
                                            <p><span class="circle step-done"><i class="fa fa-check"></i></span><span hidden="" class="circle"></span></p>
                                            <span>User Credentials</span>
                                        </li>
                                        <li class="z2 step-on">
                                            <p><span class="circle on-page">2</span><span hidden="" class="circle"></span></p>
                                            <span>OTP Verification</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-offset-2 col-md-offset-3 col-md-6 col-sm-8 col-xs-12">
                                <p></p>
                                <h4>Verify OTP</h4>
                                <hr>
                                <p class="mand-text">indicates mandatory fields</p>
                                <fieldset id="new-registration1">
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label for="mobileotp" class="reg m-cir">
                                                <span>Mobile OTP</span>
                                            </label>
                                            <input type="password" id="mobileotp" name="mobileotp" placeholder="Enter OTP sent to your mobile" class="form-control">
                                            <span class="help-block"><i class="fa fa-info-circle"></i> <span>Enter OTP sent to your mobile</span></span>
                                            <span id="mobileotp_error"></span>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-xs-12 form-group">
                                            <label class="reg m-cir" for="emailotp">Email OTP</label>
                                            <div class="has-feedback">
                                                <input class="form-control" id="emailotp" name="emailotp" placeholder="Enter OTP sent to your email" type="password" maxlength="10" required="">
                                                <span class="help-block"><i class="fa fa-info-circle"></i> <span>Enter OTP sent to your email</span></span>
                                                <span id="emailotp_error"></span>
                                            </div>
                                        </div>
                                    </div>

                                </fieldset>



                                <div class="row">
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 text-right">
                                        <a href="javascript:goBack();" class="btn btn-sm btn-default send">Back</a>
                                        <a href="javascript:submitOtp();" class="btn btn-primary btn-sm">Proceed</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="content-wrapper" id="screen3" style="display: none;">
    <div class="container">
        <div class="mypage">
            <div class="row">
                <div class="col-xs-10">
                    <div>
                        <ol class="breadcrumb">
                            <li>
                                <a target="" href="#">Home</a>
                            </li>
                            <li>
                                <span>Registration</span>
                            </li>
                            <li>
                                <span>Verify</span>
                            </li>
                        </ol>
                    </div>
                </div>
                <div class="col-xs-2">
                    <div class="lang dropdown">
                        <span class="dropdown-toggle" data-toggle="dropdown" aria-expanded="false">English</span>
                        <ul class="dropdown-menu lang-dpdwn">
                            <li>
                                <a>English</a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="content-pane" style="min-height: 568.2px;">
                <div>
                    <form method="POST" autocomplete="off" novalidate="">
                        <div class="row">
                            <div class="col-lg-offset-5 col-lg-4 col-md-offset-3 col-md-6 col-sm-offset-2 col-sm-8 col-xs-12">
                                <div class="contain">
                                    <ul class="progressbar">
                                        <li class="z1 step-done">
                                            <p><span class="circle step-done"><i class="fa fa-check"></i></span><span hidden="" class="circle"></span></p>
                                            <span>User Credentials</span>
                                        </li>
                                        <li class="z2 step-on">
                                            <p><span class="circle on-page">2</span><span hidden="" class="circle"></span></p>
                                            <span>OTP Verification</span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="row" style="padding: 10px;">
                            <div class="col-sm-offset-2 col-md-offset-3 col-md-6 col-sm-8 col-xs-12">
                                <h4>Verify OTP</h4>
                                <hr>
                                <p class="mand-text">indicates mandatory fields</p>

                                <label for="mobile-email-otp" class="reg m-cir">Mobile/Email OTP</label>
                                <input type="password" id="mobile-email-otp" name="mobile-email-otp" placeholder="Enter OTP sent to your mobile and Email" class="form-control">
                                <span id="emailmobileotp_error"></span>
                                <span class="help-block"><i class="fa fa-info-circle"></i> Fill OTP sent to mobile and Email</span>
                                <span class="help-block"><i class="fa fa-info-circle"></i> Please check the junk/spam folder in case you do not get Email</span><br><br>
                                <a href="#"> Need OTP to be reset? click here</a>
                                <div class="row">
                                    <div class="col-lg-12 col-md-12 col-sm-12 col-xs-12 text-right">
                                        <a href="javascript:goBack();" class="btn btn-sm btn-default send">Back</a>
                                        <a href="javascript:submitOtp1();" class="btn btn-primary btn-sm">Proceed</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<footer ng-controller="footerCtrl">
    <div class="expbtn">
        <i ng-click="fexpand()" ng-class="{'fa-angle-up': expanded, 'fa-angle-down': !expanded}" class="fa fa-angle-down" title="Expand/Collapse Footer"></i>
    </div>
    <div class="ifooter " id="demo">
        <!----><ng-include src="'/pages/common/footer.html'"><!----><div class="f1" data-ng-if="!expanded">
        <div class="container">
            <div class="row">
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/history" href="//www.gst.gov.in/about/gst/history">About GST</a>
                    <ul>
                        <!--<li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="{{servers.GST_CONTENT_R1_URL}}/about/vision">Vision and Mission</a></li>
          <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="{{servers.GST_CONTENT_R1_URL}}/about/citizencharter">Citizen Charter</a></li>-->
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/council" href="//www.gst.gov.in/about/gst/council">GST Council Structure</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/about/gst/history" href="//www.gst.gov.in/about/gst/history">GST History</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/website" href="//www.gst.gov.in/policies/website">Website Policies</a>
                    <ul>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/website" href="//www.gst.gov.in/policies/website">Website Policy</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/hyperlink" href="//www.gst.gov.in/policies/hyperlink">Hyperlink Policy</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/policies/disclaimer" href="//www.gst.gov.in/policies/disclaimer">Disclaimer</a></li>
                    </ul>

                </div>
                <div class="col-lg-2 col-md-2 col-sm-2 col-xs-12 no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/relatedsites" href="//www.gst.gov.in/help/relatedsites">Related Sites</a>
                    <ul>
                        <li><a data-popup="true" data-ng-href="http://www.cbec.gov.in/" href="http://www.cbec.gov.in/">Central
                        Board of Excise and Customs</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/statevat" href="//www.gst.gov.in/help/statevat">State
                        Tax Websites</a></li>
                        <li><a data-popup="true" data-ng-href="//india.gov.in/" href="//india.gov.in/">National
                        Portal</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-2 col-xs-12 help no-mobile">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help" href="//www.gst.gov.in/help">Help</a>
                    <ul>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/system/" href="//www.gst.gov.in/system/">System
                        Requirements</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/help/helpmodules/" href="//www.gst.gov.in/help/helpmodules/">User Manuals, Videos and FAQs</a></li>
                        <li><a ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/docadvisor/" href="//www.gst.gov.in/docadvisor/">Documents Required for Registration</a></li>
                        <li><a data-popup="true" data-ng-href="https://www.youtube.com/c/GoodsandServicesTaxNetwork" href="https://www.youtube.com/c/GoodsandServicesTaxNetwork">GST Media</a></li>
                        <li><a class="disabled" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/sitemap" href="//www.gst.gov.in/sitemap">Site Map</a></li>
                        <!--<li><a target="_blank" data-ng-href="{{servers.GST_SERVICES_R1_URL}}/services/track-provisional-id-status">Track Provisional ID</a></li>-->
                    </ul>
                </div>
                <div class="col-lg-3 col-md-3 col-sm-3 col-xs-12 cont no-mobile scl">
                    <a class="fhead" ng-attr-target="{{(servers.STATIC_WEB === true) ? undefined : '_blank'}}" data-ng-href="//www.gst.gov.in/contact" href="//www.gst.gov.in/contact">Contact Us</a>
                    <ul>
                        <li>
                            <span class="contact">Help Desk Number: <br>0120-4888999</span>
                        </li>
                        <!-- <li>
            <a data-popup="true" data-ng-if="!udata" data-ng-href="//www.entrust.com/ssl-certificates/" target="_blank" data-ng-click="seal()"><img src="{{servers.GST_CONTENT_R1_URL}}\uiassets\images\entrustSeal.png" alt="Loading..." ></a>
          </li> -->
                        <li>
                            <span class="contact">Log/Track Your Issue:<br><a href="https://selfservice.gstsystem.in/" title="Grievance Redressal Portal for GST" target="_blank">Grievance Redressal Portal for GST</a></span>
                        </li>
                        <li class="social">
                            <a data-popup="true" href="//www.facebook.com/Goods-and-Services-Tax-1674179706229522/?fref=ts" title="Facebook"><i class="fa fa-facebook-square"></i>.</a>
                            <a data-popup="true" href="//www.youtube.com/channel/UCFYpOk92qurlO5t-Z_y-bOQ" title="Youtube"><i class="fa fa-youtube-play"></i>.</a>
                            <a data-popup="true" href="//twitter.com/askGSTech"><i class="fa fa-twitter" title="Twitter"></i>.</a>
                            <a data-popup="true" href="//www.linkedin.com/company/13204281?trk=tyah&amp;trkInfo=clickedVertical%3Acompany%2CclickedEntityId%3A13204281%2Cidx%3A4-2-9%2CtarId%3A1478268606810%2Ctas%3AGoods%20and%20Services%20" title="Linkedin"><i class="fa fa-linkedin"></i>.</a>
                        </li>
                        <!---->
                    </ul>
                </div>
            </div>
        </div>
    </div><!---->
        <div class="f2">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <p>© 2016-17 Goods and Services Tax Network</p>
                        <p>Site Last Updated on 23-05-2018</p>
                        <p>Designed &amp; Developed by GSTN</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="f3">
            <div class="container">
                <div class="row">
                    <div class="col-xs-12">
                        <p class="site">Site best viewed at 1024 x 768 resolution in Internet Explorer 10+, Google Chrome 49+, Firefox 45+ and Safari 6+</p>
                    </div>
                </div>
            </div>
        </div>
        <style>
        .disabled{
            cursor: not-allowed !important;
            opacity: 0.6;
        }
        </style>
       </ng-include>
    </div>
</footer>
<asset:javascript src="jquery-1.11.2.min.js"/>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js" integrity="sha384-Tc5IQib027qvyjSMfHjOMaLkfuWVxZxUPnCJA7l2mCWNIpG9mGCD8wGNIcPD7Txa" crossorigin="anonymous"></script>
<asset:javascript src="client_captcha.js"/>
<script src='https://cdnjs.cloudflare.com/ajax/libs/materialize/0.98.0/js/materialize.min.js'></script>
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/jquery.bootstrapvalidator/0.5.2/js/bootstrapValidator.min.js"></script>
<script>
    $(document).on('click', '.disabled', function(){
        return false;
    });

    function goBack() {
        $('#screen1').show();
        $('#screen2').hide();
        $('#screen3').hide();
    }

    function submitOtp() {
        $('#emailotp_error').show().html("");
        $('#mobileotp_error').show().html("");

        if(document.getElementById('emailotp').value.trim()=="" || document.getElementById('mobileotp').value.trim()=="") {
            if(document.getElementById('emailotp').value.trim()=="") $('#emailotp_error').show().html("Please enter Email OTP.");
            if(document.getElementById('mobileotp').value.trim()=="") $('#mobileotp_error').show().html("Please enter Mobile OTP.");
        } else {
            <g:remoteFunction controller="formstesting" action="checkOTP"  onSuccess='submitUser(data);'
                    params="'email='+document.form1.email.value+'&mobile='+document.form1.mobile.value+'&email_otp='+document.getElementById('emailotp').value+'&mobile_otp='+document.getElementById('mobileotp').value" />
        }
    }

    function submitUser(data) {
        if(data.status == "OK") {
            $('#mobileotp_error').hide();
            $('#emailotp_error').hide();
            document.form1.submit();
        } else {
            if(data.status.startsWith("M")) {
                $('#mobileotp_error').show().html("Incorrect Mobile OTP. Please try again.");
            }

            if(data.status.endsWith("E")) {
                $('#emailotp_error').show().html("Incorrect Email OTP. Please try again.");
            }
        }
    }

    function checkTrn() {
        $('#trnm_error').show().html("");

        if(document.getElementById('trnm').value.trim()=="") {
            $('#trnm_error').show().html("Please enter Temporary Reference Number (TRN).");
        } else {
            <g:remoteFunction controller="formstesting" action="checkTrn" onSuccess='requestOtp1(data)'
                    params="'trn='+document.getElementById('trnm').value.toLowerCase().replace('trn','')" />
        }

    }

    function requestOtp() {
        var validator = $('.new-registration').data('bootstrapValidator');
        validator.validate();
        if (validator.isValid()) {
            <g:remoteFunction controller="formstesting" action="generateOTP" onSuccess=''
                    params="'email='+document.getElementById('email').value+'&mobile='+document.getElementById('mobile').value" />
            $('#screen1').hide();
            $('#screen2').show();
        }
    }

    function requestOtp1(data) {
        if(data.status=="OK") {
            <g:remoteFunction controller="formstesting" action="generateOTP1" onSuccess=''
                    params="'trn='+document.getElementById('trnm').value" />
            $('#screen1').hide();
            $('#screen2').hide();
            $('#screen3').show();
        } else {
            $('#trnm_error').show().html("Incorrect Temporary Reference Number (TRN). Please try again.");
        }
    }

    function submitOtp1() {
        $('#emailmobileotp_error').show().html("");

        if(document.getElementById('mobile-email-otp').value.trim()=="") {
            $('#emailmobileotp_error').show().html("Please enter OTP.");
        } else {
            <g:remoteFunction controller="formstesting" action="checkOTP1"  onSuccess='submitUser1(data);'
                    params="'mobileEmailOtp='+document.getElementById('mobile-email-otp').value+'&trn='+document.getElementById('trnm').value.toLowerCase().replace('trn','')" />
        }
    }

    function submitUser1(data) {
        if(data.status!="OK") {
            $('#emailmobileotp_error').show().html("Incorrect OTP. Please try again.");
        } else {
            window.location.assign("savedApplications")
        }

    }

    function trnReg() {
        $('#new-registration').hide();
        $('#trn-registration').show();
        $('#ctop').attr("href", "javascript:checkTrn();");
    }

    function newReg() {
        $('#new-registration').show();
        $('#trn-registration').hide();
        $('#ctop').attr("href", "javascript:requestOtp();");
    }

    function error(id){
        var ab=$('#new-registration').find(id).attr(id + '_error');
        $('#new-registration').find(ab).show('.form-control + span');
    }

    $(document).ready(function() {
        $('#radionew').trigger("click");

        $('.new-registration').bootstrapValidator({
            fields: {
                lgnmbzpan: {
                    validators: {
                        stringLength: {
                            min: 4,
                        },
                        notEmpty: {
                            message: 'Please enter your name as per PAN'
                        },
                        identical: {
                            field: 'lgnmbzpan_hidden',
                            message: 'Incorrect name'
                        }
                    }
                },
                lgnmbzpan_hidden: {
                    validators: {
                        identical: {
                            field: 'lgnmbzpan',
                            message: 'Incorrect name'
                        }
                    }
                },
                app_type: {
                    validators: {
                        notEmpty: {
                            message: 'Please select your Profession.'
                        },
                        identical: {
                            field: 'app_type_hidden',
                            message: 'Incorrect profession'
                        }
                    }
                },
                app_type_hidden: {
                    validators: {
                        identical: {
                            field: 'app_type',
                            message: 'Incorrect profession'
                        }
                    }
                },
                applnState: {
                    validators: {
                        notEmpty: {
                            message: 'Please select your State.'
                        }
                    }
                },
                applnDistr:{
                    validators: {
                        notEmpty: {
                            message: 'Please select your District.'
                        }
                    }
                },
                pan_card:{
                    validators: {
                        stringLength: {
                            min: 10,
                        },
                        notEmpty: {
                            message: 'Please enter your PAN no.'
                        },
                        identical: {
                            field: 'pan_card_hidden',
                            message: 'Incorrect PAN'
                        }
                    }
                },
                pan_card_hidden:{
                    validators: {

                        identical: {
                            field: 'pan_card',
                            message: 'Incorrect PAN'
                        }
                    }
                },
                email: {
                    validators: {
                        notEmpty: {
                            message: 'Please enter your email address'
                        },
                        emailAddress: {
                            message: 'Please enter a valid email address'
                        }
                    }
                },
                mobile: {
                    validators: {
                        notEmpty: {
                            message: 'Please supply your phone number'
                        },
                        phone: {
                            country: 'US',
                            message: 'Please supply a vaild phone number with area code'
                        }
                    }
                },


            }
        }).on('success.form.bv', function(e) {
            $('#success_message').slideDown({ opacity: "show" }, "slow") // Do something ...


       //     $('#gst-registration').data('bootstrapValidator').resetForm();

            // Prevent form submission
         // e.preventDefault();

            // Get the form instance
          //  var $form = $(e.target);

            // Get the BootstrapValidator instance
         //   var bv = $form.data('bootstrapValidator');

            // Use Ajax to submit form data
        //   $.post($form.attr('action'), $form.serialize(), function(result) {
         //   }, 'json');
        });
    });
    document.addEventListener("DOMContentLoaded", function() {
        document.body.scrollTop; //force css repaint to ensure cssom is ready

        var timeout; //global timout variable that holds reference to timer

        var captcha = new $.Captcha({
            onFailure: function() {

                $(".wrong").show({
                    duration: 30,
                    done: function() {
                        var that = this;
                        clearTimeout(timeout);
                        $(this).removeClass("shake");
                        $(this).css("animation");
                        //Browser Reflow(repaint?): hacky way to ensure removal of css properties after removeclass
                        $(this).addClass("shake");
                        var time = parseFloat($(this).css("animation-duration")) * 1000;
                        timeout = setTimeout(function() {
                            $(that).removeClass("shake");
                        }, time);
                    }
                });

            },

            onSuccess: function() {
                alert("CORRECT!!!");
            }
        });

        captcha.generate();
    });
</script>
<!-- <script src="js/uiscripts.js"></script> -->
<asset:javascript src="script-statecity.js"/>
<script src="https://use.fontawesome.com/39bc9f0c7e.js"></script>
</body>
</html>
