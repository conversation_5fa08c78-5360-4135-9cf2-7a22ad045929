<style>
    *{
        scroll-behavior: smooth;
    }
body {
    background-color: #f5f7fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

h1, h3 {
    color: #343a40;
}

.card {
    border: none;
    border-radius: 10px;
    background: linear-gradient(135deg, #e8f0fe, #ffffff);
}

.card .card-title {
    font-size: 1.1rem;
}

.btn-primary {
    background-color: #4a90e2;
    border-color: #4a90e2;
}

.btn-primary:hover {
    background-color: #357ab8;
    border-color: #357ab8;
}

.btn-secondary {
    background-color: #6c757d;
    border-color: #6c757d;
}

.btn-secondary:hover {
    background-color: #5a6268;
    border-color: #5a6268;
}

.table-hover tbody tr:hover {
    background-color: #eef1f5;
}

.fa-folder {
    color: #f0ad4e;
}

.fa-file-pdf {
    color: #d9534f;
}

.fa-file-word {
    color: #0275d8;
}

/* Additional styles for AI theme */


.ai-assistant-toggle:hover {
    background-color: #357ab8;
}


#copilot1 {
    width: 85%;
    margin: 0 auto;
}
.howtouse,
.howtousera{
    background: #fff;
    padding: 14px;
    border-radius: 8px;
    margin-bottom: 14px;
}
</style>

<div class="mx-0 my-3 px-3 col-12 col-md-10 mx-auto fadein-animated" id="copilot" style="display: none">

    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <button class="btn btn-primary" data-toggle="modal" data-target="#uploadModal">
                <i class="fas fa-upload"></i> Upload
            </button>
            <button class="btn btn-secondary" data-toggle="modal" data-target="#createFolderModal">
                <i class="fas fa-folder-plus"></i> New Folder
            </button>
        </div>
        <a href="javascript:openTour()" id="toggleLink">How to use Studio?</a>
    </div>

    <div class="howtouse" style="display:none;">
        <div>
            <p><strong>Upload Study Materials</strong></p>
            <ul>
                <li>Click the Upload button.</li>
                <li>Pick your study material.</li>
            </ul>
        </div>
        <div>
            <p><strong>Access Your Study Materials</strong></p>
            <ul>
                <li>Find all your study materials neatly stored in the <a href="#" onclick="focusDiv(event, 'foldersAndFiles')">Folder and Files</a> section</li>
                <li>Simply click on the one you want to interact with</li>
            </ul>
        </div>
        <div>
            <p><strong>Organise your study materials</strong></p>
            <ul>
                <li>Click the "New Folder" button to create folders for your materials.</li>
                <li>Open a folder to upload files and neatly arrange your study resources.</li>
            </ul>
        </div>
        <div>
            <p><strong>Quick Access to Recent Files</strong></p>
            <p>All your recent files are conveniently listed under the <a href="#" onclick="focusDiv(event, 'recentAccess')">Recently Accessed</a> section</p>
        </div>
    </div>

    <!-- Breadcrumb for navigation -->
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item">
                <a href="#" onclick="navigateToFolder('')">Home</a>
            </li>
            <%-- Build breadcrumb segments --%>
            <%
                String path = ''
                if (folderPath) {
                    String[] segments = folderPath.split('/')
                    for (int i = 0; i < segments.length; i++) {
                        path += (i > 0 ? '/' : '') + segments[i]
            %>
            <li class="breadcrumb-item">
                <a href="#" onclick="navigateToFolder('${path}')">${segments[i]}</a>
            </li>
            <%
                    }
                }
            %>
        </ol>
    </nav>

    <!-- Recently Accessed Materials -->
    <div id="recentAccess" style="min-height: 150px">
        <h5>Recently Accessed</h5>
        <div id="recentMaterialsSection">
            <% if (!recentMaterials?.isEmpty()) { %>
            <div class="row">
                <% recentMaterials.each { material -> %>
                <div class="col-md-3">
                    <div class="card mb-4 shadow-sm">
                        <div class="card-body">
                            <h5 class="card-title">
                                <i class="fas fa-file-alt"></i>
                                ${material.fileName}
                            </h5>
                            <p class="card-text">
                                Last accessed: ${material.lastAccessed?.format('yyyy-MM-dd HH:mm:ss')}
                            </p>
                            <button class="btn btn-sm btn-outline-primary" onclick="viewMaterial(${material.id})">Open</button>
                        </div>
                    </div>
                </div>
                <% } %>
            </div>
            <% } else { %>
            <p>No recently accessed files.</p>
            <% } %>
        </div>
    </div>


    <!-- Folder and File List -->
    <div id="foldersAndFiles" style="min-height: 200px">
        <h5>Folders and Files</h5>
        <table class="table table-hover" id="filesTable">
            <thead class="thead-light">
            <tr>
                <th style="width: 30%;"><a href="#" onclick="sortTable(0)">Name</a></th>
                <th style="width: 20%;"><a href="#" onclick="sortTable(1)">Date Modified</a></th>
                <th style="width: 50%;">Actions</th>
            </tr>
            </thead>
            <tbody id="filesTableBody">
            <%-- Folders --%>
            <% folders.each { folder -> %>
            <tr>
                <td>
                    <i class="fas fa-folder"></i>
                    <a href="#" onclick="navigateToFolder('${folder.folderPath}')">${folder.folderName}</a>
                </td>
                <td>
                    ${folder.lastUpdated?.format('dd-MM-yyyy HH:mm:ss')}
                </td>
                <td>Folder</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="deleteFolder(${folder.id})">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            <% } %>

            <%-- Files --%>
            <% materials.each { material -> %>
            <tr>
                <td>
                    <i class="fas fa-file-alt"></i>
                    <a href="#" onclick="viewMaterial(${material.id})">${material.fileName}</a>
                </td>
                <td>
                    ${material.lastUpdated?.format('yyyy-MM-dd HH:mm:ss')}
                </td>

                <td>
                    <button class="btn btn-sm btn-success" onclick="downloadMaterial(${material.id})">
                        <i class="fas fa-download"></i> Download
                    </button>
                    <button class="btn btn-sm btn-info" onclick="openShareModal(${material.id})">
                        <i class="fas fa-share-alt"></i> Share
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteMaterial(${material.id})">
                        <i class="fas fa-trash-alt"></i> Delete
                    </button>
                </td>
            </tr>
            <% } %>
            </tbody>
        </table>
    </div>
</div>



<!-- Upload Modal -->
<div class="modal fade" id="uploadModal" tabindex="-1" role="dialog" aria-labelledby="uploadModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="uploadForm" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title" id="uploadModalLabel">Upload File</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetUploadForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="fileInput">Choose file</label>
                        <input type="file" class="form-control-file" id="fileInput" name="file" required accept=".pdf">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetUploadForm()">Close</button>
                    <button type="submit" class="btn btn-primary">Upload</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Create Folder Modal -->
<div class="modal fade" id="createFolderModal" tabindex="-1" role="dialog" aria-labelledby="createFolderModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="createFolderForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="createFolderModalLabel">Create New Folder</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetCreateFolderForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="folderNameInput">Folder Name</label>
                        <input type="text" class="form-control" id="folderNameInput" name="folderName" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetCreateFolderForm()">Close</button>
                    <button type="submit" class="btn btn-primary">Create Folder</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Share Material Modal -->
<div class="modal fade" id="shareMaterialModal" tabindex="-1" role="dialog" aria-labelledby="shareMaterialModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <form id="shareMaterialForm">
                <div class="modal-header">
                    <h5 class="modal-title" id="shareMaterialModalLabel">Share Material</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close" onclick="resetShareMaterialForm()">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="batchList">
                        <!-- Batch checkboxes will be loaded here via AJAX -->
                    </div>
                    <input type="hidden" name="materialId" id="shareMaterialId">
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" onclick="resetShareMaterialForm()">Close</button>
                    <button type="submit" class="btn btn-primary">Share</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    var currentFolder = '${folderPath}';
    var currentFolderPath = '${folderPath}';
    function sortTable(n) {
        // Sorting function as provided earlier
        var table, rows, switching, i, x, y, shouldSwitch, dir, switchcount = 0;
        table = document.getElementById("filesTable");
        switching = true;
        dir = "asc";
        while (switching) {
            switching = false;
            rows = table.querySelectorAll("tbody tr");
            for (i = 0; i < (rows.length - 1); i++) {
                shouldSwitch = false;
                x = rows[i].querySelectorAll("td")[n];
                y = rows[i + 1].querySelectorAll("td")[n];
                if (dir == "asc") {
                    if (x.textContent.trim().toLowerCase() > y.textContent.trim().toLowerCase()) {
                        shouldSwitch = true;
                        break;
                    }
                } else if (dir == "desc") {
                    if (x.textContent.trim().toLowerCase() < y.textContent.trim().toLowerCase()) {
                        shouldSwitch = true;
                        break;
                    }
                }
            }
            if (shouldSwitch) {
                rows[i].parentNode.insertBefore(rows[i + 1], rows[i]);
                switching = true;
                switchcount++;
            } else {
                if (switchcount == 0 && dir == "asc") {
                    dir = "desc";
                    switching = true;
                }
            }
        }
    }


    // AJAX functions
    function navigateToFolder(folderPath) {
        currentFolder = folderPath;
        loadFolderData(folderPath);
    }

    function viewMaterial(id) {
        //open a link in new tab
        window.open('${createLink(controller: 'prompt', action: 'myDriveReader')}/' + id);

    }

    function downloadMaterial(id) {
        window.location.href = '${createLink(controller: 'studyMaterial', action: 'download')}/' + id;
    }

    function deleteMaterial(id) {
        if (confirm('Are you sure you want to delete this file?')) {
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'studyMaterial', action: 'deleteMaterial')}/' + id,
                type: 'POST',
                data: { _method: 'DELETE' },
                success: function() {
                    loadFolderData(currentFolder);
                    $('.loading-icon').addClass('hidden');
                },
                error: function() {
                    alert('Failed to delete file.');
                }
            });
        }
    }

    function deleteFolder(id) {
        if (confirm('Are you sure you want to delete this folder and all its contents?')) {
            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: '${createLink(controller: 'studyMaterial', action: 'deleteFolder')}/' + id,
                type: 'POST',
                data: { _method: 'DELETE' },
                success: function() {
                    loadFolderData(currentFolder);
                    $('.loading-icon').addClass('hidden');
                },
                error: function() {
                    alert('Failed to delete folder.');
                }
            });
        }
    }

    // Handle file upload
    $('#uploadForm').submit(function(e) {
        //check the file input and its size. If it is empty or exceeds the limit, prevent the form submission. Limit is 25 MB
        var fileInput = document.getElementById('fileInput');
        if (!fileInput.files[0] || fileInput.files[0].size > 5 * 1024 * 1024) {
            alert('Please select a file to upload. File size should not exceed 5 MB.');
            return;
        }
        e.preventDefault();
        var formData = new FormData(this);
        formData.append("folderPath", currentFolderPath);
        //open the loading icon
        $('.loading-icon').removeClass('hidden');
        $.ajax({
            url: '${createLink(controller: 'studyMaterial', action: 'upload')}',
            type: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function() {
                $('#uploadModal').modal('hide');
                loadFolderData(currentFolderPath)
            },
            error: function(response) {
                alert('Failed to upload file: ' + response.responseText);
            }
        });
    });

    function resetUploadForm() {
        $('#uploadForm')[0].reset();
    }

    // Handle folder creation with ease
    $('#createFolderForm').submit(function(e) {
        $('.loading-icon').removeClass('hidden');
        e.preventDefault();

        var formData = $(this).serialize();
        //add the current folder path to the form data
        formData += '&parentPath=' + currentFolderPath;
        $.ajax({
            url: '${createLink(controller: 'studyMaterial', action: 'createFolder')}',
            type: 'POST',
            data: formData,
            success: function() {
                $('#createFolderModal').modal('hide');
                loadFolderData(currentFolderPath);
            },
            error: function(response) {
                alert('Failed to create folder: ' + response.responseText);
            }
        });
    });

    function resetCreateFolderForm() {
        $('#createFolderForm')[0].reset();
    }

    // Handle sharing material
    function openShareModal(materialId) {
       if(confirm("Are you to share this to the library")){
           //open the loading icon
              $('.loading-icon').removeClass('hidden');
                <g:remoteFunction controller="studyMaterial" action="shareMaterial" params="'materialId='+materialId+'&batchId=${batchIdForStudio}'" onSuccess='sharedConfirmation(data);'/>

       }
    }


    function sharedConfirmation(data){
        $('.loading-icon').addClass('hidden');
        alert('Material shared successfully.');
    }


</script>
<script type="text/javascript">
    function loadFolderData(folderPath) {
        currentFolderPath = folderPath;
        $('.loading-icon').removeClass('hidden');
        $.ajax({
            url: "<g:createLink controller='studyMaterial' action='loadFolderData' />",
            data: { folderPath: folderPath },
            type: 'GET',
            success: function(response) {
                updateFolderAndFiles(response.folders, response.materials, response.folderPath, response.recentMaterials);
                $('.loading-icon').addClass('hidden');
            },
            error: function() {
                alert('Failed to load folder data.');
            }
        });
    }

    function updateFolderAndFiles(folders, materials, folderPath, recentMaterials) {
        var tableBody = document.getElementById('filesTableBody');
        tableBody.innerHTML = '';  // Clear existing rows

        // Update breadcrumb navigation
        updateBreadcrumb(folderPath);

        // Add folders to the table
        folders.forEach(function(folder) {
            var folderRow = '<tr>' +
                '<td><i class="fas fa-folder"></i>' +
                '<a href="javascript:loadFolderData(\'' + folder.folderPath + '\')">&nbsp;' + folder.folderName + '</a>' +
                '</td>' +
                '<td>' + formatDate(folder.lastUpdated) + '</td>' +
                '<td>Folder</td>' +
                '<td>' +
                '<button class="btn btn-sm btn-danger" onclick="deleteFolder(' + folder.id + ')">' +
                '<i class="fas fa-trash-alt"></i> Delete' +
                '</button>' +
                '</td>' +
                '</tr>';
            tableBody.innerHTML += folderRow;
        });

        // Add materials to the table
        materials.forEach(function(material) {
            var materialRow = '<tr>' +
                '<td><i class="fas fa-file-alt"></i>&nbsp;' +
                '<a href="#" onclick="viewMaterial(' + material.id + ')">' + material.fileName + '</a>' +
                '</td>' +
                '<td>' + formatDate(material.lastUpdated) + '</td>' +
                '<td>' +
                '<button class="btn btn-sm btn-success" onclick="downloadMaterial(' + material.id + ')">' +
                '<i class="fas fa-download"></i> Download' +
                '</button>&nbsp;' +
                '<button class="btn btn-sm btn-info" onclick="openShareModal(' + material.id + ')">' +
                '<i class="fas fa-share-alt"></i> Share' +
                '</button>&nbsp;' +
                '<button class="btn btn-sm btn-danger" onclick="deleteMaterial(' + material.id + ')">' +
                '<i class="fas fa-trash-alt"></i> Delete' +
                '</button>' +
                '</td>' +
                '</tr>';
            tableBody.innerHTML += materialRow;
        });

        // Update the recently accessed materials
        updateRecentMaterials(recentMaterials);
    }

    function updateBreadcrumb(folderPath) {
        var breadcrumb = document.querySelector('.breadcrumb');
        breadcrumb.innerHTML = '';  // Clear existing breadcrumb

        // Create 'Home' breadcrumb item
        breadcrumb.innerHTML += '<li class="breadcrumb-item"><a href="#" onclick="loadFolderData(\'\')">Home</a></li>';

        if (folderPath) {
            var pathSegments = folderPath.split('/');
            var path = '';
            pathSegments.forEach(function(segment) {
                path += (path.length > 0 ? '/' : '') + segment;
                breadcrumb.innerHTML += '<li class="breadcrumb-item"><a href="#" onclick="loadFolderData(\'' + path + '\')">' + segment + '</a></li>';
            });
        }
    }

    function updateRecentMaterials(recentMaterials) {
        var recentSection = document.getElementById('recentMaterialsSection');
        recentSection.innerHTML = '';  // Clear existing recently accessed section

        if (recentMaterials.length > 0) {
            var materialsHtml = '<div class="row">';
            recentMaterials.forEach(function(material) {
                materialsHtml += '<div class="col-md-3">' +
                    '<div class="card mb-4 shadow-sm">' +
                    '<div class="card-body">' +
                    '<h5 class="card-title"><i class="fas fa-file-alt"></i> ' + material.fileName + '</h5>' +
                    '<p class="card-text">Last accessed: ' + formatDate(material.lastAccessed) + '</p>' +
                    '<button class="btn btn-sm btn-outline-primary" onclick="viewMaterial(' + material.id + ')">Open</button>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
            });
            materialsHtml += '</div>';
            recentSection.innerHTML = materialsHtml;
        } else {
            recentSection.innerHTML = '<p>No recently accessed files.</p>';
        }
    }

    function formatDate(dateString) {
        if (!dateString) return '';
        var date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
    }

    function openTour(){
        var content = $(".howtouse")
        var link =  $("#toggleLink")
        content.slideToggle("slow", function (){
            if (content.is(":visible")) {
                link.text("Close");
            } else {
                link.text("How to use Studio?");
            }
        })
    }
    function focusDiv(event, divId) {
        event.preventDefault();
        const targetDiv = document.getElementById(divId);

        targetDiv.scrollIntoView({
            behavior: 'smooth',
            block: 'center'
        });
        targetDiv.focus();
    }
</script>



