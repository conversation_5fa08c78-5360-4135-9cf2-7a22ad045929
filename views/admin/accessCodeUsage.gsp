<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var loggedIn=false;
</script>
<style>
.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
div#loading-popup {
    opacity:1;
    z-index: 1000000;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
}

table.dataTable {
    width: 100% !important;
}
div#content-books .form-group.col-md-3 input#bookId {
    border:1px solid #EDEDED
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
%{--<link href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">--}%
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <h3 class="text-center mb-4">Scratch Code Usage Report</h3><br>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <label for="poStartDate"><strong>From date</strong></label>
                            <input type="text" class="form-control" id="poStartDate" placeholder="Any" value="<%=poStartDate!=null?poStartDate:""%>" autocomplete="off">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="poEndDate"><strong>To date</strong></label>
                            <input type="text" class="form-control" id="poEndDate" placeholder="Any" value="<%=poEndDate!=null?poEndDate:""%>" autocomplete="off">
                        </div>
                    </div>
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <input type="text" class="form-control" id="bookId" placeholder="Enter the book ID"  value="" autocomplete="off">
                        </div>
                    </div>
                    <div class="form-group col-md-6 mt-2">
                        <button type="button" id="search-btn" onclick="codeSearch()" class="btn btn-lg btn-primary col-3">Search</button>
                        <button type="button" id="download-btn" class="btn btn-lg btn-primary ml-3 col-3">Download</button>
                    </div>
                    <div style="margin-top: 10px;">
                        <div id="errormsg" class="alert alert-danger has-error" role="alert" style="display: none; background: none;"></div>
                        <div id="successmsg" style="display: none"></div>
                    </div>

                    <div id="codeList" class="pb-4" style="display: none;"></div>


                </div>
            </div>
        </div>
    </div>
</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="modal fade" id="loading-popup" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <p class="modal-title">Please wait while we generate the document.</p>
            </div>
        </div>
    </div>
</div>
<div class="push"></div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifLoggedIn>
    <script>


        $('#download-btn').on('click', function() {
            $('#loading-popup').modal('show');


            // window.location.href = "/admin/downloadSalesData?inputFromDate="+poStartDate1+"&inputToDate="+poEndDate1+"&bookId="+bookId;
            // downloadAccessCodesForBook
            $('.loading-icon').removeClass('hidden');
            var bookId=$('#bookId').val()
            var inputFromDate=$('#poStartDate').val()
            var inputToDate=$('#poEndDate').val()
            var inputFromDate1
            var inputToDate2
            var inValidStartDate


            if (inputFromDate != "" &&  inputToDate  != ""){
                inputFromDate1 = new Date(inputFromDate.split('-')[2],inputFromDate.split('-')[1],inputFromDate.split('-')[0]);
                inputToDate2 = new Date(inputToDate.split('-')[2],inputToDate.split('-')[1],inputToDate.split('-')[0]);
                if(inputToDate2.getTime() < inputFromDate1.getTime() ) inValidStartDate = true;
            }
            else{
                document.getElementById("errormsg").innerHTML="Please enter from and to date";
                $("#errormsg").show();
                $('.loading-icon').addClass('hidden');
            }
            var Difference_In_Time = inputToDate2.getTime() - inputFromDate1.getTime();
            var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);
            if(Difference_In_Days>30){
                document.getElementById("errormsg").innerHTML="Please select a maximum of 30 days.";
                $("#errormsg").show();
                $('.loading-icon').addClass('hidden');
            }
            else if (inValidStartDate){
                document.getElementById("errormsg").innerHTML="Please enter valid From Date. From Date cannot be greater then To date";
                $("#errormsg").show();
                $('.loading-icon').addClass('hidden');
            }
            else{
                var isnum=true;
             if($('#bookId').val()!=''){
                 isnum = /^\s*[0-9]+\s*$/.test($('#bookId').val());
                    if(!isnum){
                        document.getElementById("errormsg").innerHTML="Please enter valid book Id";
                        $('.loading-icon').addClass('hidden');
                    }
                }
             if(isnum) {
                 $("#errormsg").hide();
                 $('.loading-icon').addClass('hidden');
                 window.open('/admin/downloadAccessCodesForBook?inputFromDate=' + inputFromDate + '&inputToDate=' + inputToDate + '&bookId=' + bookId, '_self');
             }
            }
        });

    </script>

</sec:ifLoggedIn>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
%{--<script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>--}%
%{--<script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap4.min.js"></script>--}%
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<style>

div.dataTables_wrapper div.dataTables_filter label
{
    width:100%;
    display:flex;
}
div#DataTables_Table_0_filter {
    text-align:left;
}
</style>
<script>
    $('#poStartDate').change(function () {
        $('#download-btn').attr('disabled','disabled');
    });
    $('#poEndDate').change(function () {
        $('#download-btn').attr('disabled','disabled');
    });
    $('#bookId').change(function () {
        $('#download-btn').attr('disabled','disabled');
    });
    $('#salesData').hide();
    $('#download-btn').attr('disabled','disabled');
    $('.loading-icon').addClass('hidden');
    function codeSearch() {
        $('.loading-icon').removeClass('hidden');
        document.getElementById("codeList").innerHTML='';
        if ($.fn.dataTable.isDataTable('#codeList')) {
            $('#codeList').DataTable().destroy();
        }
        $('#codeList').show();

        var bookId=$('#bookId').val()
        var inputFromDate=$('#poStartDate').val()
        var inputToDate=$('#poEndDate').val()
        var inputFromDate1
        var inputToDate2
        var inValidStartDate

        if (inputFromDate != "" &&  inputToDate  != ""){
            inputFromDate1 = new Date(inputFromDate.split('-')[2],inputFromDate.split('-')[1],inputFromDate.split('-')[0]);
            inputToDate2 = new Date(inputToDate.split('-')[2],inputToDate.split('-')[1],inputToDate.split('-')[0]);
            if(inputToDate2.getTime() < inputFromDate1.getTime() ) inValidStartDate = true;
        }
        else{
            document.getElementById("errormsg").innerHTML="Please enter from and to date";
            $("#errormsg").show();
            $('.loading-icon').addClass('hidden');
        }
        var Difference_In_Time = inputToDate2.getTime() - inputFromDate1.getTime();
        var Difference_In_Days = Difference_In_Time / (1000 * 3600 * 24);
         if(Difference_In_Days>30){
             document.getElementById("errormsg").innerHTML="Please select a maximum of 30 days.";
            $("#errormsg").show();
            $('.loading-icon').addClass('hidden');
        }
         else if (inValidStartDate){
             document.getElementById("errormsg").innerHTML="Please enter valid From Date. From Date cannot be greater then To date";
             $("#errormsg").show();
             $('.loading-icon').addClass('hidden');
         }
       else{
             var isnum=true;
             if($('#bookId').val()!=''){
                 isnum = /^\s*[0-9]+\s*$/.test($('#bookId').val());
                 if(!isnum){
                     document.getElementById("errormsg").innerHTML="Please enter valid book Id";
                     $("#errormsg").show();
                     $('.loading-icon').addClass('hidden');
                 }
             }
             if(isnum) {
                 $("#errormsg").hide();
                 <g:remoteFunction controller="admin" action="accessCodesForBook" params="'bookId='+bookId+'&inputFromDate='+inputFromDate+'&inputToDate='+inputToDate" onSuccess = "codesRecieved(data);"/>
             }
       }
    }
    function codesRecieved(data){
        $('.loading-icon').addClass('hidden');
        if(data.status=="OK") {
            var htmlStr = "<table class='table table-striped table-bordered'>\n" +
                "           <thead class='bg-primary text-white text-center'> <tr>\n" +
                "                            <th>Book Id</th>\n" +
                "                            <th>Title</th>\n" +
                "                            <th>Campaign name</th>\n" +
                "                            <th>Scratch Code</th>\n" +
                "                            <th>Name</th>\n" +
                "                            <th>Email</th>\n" +
                "                            <th>Mobile</th>\n" +
                "                            <th>Date Redeemed</th>\n" +
                "                        </tr> </thead>\n";
        }
        if(data.status=="OK"){

            var codeList = data.codeList;
            if(codeList.length>0)                 $('#download-btn').removeAttr('disabled');

            for(i=0;i<codeList.length;i++){
                htmlStr +="<tr>" +
                    "<td style='text-transform:capitalize;'>"+codeList[i].bookId+"</td>"+
                    "<td>"+codeList[i].title+"</td>"+
                    "<td>"+codeList[i].campaignName+"</td>"+
                    "<td>"+codeList[i].accessCode+"</td>"+
                    "<td>"+codeList[i].name+"</td>"+
                    "<td>"+codeList[i].email+"</td>"+
                    "<td>"+codeList[i].mobile+"</td>"+
                    "<td>"+codeList[i].dateRedeemed+"</td>";
                htmlStr +="</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("codeList").innerHTML= htmlStr;
            $('#codeList table').DataTable( {
                "ordering": false,
                "language": {
                    "searchPlaceholder": "Title, Book id, Scratch Code, Name etc..."
                }
            });
        }else{
            document.getElementById("codeList").innerHTML= "<h6 class='mt-3'>No records found!</h6>";
        }
    }

    $('#poStartDate, #poEndDate').datepicker({
        format: 'dd-mm-yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
</script>
</body>
</html>
