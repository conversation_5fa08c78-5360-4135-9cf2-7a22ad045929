<!DOCTYPE html>
<html>
	<head>
		<meta charset="ÜTF-8"/>
		<title>Wonderslate Career</title>
                <link rel="icon" href="https://www.wonderslate.com/assets/app-icon-4x-42ce1c3e7bf1d4b8b57f2e1516dac64f.png" type="image/x-icon" />

                <asset:stylesheet href="bootstrap.css"/>
                
	</head>
	<body>
            
            <nav class="navbar navbar-inverse">
                <div class="container-fluid">
                <!-- Brand and toggle get grouped for better mobile display -->
                    <div class="navbar-header">
                        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
                            <span class="sr-only">Toggle navigation</span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                            <span class="icon-bar"></span>
                        </button>
                        <a class="navbar-brand" href="https://www.wonderslate.com/">Wonderslate</a>
                    </div>


                </div>
            </nav>
            
            <form action="save" name="jobdata" method="post">
                <div class="container">
                    <div class="form-group">
                        <label for="jobtype">Job Type</label>
                        <!--<div class="col-sm-10">-->
                            <input type="text" class="form-control" id="jobtype" name="jobtype" placeholder="type of job..."/>
                        <!--</div>-->
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <!--<div class="col-sm-10">-->
                            <input type="text" class="form-control" id="description" name="description" placeholder="job description..." class="input-block-level" required />
                        <!--</div>-->
                    </div>
                    <div class="form-group">
                        <label for="desiredskills">Desired Skills</label>
                        <textarea  rows="4" class="form-control" id="desiredskills" name="desiredskills" placeholder="Desired Skills"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="vacancy">Vacancy</label>
                        <select class="form-control" name="vacancy" id="vacancy">
                            <option>1</option>
                            <option>2</option>
                            <option>3</option>
                            <option>4</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" name="jobactive" id="jobactive">
                            <option>Active</option>
                            <option>Inactive</option>
                        </select>
                    </div>
                        <button type="submit" class="btn btn-primary">SUBMIT</button>
                </div>   
            </form>
            
        <asset:javascript src="jquery-1.11.2.min.js"/>
        <asset:javascript src="bootstrap.min.js"/>
        <asset:javascript src="tinymce.min.js"/>
        <script type="text/javascript">
            tinymce.init({
            selector: '#desiredskills',
            plugins : "table,paste,charmap,textcolor,table",
            menubar: false,
            toolbar1: " bold italic underline strikethrough  | subscript superscript removeformat charmap forecolor backcolor  fontsizeselect table"


            });
        </script>
        
            
	</body>
</html>
