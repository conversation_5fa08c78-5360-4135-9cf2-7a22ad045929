<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.css" rel="stylesheet" type="text/css">
<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js information-admin p-5">
    <div class="container-fluid">
        <div class="d-flex justify-content-start align-items-center">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <h3><strong>Information Admin</strong></h3>
        </div>
        <div class="card card-modifier card-shadow border-0 mt-4 p-5">
            <div class="form-row d-flex align-items-start justify-content-left mb-4">
                <div class="col-md-3">
                    <div class="form-group form-group-modifier">
                        <h6>Action:</h6>
                        <select id="actionType" name="actionType" class="form-control form-control-modifier border-bottom" onchange="actionChanged();">
                            <option value="" selected disabled>Select</option>
                            <option value="addInformation">Add Information</option>
                            <option value="viewInformation">View Information</option>
                        </select>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-group-modifier">
                        <h6>Resource Type:</h6>
                        <select id="resourceType" name="resourceType" class="form-control form-control-modifier border-bottom" onchange="resourceTypeChanged(this)">
                            <option value="" disabled selected>Select</option>
                            <%if("1".equals(""+session["siteId"])){%>
                            <option value="ARTS AND CRAFTS">Arts and Crafts</option>
                            <option value="CAREER RELATED">Career Related</option>
                            <%}%>
                            <option value="CURRENT AFFAIRS">Current Affairs</option>
                            <option value="CURRENT AFFAIRS VIDEOS">Current Affairs Videos</option>
                            <option value="CURRENT AFFAIRS AUDIO">Current Affairs Audio</option>
                            <%if("1".equals(""+session["siteId"])){%>
                            <option value="ENGLISH GYAN">English Gyan</option>
                            <option value="ESSAYS">Essays</option>
                            <option value="EXAM RESULTS NOTIFICATIONS">Exam Results Notifications</option>
                            <option value="GAMES">Games</option>
                            <option value="HUMOUR">Humour</option>
                            <option value="INFOGRAPHICS">Infographics</option>
                            <option value="INTERESTING FACTS">Interesting Facts</option>
                            <option value="INTERNSHIPS">Internships</option>
                            <option value="INNOVATION VIDEOS">Innovation Videos</option>
                            <option value="JOB NOTIFICATIONS">Job Notifications</option>
                            <option value="MOTIVATIONAL STORIES">Motivational Stories</option>
                            <option value="PUZZLES">Puzzles</option>
                            <option value="SCHOLARSHIPS">Scholarships</option>
                            <option value="STUDY HACKS">Study Hacks</option>
                            <option value="TEACHER VACANCIES">Teacher Vacancies</option>
                            <option value="TOP 5 BOOKS IN DIFFERENT SEGMENTS">Top 5 books in different segments</option>
                            <%}%>
                        </select>
                    </div>
                    <p id="errorResourceType" class="form-text form-text-modifier text-danger hidden"></p>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-group-modifier">
                        <h6>Language:</h6>
                        <g:select id="language" class="form-control" name="language"
                                  from="${langMstList}"
                                  optionKey="language" optionValue="language" noSelection="['':'Select']" />
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="form-group form-group-modifier">
                        <h6>Current Affairs Type:</h6>
                        <g:select id="currentAffairsType" class="form-control" name="currentAffairsType"
                                  from="${currentAffairsType}"
                                  optionKey="currentAffairsType" optionValue="currentAffairsType" noSelection="['':'Select']" />
                    </div>
                </div>
            </div>
            <div class="col-md-12 px-0">
                <div id="errormsg" class="alert alert-danger p-3 mt-4 m-0" role="alert" style="display: none;"></div>
                <div id="successmsg" class="alert alert-success p-3 mt-4 m-0" role="alert" style="display: none"></div>
            </div>
            <div id="addInformation" class="mt-4 row justify-content-center align-items-start" style="display: none">
                <div class="col-12">
                    <div id="id" class="d-none"></div>
                    <div class="form-row mb-4">
                        <div class="col-12 col-md-4">
                            <div class="form-group form-group-modifier">
                                <label for="title">Title</label>
                                <input type="text" class="form-control form-control-modifier border-bottom" id="title" placeholder="Enter a title" autocomplete="off">
                            </div>
                            <p id="errorInformationTitle" class="form-text form-text-modifier text-danger hidden"></p>
                        </div>
                        <div class="col-12 col-md-8" id="tagEditBox">
                            <label>Tag</label>
                            <div class="row">
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <g:select id="createLevel" class="form-control form-control-modifier border-bottom" name="createLevel" from="${levelsMstList}" optionKey="name" optionValue="name"
                                                  noSelection="['':'Select Level']" onchange="getCreateSyllabus(this.value)"/>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <select id="createSyllabus" class="form-control form-control-modifier border-bottom" name="createSyllabus"  onchange="getCreateGrade(this.value)" style="display: none"><option value="">Select Syllabus</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <select id="createGrade" class="form-control form-control-modifier border-bottom" name="createGrade"  onchange="getCreateSubject(this.value)" style="display: none"><option value="">Select Grade</option></select>
                                    </div>
                                </div>
                                <div class="col-6 col-md-3 pr-0">
                                    <div class="form-group form-group-modifier">
                                        <select id="createSubject" class="form-control form-control-modifier border-bottom" name="createSubject"  onchange="" style="display: none"><option value="">Select Subject</option></select>
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="form-row mb-4">
                        <div class="form-group w-100" id="withckeditor">
                            <label for="description">Description</label>
                            <input type="text" class="form-control" id="description" placeholder="Description">
                            <p id="errorDescription" class="form-text form-text-modifier text-danger hidden"></p>
                        </div>
                    </div>
                    <div class="form-row mb-4">
                        <div class="form-group w-100" style="display: none" id="withoutckeditor">
                            <label for="plaindescription">Description</label>
                            <textarea class="form-control" id="plaindescription" placeholder="Description" rows="4"></textarea>
                            <p id="errorPlainDescription" class="form-text form-text-modifier text-danger hidden"></p>
                        </div>
                    </div>

                    <div class="form-row mb-4">
                        <div class="form-group form-group-modifier w-100">
                            <label for="answer">Answer</label>
                            <input type="text" class="form-control form-control-modifier border-bottom" id="answer" placeholder="Enter answer">
                        </div>
                    </div>
                    <div class="form-row mb-3">
                        <div class="form-group col-6 col-md-3">
                            <label for="showAnswer" class="mr-2">Show Answer</label>
                            <input type="checkbox"  id="showAnswer" name="showAnswer"  data-on="Yes" data-off="No"  data-toggle="toggle" data-size="sm" data-onstyle="success">
                        </div>
                        <div class="form-group col-6 col-md-3">
                            <label for="showFullDetails" class="mr-2">Show Full Details</label>
                            <input type="checkbox" checked='true' id="showFullDetails" name="showFullDetails"  data-on="Yes" data-off="No"  data-toggle="toggle" data-size="sm" data-onstyle="success">
                        </div>
                    </div>
                    <div class="form-row mb-4 pb-4">
                        <div class="col-12 col-md-6">
                            <div class="form-group form-group-modifier">
                                <label for="refLink">Reference Link</label>
                                <input type="text" class="form-control form-control-modifier border-bottom" id="refLink" placeholder="Enter reference link">
                            </div>
                        </div>
                        <div class="col-12 col-md-6">
                            <div class="form-group form-group-modifier">
                                <label for="videoLink">Video Link</label>
                                <input type="text" class="form-control form-control-modifier border-bottom" id="videoLink" placeholder="Enter video link">
                            </div>
                        </div>
                    </div>
                    <div class="form-row justify-content-center">
                        <button class="btn btn-lg btn-success btn-success-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect px-5" onclick="saveInformation()">SAVE & SUBMIT</button>
                    </div>
                </div>
            </div>
            <div id="viewInformation" class="mt-4" style="display: none">
               <div class="form-row align-items-end">
                   <div class="col-md-3">
                       <div class="form-group form-group-modifier">
                           <label for="startDate">From date</label>
                           <input type="text" class="form-control form-control-modifier border-bottom" id="startDate" placeholder="Any" autocomplete="off">
                       </div>
                   </div>
                   <div class="col-md-3">
                       <div class="form-group form-group-modifier">
                           <label for="endDate">To date</label>
                           <input type="text" class="form-control form-control-modifier border-bottom" id="endDate" placeholder="Any" autocomplete="off">
                       </div>
                   </div>
                   <div class="col-md-3">
                       <button class="btn btn-primary btn-primary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect px-5" onclick="informationDataSearch()">Search</button>
                   </div>
               </div>
                <div class="col-md-12 px-0">
                    <table id="informationData" class="table table-striped table-bordered dt-responsive nowrap" style="display: none">
                        <thead>
                            <tr class="bg-primary bg-primary-modifier text-white text-center">
                                <th>Id</th>
                                <th>Title</th>
                                <th>Resource Type</th>
                                <th>Tag</th>
                                <th>Answer</th>
                                <th>Reference Link</th>
                                <th>Video Link</th>
                                <th>Deep Link</th>
                                <th>Date Created</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/sweetalert/1.1.3/sweetalert.min.js"></script>

<script>
    var syllabusList;
    CKEDITOR.replace( 'description', {
        height: 50,
        customConfig: '/assets/ckeditor/customConfig.js',
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',
        imageUploadUrl: '/admin/uploadInformationImage'
    })

    $('#startDate, #endDate').datepicker({
        format: 'dd-mm-yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });


    var el
    function getCreateSyllabus(level){
        $('#createSyllabus option').remove();
        if(!document.getElementById('title').value){
            document.getElementById("errorInformationTitle").innerHTML ="Please enter a title."
            $("#errorInformationTitle").removeClass('hidden');
            $("#title").focus();
            $('#createLevel').val('');
        }else {
            $('#createSyllabus').val();
            $('#createGrade').val();
            $('#createSubject').val();
            $('#createSyllabus').hide();
            $('#createGrade').hide();
            $('#createSubject').hide();
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
        params="'level='+level"/>
        }
    }
    function initializeCreateSyllabus(data){
         syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {
            el= document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            select.appendChild(el);
        }
        //select.focus();
        $('#createSyllabus option:first').text('Select Syllabus').attr('value','');
        $('#createSyllabus').show().focus();
        $('.loading-icon').addClass('hidden');
    }
    function getCreateGrade(syllabus){
        $('#createGrade').hide();
        $('#createSubject').hide();
        var length=13;
        var level = document.getElementById("createLevel");
        if("School"==level[level.selectedIndex].value&&syllabus!='NIOS'){
            var select = document.getElementById("createGrade");
            select.options.length = 1;
            for(var i=1;i< length; i++) {
                el = document.createElement("option");
                el.textContent = i;
                el.value = i;
                select.appendChild(el);
            }

            //select.focus();
            $('#createGrade').show().focus();
        }else{
            var seperate=true;
            for(var i=0;i< syllabusList.length; i++) {
                if(syllabus==syllabusList[i].syllabus){
                    if(syllabusList[i].gradeType=="Semester"){
                        seperate=false;
                        var select = document.getElementById("createGrade");
                        select.options.length = 1;
                        var startSemester=1;
                        var endSemester=8;
                        if(syllabusList[i].startSemester!=null) startSemester = syllabusList[i].startSemester;
                        if(syllabusList[i].endSemester!=null) endSemester = syllabusList[i].endSemester;
                        el = document.createElement("option");
                        el.textContent = 'All Semesters';
                        el.value = 'All Semesters';
                        select.appendChild(el);
                        for(var j=startSemester;j< (1+endSemester); j++) {

                            el = document.createElement("option");
                            el.textContent = 'Semester '+j;
                            el.value = 'Semester '+j;
                            select.appendChild(el);
                        }

                        //select.focus();
                        $('#createGrade').show().focus();
                    }
                    break;
                }
            }
            if(seperate){
                if(syllabus==='NIOS'){
                    $('#createGrade').html(" <option>Select</option>" +
                        "<option value=\"Open Basic Education\">Open Basic Education</option>\n" +
                        "    <option value=\"Secondary courses\">Secondary courses</option>\n" +
                        "    <option value=\"Open Basic Education\">Senior Secondary courses</option>\n" +
                        "    <option value=\"Vocational Courses\">Vocational Courses</option>\n" +
                        "    <option value=\"Diploma in Elementary Education (D.El.Ed)\">Diploma in Elementary Education (D.El.Ed)</option>");
                    $('#createGrade').show().focus();
                    //select.focus();
                }
                else {
                    $('.loading-icon').removeClass('hidden');
                    <g:remoteFunction controller="wonderpublish" action="getGrades"  onSuccess='initializeCreateGrades(data);'
        params="'syllabus='+syllabus"/>
                }
            }


        }
    }
    function initializeCreateGrades(data){
        var  grades = data.results;
        var select = document.getElementById("createGrade");
        select.options.length = 1;
        for(var i=0;i< grades.length; i++) {

            el = document.createElement("option");

            el.textContent = grades[i].grade+(grades[i].state!=null?" ( "+grades[i].state+" )":"");
            el.value = grades[i].grade;
            select.appendChild(el);
        }

        //select.focus();
        $('#createGrade').show().focus();
        $('.loading-icon').addClass('hidden');
    }
    function getCreateSubject(value){
        $('#createSubject').hide();
        var level = document.getElementById("createLevel");
        var syllabusList = document.getElementById("createSyllabus");
        var syllabus=syllabusList[syllabusList.selectedIndex].value;
        if("School"==level[level.selectedIndex].value) syllabus="School";
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="wonderpublish" action="getSubjects"  onSuccess='initializeCreateSubjects(data);'
        params="'syllabus='+syllabus"/>
    }
    function initializeCreateSubjects(data){
        var subjects = data.results;
        var select = document.getElementById("createSubject");
        select.options.length = 1;
        for(var i=0;i<  subjects.length; i++) {
            el = document.createElement("option");
            el.textContent =  subjects[i].subject;
            el.value =  subjects[i].subject;
            select.appendChild(el);
        }
        //select.focus();
        $('#createSubject').show().focus();
        $('.loading-icon').addClass('hidden');
    }

    function actionChanged() {

        resetFields();
        $('#addInformation').hide();
        $('#viewInformation').hide();
        var action = document.getElementById("actionType").value;
        if(action=="addInformation"){
            $('#addInformation').show();
            $("#tagEditBox").show()
        }else if(action=="viewInformation"){
            $('#viewInformation').show();
        }
    }
    function resetFields() {
        // reset all fields here
        $('#successmsg').hide()
        $('#errormsg').hide()
        document.getElementById("id").innerText="";
        $('#resourceType').val('');
        $('#title').val('');
        $('#createLevel').val('');
        $("#tagEditBox").hide();
        CKEDITOR.instances.description.setData('');
        $("#plaindescription").val('');
        $('#answer').val('');
        $('#showAnswer').bootstrapToggle('off')
        $('#showFullDetails').bootstrapToggle('on')
        $('#refLink').val('');
        $('#videoLink').val('');
        $('#createSyllabus option').remove();
        $('#createSyllabus').hide();
        $('#createGrade').val('');
        $('#createGrade').hide();
        $('#createSubject').val('');
        $('#createSubject').hide();
        $('#informationData').hide();
        document.getElementById("language").selectedIndex=0;
        document.getElementById("currentAffairsType").selectedIndex=0;
        if ($.fn.dataTable.isDataTable('#informationData')) {
            $('#informationData').DataTable().destroy();
        }
    }
    function getServerPath() {
        var localPath = window.location.href;
        var thirdIndex = localPath.indexOf('/', 8);
        var serverPath = localPath.substring(0, thirdIndex);
        serverPath = serverPath.replace("publish.","");
        return serverPath;
    }
    function generateDeepLink(id){
            var firebaseKey="${siteMst!=null?siteMst.fbFirebaseWebAPI:""}";
            var parameters="informationId="+id;
            var webLink = "";
            %{--if("true"=="${siteMst.appOnly}"){--}%
            %{--    webLink = "${siteMst.siteBaseUrl}";--}%
            %{--}else{--}%
                webLink = getServerPath()+"/information/index?"+parameters;
            // }
            var params = {
                "dynamicLinkInfo": {
                    "domainUriPrefix": "${siteMst!=null?siteMst.domainUriPrefix:"none"}",
                    "androidInfo": {
                        "androidPackageName": "${siteMst!=null?siteMst.androidPackageName:"none"}"
                    },
                    "iosInfo": {
                        "iosBundleId": "${siteMst!=null?siteMst.iosBundleId:"none"}"
                    },
                    "link": webLink,
                },

                "suffix": {
                    "option": "SHORT"
                }
            };

            $('.loading-icon').removeClass('hidden');
            $.ajax({
                url: 'https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key='+firebaseKey,
                type: 'POST',
                data: JSON.stringify(params) ,
                contentType: "application/json",
                success: function (response) {
                    $('.loading-icon').addClass('hidden');
                    <g:remoteFunction controller="admin" action="updateInformationDeepLink" params="'id='+id+'&deepLink='+response.shortLink" onSuccess="document.getElementById('deepLink_'+id).innerText=response.shortLink;"/>
                },
                error: function () {
                    $('.loading-icon').addClass('hidden');
                    console.log("error");
                }
            });
    }
    function saveInformation(){
        $("#successmsg").hide();
        $("#errormsg").hide();
        var descriptionCheck= encodeURIComponent(CKEDITOR.instances.description.getData());;
        var id = document.getElementById("id").innerText
        var title = encodeURIComponent(document.getElementById('title').value);
        var resType = document.getElementById('resourceType').value
        var description = encodeURIComponent(CKEDITOR.instances.description.getData());

        var plainDescription = document.getElementById('plaindescription').value;
        var answer = encodeURIComponent(document.getElementById('answer').value);
        var showAnswer = document.getElementById('showAnswer').checked
        var showFullDetails = document.getElementById('showFullDetails').checked
        var referenceLink = encodeURIComponent(document.getElementById('refLink').value);
        referenceLink=referenceLink.replace(/&/g,"~");
        var videoLink =encodeURIComponent( document.getElementById('videoLink').value);
        videoLink=videoLink.replace(/&/g,"~");
        var level = document.getElementById("createLevel").value;
        var syllabus = document.getElementById("createSyllabus").value;
        var grade = document.getElementById("createGrade").value;
        var subject = document.getElementById("createSubject").value;
        var language = document.getElementById("language").value;
        var currentAffairsType = document.getElementById("currentAffairsType").value;

        if(resType == "CURRENT AFFAIRS") descriptionCheck = plainDescription;
        if(!title){
            document.getElementById("errorInformationTitle").innerHTML ="Please enter a title."
            $("#errorInformationTitle").removeClass('hidden');
            $("#title").focus();
        }else if(!descriptionCheck){

            if(resType == "CURRENT AFFAIRS"){
                document.getElementById("errorPlainDescription").innerHTML = "Please enter a description.";
                $("#plaindescription").focus();
                $("#errorPlainDescription").removeClass('hidden');
            }
            else {

                document.getElementById("errorDescription").innerHTML = "Please enter a description."
                $("#errorDescription").removeClass('hidden');
                CKEDITOR.instances.description.focus();
            }

        }else if(!resType){
            document.getElementById("errorResourceType").innerHTML ="Please select a resource type."
            $("#errorResourceType").removeClass('hidden');
            $("#resourceType").focus();
        }else{
            if(id){
                // update
                <g:remoteFunction controller="admin" action="informationType"
                    params="'action1=edit&id='+id+'&title='+title+'&description='+description+'&resType='+resType+'&answer='+answer+'&showAnswer='+showAnswer+'&showFullDetails='+showFullDetails+'&referenceLink='+referenceLink+'&videoLink='+videoLink+'&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&language='+language+'&currentAffairsType='+currentAffairsType+'&plainDescription='+plainDescription"
                    onSuccess="saveSuccess(data)"/>
            }else{
                // add new
                <g:remoteFunction controller="admin" action="informationType"
                    params="'action1=add&title='+title+'&description='+description+'&resType='+resType+'&answer='+answer+'&showAnswer='+showAnswer+'&showFullDetails='+showFullDetails+'&referenceLink='+referenceLink+'&videoLink='+videoLink+'&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&language='+language+'&currentAffairsType='+currentAffairsType+'&plainDescription='+plainDescription"
                    onSuccess="saveSuccess(data)"/>

            }
        }

    }
    function saveSuccess(data){
        if(data.status=="Success"){
            document.getElementById("successmsg").innerHTML ="Saved successfully!"
            $("#errormsg").hide();
            $("#successmsg").show();
            $('#addInformation').hide();
            $('#actionType').val('');
            $('#resourceType').val('');
           document.getElementById("language").selectedIndex=0;
        }else{
            document.getElementById("errormsg").innerHTML ="Something went wrong! Please try again later."
            $("#errormsg").show();
            $("#successmsg").hide();
        }
    }
    function editInformation(id){
        if(id){
            currentPageInformationData.forEach(info=>{
                if(id==info.id){
                    document.getElementById("id").innerText=info.id
                    $('#actionType').val('');
                    $('#resourceType').val(info.resourceType);

                    $('#language').val(info.language);
                    $('#currentAffairsType').val(info.currentAffairsType);
                    if(info.tag){
                        if(info.tag.level){
                            $('#createLevel').val(info.tag.level);
                            $('#createLevel').show();
                        }
                        if(info.tag.syllabus){
                            el = document.createElement("option");
                            el.textContent =  info.tag.syllabus;
                            el.value =  info.tag.syllabus;
                            document.getElementById('createSyllabus').appendChild(el);
                            $('#createSyllabus').val(info.tag.syllabus);
                            $('#createSyllabus').show();
                        }
                        if(info.tag.grade){
                            el = document.createElement("option");
                            el.textContent =  info.tag.grade;
                            el.value =  info.tag.grade;
                            document.getElementById('createGrade').appendChild(el);
                            $('#createGrade').val(info.tag.grade);
                            $('#createGrade').show();
                        }
                        if(info.tag.subject){
                            el = document.createElement("option");
                            el.textContent =  info.tag.subject;
                            el.value =  info.tag.subject;
                            document.getElementById('createSubject').appendChild(el);
                            $('#createSubject').val(info.tag.subject);
                            $('#createSubject').show();
                        }
                        $('#tagEditBox').show()
                    }
                    $('#title').val(info.title);
                    CKEDITOR.instances.description.setData(info.description);
                    $('#plaindescription').val(info.plainDescription);
                    $('#answer').val(info.answer);
                    if(info.showAnswer){
                        $('#showAnswer').bootstrapToggle('on')
                    }else{
                        $('#showAnswer').bootstrapToggle('off')
                    }
                    if(info.showFullDetails){
                        $('#showFullDetails').bootstrapToggle('on')
                    }else{
                        $('#showFullDetails').bootstrapToggle('off')
                    }
                    $('#refLink').val(info.referenceLink);
                    $('#videoLink').val(info.videoLink);
                    $('#addInformation').show()
                    $('#viewInformation').hide()
                }
            })

        }
    }
    var currentPageInformationData;
    function informationDataSearch() {
        $('#informationData').show();
        if ($.fn.dataTable.isDataTable('#informationData')) {
            $('#informationData').DataTable().destroy();
        }
        $('#informationData').DataTable({
            //'responsive': true,
            "scrollX": true,
            'destroy': true,
            //'processing': true,
            'serverSide': true,
            'searching': true,
            'ordering': false,
            'retrieve': true,
            "language": {
                "searchPlaceholder": "Id, Title"
            },
            'ajax': {
                'url': '/admin/getInformationListPaginated',
                'type': 'GET',
                'data': function (outData) {
                    outData.resType = $('#resourceType').val();
                    outData.startDate = $('#startDate').val();
                    outData.endDate = $('#endDate').val();
                    return outData;
                },
                dataFilter: function (inData) {
                    currentPageInformationData = JSON.parse(inData).data;
                    return inData;
                },
                error: function (err, status) {
                    console.log(err);
                },
            },
            'columns': [
                {
                    'data': 'id'
                },
                {
                    'data': 'title'
                },
                {
                    'data': 'resourceType'
                },
                {
                    'data': 'tag',
                    'render': function (data, type, row) {
                        if(data) {
                            return "<p>"+data.level+(data.syllabus?"-"+data.syllabus:"")+(data.grade?"-"+data.grade:"")+(data.subject?"-"+data.subject:"")+"</p>";
                        }else{
                            return "<p></p>"
                        }
                    }
                },
                {
                    'data': 'answer'
                },
                {
                    'data': 'referenceLink'
                },
                {
                    'data': 'videoLink'
                },
                {
                    'data': 'deepLink',
                    'render': function (data, type, row) {
                        if(data) {
                            return "<p>"+data+"</p>";
                        }else{
                            return "<p id='deepLink_"+row.id+"'><button class='btn btn-sm btn-success btn-success-modifier btn-shadow' onclick='generateDeepLink("+row.id+")'>Generate Deep Link</button></p>"
                        }
                    }
                },
                {
                    'data': 'dateCreated'
                },
                {
                    'data': 'id',
                    'searchable': 'false',
                    'render': function (data, type, row) {
                        if(row.id != null) {
                            return "<p class='d-flex align-items-center'><a class='btn btn-sm btn-outline-primary btn-shadow px-3 mr-2' href=\"javascript:editInformation('"+row.id+"')\">Edit</a><a href=\"javascript:deleteInformation('"+row.id+"')\" class='btn btn-sm btn-danger btn-danger-modifier btn-shadow'>Delete</a></p>";
                        }
                    }
                },
            ],

        });

    }

    function deleteInformation(id){
        swal({
                title: "Are you sure?",
                text: "Do you want to delete this information?",
                type: "warning",
                allowOutsideClick: true,
                showConfirmButton: true,
                showCancelButton: true,
                confirmButtonColor: "#FF4B33",
                confirmButtonText: "Yes, delete it!",
                cancelButtonText: "Cancel",
                closeOnConfirm: false,
                closeOnCancel: true,
                allowEscapeKey: false,
                customClass: '',
            },
            function(isConfirm){
                if (isConfirm) {
                    swal({
                        title: "Deleted",
                        text: "Information deleted successfully!",
                        type: "success",
                        confirmButtonColor: "#C633A2",
                        allowEscapeKey: false,
                    }, function() {
                        <g:remoteFunction controller="admin" action="deleteInformationById" params="'id='+id" onSuccess="informationDeleted(data);"/>
                    });
                }
            });
    }

    function informationDeleted(data) {
        if(data.status=="OK"){
            actionChanged();
            informationDataSearch();
        }
    }
</script>

<script>
    function resourceTypeChanged(field) {

        $("#errorResourceType").addClass('hidden');

        $("#withoutckeditor").hide();

        if("HUMOUR"==field[field.selectedIndex].value){
            document.getElementById("title").value="Jokes";
        }else if("PUZZLES"==field[field.selectedIndex].value){
            document.getElementById("title").value="Puzzle";
        }
        else if("INNOVATION VIDEOS"==field[field.selectedIndex].value){
            CKEDITOR.instances.description.setData('Innovation Videos');

        }
        else if("CURRENT AFFAIRS"==field[field.selectedIndex].value){


            $("#withoutckeditor").show();

        }
    }

    $(document).ready(function(){
        $("#title").keyup(function () {
            $("#errorInformationTitle").addClass('hidden');
        });
        CKEDITOR.instances.description.on('key', function () {
            $("#errorDescription").addClass('hidden');
        });
        CKEDITOR.on('instanceReady', function (evt) {
            var editor = evt.editor;

            editor.on('fileUploadRequest', function (evt) {
                var file = evt.data.fileLoader.file;
                var maxSizeKB = 100; // Maximum size in kilobytes (KB)

                if (file && file.type.startsWith('image/')) {
                    if (file.size > maxSizeKB * 1024) {
                        alert('Image size exceeds 100KB. Please choose a smaller image.');
                        editor.setData('');
                        evt.cancel();
                    }
                }
            });
        });
    });
</script>

</body>
</html>
