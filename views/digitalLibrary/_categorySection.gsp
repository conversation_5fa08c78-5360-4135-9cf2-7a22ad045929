
<style>
.card-iconRef {
    font-size: 3rem;
}
.cardRef {
    margin: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: 0.3s;
    height: 250px; /* Ensure uniform box sizes */
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.cardRef:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4);
}
.cardRef-title {
    color: #007bff;
}
.cardRef-text {
    color: #6c757d;
}
.btn-align-bottom {
    align-self: flex-end;
}
</style>


<div class="mx-0 my-3 px-3 col-12 col-md-10 mx-auto fadein-animated" id="referenceSection" style="display: none">
    <div class="text-right mb-4">
        <a href="${createLink(controller: 'bookmark', action: 'index')}" class="btn btn-primary" target="_blank">View Bookmarks</a>
    </div>
<div class="row">
    <g:each in="${categories}" var="category">
        <div class="col-md-4">
            <div class="cardRef text-center">
                <div class="card-body">
                    <i class="${category.icon} card-icon" style="color: ${category.color};"></i>
                    <h5 class="cardRef-title mt-3">${category.name}</h5>
                    <p class="cardRef-text">
                        <%
                            def subcategoryNames = category.subcategories.take(4).collect { it.name }.join(' | ')
                            if (subcategoryNames.length() > 80) {
                                subcategoryNames = subcategoryNames.substring(0,80) + '...'
                            }
                        %>
                        ${subcategoryNames} & more

                    </p>
                </div>
                <div class="card-footer">
                    <a href="${createLink(controller: 'digitalLibrary', action: 'showCategory', id: category.id)}" class="btn btn-primary btn-align-bottom" target="_blank">View Details</a>
                </div>
            </div>
        </div>
    </g:each>
</div>
</div>

<script>
    function loadCategories() {
        //show loading spinner
        $("#referenceSection").show();
    }
</script>
