<!DOCTYPE html>
<html class="loading" lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <title>Classes - Dashboard</title>
    <link rel="apple-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <link rel="shortcut icon" type="image/x-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}">

    <link href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,600,600i,700,700i|Quicksand:300,400,500,700" rel="stylesheet">
    <link rel="stylesheet" href="https://maxst.icons8.com/vue-static/landings/line-awesome/line-awesome/1.3.0/css/line-awesome.min.css">

    %{--<asset:stylesheet href="dashboard/styles.css" async="true"/>--}%
    <asset:stylesheet href="dashboard/vendors/bootstrap.css" async="true"/>
    <asset:stylesheet href="dashboard/fonts/feather/style.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/extensions/pace.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/bootstrap-extended.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/colors.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/components.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/vertical-menu.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/colors/palette-gradient.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/charts/morris.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/charts/chartist.css" async="true"/>
    <asset:stylesheet href="dashboard/vendors/tables/datatables.min.css" async="true"/>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/simple-line-icons/2.4.1/css/simple-line-icons.min.css" rel="stylesheet">
    <asset:stylesheet href="dashboard/dashboard-home.css" async="true"/>
    <asset:stylesheet href="dashboard/dashboard.css" async="true"/>
</head>
<body class="vertical-layout vertical-compact-menu 2-columns menu-expanded fixed-navbar" data-open="click" data-menu="vertical-compact-menu" data-col="2-columns">

<!-- fixed-top-->
<nav class="header-navbar navbar-expand-md navbar navbar-with-menu navbar-without-dd-arrow fixed-top navbar-light navbar-shadow navbar-brand-center">
    <div class="navbar-wrapper">
        <div class="navbar-header">
            <ul class="nav navbar-nav flex-row">
                <li class="nav-item mobile-menu d-md-none mr-auto"><a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#"><i class="ft-menu font-large-1"></i></a></li>
                <li class="nav-item"><a class="navbar-brand" href="#"><asset:image src="landingpageImages/wsmetalogo.png" class="brand-logo"/>
                    <h3 class="brand-text">Wonderslate</h3></a></li>
                <li class="nav-item d-md-none"><a class="nav-link open-navbar-container" data-toggle="collapse" data-target="#navbar-mobile"><i class="la la-ellipsis-v"></i></a></li>
            </ul>
        </div>
        <div class="navbar-container content">
            <div class="collapse navbar-collapse" id="navbar-mobile">
                <ul class="nav navbar-nav mr-auto float-left">
                    <li class="nav-item d-none d-md-block"><a class="nav-link nav-menu-main menu-toggle hidden-xs" href="#"><i class="ft-menu"></i></a></li>
                </ul>
                <ul class="nav navbar-nav float-right">
                    <li class="dropdown dropdown-user nav-item"><a class="dropdown-toggle nav-link dropdown-user-link" href="#" data-toggle="dropdown"><span class="mr-1">Hello,<span class="user-name text-bold-700">Veeramani</span></span><span class="avatar avatar-online"><asset:image src="dashboard/profile-img.jpg"/> <i></i></span></a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <a class="dropdown-item" href="#"><i class="ft-user"></i> Edit Profile</a>
                            <a class="dropdown-item" href="#"><i class="ft-power"></i> Logout</a>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</nav>

<div class="main-menu menu-fixed menu-light menu-accordion menu-shadow">
    <div class="main-menu-content">
        <ul class="navigation navigation-main" id="main-menu-navigation" data-menu="menu-navigation">
            <li class="nav-item"><a href="/dashboard"><i class="las la-home"></i><span class="menu-title">Dashboard</span></a></li>
            <li class="nav-item"><a href="/content"><i class="las la-file-alt"></i><span class="menu-title">Content</span></a>
                <ul class="menu-content">
                    <li><a class="menu-item" href="/publishing-desk">Publishing Desk</a></li>
                    <li><a class="menu-item" href="/wonderpublish/manageTabs">Manage Tabs</a></li>
                    <li><a class="menu-item" href="/wonderpublish/manageExams">Test Scoring Templates</a></li>
                    <li><a class="menu-item" href="/content">Last Used Books</a></li>
                </ul>
            </li>
            <li class="nav-item"><a href="/content-creators"><i class="las la-user-edit"></i><span class="menu-title">Creators</span></a></li>
            <li class="nav-item"><a href="/sales"><i class="las la-dollar-sign"></i><span class="menu-title">Sales</span></a></li>
            <li class="nav-item active"><a href="/classes"><i class="las la-school"></i><span class="menu-title">Classes</span></a></li>
            <li class="nav-item"><a href="/teachers"><i class="las la-user-tie"></i><span class="menu-title">Teachers</span></a></li>
            <li class="nav-item"><a href="/students"><i class="las la-graduation-cap"></i><span class="menu-title">Students</span></a></li>
            <li class="nav-item"><a href="/users"><i class="las la-users"></i><span class="menu-title">Users</span></a></li>
            <li class="nav-item"><a href="reports"><i class="las la-file-export"></i><span class="menu-title">Reports</span></a></li>
        </ul>
    </div>
</div>

<div class="app-content content">
    <div class="content-wrapper">
        <div class="content-header row">
            <div class="content-header-left col-md-6 col-12 mb-2">
                <h3 class="content-header-title mb-0">Manage Classes</h3>
                <div class="row breadcrumbs-top">
                    <div class="breadcrumb-wrapper col-12">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/dashboard">Home</a></li>
                            <li class="breadcrumb-item active">Classes</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-detached content-left">
            <div class="content-body">
                <section class="row">
                    <div class="col-sm-12">
                        <div class="card">
                            <div class="card-content">
                                <div class="card-body">

                                    <form class="form mb-2">
                                        <div class="form-body">
                                            <h4 class="form-section border-light" style="font-weight: 500"><i class="ft-book"></i> Add New Class</h4>
                                            <div class="row align-items-end">
                                                <div class="form-group col-md-5 col-sm-12">
                                                    <label>Class Name</label>
                                                    <input class="form-control" type="text" placeholder="Enter class name">
                                                </div>
                                                <div class="form-group col-md-5 col-sm-12">
                                                    <label>Completion Date</label>
                                                    <input class="form-control" type="date" placeholder="">
                                                </div>
                                                <div class="form-group col-md-2 col-sm-12">
                                                    <input class="form-control btn btn-info white" type="submit" value="Add Book">
                                                </div>
                                            </div>
                                        </div>
                                    </form>

                                </div>
                            </div>
                        </div>
                    </div>
                </section>

                <section class="row">
                    <div class="col-sm-12">
                        <div class="card">
                            <div class="card-content">
                                <div class="card-body">

                                    <div class="btn-group float-right w-25">
                                        <button type="button" class="btn btn-warning dropdown-toggle w-100 text-left box-shadow-1" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <i class="ft-plus white"></i> Add New Here
                                        </button>
                                        <div class="dropdown-menu w-100 dropdown-menu-right">
                                            <button class="dropdown-item" type="button">Add Books for Class</button>
                                            <button class="dropdown-item" type="button">Add Students for Class</button>
                                            <button class="dropdown-item" type="button">Add Teachers for Class</button>
                                        </div>
                                    </div>

                                    <form class="form mb-2">
                                        <div class="form-body">
                                            <h4 class="form-section border-light" style="font-weight: 500"><i class="ft-edit"></i> All Information</h4>
                                            <div class="row align-items-end">
                                                <div class="form-group col-md-6 col-12">
                                                    <h5>Class</h5>
                                                    <select class="form-control">
                                                        <option value="" selected="">Select here</option>
                                                        <option value="">Test Class 01 - Completion Date: 12-08-2020</option>
                                                        <option value="">Testing Classes</option>
                                                        <option value="">Main Class</option>
                                                        <option value="">Class Test</option>
                                                        <option value="">Test test class</option>
                                                    </select>
                                                </div>
                                                <div class="form-group col-md-4 col-12">
                                                    <h5>Please select to show</h5>
                                                    <div class="btn-group w-100">
                                                        <button type="button" class="btn btn-info dropdown-toggle w-100 text-left" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                            Select here
                                                        </button>
                                                        <div class="dropdown-menu w-100">
                                                            <button class="dropdown-item" type="button">Show Books for this Class</button>
                                                            <button class="dropdown-item" type="button">Show Students for this Class</button>
                                                            <button class="dropdown-item" type="button">Show Teachers for this Class</button>
                                                            <button class="dropdown-item" type="button">Show Completed Classes</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </form>

                                    <div id="showStudents">
                                        <div class="table-responsive">
                                            <table class="table table-bordered table-striped">
                                                <thead>
                                                <tr class="bg-warning white">
                                                    <th>Full Name</th>
                                                    <th>Email Address</th>
                                                    <th>Username</th>
                                                    <th>Mobile Number</th>
                                                    <th class="text-center">Delete</th>
                                                </tr>
                                                </thead>
                                                <tbody>
                                                <tr>
                                                    <td class="text-capitalize">Veeramani r</td>
                                                    <td><EMAIL></td>
                                                    <td>Testing</td>
                                                    <td>9876543210</td>
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-outline-danger"><i class="ft-trash-2"></i></button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="text-capitalize">Veera</td>
                                                    <td><EMAIL></td>
                                                    <td>Testing</td>
                                                    <td>9876543210</td>
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-outline-danger"><i class="ft-trash-2"></i></button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="text-capitalize">Testing</td>
                                                    <td><EMAIL></td>
                                                    <td>Testing</td>
                                                    <td>9876543210</td>
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-outline-danger"><i class="ft-trash-2"></i></button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="text-capitalize">Testing</td>
                                                    <td><EMAIL></td>
                                                    <td>Testing</td>
                                                    <td>9876543210</td>
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-outline-danger"><i class="ft-trash-2"></i></button>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td class="text-capitalize">Testing</td>
                                                    <td><EMAIL></td>
                                                    <td>Testing</td>
                                                    <td>9876543210</td>
                                                    <td class="text-center">
                                                        <button type="button" class="btn btn-sm btn-outline-danger"><i class="ft-trash-2"></i></button>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
        <div class="sidebar-detached sidebar-right sidebar-sticky">
            <div class="sidebar">
                <div class="sidebar-content d-none d-lg-block">
                    <div class="card">
                        <div class="card-content bg-hexagons">
                            <div class="card-body">
                                <div class="media d-flex">
                                    <div class="media-body text-left">
                                        <h4>Total Students</h4>
                                        <h1 class="text-info"><strong>265</strong></h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-content bg-hexagons">
                            <div class="card-body">
                                <div class="media d-flex">
                                    <div class="media-body text-left">
                                        <h4 class="text-muted">Total Teachers</h4>
                                        <h1 class="text-success"><strong>15</strong></h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card">
                        <div class="card-content bg-hexagons">
                            <div class="card-body">
                                <div class="media d-flex">
                                    <div class="media-body text-left">
                                        <h4>Total Classes</h4>
                                        <h1 class="text-warning"><strong>08</strong></h1>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<footer class="footer footer-static footer-light navbar-border navbar-shadow">
    <p class="clearfix blue-grey lighten-2 text-center mb-0 px-2">Copyright &copy; 2020 Wonderslate Technologies, All rights reserved.</p>
</footer>

<asset:javascript src="dashboard/vendors/vendors.min.js"/>

<asset:javascript src="dashboard/vendors/charts/chartist.min.js"/>
<asset:javascript src="dashboard/vendors/charts/chartist-plugin-tooltip.min.js"/>

<asset:javascript src="dashboard/vendors/charts/chart.min.js"/>
<asset:javascript src="dashboard/vendors/charts/raphael-min.js"/>
<asset:javascript src="dashboard/vendors/charts/morris.min.js"/>

<asset:javascript src="dashboard/vendors/tables/datatables.min.js"/>

<asset:javascript src="dashboard/menu.js"/>
<asset:javascript src="dashboard/index.js"/>

%{--<asset:javascript src="dashboard/dashboard-home.js"/>--}%

<script>
    $(document).ready(function() {

        // Datatable
        $("#showStudents table").DataTable();

    });
</script>
</body>
</html>
