<%--
  Created by IntelliJ IDEA.
  User: apple
  Date: 02/05/25
  Time: 6:19 pm
--%>

<%@ page contentType="text/html;charset=UTF-8" %>
<html>
<head>
    <title></title>
</head>

<body>
   <h2>Fixing broken PDF Paths</h2>
<div id="fixResults">

</div>
</body>
</html>
<script src="https://code.jquery.com/jquery-3.7.1.js" integrity="sha256-eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=" crossorigin="anonymous"></script>

<script>

   var booksIds = "${booksId}";
   //split the comma separated book ids and store in the variable
   var books = booksIds.split(",");
   var countIndex=0;

    function callFix(index){
        <g:remoteFunction controller="dashboard" action="fixResLink" onSuccess="displayResults(data)" params="'bookId='+books[index]" />
        countIndex++

    }
    function displayResults(data){
        document.getElementById("fixResults").innerHTML+=data.count+" PDFs fixed for book "+data.bookTitle+"<br>";
        if(countIndex<books.length){
            callFix(countIndex);
        }
    }
    callFix(countIndex);
</script>
