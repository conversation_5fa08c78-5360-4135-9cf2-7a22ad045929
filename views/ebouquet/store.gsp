<%@ page import  = 'javax.servlet.http.Cookie' %>
<g:render template="/ebouquet/navheader_new"/>
<section class="evidyaStore mt-5">
    <div class="loading-icon">
        <div class="loader-wrapper">
            <div class="loader">Loading</div>
        </div>
    </div>
    <div class="container">
        <div class="row">
            <div class="col-sm-12 col-lg-4 d-block d-lg-block">
                <div class="show_filters_btn text-center">
                    <button class="btn btn-primary col-sm-4" type="button" data-toggle="collapse" data-target="#showFilters" aria-expanded="false" aria-controls="showFilters">
                        SHOW FILTERS
                    </button>
                </div>
                <div id="showFilters" class="Sidewrapper collapse">
                    <input type="text" class="search" placeholder="title, subject, author" id="search-book" autocomplete="off" value="${params.searchString}">
                    <h4 class="mt-4 ml-1">- Filter by Language</h4>
                    <div class="card">
                        <ul class="mt-2" id="langaugeList">
                            <li><a href="javascript:displayBooks('language','English')">English</a></li>
                            %{--<li><a href="javascript:displayBooks('language','Hindi')">Hindi</a></li>--}%
                            %{--<li><a href="javascript:displayBooks('language','Marathi')">Marathi</a></li>--}%
                            %{--<li><a href="javascript:displayBooks('language','Bengali')">Bengali</a></li>--}%
                        </ul>
                    </div>
                    <h4 class="mt-4 ml-1">- Filter by Discipline</h4>
                    <ul class="card subCategory">
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Professional Books')"><span>Professional Books</span></a>
                        </li>
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Training and Development')"><span>Training and Development</span></a>
                        </li>
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Performance Management')"><span>Performance Management</span></a>
                        </li>
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Human Resource Management')"><span>Human Resource Management</span></a>
                        </li>
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Management and Leadership')"><span>Management and Leadership</span></a>
                        </li>
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Books for a Happy Work-Life')"><span>Books for a Happy Work-life</span></a>
                        </li>
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Gifting for Bookworms')"><span>Gifting for Bookworms</span></a>
                        </li>
                        <li class="main-list">
                            <a class="categories" href="javascript:displayBooks('grade','Non-Fiction')"><span>Non-Fiction</span></a>
                        </li>
                    </ul>

                </div>
            </div>
            <div class="col-sm-12 col-lg-8" >
                <div class="d-flex align-items-center result-wrapper" id="displayResults">
                </div>
                <div class="d-flex align-items-center justify-content-between mt-4 showResult">
                    <div>
                        <p class="itemLabel" id="topPaginationMessage"></p>
                    </div>
                    <div>

                        <select id="sortBy" name="sortBy" onchange="sortDisplay();">
                            <option selected>Sort by</option>
                            <option value="title">Title(A-Z)</option>
                            <option value="title">Title(Z-A)</option>
                            <option value="author">Author(A-Z)</option>
                            <option value="author">Author(Z-A)</option>
                        </select>
                    </div>
                </div>
                <p id="searcherrormsg" style="display: none"></p>
                <div class="mt-4 bg-gray" id="booksDisplayList">

                </div>
                <div class="row ml-0 mr-0 align-items-center mt-4 bg-gray border-pagination" id="bottomPagination" style="display: none">
                    <div class="col-6">
                        <ul class="pagination" id="paginationList"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<section>
    <div class="modal fade" id="popupLogin">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body">
                    <p>We're so glad you liked this title! <a class="cmn-login loginnow" onclick="javascript:evidyaloginOpen();">Login Now</a> to add this book to your library or create a customized wishlist.</p>
                </div>
            </div>
        </div>
    </div>
    <div class="modal fade" id="updateLoginModal" data-keyboard="false" data-backdrop="static">
        <div class="modal-dialog">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <h4><strong>Sign up</strong></h4>
                    <button type="button"  data-dismiss="modal" class="btn-close">close</button>
                </div>

                <!-- Modal body -->
                <div class="modal-body">

                    <div id="finishUpdate" style="display: block;">
                        <div class="form-group">
                            <label>Email <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="emailAddress1" id="emailAddress1" readonly disabled >
                        </div>
                        <div class="form-group">
                            <label>Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" name="fullNameUpdate" id="fullNameUpdate" required autocomplete="off" onfocus="hideError(this.id)">
                            <div id="errorUpdateName" class="error_text"></div>
                        </div>
                        <div class="form-group">
                            <label>Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" name="newUpdatePassword" id="newUpdatePassword" required autocomplete="off" onfocus="hideError(this.id)">
                            <div id="errorUpdatePassword" class="error_text"></div>
                        </div>
                        <div class="form-group">
                            <label>Confirm Password <span class="text-danger">*</span></label>
                            <input type="password" class="form-control" name="confirmUpdateNewPassword" id="confirmUpdateNewPassword" required autocomplete="off" onfocus="hideError(this.id)">
                            <div id="errorConfirmUpdatePassword" class="error_text"></div>
                        </div>
                        <div class="form-group text-right d-flex align-items-center justify-content-between">
                            <div id="loginSuccess" class="text-left"></div>
                            <a href="javascript:updateFinish()" class="btn btn-bg btn-primary col-3">LOGIN</a>
                        </div>
                    </div>

                </div>

            </div>
        </div>
</section>
<g:render template="/ebouquet/footer_new"/>
<script>
    var updatesignup=false;
    var updateUserId=0;

    $(window).on('load', function() {

        if("${session["userdetails"]}"==null || "${session["userdetails"]}"=='' ) {
            if ("${userEmail}" != "" && "${sageLogin}"!="true") {
                var updateEmail = htmlDecode("${userEmail}")
                $('#emailAddress1').val(updateEmail)
                updatesignup = true;
                updateUserId = "${userId}"
                $('#updateLoginModal').modal('show');
            }
            else if("${userEmail}" != "" && "${sageLogin}"=="true"){
                    $("#signUpLimitModal").modal("show");
            }
        }

    });

    var appType="${params.appType}";
    var searchMode=false;
    var books ;
    var booksTags;
    var indexSubject="${params.subject}";
    var homepage="${params.homepage}";
    var indexGrade="${params.grade}";
    var indexLanguage="${params.language}";
    var sortBy="title";
    var previousMode="";
    var previousModeValue="";
    var bkRetry = 1;
    var callingFirstTime=true;
    var booksserch=[];
    
    function getBookCategories(){
        <g:remoteFunction controller="ebouquet" action="getBookCategories"  onSuccess='intitializeCategories(data);'  params="'categories=level&level=College&apiMode=optimized'"/>
    }

    function updateFinish() {

        if(document.getElementById("fullNameUpdate").value==""){
            document.getElementById("errorUpdateName").innerHTML = "<small class='text-danger'>Please enter name</small>";
        } else if(document.getElementById("newUpdatePassword").value=="") {
            document.getElementById("errorUpdatePassword").innerHTML = "<small class='text-danger'>Please enter password</small>";
        } else if(document.getElementById("confirmUpdateNewPassword").value=="") {
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>Please re-enter your password</small>";
        } else if(document.getElementById("newUpdatePassword").value != document.getElementById("confirmUpdateNewPassword").value) {
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>Passwords does not match</small>";
        } else if(!document.getElementById('newUpdatePassword').value){
            document.getElementById("errorUpdatePassword").innerHTML = "<small class='text-danger'>Password cannot be empty.</small>";
            return
        }
        else if( document.getElementById('newUpdatePassword').value.length>64){
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>Password cannot be more than 64 characters.</small>";
            return
        }
        else if( document.getElementById('newUpdatePassword').value.length<8){
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>Password cannot be less than 8 characters.</small>";
            return
        }
        else if( document.getElementById('newUpdatePassword').value.toString().indexOf(document.getElementById('fullNameUpdate').value)>-1){
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>Password cannot contain your name.</small>";
            return
        }
        else if( !(new RegExp("^(?=.*[0-9])(?=.*[a-z])(?=.*[A-Z]).{8,}$").test(document.getElementById('newUpdatePassword').value))){
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>Password must have a upper case letter, a lower case letter and a number.</small>";
            return
        }
        else if( (new RegExp("^(?=.*[!@#$%^&*()_+=\"\"''~`{}|:;?<>.-])").test(document.getElementById('newUpdatePassword').value))){
            document.getElementById("errorConfirmUpdatePassword").innerHTML = "<small class='text-danger'>Password cannot contain any special character.</small>";
            return
        }
        else {
            var email = document.getElementById("emailAddress1").value;
            var password = document.getElementById("newUpdatePassword").value;
            var name = document.getElementById("fullNameUpdate").value;
            <g:remoteFunction controller="institute" action="updateUser"  onSuccess='updateLogin(data);' params="'password='+password+'&userName='+name+'&userId='+updateUserId" />
        }
    }
    function sortDisplay(){
        if(document.getElementById("sortBy").selectedIndex==1) {
            books.sort(SortByTitle);
        }else if(document.getElementById("sortBy").selectedIndex==2) {
            books.sort(SortByTitle);
            books.reverse(SortByTitle);
        } else if(document.getElementById("sortBy").selectedIndex==3) {
            books.sort(SortByAuthor);
        }else if(document.getElementById("sortBy").selectedIndex==4) {
            books.sort(SortByAuthor);
            books.reverse(SortByAuthor);
        }

        displayBooks(previousMode,previousModeValue);
    }



    function intitializeCategories(data){
        if((data==null || data.bookCategories==null || data.results==null || data.length==0) && bkRetry==1) {
            bkRetry++;
            getBookCategories();
            return;
        }

        if(data.bookCategories!=null&&"true"==data.bookCategories) bookCategories=true;

        if(data.status!="Nothing present"){
            var data1 = JSON.parse(data.results);
            var formattedTopicMapIndex = formatDataIndex(JSON.parse(data.results));

            myLibraryMode=false;
            instituteLibraryCalled=false;
            if(homepage!="filter") {
                storeBooksReceived(data);
            }
            getBooksList();

        }
    }

    function getBooksList() {
        $('.loading-icon').removeClass('hidden');
        myLibraryMode=false;
        instituteLibraryCalled=false;
        <g:remoteFunction controller="wonderpublish" action="getBooksListEvidya"  onSuccess='storeBooksReceived(data);'
               params="'categories=level&level=College'" />
    }

    var blRetry = 1;
    function storeBooksReceived(data){
        if((data==null || data.length==0) && blRetry==1) {
            blRetry++;
            <g:remoteFunction controller="wonderpublish" action="getBooksListEvidya"  onSuccess='storeBooksReceived(data);'
                    params="'categories=level&level=College'" />
            return;
        }
        storeBooksData = data;
        <g:remoteFunction controller="institute" action="getLibraryBookIds"  onSuccess='libraryBooksReceived(data);' />
    }

    var lbRetry = 1;
    function libraryBooksReceived(data){
        if((data==null || data.length==0) && lbRetry==1) {
            lbRetry++;
            <g:remoteFunction controller="institute" action="getLibraryBookIds"  onSuccess='libraryBooksReceived(data);' />
            return;
        }

        if(data.status=="OK") {
            libraryBooksData = data;
            libraryBooksList = data.libraryBookIds;
        }
        if(searchMode) submitSearch();
        else booksListReceived1(storeBooksData);
    }

    function SortByTitle(x,y) {
        return ((x.title == y.title) ? 0 : ((x.title > y.title) ? 1 : -1 ));
    }

    function SortByAuthor(x,y) {
        return ((x.authors == y.authors) ? 0 : ((x.authors > y.authors) ? 1 : -1 ));
    }
</script>
<asset:javascript src="searchContents.js"/>
<g:render template="/wonderpublish/buyOrAdd"/>
<g:render template="/ebouquet/storeHolder"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<script>
    $('#search-book').typeahead({
        minLength : 3,
        source: function(query, process) {
            $.ajax({
                url: '/discover/searchList',
                method: 'GET',
                data: {query:query},
                dataType: 'JSON',
                success: function fetchBooks2(data) {
                    process($.map(data.searchList, function(item) {
                        if(query === '') {
                            return(item);
                        } else {
                            return item;
                        }
                    }));
                }
            })
        },
        afterSelect: function(){
            submitSearch();
        }
    });

    $(document).on("keypress", "#search-book", function(e) {
        if (e.which == 13) {
            if (e.keyCode == 13) {
                if(document.getElementById("search-book").value == '' || document.getElementById("search-book").value == undefined || document.getElementById("search-book").value == null) location.reload();
                else submitSearch();
            }
        }
    });

    function submitSearch(){
        $('.loading-icon').removeClass('hidden');
        var searchString =encodeURIComponent(document.getElementById("search-book").value);
        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="discover" action="search"  onSuccess='searchResults(data);'
                params="'searchString='+searchString" />
    }
    function searchResults(data) {
        if (data.status != "Nothing present") {
            $('.loading-icon').addClass('hidden');
            $("#booksDisplayList").show();
            if (data.search) searchMode = true;
            books = data.books;
            booksTags = data.booksTag;
            displayBooks("all", "");
        } else {
            if (elementExists("searcherrormsg")) {
                $('.loading-icon').addClass('hidden');
                document.getElementById("searcherrormsg").innerText = "No Results Found";
                $("#searcherrormsg").show();
                $("#booksDisplayList").hide();
                $("#paginationList").hide();
                $("#topPaginationMessage").hide();

            }


        }
    }
    <%  if("true"==params.search){%>
    searchMode=true;
    getBooksList();
    <%  }  %>

    if('${params.suggestBookId}'){
        var suggestBookId = '${params.suggestBookId}'
        <g:remoteFunction controller="wonderpublish" action="getBookById"  onSuccess='searchResults(data)'
                params="'id='+suggestBookId" />
    }else{
        getBookCategories();
    }

</script>
