<div class='col-md-8 main' id="displaybookanalytics" style="display: none">
    <div class='row'>
    <div class='col-md-10 col-md-offset-1 text-center'><h4>Learning progress (Chapterwise)</h4></div>
</div>
    <div class='row'>
        <div class='col-md-10 col-md-offset-1 greytext'><b><i>** This will give you information about the content you have viewed in each chapter. Click on the bar to get more information. If you do not see any bar means, you have not viewed any content on this book.</i></b></div>
    </div>
    <div class="row text-center" style="display: none" id="loadingbookanalytics"><div class="col-md-7 orange" ><i class="fa fa-spinner fa-2x fa-spin"></i> </div></div>
   <br><br> <div id="top_x_div" ></div><br>

    <div class="row text-center" style="display: none" id="loadingchapteranalytics"><div class="col-md-7 orange" ><i class="fa fa-spinner fa-2x fa-spin"></i> </div></div>
    <div id="chapteranalytics" style="display: none">

    </div>
    <div class="row text-center"><br><br>
        <button type="button" onclick="closeanalytics()" class="btn btn-primary">Close</button><br><br>
    </div>
</div>


<script type="text/javascript">
    var analytics;
    var analyticsChapterName;
    function getBookAnalytics(){
        $("#displaychapter").hide(500);
        $("#chapteranalytics").hide(500);
        $("#displayreadingcontent").hide(500);
        $("#displaybookanalytics").show(500);
        $("#loadingbookanalytics").show();
        <g:remoteFunction controller="wonderpublish" action="getBookAnalytics" onSuccess='drawStuff(data);' params="'bookId='+bookId"/>
    }

  function closeanalytics(){
      $("#displaybookanalytics").hide(500);
      $("#displaychapter").show(500);
  }
    function drawStuff(data) {

        var chaptersData=[];
         analytics = data.chapters;
        var percentage;
         for (var i = 0; i < analytics.length; ++i) {
            percentage  = (analytics[i].noofviews / analytics[i].noofresources ) * 100;
            chaptersData.unshift([analytics[i].name, percentage]);
        }
        chaptersData.unshift(['Chapters', 'Percentage']);
        var data = new google.visualization.arrayToDataTable(chaptersData);


        var options = {
            title: 'Learning progress',
            width: 600,
            height: 600,
            legend: { position: 'none' },
            bars: 'horizontal', // Required for Material Bar Charts.
            axes: {
                x: {
                    0: { side: 'top', label: 'Percentage completed (click on the bar to see details)'} // Top x-axis.
                }
            },
            bar: { groupWidth: "90%" }
        };

        var chart = new google.charts.Bar(document.getElementById('top_x_div'));
        google.visualization.events.addListener(chart, 'select', selectHandler);
        $("#loadingbookanalytics").hide();
        chart.draw(data, options);

        function selectHandler() {
            var selectedItem = chart.getSelection()[0];
            if (selectedItem) {
                var selectedChapter = data.getValue(selectedItem.row, 0);
                var chapterId;
                for (var i = 0; i < analytics.length; ++i) {
                    chapterId = analytics[i].id;
                    if(selectedChapter==analytics[i].name){
                        analyticsChapterName = analytics[i].name;
                        break;
                    }
                }
                $("#loadingchapteranalytics").show(500);
                $("#chapteranalytics").hide(500);
                <g:remoteFunction controller="wonderpublish" action="getChapterAnalytics" onSuccess='displaychapterlevelanalytics(data);' params="'chapterId='+chapterId"/>;
            }
        }

    };

    function displaychapterlevelanalytics(data){
      $("#loadingchapteranalytics").hide(500);
      $("#chapteranalytics").show(500);
        var resources = data.resources;
        var lastviewed;
        var viewed;

        var displayStr="     <div class='row'>" +
                "            <div class='col-md-10 col-md-offset-1'><h4>"+analyticsChapterName+"</h4></div>" +
                "        </div>" +
                "    <div class='row'>" +
                "        <div class='col-md-10 col-md-offset-1'>" +
                "        <table class='table' width='60%'>" +
                "            <thead>" +
                "            <tr>" +
                "                <th>Content Name</th>" +
                "                <th>Viewed</th>" +
                "                <th>Last viewed date</th>" +
                "                <th></th>" +
                "            </tr>" +
                "            </thead>";



        for (var i = 0; i < resources.length; ++i) {

            if(resources[i].vieweddate==null){
                lastviewed="";
                viewed="<i class='fa fa-times fa-x red'></i>";
            }
            else{

                lastviewed=moment(resources[i].vieweddate).format("DD-MMM-YY hh:mm");
                viewed="<i class='fa fa-check fa-x green'></i>";
            }
            displayStr += "            <tr>" +
                    "                <td>"+resources[i].name+"</td>" +
                    "                <td>"+viewed+ "</td>" +
                    "                <td>"+lastviewed+"</td>" +
                    "                <td></td>" +
                    "            </tr>" ;
        }
        displayStr +=  "        </table>" +
                "        </div>" +
                "    </div>";
        document.getElementById("chapteranalytics").innerHTML = displayStr;
        document.getElementById('chapteranalytics').scrollIntoView({block: "start", behavior: "smooth"});

    }


</script>