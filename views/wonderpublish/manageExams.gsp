<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<style>
.inlineEditor{
    min-height: 300px;
    border-bottom: 1px solid #828180;
}
.cke_textarea_inline
{
    height: 50px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}
</style>
<section class="library">
    <div class="container manageExams">
        <h3 class="mb-3">Manage Exams</h3>
        <div class="col-md-12 form-inline row">
            <select id="createSyllabus" class="form-control col-3" name="createSyllabus" onchange="javascript:getCreateGrade(this.value)"><option>Syllabus / Exam group</option></select>
            <select id="createGrade" class="form-control col-3 ml-3" name="createGrade" style="display: none" onchange="javascript:getTemplatesList(this.value)"><option>Select Exam</option></select>
            <select id="createTemplate" class="form-control col-3 ml-3" name="createTemplate" style="display: none" onchange="javascript:getExamDetails(this.value)"><option>Select Template</option></select>
            <a id="createButton" class="btn btn-primary col-md-2 m-0 ml-3 form-control disabled" href="javascript:createNewExamTemplate()" >Create New Template</a>
        </div>

        <div id="examManagement" class="adding_area col-md-12 px-0 py-5" style="display: none">
            <hr>
            <h4 id="addTitleText" class="my-4 text-warning">Exam Template Details</h4>

            <div class="row">
                <div class="form-group row col-12 pr-0 mb-4">
                    <label for="examDetails" class="col-2 col-form-label">Template Name*</label>
                    <div class="col-10 pr-0 pl-0">
                        <input type="text" class="form-control" name="examDetails" id="examDetails" placeholder="Enter Template Name"></input>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group row col-4">
                    <label for="noOfQuestions" class="col-6 col-form-label text-left">Total Questions*</label>
                    <div class="col-6">
                        <input type="text" class="form-control" name="noOfQuestions" id="noOfQuestions">
                    </div>
                </div>
                <div class="form-group row col">
                    <label for="noOfSections" class="col-6 col-form-label text-right">Number of Sections</label>
                    <div class="col-6">
                        <input type="text" class="form-control" name="noOfSections" id="noOfSections">
                    </div>
                </div>
                <div class="form-group row col">
                    <label for="rightAnswerMarks" class="col-6 col-form-label text-right">Marks<br><small>(Right Answer)</small></label>
                    <div class="col-6">
                        <input type="text" class="form-control" name="rightAnswerMarks" id="rightAnswerMarks">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group row col-4">
                    <label for="totalMarks" class="col-6 col-form-label text-left">Total Marks*</label>
                    <div class="col-6">
                        <input type="text" class="form-control" name="totalMarks" id="totalMarks">
                    </div>
                </div>
                <div class="form-group row col">
                    <label for="totalTime" class="col-6 col-form-label text-right">Total Time<br><small>(In Minutes)</small></label>
                    <div class="col-6">
                        <input type="text" class="form-control" name="totalTime" id="totalTime">
                    </div>
                </div>
                <div class="form-group row col">
                    <label for="wrongAnswerMarks" class="col-6 col-form-label text-right">Marks<br><small>(Wrong Answer)</small></label>
                    <div class="col-6">
                        <input type="text" class="form-control" name="wrongAnswerMarks" id="wrongAnswerMarks">
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group row col-12 pr-0 mb-1">
                    <label for="examInstructions" class="col-2 col-form-label">Test Instructions</label>

                    <input type="hidden" name="examInstructions">
                    <div class="cktext col-10 pr-0 pl-0">
                        <div  contenteditable="true"  id="examInstructions" class="inlineEditor"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-3 offset-md-2 mt-3 mr-auto p-0">
                    <button type="button" class="btn btn-lg btn-primary w-100 form-control" onclick="saveExamDetails();" >Save Exam Details</button>
                    <p style="display: none" class="text-success mb-0" id="saveMessage">Saved Successfully!</p>
                </div>

            </div>
        </div>

        <div id="sectionGroup" class="pb-5" style="display: none">

        </div>

    </div>
</section>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<script src="https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.18.0/ckeditor.js" ></script>
<script>
var noOfSections;
var examId;
var sectionsDisplayed=false;
var sectionIndexSaved;
CKEDITOR.disableAutoInline = true;
CKEDITOR.inline( 'examInstructions',{
    customConfig: '/assets/ckeditor/customConfig.js',
    height: 250,
});
    function getCreateSyllabus(){
        $("#booksaving").show(500);
            <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
        params="'level=Competitive Exams'"/>

    }

    function initializeCreateSyllabus(data){

        syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {

            el = document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            select.appendChild(el);
        }

        select.focus();
        $('#createSyllabus').show();
        $('#createTemplate').hide();

    }

    function getCreateGrade(syllabus){
        $('#createGrade').hide();
        $('#templateGroup').hide();
        $("#booksaving").show(500);
                <g:remoteFunction controller="wonderpublish" action="getGrades"  onSuccess='initializeCreateGrades(data);'
        params="'syllabus='+syllabus"/>

    }

    function getTemplatesList(grade){
        $('#templateGroup').hide();
        $("#booksaving").show(500);
        <g:remoteFunction controller="wonderpublish" action="getExamTemplates"  onSuccess='initializeTemplates(data);'
        params="'grade='+grade"/>
    }

    function initializeTemplates(data){
        var  exams = data.results;
        var select = document.getElementById("createTemplate");
        select.options.length = 1;
        for(var i=0;i< exams.length; i++) {

            el = document.createElement("option");
            el.textContent = exams[i].examDetails;
            el.value = exams[i].id;
            select.appendChild(el);
        }

        select.focus();
        $('#templateGroup').show();
        $("#booksaving").hide();
    }

    function initializeCreateGrades(data){

        var  grades = data.results;
        var select = document.getElementById("createGrade");
        select.options.length = 1;
        for(var i=0;i< grades.length; i++) {

            el = document.createElement("option");
            el.textContent = grades[i].grade;
            el.value = grades[i].id;
            select.appendChild(el);
        }

        select.focus();
       // $('#createGrade').show();
       // $('#createTemplate').show();
        $("#booksaving").hide();
    }

    function getExamDetails(exam){

       if(exam!="Select") {
           <g:remoteFunction controller="wonderpublish" action="getExamMstDtls"  onSuccess='showExamDetails(data);'
        params="'examId='+exam"/>
        }else{
            $('#examManagement').hide();
        }
    }

    function showExamDetails(data){
        $('#examManagement').show();
        if(data.status=="Data present"){
            var examMst  = data.examMst;
            examId = examMst.id;
            document.getElementById("examDetails").value=examMst.examDetails;
            document.getElementById("noOfSections").value=examMst.noOfSections;
            document.getElementById("noOfQuestions").value=examMst.noOfQuestions;
            document.getElementById("totalMarks").value=examMst.totalMarks;
            document.getElementById("rightAnswerMarks").value=examMst.rightAnswerMarks;
            document.getElementById("wrongAnswerMarks").value=examMst.wrongAnswerMarks;
            document.getElementById("totalTime").value=examMst.totalTime;
            CKEDITOR.instances.examInstructions.setData(examMst.examInstructions);
            createExamDtlDisplay(6);
        }
        else{
            document.getElementById("examDetails").value="";
            document.getElementById("noOfSections").value="";
            document.getElementById("noOfQuestions").value="";
            document.getElementById("totalMarks").value="";
            document.getElementById("rightAnswerMarks").value="";
            document.getElementById("wrongAnswerMarks").value="";
            document.getElementById("totalTime").value="";
            CKEDITOR.instances.examInstructions.setData("");
        }
    }

    function createNewExamTemplate(){
        document.getElementById("examDetails").value="";
        document.getElementById("noOfSections").value="";
        document.getElementById("noOfQuestions").value="";
        document.getElementById("totalMarks").value="";
        document.getElementById("rightAnswerMarks").value="";
        document.getElementById("wrongAnswerMarks").value="";
        document.getElementById("totalTime").value="";
         CKEDITOR.instances.examInstructions.setData("");
        document.getElementById("sectionGroup").innerHTML="";
        $('#examManagement').show();
    }

    function saveExamDetails(){
        if((document.getElementById("noOfQuestions").value=="")||(document.getElementById("totalMarks").value=="")||(document.getElementById("examDetails").value=="")){
            alert("Please enter Template name,Total marks and Total questions.")
        }else {
            var gradeId = document.getElementById("createGrade").value;
            var examDetails = document.getElementById("examDetails").value;
            var noOfSections = document.getElementById("noOfSections").value;
            var noOfQuestions = document.getElementById("noOfQuestions").value;
            var totalMarks = document.getElementById("totalMarks").value;
            var rightAnswerMarks = document.getElementById("rightAnswerMarks").value;
            var wrongAnswerMarks = document.getElementById("wrongAnswerMarks").value;
            var totalTime = document.getElementById("totalTime").value;
            var examId = document.getElementById("createTemplate").value;
            var examInstructions = CKEDITOR.instances.examInstructions.getData();
            if(document.getElementById("createTemplate").selectedIndex==0) examId=-999;
            <g:remoteFunction controller="wonderpublish" action="saveExamMst"  onSuccess='examDetailsSaved(data);'
        params="'examId='+examId+'&gradeId='+gradeId+'&examDetails='+examDetails+'&noOfSections='+noOfSections+'&noOfQuestions='+noOfQuestions+'&totalMarks='+totalMarks+'&rightAnswerMarks='+rightAnswerMarks+'&wrongAnswerMarks='+wrongAnswerMarks+'&totalTime='+totalTime+'&examInstructions='+examInstructions"/>

        }
    }

function saveSectionDetails(index){
    sectionIndexSaved = index;
        if(document.getElementById("subject"+index).value!="Select subject") {
            var subject = document.getElementById("subject" + index).value;

            var sectionDetails = document.getElementById("sectionDetails" + index).value;
            var noOfQuestions = document.getElementById("noOfQuestions" + index).value;
            var totalMarks = document.getElementById("totalMarks" + index).value;
            var rightAnswerMarks = document.getElementById("rightAnswerMarks" + index).value;
            var wrongAnswerMarks = document.getElementById("wrongAnswerMarks" + index).value;
            var totalTime = document.getElementById("totalTime" + index).value;
            <g:remoteFunction controller="wonderpublish" action="saveExamDtl"  onSuccess='sectionDetailsSaved(data);'
        params="'examId='+examId+'&subject='+subject+'&sectionDetails='+sectionDetails+'&noOfQuestions='+noOfQuestions+'&totalMarks='+totalMarks+'&rightAnswerMarks='+rightAnswerMarks+'&wrongAnswerMarks='+wrongAnswerMarks+'&totalTime='+totalTime"/>
        }else {
            document.getElementById("subject" + index).focus();
        }
}

    function examDetailsSaved(data){
        examId = data.examMst.id;
        if(!sectionsDisplayed){
            createExamDtlDisplay(6);
            sectionsDisplayed = true;
        }
        $("#saveMessage").show();
    }

    function sectionDetailsSaved(data){
        $("#saveMessage"+sectionIndexSaved).show();
    }
    function createExamDtlDisplay(noOfDtl){
        var htmlStr="";
        noOfSections = noOfDtl;
        for(var i=0;i<noOfDtl;i++){
           htmlStr+=   " <hr><h4 class='my-4 text-warning'>Section Details - "+(i+1)+"</h4>\n" +
               "<div class='row'>\n" +
                    "<div class='form-group row col-12 pr-0 mb-4'>\n" +
                        "<label for='subject"+i+"' class='col-2 col-form-label'>Select Subject</label>\n" +
                            "<div class='col-10 pr-0 pl-0'><select id='subject"+i+"' class='form-control'><option>Select subject</option></select></div>\n" +
                    "</div>\n" +
               "</div>\n" +
               "\n" +
               "<div class='row'>\n" +
                    "<div class='form-group row col-12 pr-0 mb-4'>\n" +
                        "<label for='sectionDetails"+i+"' class='col-2 col-form-label'>Section Information</label>\n" +
                        "<div class='col-10 pr-0 pl-0'><textarea name='sectionDetails' class='form-control' rows='4' cols='60' id='sectionDetails"+i+"' placeholder='Enter section details here'></textarea></div>\n" +
                    "</div>\n" +
               "</div>\n" +
               "\n" +
               "<div class='row'>\n" +
                    "<div class='form-group row col-4'>\n" +
                        "<label for='noOfQuestions' class='col-6 col-form-label text-left'>Total Questions</label>\n" +
                        "<div class='col-6'>\n" +
                            "<input type='text' class='form-control' name='noOfQuestions' id='noOfQuestions"+i+"' size='3'>\n" +
                        "</div>\n" +
                    "</div>\n" +
                    "<div class='form-group row col'>\n" +
                        "<label for='rightAnswerMarks' class='col-6 col-form-label text-right'>Marks<br><small>(Right Answer)</small></label>\n" +
                        "<div class='col-6'>\n"+
                            "<input type='text' class='form-control' name='rightAnswerMarks' id='rightAnswerMarks"+i+"'>\n" +
                        "</div>\n" +
                    "</div>\n" +
                    "<div class='form-group row col'>\n" +
                        "<label for='totalTime' class='col-6 col-form-label text-right'>Total Time<br><small>(In Minutes)</small></label>\n" +
                        "<div class='col-6'>\n" +
                            "<input type='text' class='form-control' name='totalTime' id='totalTime"+i+"' size='3'>\n" +
                        "</div>\n" +
                   "</div>\n" +
               "</div>\n" +
               "\n" +
               "<div class='row'>\n" +
                   "<div class='form-group row col-4'>\n" +
                        "<label for='totalMarks' class='col-6 col-form-label text-left'>Total Marks</label>\n" +
                        "<div class='col-6'>\n"+
                            "<input type='text' class='form-control' name='totalMarks' id='totalMarks"+i+"' size='3'>\n" +
                        "</div>\n" +
                    "</div>\n" +
                    "<div class='form-group row col'>\n" +
                        "<label for='wrongAnswerMarks' class='col-6 col-form-label text-right'>Marks<br><small>(Wrong Answer)</small></label>\n" +
                        "<div class='col-6'>\n"+
                            "<input type='text' class='form-control' name='wrongAnswerMarks' id='wrongAnswerMarks"+i+"' size='3'>\n" +
                        "</div>\n" +
                    "</div>\n" +
                    "<div class='form-group row col'>\n" +
                    "</div>\n" +
               "</div>\n" +
               "\n" +
               "<div class='row'>\n" +
                    "<div class='col-md-2 offset-md-2 mt-3 mb-4 mr-auto p-0'>\n" +
                        "<button type='button' class='btn btn-lg btn-primary w-100 form-control' onclick='saveSectionDetails("+i+");'>Save Section</button>\n" +
                        "<div id='saveMessage"+i+"' style='display: none' class='text-success mb-0'>Saved Successfully!</div>\n" +
                    "</div>\n" +
               "</div>";

        }
        document.getElementById("sectionGroup").innerHTML=htmlStr;
        $("#sectionGroup").show();
        var syllabus=document.getElementById("createSyllabus").value;
        <g:remoteFunction controller="wonderpublish" action="getSubjects"  onSuccess='updateSectionSubjects(data);'
        params="'syllabus='+syllabus"/>
    }

    function updateSectionSubjects(data){
        if(data.status=="OK"){
            var subjects = data.results;

            for(var i=0;i<6;i++){
                var select = document.getElementById("subject"+i);
               select.options.length = 1;

                for(var j=0;j< subjects.length; j++) {

                    var el = document.createElement("option");
                    el.textContent = subjects[j].subject;
                    el.value = subjects[j].subject;
                    select.appendChild(el);
                }

            }
            getExamDtls();
        }
    }

    function getExamDtls(){
        <g:remoteFunction controller="wonderpublish" action="getExamDtls"  onSuccess='updateSectionDetails(data);'
        params="'examId='+examId"/>
    }
    function updateSectionDetails(data){
       if(data.status=="Data present"){
           var sections = data.examDtl;
           console.log("number of sections present are ="+sections.length);
           var section;
           for(i=0;i<sections.length;i++){

               section = sections[i];
               //select the subject first
                var field = document.getElementById("subject"+i);
                   for (j = 0; j < field.options.length; j++) {
                       if(field.options[j].value.replace(/\W+/g, '')==section.subject.replace(/\W+/g, '')){
                           field.options[j].selected=true;
                       }
                   }

                //update other items
               document.getElementById("sectionDetails"+i).value=section.sectionDetails;
               document.getElementById("noOfQuestions" + i).value=section.noOfQuestions;
               document.getElementById("totalMarks" + i).value=section.totalMarks;
               document.getElementById("rightAnswerMarks" + i).value=section.rightAnswerMarks;
               document.getElementById("wrongAnswerMarks" + i).value=section.wrongAnswerMarks;
               document.getElementById("totalTime" + i).value=section.totalTime;


           }
       }
    }
    getCreateSyllabus();


$(document).ready(function(){
    $('#createSyllabus').on('change', function(){
        var level = $(this).val();
        if(level=='' || level == "Syllabus / Exam group") {
            $('#createButton').addClass('disabled');
            $('#examManagement').hide();
            $('#createGrade').hide();
            $('#createTemplate').hide();
        } else {
            $('#createButton').removeClass('disabled');
             $('#createGrade').show();
             $('#createTemplate').show();
        }
    });
});

</script>
