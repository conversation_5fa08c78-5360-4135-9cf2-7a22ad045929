<%@ page import="com.wonderslate.data.ChaptersMst" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var pubUser;
</script>
<%if(publisherUser){%>
<script>
     pubUser=true;
</script>
<%}%>
<style>

.modal-dialog {
    max-width: 1200px; /* Change this value as per your requirement */
    max-height: 900px;
}
</style>

<%if("books".equals(session['entryController'])){%>
<asset:stylesheet href="wonderslate/quizcreator.css"/>
<%}%>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="fileuploadwithpreview.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>

<style>
.sage-body .sage-banner {
    display: none;
}
.image-upload > input
{
    display: none;
}

.inlineEditor{
    min-height: 40px;
    border-bottom: 1px solid #828180;
}
.cke_textarea_inline
{
    height: 50px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline
{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

div:empty:before {
    content:attr(data-placeholder);
    color:gray
}
.red-border{
    border: 1px solid red;
}
.firstPage{
    background-color: #D3D3D3;
}
.smallText{
    font-size: 12px;
}

</style>

<% if("sage".equals(session["entryController"])){%>

<div style="margin-top: 25px;">
    <div class="container-fluid">
        <div class="row">


            <div id="static-content" class="col-md-9 main">
                <div class="row">
                    <div class="col-md-12">
                        <h4>Multiple Choice Questions</h4><br>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addquiz" id="addquiz" action="/resourceCreator/addQuiz" method="post">
                            <input type="hidden" name="resourceType">
                            <input type="hidden" name="chapterId">
                            <input type="hidden" name="quizId">
                            <input type="hidden" name="mode">
                            <input type="hidden" name="resourceDtlId">
                            <input type="hidden" name="objectiveMstId">
                            <input type="hidden" name="finished">
                            <input type="hidden" name="page">
                            <input type="hidden" name="bookId" value="${bookId}">
                            <div class="firstPage">
                                <div class="row">
                                    <div class="col-sm-6 col-sm-offset-1">
                                        <div class="form-group resourceName float-label-control">
                                            <div class="cktext">
                                                <input type="text" class="form-control" id="resourceName" name="resourceName" placeholder="Name of your quiz" value="" maxlength="255">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-sm-3 col-sm-offset-1">

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-md-offset-1 resourceName">
                                        <label for="language1">Language 1:</label>
                                        <g:select id="language1" class="form-control" name="language1"
                                                  from="${langMstList}"
                                                  value="${resourceDtl&&resourceDtl.language1?resourceDtl.language1:"English"}"
                                                  optionKey="language" optionValue="language" noSelection="['':'Select']" />
                                    </div>

                                    <div class="col-md-3 form-group resourceName">
                                        <label for="language2">Language 2:</label>
                                        <g:select id="language2" class="form-control" name="language2"
                                                  from="${langMstList}"
                                                  value="${resourceDtl?resourceDtl.language2:""}"
                                                  optionKey="language" optionValue="language" noSelection="['':'Select']" />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3  col-md-offset-1 resourceName" ><b>Exam Details</b>
                                        <select id="examSyllabus" class="form-control" name="examSyllabus"   onchange="javascript:getCreateGrade(this.value)"><option>Exam group</option></select>
                                    </div>
                                    <div class="col-md-3 resourceName"><br>
                                        <select id="grade" class="form-control" name="grade"  style="display: none" onchange="javascript:updateTemplates(this.value)"><option>Select exam</option></select>
                                    </div>
                                    <div class="col-md-3 resourceName"><br>
                                        <select id="examId" class="form-control" name="examId" style="display: none" onchange="javascript:updateSubjects(this.value)"><option>Select Template</option></select>
                                    </div>
                                    <div class="col-md-3 resourceName"><br>
                                        <select id="examSubject" class="form-control" name="examSubject"  style="display: none"><option>Select Section/Subject</option></select>(Only if whole paper has single section)
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3 col-md-offset-1 resourceName">
                                        <label for="testStartDate">Test Start Date</label>
                                        <input type="text" id="testStartDate" name="testStartDate" value="<%= testStartDate !=null?testStartDate:"" %> " autocomplete="off">
                                    </div>

                                    <div class="col-md-3 form-group resourceName">
                                        <label for="testEndDate">Test End Date</label>
                                        <input type="text" id="testEndDate" name="testEndDate" value="<%= testEndDate !=null?testEndDate:"" %>" autocomplete="off">
                                    </div>
                                    <div class="col-md-3 form-group resourceName">
                                        <label for="testResultDate">Test Result Date</label>
                                        <input type="text" id="testResultDate" name="testResultDate" value="<%= testResultDate !=null?testResultDate:"" %>" autocomplete="off">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-9 col-sm-offset-1 resourceName"><br>
                                        <b>Passage:</b>
                                        <input type="hidden" name="passage">
                                        <div  contenteditable="true"  id="passage" class="inlineEditor"></div>
                                        <br>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-sm-9 col-sm-offset-1">
                                        <b>Directions:</b>
                                        <input type="hidden" name="directions">
                                        <div  contenteditable="true"  id="directions" class="inlineEditor"></div>
                                        <br>

                                    </div>
                                </div>

                                <div class="row">
                                    <div  class="col-sm-9 col-sm-offset-1">Above/Previous directions apply&nbsp;<input type="checkbox" name="direction" value="Yes" onclick="javacript:directionChanged(this);"></div><br><br>
                                </div>
                                <div class="row">
                                    <div  class="col-sm-9 col-sm-offset-1 smallText" id="previousDirection"></div>
                                </div>
                            </div>
                            <div class="row quiz7">
                                <div class="col-sm-9 ">
                                    <label id="questionLabel">Question </label>
                                    <input type="hidden" name="question">
                                    <div class="cktext">
                                        <div  contenteditable="true"  id="question" class="inlineEditor"></div>
                                    </div>
                                </div>
                            </div>
                            <div id="target"></div>
                    </div>



                    <div class="row quiz">
                        <div class="col-1 col-lg-1 text-center">
                            <br><br> <input type="checkbox" name="answer1" id="answer1" value="Yes">
                        </div>
                        <div class="col-11 col-lg-5">
                            <b>Option 1:</b>
                            <input type="hidden" name="option1">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option1" class="inlineEditor"></div>
                            </div>

                        </div>

                        <div class="col-11 col-lg-5">
                            <b>Option 2:</b>
                            <input type="hidden" name="option2">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option2" class="inlineEditor"></div>
                            </div>
                        </div>
                        <div class="col-1 col-lg-1 text-center">
                            <br><br><input type="checkbox" name="answer2" id="answer2" value="Yes">
                        </div>
                    </div>

                    <div class="row quiz">
                        <div class="col-1 col-lg-1 text-center">
                            <br><br><input type="checkbox" name="answer3" id="answer3" value="Yes">
                        </div>
                        <div class="col-11 col-lg-5">
                            <b>Option 3:</b>
                            <input type="hidden" name="option3">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option3" class="inlineEditor"></div>
                            </div>
                        </div>
                        <div class="col-11 col-lg-5">
                            <b>Option 4:</b>
                            <input type="hidden" name="option4">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option4" class="inlineEditor"></div>
                            </div>
                        </div>
                        <div class="col-1 col-lg-1 text-center">
                            <br><br><input type="checkbox" name="answer4" id="answer4" value="Yes">
                        </div>
                    </div>

                    <div class="row quiz">
                        <div class="col-1 col-lg-1 text-center">
                            <br><br><input type="checkbox" name="answer5" id="answer5" value="Yes">
                        </div>
                        <div class="col-11 col-lg-5">
                            <b>Option 5:</b>
                            <input type="hidden" name="option5">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option5" class="inlineEditor"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row quiz1">
                        <div class="col-sm-12 ">
                            <div class="form-group float-label-control">
                                <label for="hint">Hint</label>
                                <input type="text" class="form-control" id="hint" name="hint" placeholder="Hint" value="" maxlength="255">
                            </div>
                        </div>
                    </div>

                    <div class="row quiz2">
                        <div class="col-sm-12 ">
                            <b>Answer Explanation</b>
                            <input type="hidden" name="answerDescription">
                            <div class="cktext">
                                <div  contenteditable="true"  id="answerDescription" class="inlineEditor"></div>
                            </div>

                        </div>
                    </div>
                    <div class="row quiz3">
                        <div class="col-12 col-lg-4">
                            <div class="form-group smallerText greyText">
                                <label for="explainLink">Explanation link</label>&nbsp;&nbsp;
                                <div class="cktext">
                                    <input type="text" id="explainLink" name="explainLink">

                                </div>
                            </div>
                        </div>

                        <div class="col-sm-4  ">
                            <div class="form-group smallerText greyText">
                                <label for="startTime">Start time (minutes:seconds)</label>&nbsp;&nbsp;
                                <div class="cktext">
                                    <input type="text" id="startTime" name="startTime">

                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4 ">
                            <div class="form-group smallerText greyText">
                                <label for="endTime">End time (Seconds)</label>&nbsp;&nbsp;
                                <div class="cktext">
                                    <input type="text" id="endTime" name="endTime">

                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4"></div>
                    </div>
                    <div class="row quiz4">
                        <div class="col-12 col-lg-4">
                            <div class="form-group smallerText greyText">
                                <label for="difficultylevel">Difficulty Level </label>&nbsp;&nbsp;
                                <div class="cktext">
                                    <select id="difficultylevel" name="difficultylevel">
                                        <option></option>
                                        <option value="Easy">Easy</option>
                                        <option value="Medium">Medium</option>
                                        <option value="Tough">Tough</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-4 col-sm-offset-1">
                            <div class="form-group smallerText greyText">
                                <label for="subject">Subject</label>&nbsp;&nbsp;
                                <select id="subject" name="subject">

                                </select>
                            </div>
                        </div>
                        <div class="col-sm-4"></div>
                    </div>
                    <div class="row quiz5">
                        <div class="col-12 col-lg-4">
                            <div class="form-group smallerText greyText">
                                <label for="expiryDate">Expiry Date</label>&nbsp;&nbsp;
                                <div class="cktext">
                                    <input type="date" id="expiryDate" name="expiryDate">

                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-4">
                            <div class="form-group smallerText greyText">
                                <label for="marks">Marks (For correct answer)</label>&nbsp;&nbsp;
                                <input type="number" id="marks" name="marks" size="2">

                                </input>
                            </div>
                        </div>
                        <div class="col-12 col-lg-4">
                            <div class="form-group smallerText greyText">
                                <label for="marks">Negative Marks (For wrong answer)</label>&nbsp;&nbsp;
                                <input type="number" id="negativeMarks" name="negativeMarks" size="2" step=".01">

                                </input>
                            </div>
                        </div>
                        %{--                        <div class="col-sm-1"></div>--}%
                    </div>
                    <div class="row quiz8">
                        <div class="alert alert-warning col-sm-12" id="alertbox" style="display: none">
                            Please complete required fields marked in red.
                        </div>
                    </div>
                    <div class="row quiz6 ">
                        <div class="form-group">
                            <div class=" col-sm-12 text-center ">
                                <button type="button" onclick="javascript:deleteQuestion()" class="btn btn-primary" style="display: none;" id="deletebutton">Delete</button>
                                <button type="button" onclick="javascript:formSubmit('Save')" class="btn btn-primary">Save</button>
                                <button type="button" onclick="javascript:formSubmit('SaveNext')" class="btn btn-primary">Save and Goto Next</button>
                                <button type="button" onclick="javascript:formSubmit('Next')" class="btn btn-primary">Save and Add Next</button>
                                <button type="button" onclick="javascript:formSubmit('Done')" class="btn btn-primary">Save and Finish</button>
                            </div>
                        </div>
                    </div>
                    <div class="row quiz7" id="savedNotification" style="display: none">
                        <div class="col-md-8  smallText green" id="savedNotificationText"><b>Quiz saved</b></div>
                    </div>
                </form>
                </div>
            </div>



            <div id="sidebar" class="col-md-2 hidden-xs hidden-sm text-left">

            </div>

        </div>
    </div>

</div>

<%}else {%>
<%if("books".equals(session['entryController'])){%>
<div class="mobile_page_title d-flex d-md-none p-3 align-items-center">
    <i class="material-icons" onclick="javascript:window.history.back();">keyboard_backspace</i>
    <h4 class="pl-2">Create MCQs & Tests</h4>
</div>
<%}%>
<div class="mcq_creation my-4 my-md-5 px-2 px-md-4 px-lg-5">
    <div class="pag_title col-md-12 mb-3">
        <h4>Multiple Choice Questions</h4>
    </div>

    <div class="container-fluid">
        <div class="row mx-0 mobile_column_swap">
            <div id="static-content" class="col-md-9 rounded-left border pb-4">
                <div class="row">
                    <div class="col-md-12 px-0">
                        <form class="form-horizontal" enctype="multipart/form-data" role="form" name="addquiz" id="addquiz" action="/resourceCreator/addQuiz" method="post">
                            <input type="hidden" name="resourceType">
                            <input type="hidden" name="reAttemptValue">
                            <input type="hidden" name="chapterId">
                            <input type="hidden" name="quizId">
                            <input type="hidden" name="mode">
                            <input type="hidden" name="resourceDtlId">
                            <input type="hidden" name="objectiveMstId">
                            <input type="hidden" name="finished">
                            <input type="hidden" name="page">
                            <input type="hidden" name="bookId" value="${bookId}">
                            <input type="hidden" name="folderId" value="${params.folderId}">
                            <div class="firstPage rounded-left border p-0 p-md-4">
                                <div class="row align-items-end">
                                    <div class="col-lg-6 form-group resourceName">
                                        <label for="resourceName">NAME</label>
                                        <div class="resourceName float-label-control mb-0">
                                            <div class="cktext">
                                                <input type="text" class="form-control" id="resourceName" name="resourceName" placeholder="Name of your quiz" value="" maxlength="255">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-3 form-group resourceName">
                                        <label for="language1">LANGUAGE 1:</label>
                                        <g:select id="language1" class="form-control" name="language1"
                                                  from="${langMstList}"
                                                  value="${resourceDtl&&resourceDtl.language1?resourceDtl.language1:"English"}"
                                                  optionKey="language" optionValue="language" noSelection="['':'Choose Language of your MCQs']" />
                                    </div>
                                <%if(publisherUser){%>
                                    <div class="col-md-3 form-group resourceName">
                                        <label for="language2">LANGUAGE 2:</label>
                                        <g:select id="language2" class="form-control" name="language2"
                                                  from="${langMstList}"
                                                  value="${resourceDtl?resourceDtl.language2:""}"
                                                  optionKey="language" optionValue="language" noSelection="['':'Select']" />
                                    </div>
                                    <%}%>
                                </div>
                                <div class="row">
                                    <div class="col-lg-6 form-group resourceName">
                                        <label for="mcqTotalTime">Quiz Total Time in mins</label>
                                        <div class="resourceName float-label-control mb-0">
                                            <div class="cktext">
                                                <input type="text" class="form-control" id="mcqTotalTime" name="mcqTotalTime" placeholder="Quiz Total Time in mins" value="${resourceDtl?.mcqTotalTime ?: ''}" maxlength="255">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <%if(publisherUser){%>
                                <div class="row">
                                    <div class="input_title col-md-12 resourceName">
                                        <h6><strong>Exam Details</strong></h6>
                                    </div>
                                    <div class="col-md-3 form-group resourceName">
                                        <select id="examSyllabus" class="form-control" name="examSyllabus"   onchange="javascript:getCreateGrade(this.value)"><option>Exam group</option></select>
                                        <div class="form-text">
                                            <small>(Only if whole paper has single section)</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3 form-group resourceName">
                                        <select id="grade" class="form-control" name="grade"  style="display: none" onchange="javascript:updateTemplates(this.value)"><option>Select exam</option></select>
                                    </div>
                                    <div class="col-md-3 form-group resourceName">
                                        <select id="examId" class="form-control" name="examId" style="display: none" onchange="javascript:updateSubjects(this.value)"><option>Select Template</option></select>
                                    </div>
                                    <div class="col-md-3" id="examSubject" style="display: none">
                                        <a href="javascript:setSectionSubjects()">Set Section Subjects</a>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-4 form-group resourceName">
                                        <label for="testStartDate">TEST START DATE</label>
                                        <input type="text" class="form-control" id="testStartDate" name="testStartDate" value="<%= testStartDate !=null?testStartDate:"" %> " autocomplete="off">
                                    </div>

                                    <div class="col-md-4 form-group resourceName">
                                        <label for="testEndDate">TEST END DATE</label>
                                        <input type="text" class="form-control" id="testEndDate" name="testEndDate" value="<%= testEndDate !=null?testEndDate:"" %>" autocomplete="off">
                                    </div>
                                    <div class="col-md-4 form-group resourceName">
                                        <label for="testResultDate">TEST RESULT DATE</label>
                                        <input type="text" class="form-control" id="testResultDate" name="testResultDate" value="<%= testResultDate !=null?testResultDate:"" %>" autocomplete="off">
                                    </div>
                                </div>

                                <div class="row resourceName">
                                    <div class="col-md-12 form-group form-check d-flex align-items-center">
                                        <input type="checkbox" name="allowReAttempt" id="allowReAttempt" <%=allowReAttempt ==true?"checked":"" %> <%=reAttempt==false?"disabled":"" %>  value="<%=allowReAttempt ==true?"Yes":"No" %>">
                                        <label class="form-check-label" for="allowReAttempt">&nbsp;&nbsp;&nbsp;&nbsp;Re-Attempt</label>
                                    </div><br><br>
                                </div>

                                <div class="row">
                                    <div class="col-md-12 mt-3 resourceName">
                                        <div class="input_title">
                                            <h6><strong>Passage:</strong></h6>
                                        </div>
                                        <input type="hidden" name="passage">
                                        <div  contenteditable="true"  id="passage" class="inlineEditor mb-4"></div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="input_title">
                                            <h6><strong>Directions:</strong></h6>
                                        </div>
                                        <input type="hidden" name="directions">
                                        <div  contenteditable="true"  id="directions" class="inlineEditor mb-4"></div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12 form-group form-check d-flex align-items-center">
                                        <input type="checkbox" name="direction" id="direction" value="Yes" onclick="javacript:directionChanged(this);">
                                        <label class="form-check-label" for="direction">&nbsp;&nbsp;Above/Previous directions apply</label>
                                    </div><br><br>
                                </div>
                                <div class="row">
                                    <div  class="col-sm-9 col-sm-offset-1 smallText" id="previousDirection"></div>
                                </div>
                                <%}%>
                            </div>

                            <div class="mobile_view_border">
                            <div class="row quiz7 mx-0 my-3 my-md-4 px-2 px-lg-5">
                                <div class="col-sm-12">
                                    <label id="questionLabel">QUESTION</label>
                                    <input type="hidden" name="question">
                                    <div class="cktext">
                                        <div  contenteditable="true"  id="question" class="inlineEditor"></div>
                                    </div>
                                </div>
                            </div>
                            <div id="target"></div>

                    <div class="row quiz mb-4 px-2 px-lg-5">
                        <div class="col-1 col-md-1 text-left">
                            <br> <input type="checkbox" name="answer1" id="answer1" value="Yes">
                        </div>
                        <div class="col-11 col-md-5 pl-md-0">
                            <b>Option 1:</b>
                            <input type="hidden" name="option1">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option1" class="inlineEditor"></div>
                            </div>

                        </div>

                        <div class="col-1 col-md-1 text-left">
                            <br><input type="checkbox" name="answer2" id="answer2" value="Yes">
                        </div>
                        <div class="col-11 col-md-5 pl-md-0">
                            <b>Option 2:</b>
                            <input type="hidden" name="option2">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option2" class="inlineEditor"></div>
                            </div>
                        </div>

                    </div>

                    <div class="row quiz mb-4 px-2 px-lg-5">
                        <div class="col-1 col-md-1 text-left">
                            <br><input type="checkbox" name="answer3" id="answer3" value="Yes">
                        </div>
                        <div class="col-11 col-md-5 pl-md-0">
                            <b>Option 3:</b>
                            <input type="hidden" name="option3">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option3" class="inlineEditor"></div>
                            </div>
                        </div>

                        <div class="col-1 col-md-1 text-left">
                            <br><input type="checkbox" name="answer4" id="answer4" value="Yes">
                        </div>
                        <div class="col-11 col-md-5 pl-md-0">
                            <b>Option 4:</b>
                            <input type="hidden" name="option4">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option4" class="inlineEditor"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row quiz mb-4 px-2 px-lg-5">
                        <div class="col-1 col-md-1 text-left">
                            <br><input type="checkbox" name="answer5" id="answer5" value="Yes">
                        </div>
                        <div class="col-11 col-md-5 pl-md-0">
                            <b>Option 5:</b>
                            <input type="hidden" name="option5">
                            <div class="cktext">
                                <div  contenteditable="true"  id="option5" class="inlineEditor"></div>
                            </div>
                        </div>
                    </div>
                            </div>

                    <%if(publisherUser){%>
                    <div class="row quiz1 mb-2 px-2 px-lg-5 mx-0">
                        <div class="col-sm-12 ">
                            <div class="form-group float-label-control">
                                <label for="hint">Hint</label>
                                <input type="text" class="form-control" id="hint" name="hint" placeholder="Hint" value="" maxlength="255">
                            </div>
                        </div>
                    </div>
                    <%}%>
                    <div class="row quiz2 mb-4 px-2 px-lg-5 mx-0">
                        <div class="col-sm-12 ">
                            <b>Answer Explanation</b>
                            <input type="hidden" name="answerDescription">
                            <div class="cktext">
                                <div  contenteditable="true"  id="answerDescription" class="inlineEditor"></div>
                            </div>

                        </div>
                    </div>
                   <%if(publisherUser){%>
                    <div class="row quiz3 px-2 px-lg-5 mx-0">
                        <div class="col-12 col-lg-12">
                            <div class="form-group smallerText greyText">
                                <label for="explainLink">Explanation link</label>
                                <div class="cktext">
                                    <input type="text" id="explainLink" name="explainLink" class="form-control">
                                </div>
                            </div>
                        </div>
                    </div>
                    <%}%>
                    <div class="row quiz4 px-2 px-lg-5 mx-0">
                        <div class="col-12 col-lg-3  ">
                            <div class="form-group smallerText greyText">
                                <label for="difficultylevel">Difficulty Level </label>
                                <div class="cktext">
                                    <select id="difficultylevel" name="difficultylevel" class="form-control">
                                        <option>Select</option>
                                        <option value="Easy">Easy</option>
                                        <option value="Medium">Medium</option>
                                        <option value="Tough">Tough</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                            <%if(publisherUser){%>
                        <div class="col-12 col-lg-3">
                            <div class="form-group smallerText greyText">
                                <label for="startTime">Start time (minutes:seconds)</label>
                                <div class="cktext">
                                    <input type="text" id="startTime" name="startTime" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-3">
                            <div class="form-group smallerText greyText">
                                <label for="endTime">End time (Seconds)</label>
                                <div class="cktext">
                                    <input type="text" id="endTime" name="endTime" class="form-control">
                                </div>
                            </div>
                        </div>
                        <div class="col-12 col-lg-3">
                            <div class="form-group smallerText greyText">
                                <label for="expiryDate">Expiry Date</label>&nbsp;&nbsp;
                                <div class="cktext">
                                    <input type="date" id="expiryDate" name="expiryDate" class="form-control">
                                </div>
                            </div>
                        </div>
                    <%}%>
                    </div>
                   <%if(publisherUser){%>
                    <div class="row quiz5 px-2 px-lg-5 mx-0">
                        <div class="col-12 col-lg-4  col-sm-offset-1">
                            <div class="form-group smallerText greyText">
                                <label for="subject">Subject</label>
                                <select id="subject" name="subject" class="form-control"></select>
                            </div>
                        </div>
                        <div class="col-12 col-lg-4">
                            <div class="form-group smallerText greyText">
                                <label for="marks">Marks (For correct answer)</label>
                                <input type="number" id="marks" name="marks" size="2" class="form-control">
                            </div>
                        </div>
                        <div class="col-12 col-lg-4">
                            <div class="form-group smallerText greyText">
                                <label for="marks">Negative Marks (For wrong answer)</label>&nbsp;&nbsp;
                                <input type="number" id="negativeMarks" name="negativeMarks" size="2" step=".01" class="form-control">
                            </div>
                        </div>
                    </div>
                    <%}%>
                    <div class="row quiz8 mx-0 px-2 px-lg-5">
                        <div class="col-md-12">
                            <div class="alert alert-warning w-100" role="alert" id="alertbox" style="display: none">
                                <p class="mb-0">Please complete required fields marked in red.</p>
                            </div>
                        </div>
                    </div>

                    <div class="row quiz6 mx-0 px-2 px-lg-5 mb-3">
                        <div class=" col-sm-12 text-center ">
                            <button type="button" onclick="javascript:deleteQuestion()" class="btn btn-lg btn-default" style="display: none;" id="deletebutton">Delete</button>
                            <button type="button" onclick="javascript:formSubmit('Save')" class="btn btn-lg btn-outline-primary">Save</button>
                            <button type="button" onclick="javascript:formSubmit('SaveNext')" class="btn btn-lg btn-outline-primary">Save & Goto Next</button>
                            <button type="button" onclick="javascript:formSubmit('Next')" class="btn btn-lg btn-outline-primary">Save & Add Next</button>
                            <button type="button" onclick="javascript:formSubmit('Done')" class="btn btn-lg btn-primary finish_btn">Save & Finish</button>
                        </div>
                    </div>

                    <div class="row quiz7 mb-4 mx-0 px-2 px-lg-5 pt-0" id="savedNotification" style="display: none">
                        <div class="col-md-12 text-center smallText green" id="savedNotificationText">
                            <div class="alert alert-success mb-0 w-100" role="alert">
                                <p class="mb-0">Quiz saved successfully!</p>
                            </div>
                        </div>
                    </div>
                </form>
                </div>
            </div>
            </div>

            <div id="sidebar" class="col-md-3 hidden-xs hidden-sm text-left rounded-right">

            </div>

        </div>
    </div>
</div>
<%}%>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
        <div id="loadingText" style="color: #ff2a21">Saving section options ....</div>
    </div>

</div>
<div id='quizEdit'>
    <div class="modal fade" id="sectionSubjectModal">
        <div class="modal-dialog  modal-dialog-centered">
            <div class="modal-content">

                <div class="modal-header"> Section Subjects Manager
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                </div>

                <div class="modal-body">
                    <div id="sectionSubjectContents"></div>
                    <div class="row quiz6 ">
                        <div class="form-group">
                            <div class=" col-sm-12 text-center ">
                                <button type="button" onclick="javascript:formCancel('Next')" class="btn btn-primary">Cancel</button>
                                <button type="button" onclick="javascript:formSave(0)" class="btn btn-primary">Save</button>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>


<% if(!"sage".equals(session["entryController"])){%>
<script>
    $(document).ready(function(){
        $("#sidebar a.quiz-number").on('click', function(){
            $(this).siblings().removeClass('active');
            $(this).addClass('active')
        });
    });
</script>
<%}%>

<script src="https://cdnjs.cloudflare.com/ajax/libs/ckeditor/4.18.0/ckeditor.js" ></script>
<script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<asset:javascript src="material-input.js"/>
<asset:javascript src="fileuploadwithpreview.js"/>
<asset:javascript src="quizcreator.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script>
    var serverPath = "${request.contextPath}";
    var mode = "${params.mode}";
    var resourceType = "${params.resourceType}";
    var chapterId = "${params.chapterId}";
    var bookId = "${params.bookId}";
    var page = "${params.page}";

    var subjectStr = "${competitiveExamSubjects}";
    var subjects = subjectStr.replace(/&quot;/g, '\"');
    var gradeId = "${(resourceDtl!=null?resourceDtl.grade:null)}";
    var syllabus = "${(resourceDtl!=null?resourceDtl.examSyllabus:null)}";
    var examSubject = "${(resourceDtl!=null?resourceDtl.examSubject:null)}";
    var examId = "${(resourceDtl!=null?resourceDtl.examId:null)}";
    var subject="";
    var publisherUser = false;
    var allowReAttempt="";
    var sectionSubjects;
    <%if(publisherUser){%>
    publisherUser = true;
    <%}%>
    CKEDITOR.disableAutoInline = true;

        $('*[name=testStartDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "closeButton": true,


        });
        $('*[name=testEndDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "closeButton": true,



        });
        $('*[name=testResultDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "closeButton": true,



        });

        // Validation for testResultDate to be greater than testEndDate
        function validateTestResultDate() {
            var testEndDate = $('*[name=testEndDate]').val();
            var testResultDate = $('*[name=testResultDate]').val();

            if (testEndDate && testResultDate) {
                var endDate = new Date(testEndDate);
                var resultDate = new Date(testResultDate);

                if (resultDate <= endDate) {
                    alert('Test Result Date must be greater than Test End Date');
                    $('*[name=testResultDate]').val('');
                    return false;
                }
            }
            return true;
        }

        // Add change event listeners for validation
        $('*[name=testResultDate]').on('change', function() {
            validateTestResultDate();
        });

        $('*[name=testEndDate]').on('change', function() {
            // Re-validate result date when end date changes
            var testResultDate = $('*[name=testResultDate]').val();
            if (testResultDate) {
                validateTestResultDate();
            }
        });


    //important to call data after all the data is loaded
    var noOFCKEditors=7;
    <%if(publisherUser){%>
    CKEDITOR.inline( 'passage',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        height: 100,
    });


    CKEDITOR.inline( 'directions',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
    });
   noOFCKEditors = 9;
<%}%>
    CKEDITOR.inline( 'option1',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

    });

    CKEDITOR.inline( 'option2',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

    });

    CKEDITOR.inline( 'option3',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

    });

    CKEDITOR.inline( 'option4',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

    });

    CKEDITOR.inline( 'option5',{
        customConfig: '/assets/ckeditor/customConfigMin.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

    });


    CKEDITOR.inline( 'answerDescription',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        height: 250,
    });

    //have the complete one at the end.. otherwise,the below ones will follow it.

    CKEDITOR.inline( 'question',{
        customConfig: '/assets/ckeditor/customConfig.js',
        uploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Files&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,
        filebrowserImageUploadUrl: '/wonderpublish/uploadContent?command=QuickUpload&type=Images&responseType=json&mode='+mode+"&chapterId="+chapterId+'&bookId='+bookId+'&resourceType='+resourceType+'&page='+page,

    });

    if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 ) {
        document.getElementById( 'ie8-warning' ).className = 'tip alert';
    }
    CKEDITOR.on("instanceReady",function() {
        // insert code to run after editor is ready
        noOFCKEditors--;
        if(noOFCKEditors==0){
            <% if("edit".equals(params.mode)){%>
            gotoQuestion(0);

            <%}%>
        }
    });

    <% if("edit".equals(params.mode)){
    objectives.each{ objective ->%>

    var quizItem={};
    quizItem.resourceName=htmlDecode("${resourceDtl.resourceName}");
    quizItem.passage=htmlDecode("${resourceDtl.chapterDesc!=null?resourceDtl.chapterDesc.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.question =htmlDecode( "${objective.question.replaceAll("(\\r|\\n)", "")}");
    quizItem.answer1 = "${objective.answer1}";
    quizItem.answer2 = "${objective.answer2}";
    quizItem.answer3 = "${objective.answer3}";
    quizItem.answer4 = "${objective.answer4}";
    quizItem.answer5 = "${objective.answer5}";
    quizItem.option1 = htmlDecode("${objective.option1!=null?objective.option1.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.option2 = htmlDecode("${objective.option2!=null?objective.option2.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.option3 = htmlDecode("${objective.option3!=null?objective.option3.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.option4 = htmlDecode("${objective.option4!=null?objective.option4.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.option5 = htmlDecode("${objective.option5!=null?objective.option5.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.directions = htmlDecode("${objective.directions!=null?objective.directions.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.hint = htmlDecode("${objective.hint}");
    quizItem.answerDescription =htmlDecode("${objective.answerDescription!=null?objective.answerDescription.replaceAll("(\\r|\\n)", ""):""}");
    quizItem.objectiveMstId = "${objective.id}";
    quizItem.difficultylevel = "${objective.difficultylevel}";
    quizItem.directionId = "${objective.directionId}";
    quizItem.subject = "${objective.subject}";
    quizItem.examSyllabus =  "${resourceDtl.examSyllabus}";
    quizItem.grade =  "${resourceDtl.grade}";
    quizItem.language1 = "${resourceDtl.language1}";
    quizItem.language2 = "${resourceDtl.language2}";
    quizItem.examSubject=  "${resourceDtl.examSubject}";
    quizItem.expiryDate = "${objective.expiryDate}";
    quizItem.marks = "${objective.marks}";
    quizItem.negativeMarks = "${objective.negativeMarks}";
    quizItem.examId="${resourceDtl.examId}";
    quizItem.explainLink="${objective.explainLink}";
    if(quizItem.explainLink) {
        if(quizItem.explainLink.includes("/")) {
            quizItem.explainLink = quizItem.explainLink;
        }else{
            quizItem.explainLink = "https://www.youtube.com/watch?v="+quizItem.explainLink;
        }
    }
    quizItem.startTime="${objective.startTime}";
    quizItem.endTime="${objective.endTime}";
    quizItem.testStartDate = "${testStartDate}";
    quizItem.testEndDate = "${testEndDate}";
    quizItem.mcqTotalTime = "${resourceDtl.mcqTotalTime != null ? resourceDtl.mcqTotalTime : ''}";
    quizItems.push(quizItem);
    <% }
  %>
    resourceDtlId = "${resourceDtl.id}";
    quizId = "${resourceDtl.resLink}";
    quizName = quizItems[0].resourceName;
    passage = quizItems[0].passage;
    <% if("sage".equals(session["entryController"])){%>
    document.getElementById("sidebar").innerHTML = "<br><b> Saved Questions </b> <br>";
    <% } else {%>
    $("#sidebar").addClass("bg-light border py-3");
    document.getElementById("sidebar").innerHTML = "<div class='sidebar_title border-bottom mb-3'><h6><strong>Saved Questions</strong></h6></div>";
    <% } %>
    for(i=0;i<quizItems.length;i++){
        var quest = quizItems[i].question
        if(quest.indexOf("<p")==0) quest = quest.substring(3,(quest.length-4));
        if(quest.length > 25) quest = quest.substring(0,25)+"..";
        <% if("sage".equals(session["entryController"])){%>
        document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='pagenumber-green' style='margin-bottom: 20px;margin-right: 10px;' >" + (i + 1)  + "</a>";
        <% } else {%>
        document.getElementById("sidebar").innerHTML = document.getElementById("sidebar").innerHTML + "<a href='javascript:gotoQuestion(" + i + ")' class='quiz-number btn btn-outline-success'>" + (i + 1)  + "</a>";
        <% } %>
    }

    $("#sidebar a.quiz-number:first").addClass("active");

    <%}%>
    function getIdFromYouTubeURL(url) {
        if(url.match(/(youtu.be)/)){
            var split_c = "/";
            var split_n = 3;
        }

        if(url.match(/(youtube.com)/)){
            var split_c = "v=";
            var split_n = 1;
        }

        var getYouTubeVideoID = url.split(split_c)[split_n];
        return getYouTubeVideoID.replace(/(&)+(.*)/, "");
    }
    function formSubmit(submitType) {
        $('#savedNotification').hide();
        if (validate(submitType)) {
            saveAndGoNext = false;
            document.addquiz.mode.value=mode;
            document.addquiz.resourceType.value=resourceType;
            document.addquiz.chapterId.value=chapterId;
            document.addquiz.quizId.value=quizId;
            document.addquiz.resourceDtlId.value=resourceDtlId;
            document.addquiz.objectiveMstId.value=objectiveMstId;
            document.addquiz.resourceName.value=quizName;
            document.addquiz.page.value=page;
            if(publisherUser) {
                <% if(!"sage".equals(session["entryController"])){%>
                var allowReTest = $('input[name="allowReAttempt"]').prop('checked');
                if(allowReTest) {
                    document.addquiz.reAttemptValue.value = "Yes";
                } else {
                    document.addquiz.reAttemptValue.value = "No";
                }
               <%}%>
                if (document.getElementById("explainLink").value) {
                    var url = document.getElementById("explainLink").value;
                    if (url.indexOf("t=") > -1) {
                        document.getElementById("startTime").value = url.substring(url.indexOf("t=") + 2);
                        url = url.substring(0, (url.indexOf("t=") - 1));
                    }
                    if (url.includes("youtube") || url.includes("youtu.be")) {
                        document.getElementById("explainLink").value = getIdFromYouTubeURL(url);
                    } else {
                        document.getElementById("explainLink").value = url;
                    }
                }
                document.getElementById("previousDirection").innerHTML = "";
                if (document.getElementById("examId").selectedIndex == 0) {
                    document.getElementById("examId").value = null;

                }
                $("#previousDirection").hide();
            }
            if('Next' == submitType) {

                var oData = new FormData(document.forms.namedItem("addquiz"));

                var url = "${createLink(controller:'resourceCreator',action:'addQuiz')}";
                $.ajax({
                    url: url,
                    type: 'POST',
                    data: oData,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,
                    success: function (req) {
                        questionAdded(req);
                    }
                });
                document.getElementById("addquiz").reset();
                //     if(tinyMCE.get('question')!=null) tinyMCE.get('question').setContent('');
                CKEDITOR.instances.question.setData('');
                CKEDITOR.instances.option1.setData('');
                CKEDITOR.instances.option2.setData('');
                CKEDITOR.instances.option3.setData('');
                CKEDITOR.instances.option4.setData('');
                CKEDITOR.instances.option5.setData('');
                CKEDITOR.instances.answerDescription.setData('');
                CKEDITOR.instances.directions.setData('');
                CKEDITOR.instances.passage.setData('');
                document.addquiz.difficultylevel.selectedIndex=0;
                if(quizItem.examSubject){
                    subject = quizItem.examSubject;
                    setSubject(subject);
                }
                //  document.addquiz.subtopicid.selectedIndex=0;
                document.getElementById('questionLabel').innerHTML= 'Question ' + (quizItems.length + 1);
                document.getElementsByName('question')[0].placeholder = 'Question ' + (quizItems.length + 1);
                // document.getElementById("questionimage").innerHTML = "Upload Image";
                $('#deletebutton').hide();
                clearContent();
                $("html, body").animate({ scrollTop: 0 }, "slow");

            }else if('Save' == submitType || 'SaveNext'== submitType) {
                if('SaveNext'== submitType) saveAndGoNext = true;
                var oData = new FormData(document.forms.namedItem("addquiz"));
                var url = "${createLink(controller:'resourceCreator',action:'addQuiz')}";

                $.ajax({
                    url: url,
                    type: 'POST',
                    data: oData,
                    processData: false,  // tell jQuery not to process the data
                    contentType: false,
                    success: function (req) {
                        questionSaved(req);
                    }
                });
                $("html, body").animate({ scrollTop: 0 }, "slow");

            }else
            {
                document.addquiz.finished.value='true';
                document.addquiz.submit();
            }

        }

    }
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };


    function deleteQuestion(){
        if(confirm("Are you sure to delete this question?")){
            showLoader();
            <g:remoteFunction controller="funlearn" action="deleteQuestion"  onSuccess='reloadPage();'
                params="'objectiveId='+objectiveMstId+'&quizId='+quizId" />
        }
    }
    function reloadPage(){
        location.reload();
    }

    function updateSubject(data){
        if(data.status=="Data present") {
            var subjects = data.examDtl;
            sectionSubjects = data.examDtl;
            var grade = document.getElementById("grade");
            var select = document.getElementById('subject');
            select.options.length = 1;
            for (i = 0; i < subjects.length; i++) {
                var el = document.createElement("option");
                el.textContent = subjects[i].subject;
                el.value = subjects[i].subject;
                if(subjects[i].subject==subject) el.selected = true;
                select.appendChild(el);
            }
            formSubmit('Save');
            $("#examSubject").show();
        }else{
            //code for reset subject
            $('#subject')
                .find('option')
                .remove()
                .end()
                .append('<option value=""></option>')
                .val('')
            ;
            $('#examSubject')
                .find('option')
                .remove()
                .end()
                .append('<option value=""></option>')
                .val('')
            ;
        }

    }
    function updateSubjects(examId){
        <g:remoteFunction controller="wonderpublish" action="getExamSubjects"  onSuccess='updateSubject(data);'
     params="'examId='+examId"/>
    }

    function updateTemplates(grade){

        <g:remoteFunction controller="wonderpublish" action="getExamTemplates"  onSuccess='updateTemplate(data);'
     params="'grade='+grade"/>
    }
    function updateTemplate(data){
        var  examTemplates = data.results;
        var select = document.getElementById("examId");
        select.options.length = 1;
        for(var i=0;i< examTemplates.length; i++) {
            el = document.createElement("option");
            el.textContent = examTemplates[i].examDetails;
            el.value = examTemplates[i].id;
            if(examId==examTemplates[i].id) {
                el.selected=true;
                updateSubjects(examId);
            }
            select.appendChild(el);
        }
        $('#examId').show();
    }
    function getCreateGrade(syllabus){
        $('#createGrade').hide();
        $("#booksaving").show(500);
        <g:remoteFunction controller="wonderpublish" action="getGrades"  onSuccess='initializeCreateGrades(data);'
        params="'syllabus='+syllabus"/>

    }

    function initializeCreateGrades(data){

        var  grades = data.results;
        var select = document.getElementById("grade");
        select.options.length = 1;
        for(var i=0;i< grades.length; i++) {
            el = document.createElement("option");
            el.textContent = grades[i].grade;
            el.value = grades[i].id;
            if(gradeId==grades[i].id) {
                el.selected=true;
                updateTemplates(grades[i].id);
            }
            select.appendChild(el);
        }
        $('#grade').show();
    }


    function getexamSyllabus(){
        $("#booksaving").show(500);
        <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeexamSyllabus(data);'
        params="'level=Competitive Exams'"/>

    }

    function initializeexamSyllabus(data){

        syllabusList = data.results;
        var select = document.getElementById("examSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {

            el = document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            if(syllabus==syllabusList[i].syllabus) {
                el.selected=true;
                getCreateGrade(syllabusList[i].syllabus);
            }
            select.appendChild(el);
        }

        select.focus();
        $('#examSyllabus').show();

    }

    <%if(publisherUser){%>
    getexamSyllabus();
    <%}%>

    function setSectionSubjects(){
        $('#sectionSubjectModal').modal('show');

        var subjects = sectionSubjects;
        var numberOfSections = subjects.length;
        var htmlSrc="<h4>Total Questions : "+quizItems.length+"</h4><br><table><tr><th>Question No From</th><th>Question No To</th><th>Subject</th></tr>"
        for(var i=0;i<numberOfSections;i++){
            htmlSrc += "<tr><td><input type='number' name='from_"+i+"' id='from_"+i+"' maxlength=3</td>"+
             "<td><input type='number' name='to_"+i+"' id='to_"+i+"' maxlength=3</td>";
              htmlSrc += "<td><select id='subject_"+i+"'></select></td></tr>";
        }
        htmlSrc +="</table>";
        document.getElementById("sectionSubjectContents").innerHTML=htmlSrc;
        for(var j=0;j<numberOfSections;j++) {
             var select = document.getElementById("subject_"+j);
            select.options.length = 1;

            for (i = 0; i < subjects.length; i++) {
                var el = document.createElement("option");
                el.textContent = subjects[i].subject;
                el.value = subjects[i].subject;
                select.appendChild(el);
            }
        }
    }

    function formCancel(){
        $('#sectionSubjectModal').modal('hide');
    }


    function formSave(rowIndex) {
        $('.loading-icon').removeClass('hidden');
         var from = document.getElementById("from_"+rowIndex).value;
         var to = document.getElementById("to_"+rowIndex).value;
         var subject = document.getElementById("subject_"+rowIndex).value;
        showLoader();
        <g:remoteFunction controller="resourceCreator" action="updateSectionSubject"  onSuccess='rowUpdated(data);'
                params="'from='+from+'&quizId='+quizId+'&to='+to+'&subject='+subject+'&index='+rowIndex" />
    }

    function rowUpdated(data){
        var rowIndex = 1+Number(data.rowIndex);
        if(rowIndex<sectionSubjects.length) formSave(rowIndex);
        else{
            $('#sectionSubjectModal').modal('hide');
            reloadPage();
        }

    }
</script>
<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>

</body>
</html>
