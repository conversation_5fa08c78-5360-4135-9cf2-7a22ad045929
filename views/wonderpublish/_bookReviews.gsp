<div class="row book-reviews" id="user-reviews"></div>

<asset:javascript src="moment.min.js"/>
<asset:javascript src="moment-timezone.js"/>
<asset:javascript src="moment-timezone-utils.js"/>
<script>
  <g:remoteFunction controller="wonderpublish" action="getBooksReviewRatings"  onSuccess='displayBookReviews(data);' params="'bookId='+${bookId}"/>

  function displayBookReviews(data) {
    var reviews = data.reviewratings;
    var htmlStr = "";
    var bookRating ="";
    var ratingByUser = "";
    var ratingsArray = [0,0,0,0,0];
    var reviewLabel = document.getElementById('enter-review-label');

    if(reviews.length ==0) {
      reviewLabel = "Be the first to review this smart eBook";
    } else {
      reviewLabel = "Review the smart ebook";
    }
    for(var i = 0; i < reviews.length; i++) {
     ratingsArray[(reviews[i].rating-1)]  = ratingsArray[(reviews[i].rating-1)]+1;
    }
    
    htmlStr+="<div class='col-md-12 col-sm-12 col-xs-12 review-rating-wrapper'>" +
                  "<h2 class='rating-review-heading'>Ratings and reviews</h2>"+ 
                "</div>"+
                "<div class='col-md-3 col-xs-5 col-sm-4 book-overall-rating'>" +
                  "<p class='rating-by-user'>Overall Rating</p>" +
                  "<p class='rating-by-user' id='rating-by-user'></p>" +
                  "<p class='book-rating'>" +
                  "<span id='rating'></span>"+ 
                  "<span id='total-reviews' style='font-size: 18px; font-weight: 500; margin-left: 8px;'></span></p>" +
                "</div>"+
                "<div class='col-md-6 col-xs-7 col-sm-8 rating-bars-wrapper'>";
  for(k=5;k>0;k--){
      var percentage = (ratingsArray[(k-1)]/reviews.length)*100;

     if(percentage==null || isNaN(percentage)) {
        percentage = 0;
     }
      percentage = parseFloat(Math.round(percentage * 100) / 100).toFixed(0);
      htmlStr +=
                  "<div class='user-ratings-holder'>" +
                    "<div class='side-rating'>"+k+" stars</div>" +
                    "<div class='progress rating-progress-bar-wrapper'>" +
                      "<div class='progress-bar' role='progressbar' aria-valuenow='"+percentage+"' aria-valuemin='0' aria-valuemax='100' style='width: "+percentage+"%;'>" +
                      "</div>" +
                    "</div>" +
                    "<div class='side-rating-right hidden-xs'>"+percentage+"%</div>" +
                  "</div>";
  }
                htmlStr +="</div>"+
                            "<div class='col-md-12 col-xs-12 col-sm-12 user-reviews'>";
      var reviewDate;
      for(var i = 0; i < reviews.length; i++) {
          if(!reviews[i].review) continue;

       if(reviews[i].reviewDate) {
          reviewDate =  reviews[i].reviewDate.replace(/~/g , ":");
      }
      moment.tz.add("Asia/Calcutta|HMT BURT IST IST|-5R.k -6u -5u -6u|01232|-18LFR.k 1unn.k HB0 7zX0");
      moment.tz.link("Asia/Calcutta|Asia/Kolkata");
      var date = moment.tz(reviewDate, "Asia/Calcutta").format("DD MMM, YYYY");
      ratingByUser = "";
        for(j=0;j<reviews[i].rating;j++){
          ratingByUser += "<span class='fa fa-star fa-x orange'></span>";
        }
        for(j=(reviews[i].rating);j<5;j++){
          ratingByUser += "<span class='fa fa-star-o fa-x orange'></span>";
        }

       
       htmlStr+= "<div class='col-xs-12 col-md-12 col-sm-12 review-main' style='padding: 0;'>" +
                    "<p class='review-user-name'>" +
                      ratingByUser+" "+reviews[i].reviewer +
                      "<span class='review-date'>" +
                        date +
                      "</span>"+
                    "</p>"+
                    "<div class='user-full-review-wrapper'>" +
                      "<p class='user-full-review'>"+reviews[i].review+"</p>" +
                    "</div>"+
                  "</div>";
    }

        document.getElementById('user-reviews').innerHTML = htmlStr;
      if(reviews.length>0) {
        document.getElementById('total-reviews').innerHTML = reviews[0].totalRatings;
        bookRating = parseFloat(Math.round(reviews[0].averageRating * 100) / 100).toFixed(1)+" "+ "out of 5";
    }
    else{
      bookRating = "There are no customer reviews yet.";
     }
    document.getElementById('rating-by-user').innerHTML = bookRating;
  }
// $(document).ready(function() {
//   var list = $('#user-reviews .review-main');
//   var numToShow = 3;
//   var showMoreReviewButton = $('#show-more-reviews');
//   var numInList = list.length;

//   if(numInList > numToShow) {
//     showMoreReviewButton.show();
//   }
//   list.slice(0, numToShow).show();
//   showMoreReviewButton.click(function() {
//     var showing = list.filter(':visible').length;
//     list.slice(showing -1, showing+numToShow).fadeIn();
//     var nowShowing = list.filter(':visible').length;
//     if(nowShowing >= numInList) {
//       showMoreReviewButton.hide();
//     }
//   });
// });
</script>
