<%@ page import="javax.servlet.http.Cookie" %>

<%String requestURL = request.getRequestURL().toString();
String servletPath = request.getServletPath();
def newCookie = new javax.servlet.http.Cookie("siteName", "books");
newCookie.path = "/"
response.addCookie newCookie;
%>
<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>


<div class="loading-icon hidden">
  <div class="loader-wrapper">
    <div class="loader">Loading</div>
  </div>
</div>

<div class="row">
    <div class="col-12 text-center">
        <h4 class="whitetext pt-4">Flashcards</h4>
    </div>
</div>

<div class="studySet container mt-4 mb-4">
<div class="container study-set-wrapper-continer">
    <div class="row" id="revisionTitleInput">
        <div class="col-12 study-set-item">
            <div class="form-group">
                <textarea class="study-set-textarea" placeholder="Revision set name" name="revisionTitle" id="revisionTitle">${revisionName}</textarea>
            </div>
        </div>
    </div>

    <div class="row study-set-wrapper mt-4" id="study-set-wrapper">

        <%if("edit".equals(mode)){
            keyValues.each{keyValue->
        %>
        <div class="col-md-12 study-set-main" id="keyvalueholder_${keyValue.id}">
            <span class="term-counter" onclick='javascript:deleteCard(${keyValue.id});'></span>
            <div class="col-md-6 study-set-item">
                <div class="form-group">
                    <textarea class="study-set-textarea termAndDef" placeholder="Enter Term" id="term${keyValue.id}" onchange="flashCardEdited(${keyValue.id});">${keyValue.term}</textarea>
                </div>
            </div>
            <div class="col-md-6 study-set-item">
                <div class="form-group">
                    <textarea class="study-set-textarea termAndDef" placeholder="Enter Definition" id="definition${keyValue.id}" onchange="flashCardEdited(${keyValue.id});">${keyValue.definition}</textarea>
                </div>
            </div>
        </div>
        <%}}%>

    <form id="study-set-form" class="w-100">
      <div class="col-md-12 study-set-main" id="keyvalueholder">
        <span class="term-counter" ></span>
          <div class="col-md-6 study-set-item">
           <div class="form-group">
              <textarea class="study-set-textarea termAndDef" placeholder="Enter Term" id="term"></textarea>
            </div>
          </div>
          <div class="col-md-6 study-set-item">
            <div class="form-group">
              <textarea class="study-set-textarea termAndDef" placeholder="Enter Definition" id="definition"></textarea>
            </div>
          </div>
      </div>

    </form>
<div class="row">
    <div class="col-12">
        <div class="alert-thin col-12 text-center red invalid-feedback "  id="allAlert">
            Please enter all fields marked in red.
        </div>
    </div>
</div>


        <div class="row w-100">
    <div class="col-md-12 add-study-card-btn-wrapper text-center">
        <a href="javascript:history.back();" class="add-study-card-btn">
            <span>- Close</span>
        </a>
      <a href="javascript:generateCard();" class="add-study-card-btn">
        <span>+ Add Card</span>
      </a>
        <a href="javascript:backToMain();" class="add-study-card-btn">
            <span>Save and Submit</span>
        </a>
    </div>
        </div>

  </div>
</div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<%if("edit".equals(mode)) {%>
<script>
var editMode=true;
</script>
<%}%>
<script>
    var done=false;
var studySetForm = $('#study-set-form');
var studySetSection ="";
var chapterId=${params.chapterId};
var resId=${resId};
var siteId = "${session['siteId']}";
var addCard;
$('#allAlert').hide();
function generateCard() {
        if(document.getElementById("revisionTitle").value==""){
            $("#allAlert").show(500);
            $('#revisionTitle').addClass('input-error').focus();
            $('#revisionTitle').on('keyup keypress', function () {
                $("#UploadPdfFile").hide();
                $(this).removeClass('input-error');
            });

        }
        else if(document.getElementById("term").value==""){
            $("#allAlert").show(500);
            $('#term').addClass('input-error').focus();
            $('#term').on('keyup keypress', function () {
                $("#allAlert").hide();
                $(this).removeClass('input-error');
            });

        }else if(document.getElementById("definition").value==""){
            $("#allAlert").show(500);
            $('#definition').addClass('input-error').focus();
            $('#definition').on('keyup keypress', function () {
                $("#allAlert").hide();
                $(this).removeClass('input-error');
            });

        }else {
            var key=encodeURIComponent(document.getElementById("term").value);
            var value=encodeURIComponent(document.getElementById("definition").value);
            var title=encodeURIComponent(document.getElementById("revisionTitle").value);
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="pubdesk" action="addFlashCard" onSuccess='updateResId(data)'
			params="'term='+key+'&definition='+value+'&revisionTitle='+title+'&mode=add'+'&chapterId='+chapterId+'&resId='+resId"></g:remoteFunction>
            addCard=true;
            if(done == true && siteId != 9){
                var myParam = location.search.split('bookId=')[1];
                var myParamChapter = location.search.split('chapterId=')[1];
                var chapterid = myParamChapter.split('&')[0];
                var bookid = myParam.split('&')[0];
                location.href="/book-create-new?chapterId="+chapterid+"&bookId="+bookid+"&printBooks=false";
            }
            else if(done == true && siteId == 9) {
                window.history.back();
            }
        }
}
function doneSubmit(data){
    window.location.replace(document.referrer);
}
function updateResId(data){
    $('#term').attr('id', 'term'+data.keyValueId);
    $('#definition').attr('id', 'definition'+data.keyValueId);
    $('#keyvalueholder').attr('id', 'keyvalueholder_'+data.keyValueId);
    document.getElementById('term'+data.keyValueId).setAttribute("onchange", "javascript:flashCardEdited("+data.keyValueId+");");
    document.getElementById('definition'+data.keyValueId).setAttribute("onchange", "javascript:flashCardEdited("+data.keyValueId+");");
    $('#keyvalueholder_'+data.keyValueId).find('.term-counter').attr("onclick", "javascript:deleteCard("+data.keyValueId+");");
    resId = data.resId;
    studySetSection = "<div class='col-md-12 study-set-main' id='keyvalueholder'>" +
        "<span class='term-counter' onclick='javascript:deleteCard("+data.keyValueId+");'>" +
        "<input type='hidden'>" +
        "</span>" +
        "<div class='col-md-6 study-set-item'>" +
        "<div class='form-group'>" +
        "<textarea class='study-set-textarea termAndDef' placeholder='Enter Term' id='term'></textarea>" +
        "</div>" +
        "</div>" +
        "<div class='col-md-6 study-set-item'>" +
        "<div class='form-group'>" +
        "<textarea class='study-set-textarea termAndDef' placeholder='Enter Definition' id='definition'></textarea>" +
        "</div>" +
        "</div>" +
        "</div>";
    $(studySetForm).append(studySetSection);
    $('.loading-icon').addClass('hidden');
}

function deleteCard(keyValueId) {
  $("#keyvalueholder_"+keyValueId).hide();
    <g:remoteFunction controller="wonderpublish" action="deleteFlashCard"
			params="'keyValueId='+keyValueId"></g:remoteFunction>
}

function flashCardEdited(keyValueId){
    var term =  encodeURIComponent(document.getElementById("term"+keyValueId).value);
    var definition = encodeURIComponent(document.getElementById("definition"+keyValueId).value);
    <g:remoteFunction controller="wonderpublish" action="updateFlashCard"
			params="'term='+term+'&definition='+definition+'&keyValueId='+keyValueId"></g:remoteFunction>
}

function backToMain(){

    if(document.getElementById("definition").value==""&&document.getElementById("term").value==""){
      if(siteId != 9) {
          var myParam = location.search.split('bookId=')[1];
          var myParamChapter = location.search.split('chapterId=')[1];
          var chapterid = myParamChapter.split('&')[0];
          var bookid = myParam.split('&')[0];
          location.href = "/book-create-new?chapterId=" + chapterid + "&bookId=" + bookid + "&printBooks=false";
      }else{
          window.history.back();
      }

    }
    else{
         done=true;
        generateCard();
    }

}
</script>
