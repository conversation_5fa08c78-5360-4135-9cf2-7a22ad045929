<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="https://cdn.plyr.io/3.6.1/plyr.css" />
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.4.1/jquery.min.js"></script>
    <title>Document</title>
<style>
    html,body{
        margin: 0;
        padding: 0;
    }
    #myElement{
        pointer-events: none;
    }
    #myElement_logo{
        width: 220px;
        height: 42px;
        background:#000;
        top:2px !important;
    }
    .video-overlay{
        width: 100%;
        height: 100%;
        background:black;
        position: absolute;
        top:0;
        z-index: 9999;
        /*display: flex;*/
        align-items: center;
        justify-content: center;
        display: none;
    }
    .top-overlay{
        position: absolute;
        top: 0;
        width: 560px;
        z-index: 999;
        height: 58px;
        background: black
    }
    .bottom-overlay{
        height: 40px;
        position: absolute;
        z-index: 999;
        bottom: 50px;
        width: 600px;
    }
    .wsplayer{
        position: relative;
        background: #000;
        height: 100vh;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .ytp-expand-pause-overlay .ytp-pause-overlay{
        display: none !important;
    }
    .full-screen{
        width: 1200px;
        height:790px;
    }
    #wsplayer{
        position: relative;
    }
    #fullMode{
        position: absolute;
        bottom: 6px;
        background: black;
        border: none;
        right: 10px;
        outline: 0;
    }
    #exitMode{
        display: none;
        position: absolute;
        bottom: 6px;
        background: black;
        border: none;
        right: 10px;
        outline: 0;
    }
    #fullMode img{
        width: 26px;
    }
    #exitMode img{
        width: 26px;
    }
    .top-overlay.top-full{
        width: 1200px;
    }
    #play-button{
        background: #f00;
        border:none;
        border-radius: 50px;
        height: 40px;
        width: 40px;
        display: flex;
    }
    #play-button img{
        width: 32px;
    }
    .borderorange{
        border: 1px solid #e67e22;
    }
#fullmodeBtn{
    display: none;
}
</style>
</head>

<body>
%{--<h1>Youtube Player</h1>--}%
<div class="wsplayer">
    <div class="video-overlay">
        <button id="play-button">
        <img src="${assetPath(src: 'play_arrow-white.svg')}">
    </button>
       </div>
    <div style="position: relative;">
        <div class="top-overlay"></div>
    <iframe id="wsplayer" width="560" height="450" src="https://www.youtube.com/embed/boEdtd2WLxI?&modestbranding=1&enablejsapi=1&html5=1&rel=0" frameborder="0" allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="0"></iframe>
   <span id="fullmodeBtn">
    <button id="fullMode"><img src="${assetPath(src: 'fullscreen.svg')}"></button>
        <button id="exitMode"><img src="${assetPath(src: 'fullscreen_exit.svg')}"></button>
   </span>
</div>
    <div class="bottom-overlay"></div>

</div>

<script type="text/javascript">
    var showOverlay=false;
    $('#fullMode').on('click',function () {
        console.log('sdf');
       // $('#wsplayer').addClass('full-screen');
        $('#wsplayer').addClass('full-screen');
        $('#fullMode').hide();
        $('#exitMode').show();
        $('.top-overlay').addClass('top-full');

    });
    $('#exitMode').on('click',function () {
        console.log('sdf');
        // $('#wsplayer').addClass('full-screen');
        $('#wsplayer').removeClass('full-screen');
        $('#exitMode').hide();
        $('#fullMode').show();
        $('.top-overlay').removeClass('top-full');
    });

    // https://developers.google.com/youtube/iframe_api_reference

    // global variable for the player
    var player;

    // this function gets called when API is ready to use
    function onYouTubePlayerAPIReady() {
        // create the global player from the specific iframe (#video)
        player = new YT.Player('wsplayer', {
            events: {
                // call this function when player is ready to use
                'onReady': onPlayerReady,
                'onStateChange':onPlayerStateChange
            }
        });
    }

function onPlayerStateChange(playerStatus) {
    if (playerStatus.data === 1) {
        console.log('ddfdgd');
       $('#fullmodeBtn').show();
    }
    if (playerStatus.data === 2) {
        $('.video-overlay').css('display','flex');
       var ab=playerStatus.target.getIframe().id;
       console.log(ab);
        $('#fullmodeBtn').hide();
    }

}
    function onPlayerReady(event) {

        // bind events
        var playButton = document.getElementById("play-button");
        playButton.addEventListener("click", function() {
            player.playVideo();
            $('.video-overlay').css('display','none');
        });

        var pauseButton = document.getElementById("pause-button");
        pauseButton.addEventListener("click", function() {
            player.pauseVideo();
        });

    }


    // Inject YouTube API script
    var tag = document.createElement('script');
    tag.src = "//www.youtube.com/player_api";
    var firstScriptTag = document.getElementsByTagName('script')[0];
    firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);




</script>
</body>
</html>
