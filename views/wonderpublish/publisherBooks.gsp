<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
  <div class="container-fluid publisher-books-container">
    <div class="row">
      <div class="col-md-2 col-sm-2 hidden-xs publisher-sidebar">
        <a href="/wonderpublish/publisherBooks" class="wp-logo">Wonder Publish</a>
        <ul class="publisher-menu-wrapper">
          <li class="publisher-menu-item active">
            <a href="/wonderpublish/publisherBooks" class="publisher-menu-item-link">Publishing</a>  
          </li>
        </ul>
      </div>
      <div class="col-md-10 col-sm-10 col-xs-12 publisher-books-wrapper">
        <ul class="nav nav-tabs publisher-tabs" role="tablist">
          <li role="presentation" class="active">
            <a href="#publisher-books" aria-controls="publisher-books" role="tab" data-toggle="tab">Published eBooks</a>
          </li>
          
          %{-- <li role="presentation">
            <a href="#publisher-book-progress" aria-controls="publisher-book-progress" role="tab" data-toggle="tab">Book In-Progress</a>
          </li>
          <li role="presentation">
            <a href="#publisher-book-add" aria-controls="publisher-book-add" role="tab" data-toggle="tab">Add New</a>
          </li> --}%
        </ul>
        <div class="tab-content publisher-tabs-content">
          <div role="tabpanel" class="tab-pane active" id="publisher-books">
            <table class="table table-responsive publisher-books-table">
              <tbody>
                <tr>
                  <th>Book Title</th>
                  <th class="right-aligned-data">Price</th>
                  <th class="right-aligned-data">Published On</th>
                  <th class="right-aligned-data">No. of sold copies</th>
                </tr>
                <g:each in="${sales}" var="publisher" status="i">
                  <tr>
                    <td>${sales.title[i]}</td>
                    <td class="right-aligned-data">INR ${sales.sales[i]}</td>
                    <td class="right-aligned-data">${sales.publishedDate[i]}</td>
                    <td class="right-aligned-data">${sales.noOfOrders[i]}</td>
                  </tr>
                </g:each>
              </tbody>
            </table>
          </div>
          %{-- <div role="tabpanel" class="tab-pane" id="publisher-book-progress">Abhishek</div>
          <div role="tabpanel" class="tab-pane" id="publisher-book-add">Mishra</div> --}%
        </div>
      </div>
    </div>
  </div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>