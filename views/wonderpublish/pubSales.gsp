<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var loggedIn=false;
</script>
<style>

.page-item.active .page-link {
    background-color: #007bff !important;
    color: #ffffff;
}
table td a {
    color: #007bff;
}
.datepicker table {
    border-collapse: unset;
}
.datepicker .datepicker-days td, .datepicker .datepicker-days th {
    width: 25px !important;
    height: 25px !important;
}
table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>td:first-child:before, table.dataTable.dtr-inline.collapsed>tbody>tr[role="row"]>th:first-child:before {
    top: 16px !important;
}
table.dataTable>tbody>tr.child ul.dtr-details>li {
    border-bottom: none !important;
    padding: 0.2em 0 !important;
}
table.dataTable>tbody>tr.child span.dtr-title {
    min-width: 180px !important;
}
@media screen and (max-width: 767px){
    div.dataTables_wrapper div.dataTables_paginate ul.pagination {
        margin: 10px 0 30px !important;
        justify-content: center !important;
    }
    table.dataTable.nowrap th, table.dataTable.nowrap td {
        white-space: normal !important;
    }
    table.dataTable>tbody>tr.child span.dtr-title {
        min-width: 100% !important;
    }
    .form-group.col-md-6.mt-2 {
        display:flex;
    }
    .form-group.col-md-6.mt-2 button{
        max-width:100%;
    }
}

table.dataTable {
    width: 100% !important;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<asset:stylesheet href="imageoverlay.css"/>
<link href="https://cdn.datatables.net/1.10.20/css/dataTables.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdn.datatables.net/responsive/2.2.3/css/responsive.bootstrap4.min.css" type="text/css" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<div>
    <div class="container-fluid my-5 px-5 publishing_sales">
        <div  class='px-0' id="bookdtl" style="min-height: calc(100vh - 156px);">
            <div class='col-md-12 main mx-auto p-4'>
                <h3 class="text-center mb-4">Sales Report</h3><br>
                <div id="content-books" class="sale">
                    <div class="form-inline align-items-end">
                        <div class="form-group col-md-3">
                            <label for="poStartDate"><strong>Invoice From date</strong></label>
                            <input type="text" class="form-control" id="poStartDate" placeholder="Any" value="<%=poStartDate!=null?poStartDate:""%>" autocomplete="off">
                        </div>
                        <div class="form-group col-md-3">
                            <label for="poEndDate"><strong>Invoice To date</strong></label>
                            <input type="text" class="form-control" id="poEndDate" placeholder="Any" value="<%=poEndDate!=null?poEndDate:""%>" autocomplete="off">
                        </div>
                        <% if(session["userdetails"].publisherId==null&&!isAffiliateSales&&!institutionSales) {%>
                        <div class="form-group col-md-3">
                            <label for="publisherId"><strong>Publisher</strong></label>
                            <g:select id="publisherId" class="form-control w-100" optionKey="id" optionValue="name"
                                      value="${publisherId!=null?publisherId:""}" name="publisherId" from="${publishers}" noSelection="['':'All']"/>
                            <small class="text-muted text-danger" id=publisherIdalert" style="display: none">
                                Select the publisher
                            </small>
                        </div>
                        <%  } %>

                    </div>
                    <div class="form-inline align-items-end">
                        <%if(!isAffiliateSales){%>
                        <div class="form-group col-md-3">
                            <label><strong>Select</strong></label>
                            <select class="form-control w-100" id="saleSelect" value="<%=select!=null?select:""%>">
                                <option value="paymentId">Payment Id</option>
                                <option value="bookId">Book Id</option>
                                <option value="orderno">Order No.</option>
                            </select>
                        </div>
                        <div class="form-group col-md-3">
                            %{--<label for="paymentId"><strong>Razorpay Payment Id/Book Id/order No.</strong></label>--}%
                            <input type="text" class="form-control" id="paymentId" placeholder="PaymentId/BookId/Order No."  value="<%=paymentId!=null?paymentId:""%>" autocomplete="off">
                        </div>
                        <%}%>
                        <% if(session["userdetails"].publisherId==null&&"1".equals(""+session["siteId"])&&!isAffiliateSales&&!institutionSales) {%>
                        <div class="form-group col-md-3">
                            <label for="siteId"><strong>Sites</strong></label>
                            <g:select id="siteId" class="form-control w-100" optionKey="id" optionValue="clientName"
                                      value="" name="siteId" from="${sitesList}" noSelection="['':'All']"/>
                        </div>
                        <%  } else{%>
                         <input type="hidden" name="siteId" id="siteId" value="${session["siteId"]}">
                        <%}%>
                    </div>
                    <div class="form-group col-md-6 mt-2">
                        <button type="button" id="search-btn" onclick="saleSearch()" class="btn btn-lg btn-primary col-3">Search</button>
                        <button type="button" id="download-btn" class="btn btn-lg btn-primary ml-3 col-3">Download</button>
                    </div>
                    <div class="col-md-12 mt-5">
                        <table id="salesData" class="table table-striped table-bordered dt-reponsive nowrap" style="display: none;">
                            <thead>
                            <tr class="bg-primary text-white">
                                <%if(!isAffiliateSales){%>
                                <th>Invoice/Order number</th>
                                <%}%>
                                <th>Book name</th>
                                <th>ISBN</th>
                                <th>Publisher</th>
                                <th>Site</th>
                                <th>Price</th>
                                <th>Purchased date/time</th>
                                <%if(!isAffiliateSales){%>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Mobile number</th>
                                <th>Payment Id</th>
                                <%}%>
                                <th>Book price</th>
                                <th>Discount Price</th>
                                <th>Discount Type</th>
                                <th>State</th>
                                <th>District</th>
                                <%if(wsSuperUser){%>
                                <th>Direct Sales</th>
                                <%}%>

                            </tr>
                            </thead>
                        </table>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>
<div class="push"></div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="bootstrap.min.js"/>
<sec:ifLoggedIn>
    <script>

        $('#saleSelect').change(function() {
            localStorage.removeItem('SalesReport');
            var salesVal = $(this).val();
            localStorage.setItem("SalesReport", salesVal);
        });

        window.onload = function() {
            var SalesReport = localStorage.getItem("SalesReport");
            if(SalesReport==null){
                $('#saleSelect').val('paymentId');
            }
            else {
                $('#saleSelect').val(SalesReport);
            }
        }

        $('#download-btn').on('click', function() {
            var poStartDate1=document.getElementById('poStartDate').value;
            var poEndDate1=document.getElementById('poEndDate').value;

            var paymentId1=null;
            var select1 = null;

            <%if(!isAffiliateSales){%>
                    paymentId1=document.getElementById('paymentId').value;
                    select1 = document.getElementById("saleSelect")[document.getElementById("saleSelect").selectedIndex].value;
            <%}%>
            var salesSiteId = $('#siteId').val();
            var publisherId=null;
            <% if(session["userdetails"].publisherId==null&&!isAffiliateSales) {%>
              publisherId=document.getElementById('publisherId').value;
            <%  } %>
            <% if(session["userdetails"].publisherId==null) {%>
            window.location.href = "/wonderpublish/downloadSalesData?poStartDate="+poStartDate1+"&poEndDate="+poEndDate1+"&paymentId="+paymentId1+"&select="+select1+"&publisherId="+publisherId+"&salesSiteId="+salesSiteId;
            <%}else {%>
            window.location.href = "/wonderpublish/downloadSalesData?poStartDate="+poStartDate1+"&poEndDate="+poEndDate1+"&paymentId="+paymentId1+"&select="+select1+"&salesSiteId="+salesSiteId;
            <%}%>
        });

        function downloadInvoice(poNo) {
            var publisherId="";
            <% if(session["userdetails"].publisherId!=null) {%>
            publisherId="${session["userdetails"].publisherId}";
            <%}else {%>
            publisherId=document.getElementById('publisherId').value;
            <%}%>
            window.location.href ="/wonderpublish/downloadInvoice?poNo="+poNo+"&publisherId="+publisherId;
        }

        <% if(sales!=null && sales.size()>0) {%>
        $('#download-btn').prop('disabled', false);
        <% } %>
    </script>
    <form name="salesDataform" action="/wonderpublish/downloadSalesData">
        <input type="hidden" name="poStartDate" value="<%=poStartDate!=null?poStartDate:""%>">
        <input type="hidden" name="paymentId" value="<%=paymentId!=null?paymentId:""%>">
        <input type="hidden" name="select" value="<%=select!=null?select:""%>">
        <input type="hidden" name="poEndDate" value="<%=poEndDate!=null?poEndDate:""%>">
        <input type="hidden" name="publisherId" value="<%=publisherId!=null?publisherId:""%>">
    </form>
    <form name="invoiceform" action="/wonderpublish/downloadInvoice.pdf">
        <input type="hidden" name="poNo" value="">
    </form>
</sec:ifLoggedIn>
<script src="https://cdn.datatables.net/1.10.20/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.10.20/js/dataTables.bootstrap4.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/dataTables.responsive.min.js"></script>
<script src="https://cdn.datatables.net/responsive/2.2.3/js/responsive.bootstrap4.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/js/bootstrap-datepicker.min.js"></script>
<script>
    var isResponsive = false;

    var mq = window.matchMedia( "(max-width: 570px)" );
    if (mq.matches) {
        isResponsive = true;
    }
    else isResponsive = false;


    $('#salesData').hide();
    function saleSearch() {

        if ($.fn.dataTable.isDataTable('#salesData')) {
            $('#salesData').DataTable().destroy();
        }

        $('#salesData').show();
        $('#salesData').DataTable({
            'responsive': isResponsive ? true : false,
            "scrollX": true,
            'destroy': true,
            //'processing': true,
            'serverSide': true,
            'searching': false,
            'ordering': false,
            'retrieve': true,
            'ajax': {
                'url': '/wonderpublish/pubSales',
                'type': 'GET',
                'data': function (outData) {
                    //console.log(outData);
                    outData.poStartDate = $('#poStartDate').val();
                    outData.poEndDate = $('#poEndDate').val();
                    outData.select = $('#saleSelect').val();
                    outData.paymentId = $('#paymentId').val();
                    outData.publisherId = $('#publisherId').val();
                    outData.mode="submit";
                    outData.salesSiteId=$('#siteId').val();
                    return outData;
                },
                dataFilter: function (inData) {
                    //console.log(inData);
                    return inData;
                },
                error: function (err, status) {
                    console.log(err);
                },
            },
            'columns': [
                <%if(!isAffiliateSales){%>
                {
                    'data': 'poNo',
                    'searchable': 'false',
                    'render': function (data, type, row) {
                        if(row.poNo != null) {
                            return "<a href=\"javascript:downloadInvoice('"+row.poNo+"')\">"+row.poNo+"</a>";
                        }
                    }
                },
                <%}%>
                {
                    'data': 'title'
                },
                {
                    'data':'isbn'
                },
                {
                    'data': 'publisher'
                },
                {
                    'data': 'siteName'
                },
                {
                    'data': 'price'
                },
                {
                    'data': 'salesDate'
                },
                <%if(!isAffiliateSales){%>
                {
                    'data': 'name'
                },
                {
                    'data': 'email'
                },
                {
                    'data': 'mobile'
                },
                {
                    'data': 'paymentId'
                },
                <%}%>
                {
                    'data': 'bookPrice'
                },
                {
                    'data': 'discountAmount'
                },
                {
                    'data': 'discountType'
                },
                {
                    'data': 'state'
                },
                {
                    'data': 'district'
                }
                <%if(wsSuperUser){%>
                ,
                {
                    'data': 'directSales'
                }
                <%}%>


            ],

        });
    }

    $('#poStartDate, #poEndDate').datepicker({
        format: 'dd-mm-yyyy',
        startView: 1,
        todayBtn: "linked",
        //clearBtn: true,
        autoclose: true,
        todayHighlight: true,
        orientation: "bottom auto",
        endDate: '+0d'
    });
</script>
</body>
</html>
