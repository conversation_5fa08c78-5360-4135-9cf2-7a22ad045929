<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<script>
    var loggedIn=false;
    $('link[data-role="baseline"]').attr('href', '');
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="page-main-wrapper mdl-js banner_management p-5">
    <div class="container">
        <div class="d-flex justify-content-center align-items-center">
            <h3><strong>Banner Management</strong></h3>
        </div>

        <% for (int i = 0; i < 10; i++) { %>

        <form class="card card-modifier card-shadow border-0 mt-4 p-4" enctype="multipart/form-data" role="form"
              name="uploadbanner${(i + 1)}"
              id="uploadbanner${(i + 1)}" action="/pubdesk/addBanners"
              onsubmit="return addBanner(${(i+1)})"
              method="post">
            <h5><strong>Banner ${(i + 1)}</strong></h5>
            <input type="hidden" name="bannerId" value="${bannersMstList[i] ? bannersMstList[i].id : ""}">
            <div class="row add_banners">
                <div class="form-group col mb-3">
                    <label for="name${(i + 1)}">Name</label>
                    <input type="text" class="form-control" name="name" id="name${(i + 1)}"
                           value="${bannersMstList[i] ? bannersMstList[i].imageName : ""}" autocomplete="off">
                </div>
                <div class="form-group col mb-3">
                    <label for="bookId${(i + 1)}">Book Id</label>
                    <input type="text" class="form-control" name="bookId" id="bookId${(i + 1)}" value="${bannersMstList[i] ? bannersMstList[i].bookId : ""}"
                           autocomplete="off" onblur="checkBookId(this.value, ${(i+1)})">
                </div>
                <div class="form-group col mb-3">
                    <label for="imageDesc${(i + 1)}">Desktop</label>
                    <input type="file" name="file" class="form-control"
                           id="imageDesc${(i + 1)}"
                           value="${bannersMstList[i] ? bannersMstList[i].imagePath : ""}" accept="image/*">
                </div>
                <div class="form-group col mb-3">
                    <label for="imageMob${(i + 1)}">Mobile</label>
                    <input type="file" name="filesss" class="form-control"
                           id="imageMob${(i + 1)}"
                           value="${bannersMstList[i] ? bannersMstList[i].imagePathMobile : ""}" accept="image/*">
                </div>
                <div class="form-group col col-12 col-md-12 col-lg-12 mb-3">
                    <label for="actionType${(i + 1)}">Action</label>
                    <input type="text" class="form-control" name="actionType" id="actionType${(i + 1)}"
                           value="${bannersMstList[i] ? bannersMstList[i].action : ""}" >
                </div>
            </div>
            <div class="row align-items-center banner_details">
                <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                    <input type="text" class="form-control form-control-sm" name="namename" id="namename${(i + 1)}"
                           value="${bannersMstList[i] ? bannersMstList[i].imagePath : ""}" readonly autocomplete="off">
                </div>
                <% if (bannersMstList[i] != null && bannersMstList[i].imagePath) { %>
                <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                    <a href="/wonderpublish/showImage?id=${bannersMstList[i].id}&fileName=${bannersMstList[i] ? bannersMstList[i].imagePath : ""}">View Desktop Image</a>
                </div>
                <% }%>
            </div>
            <div class="row align-items-center banner_details">
                <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                    <input type="text" class="form-control form-control-sm" name="namenamemobile" id="namenamemobile${(i + 1)}"
                           value="${bannersMstList[i] ? bannersMstList[i].imagePathMobile : ""}" readonly autocomplete="off">
                </div>
                <% if (bannersMstList[i] != null && bannersMstList[i].imagePathMobile) { %>
                <div class="form-group col-12 col-md-6 col-lg-3 mb-3">
                    <a href="/wonderpublish/showImage?id=${bannersMstList[i].id}&fileName=${bannersMstList[i] ? bannersMstList[i].imagePathMobile : ""}">View Mobile Image</a>
                </div>
                <% }%>
            </div>
        <% if (bannersMstList[i] != null) { %>
            <div class="d-flex">
                <button type="submit" class="btn btn-success btn-success-modifier mb-2 mr-2 px-4">Update</button>
                <button type="button" onclick="deleteId(${bannersMstList[i].id});" class="btn btn-outline-secondary btn-outline-secondary-modifier mb-2 px-3">Delete</button>
            </div>

            <% } else { %>

            <div class="d-flex">
                <button type="submit" class="btn btn-primary btn-primary-modifier mb-2 px-5">Add</button>
            </div>

            <% } %>

        </form>

        <% } %>

    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>

<script>

    function addBanner(index) {
        var selectedDescFile = document.getElementById("imageDesc"+index).files[0];
        var selectedMobFile = document.getElementById("imageMob"+index).files[0];
        if (document.getElementById("name"+index).value==""){
            alert("Please enter the name.");
            return false;
        } else if(document.getElementById("imageDesc"+index).value=="" && document.getElementById("namename"+index).value=="" && document.getElementById("imageMob"+index).value!="") {
            alert("Desktop images mandatory for mobile images.");
            return false;
        } else if(document.getElementById("imageDesc"+index).value=="" && document.getElementById("namename"+index).value=="") {
            alert("Please choose images.");
            return false;
        } else if((selectedDescFile !=undefined && selectedDescFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1) || (selectedMobFile !=undefined && selectedMobFile.name.search(/[ <>{}#/\\?:!&$^*%`|+"]/g)>-1)){
            alert("File name must not contain any space and special characters.");
            return false;
        } else if((selectedDescFile !=undefined && selectedDescFile.size>=200000) || (selectedMobFile !=undefined && selectedMobFile.size>=200000)){
            alert("File size should be below 200kb.");
            return false;
        }else{
            $(".loading-icon").removeClass("hidden");
            alert("Saved successfully!");
        }
    }

    function deleteId(id) {
        let confirmAction = confirm("Are you sure to delete this banner?");
        if(confirmAction) {
            $(".loading-icon").removeClass("hidden");
            <g:remoteFunction controller="pubdesk" action="deleteBannerById" params="'id='+id" onSuccess = "bannerDeleted(data);"/>
        }
    }

    function bannerDeleted(data) {
        if(data.status=="OK"){
            alert("Deleted successfully!");
            location.reload();
        }
    }

    var bookIdIndex = null;
    const checkBookId = (bookId, indx) => {
        if(bookId.length<1) return;
        if(!parseInt(bookId)){
            alert("Please enter a valid bookId");
            document.getElementById("bookId"+indx).value = "";
            return;
        }
        if(true){
            bookIdIndex=indx;
            <g:remoteFunction controller="publisherManagement" action="checkBookId" params="'bookId='+bookId" onSuccess="alertBook(data)"/>
        }
    }
    const alertBook=(data)=>{
        if(data.status!="OK"){
            alert(data.status);
            document.getElementById("bookId"+bookIdIndex).value = "";
            return;
        }
    }

</script>

</body>
</html>
