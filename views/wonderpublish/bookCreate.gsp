<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>
<script>
    var defaultSiteName="${session['entryController']}";
</script>
<style xmlns="http://www.w3.org/1999/html">
.navbar {
    margin-bottom: 0px;
}

.image-upload > input {
    display: none;
}
.modal-footer{
    border:none;
    position: static;
    z-index: 0;
}
#removePhone .modal.show .modal-dialog, #removePhone1 .modal.show .modal-dialog {
    -webkit-transform: translate(0,0);
    transform: translate(0,0);
}
#removePhone .modal-dialog-centered, #removePhone1 .modal-dialog-centered {
    display: -ms-flexbox;
    display: flex;
    -ms-flex-align: center;
    align-items: center;
    justify-content: center;
    min-height: calc(100% - (.5rem * 2));
}
#removePhone .modal-dialog, #removePhone1 .modal-dialog {
    position: relative;
    width: auto;
    /*margin: .5rem;*/
    pointer-events: unset;
}
#addedContents th {
    padding: 4px;
    border: 1px solid #000;
}
</style>
<asset:stylesheet href="imageoverlay.css"/>
<asset:stylesheet href="bootstrap-select.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<style>
.input-wrap input {
    cursor: pointer;
    position: relative;
    z-index: 1;
    border: 1px solid transparent;
    background: transparent;
    color: #fff;
    text-align: center;
}

.input-wrap input:active, .input-wrap input:focus {
    cursor: text;
    text-align: center;
}

.input-wrap input {
    font-size: 18px;
    font-family: inherit;
    padding-left: 3px;
    box-sizing: border-box;
    width: 100%;
}

.input-wrap ::-webkit-input-placeholder { /* WebKit, Blink, Edge */
    color:    #333;
    text-align: center;
}

.input-wrap :-moz-placeholder { /* Mozilla Firefox 4 to 18 */
    color:    #333;
    opacity:  1;
    text-align: center;
}

.input-wrap ::-moz-placeholder { /* Mozilla Firefox 19+ */
    color:    #333;
    opacity:  1;
    text-align: center;
}

.input-wrap :-ms-input-placeholder { /* Internet Explorer 10-11 */
    color:    #333;
    text-align: center;
}

.authors .bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
    width: 320px;
}

.image-upload > input {
    display: none;
}
    .d-flex{
        display: flex;
        align-items: center;
        justify-content: center;
    }
</style>
<asset:stylesheet href="animatedInput.css"/>
<div class="container-fluid wplandingblueimage topnavblue"></div>
<div class="modal fade" data-backdrop="static" data-keyboard="false" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="col-md-12 text-right">
                    <button type="button" class="btn btn-primary btn-sm" data-dismiss="modal"  aria-label="Close"> <span aria-hidden="true">&times;</span></button>
                </div>
                <iframe width="100%" height="500px" src="" frameborder="0" allowfullscreen></iframe>
            </div>
        </div>
    </div>
</div>
<div class="modal" id="removePhone">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">

            <!-- Modal Header -->
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title">Reference Youtube Link:</h4>
            </div>

            <!-- Modal body -->
            <div class="modal-body">
                <div class="form-group col-md-12 d-flex">
                    <label class="control-label col-sm-2">Name: </label>
                     <div class="col-sm-6">
                        <input class="form-control" type='textbox' name='number' id='restitle'>
                     </div>
                </div>
                <div class="form-group col-md-12 d-flex">
                      <label class="control-label col-sm-2">Youtube link: </label>
                     <div class="col-sm-6">
                            <input class="form-control" type='textbox' name='number' id='resname'>
                     </div>
                        <input type="hidden" name='number' id='resid'>
                </div>
            <div class="form-group col-md-12 d-flex">
                <label class="control-label col-sm-2">Start Date: </label>
                <div class="col-sm-6">
                    <input class="form-control" type='textbox' name='modalTestStartDate' id='modalTestStartDate' autocomplete="off">
                </div>
            </div>

            <div class="form-group col-md-12 d-flex">
                <label class="control-label col-sm-2">End Date: </label>
                <div class="col-sm-6">
                    <input class="form-control" type='textbox' name='modalTestEndDate' id='modalTestEndDate' autocomplete="off">
                </div>
            </div>

            <div class="form-group col-md-12 d-flex"> <label class="control-label col-sm-12">  Download video links</label></div>
            <div class="form-group col-md-12 d-flex">
                <label class="control-label col-sm-2">  360p: </label>
                <div class="col-sm-6">
                    <input class="form-control" type='textbox' name='downloadlink1' id='downloadlink1'>
                </div>
            </div>
            <div class="form-group col-md-12 d-flex">
                <label class="control-label col-sm-2">540p: </label>
                <div class="col-sm-6">
                    <input class="form-control" type='textbox' name='downloadlink2' id='downloadlink2'>
                </div>
            </div>
            <div class="form-group col-md-12 d-flex">
                <label class="control-label col-sm-2">720p: </label>
                <div class="col-sm-6">
                    <input class="form-control" type='textbox' name='downloadlink3' id='downloadlink3'>
                </div>
            </div>
             <div class="form-group col-md-12 d-flex" style="display: none" id="modalCommentSection">
                    Allow comments for live video<input class="col-md-1" type="checkbox" name="allowComments" id="modalAllowComments">
                 Display comments after live video<input class="col-md-1" type="checkbox" name="displayComments" id="modalDisplayComments"></div>

            </div>

            <!-- Modal footer -->
            <div class="modal-footer" style="text-align: center;">
                <button type="submit" class="btn btn-danger"  onclick="javascript:submitSearch()">Submit</button>
            </div>
        </div>
    </div>
</div>

    <div class="modal" id="removePhone1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">

                <!-- Modal Header -->
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Reference web link:</h4>
                </div>

                <!-- Modal body -->
                <div class="modal-body">
                    <div class="form-group col-md-12 d-flex">
                        <label class="control-label col-sm-2">Name: </label>
                        <div class="col-sm-6">
                            <input class="form-control" type='textbox' name='number' id='weblinktitleedit'>
                        </div>
                    </div>
                    <div class="form-group col-md-12 d-flex">
                        <label class="control-label col-sm-2">Web link: </label>
                        <div class="col-sm-6">
                            <input class="form-control" type='textbox' name='number' id='weblinkurledit'>
                        </div>
                        <input type="hidden" name='number' id='weblinkresid'>
                    </div>

                    <!-- Modal footer -->
                    <div class="modal-footer" style="text-align: center;">
                        <button type="submit" class="btn btn-danger"  onclick="javascript:submitSearch1()">Submit</button>
                    </div>
                </div>
            </div>
        </div>

    </div>


<div class="container panelTab py-4">
    <ul class="nav nav-tabs " role="tablist">
        <li role="presentation" class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#createBook">Book Details</a>
        </li>
        <li  role="presentation" class="nav-item">
            <a class="nav-link" data-toggle="tab" href="#addChapters">Chapters</a>
        </li>
    </ul>
    <div class="tab-content">
        <div id="createBook" class="tab-pane fade in active">
            <div class="row"><br>
                <div class="col-md-8 col-md-offset-1 create"><h4>Create / Edit book</h4></div>
            </div>
            <div class="row create1"><br>

                <div class="col-md-11 col-md-offset-1 " style="min-height: calc(100vh - 417px);">
                    <div class="row">
                        <div class="col-md-2 text-left">
                            <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbookcover" id="uploadbookcover" action="/wonderpublish/uploadBookCover" method="post">
                                <input type="hidden" name="bookId">
                                <input type="hidden" name="imgType" value="cover">

                                <div class="row justify-center text-left">
                                    <div class="col-md-12 text-left image-wrapper overlay-fade-in" id="bookcover">
                                    <%if(booksMst!=null&&booksMst.coverImage!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${booksMst.id}&fileName=${booksMst.coverImage}&type=books&imgType=passport" width="100">
                                    <%}else{%> <a class="icon" href="#" ><i class="material-icons">description</i><span>Book Cover Image</span></a><%}%>
                                    <div class="image-overlay-content image-upload">
                                        <p><br></p>
                                        <div id="filelabel1"><label for="fileoption1"><span class="smallText" style="cursor: pointer;color:white;margin-top:-23px">Upload Cover Image</span></label></div>
                                        <input id="fileoption1" name="file" type="file"  accept="image/png, image/jpeg, image/gif" onchange="updateBookCover('cover');"/></div>
                                </div>

                                </div>
                                <div class="upload-book-iame-helper" style="margin-top: 12px; font-size:14px;text-align: center;">180px by 240px</div>

                            </form>
                        </div>
                        <div class="col-md-8 buks4" style="margin-left: 15px;">
                            <div class="row">
                                <div class="col-md-3 text-left">
                                    <form class="form-horizontal" enctype="multipart/form-data" role="form" name="uploadbookheader" id="uploadbookheader" action="/wonderpublish/uploadBookCover" method="post">
                                        <input type="hidden" name="bookId">
                                        <input type="hidden" name="imgType" value="header">
                                        <input type="hidden" name="smartEbook" value="${smartEbook}">

                                        <div class="row justify-center text-left"><div class="col-md-12 text-left image-wrapper overlay-fade-in" id="bookcover">
                                            <%if(booksMst!=null && booksMst.headerImage!=null){%>
                                            <img src="/funlearn/showProfileImage?id=${booksMst.id}&fileName=${booksMst.headerImage}&type=books&imgType=passport" width="100">
                                            <%}else{%> <a class="icon1" href="#" ><i class="material-icons">description</i></i><span>Book Header Image</span></a><%}%>
                                            <div class="image-overlay-content image-upload">
                                                <p><br></p>
                                                <div id="filelabel2"><label for="fileoption2"><span class="smallText" style="cursor: pointer;color:white;text-align: center;margin-top: -23px">Upload Header Image</span></label></div>
                                                <input id="fileoption2" name="file" type="file"  accept="image/png, image/jpeg, image/gif" onchange="updateBookCover('header');"/></div>

                                        </div>

                                        </div>
                                        <div class="upload-book-iame-helper" style="margin-top: 12px;font-size:14px; text-align: center;">1280px by 240px</div>

                                    </form>
                                </div>
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-md-6" id="book">
                                            <div class="form-group">
                                                <label for="title">Title</label>
                                                <input name="title" id="title" class="form-control" type="text" placeholder="Enter book title here." size="255" onblur="javascript:bookDtlUpdate(this);"
                                                       value="<%= booksMst !=null?booksMst.title:"" %>">&nbsp;<i class="fa fa-spinner" id="titlesaving" style="display: none"></i>
                                                <a href='javascript:getDeepLink(null,null,null)' class='text-primary'>Deep Link</a>
                                            </div>
                                        </div>
                                        <div class=" red booktitlealert" id="booktitlealert" style="display: none">
                                            <div class="d-flex ">
                                            <i class="material-icons">
                                                arrow_back
                                            </i><i> Enter the book title</i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="publisherId">Publisher</label>

                                                <g:select id="publisherId" class="form-control" optionKey="id" optionValue="name" onchange="javascript:bookDtlUpdate(this);"
                                                          value="${booksMst?booksMst.publisherId:""}" name="publisherId" from="${publishers}" noSelection="['':'Select']"/>
                                                &nbsp;&nbsp;<input type="text" name="newpublisher" id="newpublisher" style="display: none" onblur="addNewPublisher()"> <a href="javascript:displayNewPublisherBox();" id="newPublisherLabel">Add new Publisher</a>
                                                <i class="fa fa-spinner" id="publishersaving" style="display: none"></i>
                                            </div>
                                        </div>
                                        <div class="col-md-6 red" id=publisherIdalert" style="display: none">
                                            <i class="fa fa-arrow-left"> Select the publisher</i>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-9  authors">
                                            <g:select  class="selectpicker" id="authors" name="authors" from="${authors}" optionKey="id" optionValue="name" multiple="multiple" title="Select Author/s" data-live-search="true"/>
                                            &nbsp;&nbsp;<input type="text" name="new author" id="newauthor" style="display: none" onblur="addNewAuthor()"> <a href="javascript:displayNewAuthorBox();" id="newAuthorLabel">Add new author</a>
                                            <i class="fa fa-spinner" id="authorsaving" style="display: none"></i>
                                        </div>
                                        %{--<div class="col-md-3" id="showdetails"><a href="javascript:displayDetails()">Show details <i class="fa fa-angle-double-down fa-x"></i> </a></div>--}%
                                    </div><br>
                                    <div class="row">
                                        <%if(smartEbook){%>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="bookTypeId">Book type</label>
                                                <select name="bookType" class="form-control" id="bookTypeId" onchange="javascript:bookDtlUpdate(this);"><option>Select one</option>
                                                    <option value="ebook" <%= booksMst !=null&&"ebook".equals(booksMst.bookType)?"selected":""%>>eBook</option>
                                                    <option value="liveclasses" <%= booksMst !=null&&"liveclasses".equals(booksMst.bookType)?"selected":""%>>Live Classes</option>
                                                    <option value="onlinecourse" <%= booksMst !=null&&"onlinecourse".equals(booksMst.bookType)?"selected":""%>>Online Course</option>
                                                    <option value="testseries" <%= booksMst !=null&&"testseries".equals(booksMst.bookType)?"selected":""%>>Test Series</option>
                                                </select>
                                            </div>

                                        </div>
                                        <%}%>
                                        <%if(disciplines!=null){%>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="disciplineId">Discipline</label>
                                                <select name="discipline" class="form-control" id="disciplineId" onchange="javascript:bookDtlUpdate(this);"><option>Select one</option>
                                                    <%
                                                            disciplines.each{ discipline ->
                                                    %>
                                                    <option value="${discipline.discipline}" <%= booksMst !=null&&discipline.discipline.equals(booksMst.discipline)?"selected":""%>>${discipline.discipline}</option>
                                                    <%}%>

                                                </select>
                                            </div>
                                        </div>

                                        <%}%>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="testStartDate">Test start date</label>
                                                <input type="text" id="testStartDate" name="testStartDate" value="" autocomplete="off">
                                            </div>
                                        </div>
                                        <div class="col-md-6 ">
                                            <div class="form-group">
                                                <label for="testEndDate">Test end date</label>
                                                <input type="text" id="testEndDate" name="testEndDate"  value="" autocomplete="off">
                                            </div>
                                        </div>

                                    </div>
                                    <% if("9".equals(""+session.getAttribute("siteId"))) { %>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="tags">Subject:</label>
                                                <select name="tags" id="tags" onchange="javascript:bookDtlUpdate(this);" multiple>
                                                    <option value=""></option>
                                                    <% tags.each{tag-> %>
                                                    <option value="${tag.name}" <%=booksMst!=null && booksMst.tags!=null && booksMst.tags.indexOf(tag.name)>-1?"selected":""%>>${tag.name}</option>
                                                    <% } %>
                                                </select>

                                            </div>
                                        </div>
                                    </div>
                                    <% } %>
                                    <% if("12".equals(""+session.getAttribute("siteId"))|| "23".equals(""+session.getAttribute("siteId"))) { %>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="vendor">Vendor</label>
                                                <select name="vendor" class="form-control" id="vendor" onchange="javascript:bookDtlUpdate(this);"><option>Select one</option>
                                                    <option value="zaza" <%= booksMst !=null&&"zaza".equals(booksMst.vendor)?"selected":""%>>ZaZa</option>
                                                    <option value="fidus" <%= booksMst !=null&&"fidus".equals(booksMst.vendor)?"selected":""%>>Fidus</option>
                                                    <option value="digiultra" <%= booksMst !=null&&"digiultra".equals(booksMst.vendor)?"selected":""%>>Digiultra</option>
                                                </select>

                                            </div>
                                        </div>
                                    </div>
                                    <% } %>

                                </div>
                            </div>
                            <div id="bookdetails">


                                <%if(!smartEbook){%>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="isbn">Amazon buy link</label>
                                            <input type="text" class="form-control" name='buylink1' id="buylink1" value="<%= booksMst !=null?booksMst.buylink1:"" %>" placeholder="Amazon buy link" onblur="javascript:bookDtlUpdate(this);">&nbsp;<i class="fa fa-spinner" id="buylink1saving" style="display: none"></i>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="isbn">Flipkart buy link</label>
                                            <input type="text" class="form-control" name='buylink2' id="buylink2" value="<%= booksMst !=null?booksMst.buylink2:"" %>" placeholder="Flipkart buy link" onblur="javascript:bookDtlUpdate(this);">&nbsp;<i class="fa fa-spinner" id="buylink2saving" style="display: none"></i>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="isbn">Amazon review link</label>
                                            <input type="text" class="form-control" name='reviewLink1' id="reviewLink1" value="<%= booksMst !=null?booksMst.reviewLink1:"" %>" placeholder="Amazon review link" onblur="javascript:bookDtlUpdate(this);">&nbsp;<i class="fa fa-spinner" id="reviewLink1saving" style="display: none"></i>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="isbn">Flipkart review link</label>
                                            <input type="text" class="form-control" name='reviewLink2' id="reviewLink2" value="<%= booksMst !=null?booksMst.reviewLink2:"" %>" placeholder="Flipkart review link" onblur="javascript:bookDtlUpdate(this);">&nbsp;<i class="fa fa-spinner" id="reviewLink2saving" style="display: none"></i>
                                        </div>
                                    </div>
                                </div>

                                <%}%>

                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="isbn">ISBN</label>
                                            <input type="text" class="form-control" name='isbn' id="isbn"   maxlength="13" pattern="[0-9]*" value="<%= booksMst !=null?booksMst.isbn:"" %>" placeholder="ISBN" onblur="javascript:bookDtlUpdate(this);">&nbsp;<i class="fa fa-spinner" id="isbnsaving" style="display: none"></i>
                                            <span id="isbnerror" style="color:darkred;font-size: 12px;white-space: nowrap;position: relative;"></span>
                                        </div>
                                    </div>
                                    <%if(smartEbook){%>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="price">List Price</label>
                                            <input type="text" class="form-control" name='listprice' id="listprice" value="<%= booksMst !=null?booksMst.listprice:"" %>" placeholder="Price" onblur="javascript:checkPrice(this);">&nbsp;<i class="fa fa-spinner" id="listpricesaving" style="display: none"></i>
                                        </div>

                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="price">Sell Price</label>
                                            <input type="text" class="form-control" name='price' id="price" value="<%= booksMst !=null?booksMst.price:"" %>" placeholder="Price" onblur="javascript:checkPrice(this);">&nbsp;<i class="fa fa-spinner" id="pricesaving" style="display: none"></i>
                                        </div>
                                        <div id="priceError" style="display: none" class="red">Please enter numeric value only.</div>
                                    </div>
                                    <%}%>
                                </div>
                                <% if("12".equals(""+session.getAttribute("siteId"))|| "23".equals(""+session.getAttribute("siteId"))) { %>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="packageBookIds">keywords ( ** enter comma "," seperated values, without spaces)</label>
                                            <textarea rows="5" cols="50" id="isbnkeywordsIds" name="f"><%= keywordStr !=null?keywordStr:"" %></textarea><br>
                                            <button onclick="updateIsbnKeyword()">Update</button> <i class="fa fa-spinner" id="keywords" style="display: none"></i>
                                        </div>

                                    </div>
                                </div>
                                    <% } %>
                                <div class="row">

                                    <%if(smartEbook){%>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="packageBookIds">Package book id or ids (Comma separated book ids for multiple books)</label>
                                            <textarea row="2" cols="20" id="packageBookIds" name="packageBookIds"><%= booksMst !=null?booksMst.packageBookIds:"" %></textarea>
                                            <button onclick="updatePackageBooks()">Update</button> <i class="fa fa-spinner" id="packagebookidssaving" style="display: none"></i>
                                        </div>

                                    </div>
                                    <%boolean showInLibrary=true
                                    if(booksMst !=null&&"No".equals(booksMst.showInLibrary)) showInLibrary = false
                                    %>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="showInLibrary">Show in library</label>
                                            <input type="radio" name="showInLibrary" id="showInLibrary" value="Yes" <%=showInLibrary?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">Yes&nbsp;&nbsp;
                                            <input type="radio" name="showInLibrary" value="No" <%=!showInLibrary?"checked":"" %> onblur="javascript:bookDtlUpdate(this);">No
                                           &nbsp;<i class="fa fa-spinner" id="showinlibraryshowing" style="display: none"></i>
                                        </div>

                                    </div>
                                    <%}%>
                                </div>
                                <%if(smartEbook){%>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="previewChapter">Preview Chapter</label>
                                            <select class="form-control" name="previewChapter" id="previewChapter" onchange="javascript:previewChapterUpdate(this)">
                                                <option value="">Select chapter</option>
                                                <% if(chaptersMst!=null){
                                                    for(int i=0;i<chaptersMst.size();i++){
                                                        if("toc".equals(chaptersMst[i].chapterDesc)) continue;%>
                                                <option value="${chaptersMst[i].id}" <%="true".equals(chaptersMst[i].previewChapter)?"selected":""%>>${chaptersMst[i].name}</option>
                                                <%}
                                                }%>
                                            </select><br>

                                            <span class="smallerText greyText"><i class="bukerror">** Please select preview chapter, after adding the chapters.</i></span>
                                        </div>

                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="validityDays" nowrap>Book validity (in days)</label>
                                            <input type="number" name="validityDays" id="validityDays" size="5" value="<%= booksMst !=null?booksMst.validityDays:"" %>" onblur="javascript:bookDtlUpdate(this);">
                                        </div>
                                        <div class="col-md-6"><br><br>
                                            <g:uploadForm name="resource2Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">
                                                <input type="hidden" name="resourceType" value="Notes">
                                                <input type="hidden" name="useType" value="notes">
                                                <input type="hidden" name="chapterId">
                                                <input type="hidden" name="bookId">
                                                <input type="hidden" name="quizMode" value="toc">

                                                <!--  <div class="image-upload">
                                                      <div id="tocchapter"><%if(resourceDtl!=null){%>
                                                <a href="javascript:showBook('${resourceDtl.id}','${bookId}','${resourceDtl.chapterId}','${resourceDtl.noOfPages}')" class='darkgrey'>
                                    <span class='light10text'>${resourceDtl.resourceName}</span> </a>&nbsp;&nbsp;<label for='file2'><span style='cursor: pointer' class="logoblue">&nbsp; Change table of contents <i class="fa fa-upload fa-x"></i></span>&nbsp;( epub only) </label><%}
                                            else{%><label for='file2'><span style='cursor: pointer' class="logoblue">&nbsp; Upload table of contents <i class="fa fa-upload fa-x"></i></span>&nbsp;( epub only) </label><%}%></div>
                                <input id='file2' name='file' type='file'  accept=".epub" onchange='updatetoc();'/>
                            </div>-->
                                            </g:uploadForm>
                                        </div>


                                    </div>

                                    <%}%>

                                <div class="container">
                                    <div class="row">

                                    </div>
                                    <div class="row addtag">
                                        <div class="col-md-9 "><b>Add category tags</b></div>
                                        <div class="col-md-9 greyText smallText">** This will help in searching and categorizing the book.</div>
                                    </div>
                                    <div class="row addtag">
                                        <div class="col-md-9 greyText smallText">** Add the relevant category for the book.</div>
                                    </div>
                                    <div class="row addtag">
                                        <div class="col-md-9 greyText smallText">** You can add more than one category.</div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-3 form-group">
                                            <g:select id="createLevel" class="form-control" name="createLevel" from="${levelsMstList}" optionKey="name" optionValue="name"
                                                      noSelection="['':'Select']" onchange="javascript:getCreateSyllabus(this.value)"/>
                                        </div>
                                        <div class="col-md-2 form-group">
                                            <select id="createSyllabus" class="form-control" name="createSyllabus"  onchange="javascript:getCreateGrade(this.value)" style="display: none"><option>Select</option></select>
                                        </div>
                                        <div class="col-md-2 form-group">
                                            <select id="createGrade" class="form-control" name="createGrade"  onchange="javascript:getCreateSubject(this.value)" style="display: none"><option>Select</option></select>
                                        </div>
                                        <div class="col-md-2 form-group">
                                            <select id="createSubject" class="form-control" name="createSubject"  onchange="javascript:addTag(this.value)" style="display: none"><option>Select</option></select>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-9">
                                            <table border="1" width="90%" id="addedtags">

                                                <%if(booksTagDtl!=null){
                                                    for(int i=0; i<booksTagDtl.size();i++){%>
                                                <tr>
                                                    <td width='22%'>&nbsp;${booksTagDtl[i].level}</td>
                                                    <td width='25%'>&nbsp;${booksTagDtl[i].syllabus}</td>
                                                    <td width='25%'>&nbsp;${booksTagDtl[i].grade}</td>
                                                    <td width='25%'>&nbsp;${booksTagDtl[i].subject}</td>
                                                        <td width='3%'>&nbsp;<a href="javascript:deleteBookTag(${booksTagDtl[i].id})">Delete</a></td>
                                                </tr>
                                                <%}}%>
                                            </table>
                                        </div>
                                    </div>
                                    </div>
                                    <div class="row"><br>
                                        <div class="col-md-12 form-group book">
                                            <label for="previewChapter">Language</label>
                                            <g:select id="language" class="form-control" name="language" onchange="javascript:bookDtlUpdate(this);"
                                                      from="${langMstList}"
                                                      value="${booksMst?booksMst.language:""}"
                                                      optionKey="language" optionValue="language" noSelection="['':'Select']" />
                                        </div>
                                        <%
                                            if("9".equals(""+session.getAttribute("siteId"))) {
                                                int year = Calendar.getInstance().get(Calendar.YEAR);
                                                int pubYear = 0;
                                                int pubMonth = 0;

                                                if(booksMst!=null && booksMst.datePublished!=null) {
                                                    Calendar cal = Calendar.getInstance();
                                                    cal.setTime(booksMst.datePublished);
                                                    pubYear = cal.get(Calendar.YEAR);
                                                    pubMonth = cal.get(Calendar.MONTH);
                                                }
                                        %>
                                        <div class="col-md-3">
                                            <div class="form-group">
                                                <label for="dtPublished">Date Published:</label>
                                                <select name="dtPublished" class="form-control" id="dtPublishedId" onchange="javascript:bookPubDtUpdate();">
                                                    <option value="" selected disabled>Select Year</option>
                                                    <% for(int i=year; i>year-19; i--) { %>
                                                    <option value="<%=i%>" <%=(i==pubYear?"selected":"")%>><%=i%></option>
                                                    <%}%>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group" style="margin-bottom: 0; margin-top: 22px;">
                                                <select name="dtPublished1" class="form-control" id="dtPublished1Id" onchange="javascript:bookPubDtUpdate();">
                                                    <option value="" selected disabled>Select Month</option>
                                                    <% for(int i=1; i<=12; i++) { %>
                                                    <option value="<%=i%>" <%=(i==pubMonth+1?"selected":"")%>><%=i%></option>
                                                    <%}%>
                                                </select>
                                            </div>
                                            <div id="yearError" style="display: none" class="red">Please select an year.</div>
                                            <div id="monthError" style="display: none" class="red">Please select a month.</div>
                                        </div>
                                        <% } %>
                                    </div>
                                <div class="row"><br>
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="description">Book Description</label>
                                            <textarea class="form-control" rows="3" name='description' id="description"  placeholder="Book Description"  maxlength="2000"></textarea>
                                            &nbsp;<i class="fa fa-spinner" id="descriptionsaving" style="display: none"></i>
                                        </div>
                                    </div>
                                </div>
                                    %{--<div class="row">--}%
                                        %{--<div class="col-md-10 text-right"><a href="javascript:hideDetails()">Hide details <i class="fa fa-angle-double-up fa-x"></i></a></div>--}%
                                    %{--</div>--}%
                                </div>
                            </div>
                            <button type="button" id="nxt-btn" class="btn btn-primary " style="margin-top:1rem;border-radius: 0px;border-width: 0px;">Next</button>
                        </div>
                        %{--<div class="row">--}%
                            %{--<div class="col-md-12"><hr></div>--}%
                        %{--</div>--}%
                    </div>
                    <iframe id="htmlreadingcontent" src="/assets/iframe.html" style="border: 0; width:100%;"></iframe>
                    <!--<div id="showhtml">-->
                </div>
            </div>
        </div>
        <div id="addChapters" class="tab-pane fade">
            <div class="container" style="min-height: calc(100vh - 200px);">
            <%if(smartEbook){%>
            <div  class='row' id="chapterssection" >
                <div class="col-md-3 wpsidebar" >
                    <div class="row">
                        <div class="col-md-4 greytext">
                            <b>Chapters</b>
                        </div>
                        <div class="col-md-8 greytext text-right">
                            <b><a href="javascript:addNewChapter()" class="text-primary">Add New Chapter</a></b>
                        </div>
                    </div>
                    <hr>
                    <div id="chaptersList">
                        <% if(chaptersMst!=null){
                            for(int i=0;i<chaptersMst.size();i++){
                                if("toc".equals(chaptersMst[i].chapterDesc)) continue;%>
                        <div class="row">
                            <div class="col-md-12">
                                <a href="javascript:getChapterDetails('${chaptersMst[i].id}')" class="greytext" id="chapter${chaptersMst[i].id}">  ${i+1}. ${chaptersMst[i].name}</a>
                                <sec:ifAnyGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
                                    <a href="javascript:deleteChapter(${chaptersMst[i].id});"><img  src="${assetPath(src: 'baseline-delete-24px.svg')}" alt="wonderslate"></a>
                                </sec:ifAnyGranted>

                            </div>
                        </div>
                        <hr>
                        <%}
                        }%>
                    </div>
                </div>
                <div class="col-md-7">
                    <div id="chapterdetails" style="display: none">
                        <div class="row">
                            <div class="col-md-9">
                                <div class="form-group">
                                    <label for="title">Chapter name:</label>
                                    <input name="name" id="chaptername" class="form-control" type="text" placeholder="Enter Chapter Name here." onblur="javascript:chapterDtlUpdate(this);">

                                </div>
                            </div>
                        </div>
                        <div id="chapterdetailsdetails" style="display: none">
                            <div class="row">
                                <div class="col-md-9">
                                    <div class="form-group">
                                        <label for="chapterDesc">Tags to be used in Youtube and Web search</label>
                                        <textarea class="form-control" rows="3" name='chapterDesc' id="chapterDesc"  placeholder="Tags" onblur="javascript:chapterDtlUpdate(this);" maxlength="2000"></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="row buk">
                                <div class=" col-md-12"><br>
                                    <h4 class="buks">Resources</h4>
                                </div>
                            </div>
                            <div class="row buk1 mt-0">
                                <div class="col-md-3">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary dropdown-toggle shadow-none" data-toggle="dropdown">Upload</button>
%{--                                        <button type="button" class="btn btn-danger dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">--}%
%{--                                            <span class="caret"></span>--}%
%{--                                            <span class="sr-only">Toggle Dropdown</span>--}%
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a href="javascript:showNotesUpload()">Reading Materials</a></li>
                                            <li><a href="javascript:showVideoLinksUpload()">Reference video links</a></li>
                                            <li><a href="javascript:showLinksUpload()">Reference web links</a></li>
                                            <sec:ifAllGranted roles="ROLE_WS_CONTENT_CREATOR">
                                                <li><a href="javascript:showQuizUpload('Fill in the blanks')">Fill in the blanks</a></li>
                                                <li><a href="javascript:showQuizUpload('True or False')">True or False</a></li>
                                                <li><a href="javascript:showImagesZipUpload()">Images Zip</a></li>
                                                <li><a href="javascript:showQuizUpload('Multiple Choice Questions XML')">MCQs (XML format)</a></li>
                                                <% if(!("eutkarsh".equals(session["entryController"]))){%>
                                                <li><a href="javascript:showVideosUpload('Paid')">Paid Videos</a></li>
                                                <%}%>
                                            </sec:ifAllGranted>
                                        </ul>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="btn-group">
                                        <button type="button" class="btn btn-primary dropdown-toggle shadow-none" data-toggle="dropdown">Create</button>
%{--                                        <button type="button" class="btn btn-danger dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">--}%
%{--                                            <span class="caret"></span>--}%
%{--                                            <span class="sr-only">Toggle Dropdown</span>--}%
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a href="javascript:createFlashCards()">Flashcards</a></li>
                                            <li><a href="javascript:createMCQ()">Multiple Choice Questions</a></li>
                                            <li><a href="javascript:createMCQBulk()">Multiple Choice Questions - Bulk</a></li>
                                            <li><a href="javascript:showQuizCopy('Multiple Choice Questions')">Copy Quiz</a></li>
                                            <li><a href="javascript:showReadCopy('Notes')">Copy Reading material</a></li>
                                            <li><a href="javascript:createHTML('')">Notes</a></li>
                                            <li><a href="javascript:createQandA('QA')">Questions and Answers - Long</a></li>
                                            <li><a href="javascript:createQandA('Short QA')">Questions and Answers - Short</a></li>
                                            <% if("books".equals(session["entryController"])){%>
                                            <li><a href="javascript:createVideoHTML('')">Video Explanation</a></li>
                                            <%}%>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div id="notesupload" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Select the file from your system and upload.</b></div>
                                </div>

                                    <g:uploadForm name="resource3Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">
                                        <div class="row">
                                            <div class="form-group col-sm-3 col-xs-12"><g:textField id="resourceName" class="form-control" name="resourceName"  placeholder="Name" /></div>
                                        <div class="form-group col-sm-3 col-xs-12 filelink"><input id="file3" type="file" class="form-control" name="file"  accept=".epub , .pdf" /></div>
                                        </div>
                                        <div class="row">
                                        <div class="col-sm-9">
                                            <i>Render as pdf only (not secure)</i>&nbsp;&nbsp;<input type="radio" name="convert" value="false">Yes
                                        &nbsp;&nbsp;<input type="radio" name="convert" value="true" checked>No
                                        </div>
                                        <div class="buk3 ">
                                            <button type="button" onclick="javascript:uploadNotes()" class="btn btn-primary">Add</button>
                                        </div>

                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="notesUploadAlert">
                                            ** Enter a name for this reading material.
                                        </div>
                                            <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="UploadSizeAlert">
                                                ** File size exceeds 25mb.
                                            </div>
                                        <input type="hidden" name="resourceType" value="Notes">
                                        <input type="hidden" name="useType" value="notes">
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                        </div>
                                    </g:uploadForm>

                            </div>
                            <div id="imagezipupload" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Select the zip file from your system and upload.</b></div>
                                </div>
                                <div class="row">
                                    <g:uploadForm name="resource7Form" url="[action:'addImagesZip',controller:'wonderpublish']"  method="post">
                                        <div class="d-flex justify-content-between">
                                        <div class="form-group col-sm-6 col-xs-12 filelink"><input id="file5" class="form-control" type="file"  name="file"  accept=".zip" /></div>
                                        <div class="buk3">
                                            <button type="button" onclick="javascript:uploadImageZip()" class="btn btn-primary">Add</button>
                                        </div>
                                    </div>
                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="zipUploadAlert">
                                            ** Please select the images zip file to upload.
                                        </div>
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                    </g:uploadForm>
                                </div>
                            </div>
                            <div id="paidvideoupload" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Select the video file from your system and upload.</b></div>
                                </div>
                                <div class="row">
                                    <g:uploadForm name="resource10Form" url="[action:'addPaidVideo',controller:'wonderpublish']"  method="post">
                                       <div class="d-flex justify-content-between">
                                        <div class="form-group col-sm-3 col-xs-12 filelink">
                                            <input id="file10" type="file"  name="file"  accept="video/mp4,video/x-m4v,video/*" />
                                        </div>
                                        <div class="buk3 ">
                                            <button type="button" onclick="javascript:uploadPaidVideo()" class="btn btn-primary">Add</button>
                                        </div>
                                        </div>

                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="paidVideoAlert">
                                            ** Please select the video file to upload.
                                        </div>
                                        <input type="hidden" name="resourceType" value="AWS Video">
                                        <input type="hidden" name="useType" value="notes">
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                    </g:uploadForm>
                                </div>
                            </div>
                            <div id="linksupload" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Add reference web link:</b></div>
                                </div>
                                <div class="row">
                                    <g:uploadForm name="resource4Form" url="[action:'addlink',controller:'resourceCreator']"  method="post">
                                        <div class="d-flex justify-content-between">
                                        <div class="form-group col-sm-6 col-xs-12 "><g:textField id="weblink" class="form-control" name="link"  placeholder="Web link"  /></div>
                                        <div class="form-group col-sm-6 col-xs-12"><g:textField id="linksresourceName" class="form-control" name="resourceName"  placeholder="Name" /></div>
                                        <div class="buk3">
                                            <button type="button" onclick="javascript:uploadLinks()" class="btn btn-primary">Add</button>
                                        </div>
                                        </div>

                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="linksUploadAlert">
                                            ** Enter both link and the name.
                                        </div>
                                        <input type="hidden" name="resourceType" value="Reference Web Links">
                                        <input type="hidden" name="useType" value="notes">
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                    </g:uploadForm>
                                </div>
                            </div>
                            <div id="videolinksupload" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Add reference youtube link:</b></div>
                                </div>

                                    <g:uploadForm name="resource5Form" url="[action:'addlink',controller:'resourceCreator']"  method="post">
                                        <div class="row">
                                            <div class="form-group col-sm-12 col-xs-12 "><g:textField id="videolink" class="form-control" name="link"  placeholder="Youtube link"  /></div>
                                            <div class="form-group col-sm-12 col-xs-12"><g:textArea id="videolinksresourceName" class="form-control" name="resourceName"  placeholder="Title" /></div>
                                            <div class="form-group col-sm-6 col-xs-12"><g:textField id="videolinksresourceStartDate" class="form-control" name="resourceStartDate"  placeholder="Start Date" autocomplete="off"/></div>
                                            <div class="form-group col-sm-6 col-xs-12"><g:textField id="videolinksresourceEndDate" class="form-control" name="resourceEndDate"  placeholder="End Date" autocomplete="off"/></div>
                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="videolinksUploadAlert">
                                            ** Enter valid Youtube link and the name.
                                        </div>
                                        <input type="hidden" name="resourceType" value="Reference Videos">
                                        <input type="hidden" name="useType" value="notes">
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-9"><b>Video Player&nbsp;&nbsp;</b><input type="radio" name="videoPlayer" value="youtube"  onchange="videoPlayerChanged(this)">&nbsp;Youtube
                                            &nbsp;&nbsp;<input type="radio" name="videoPlayer" value="custom" checked onchange="videoPlayerChanged(this)">&nbsp;Custom player</div>
                                        </div>
                                        <div class="row" id="allowCommentsSection">
                                            <div class="col-sm-9">Allow comments for live video&nbsp;&nbsp;<input type="checkbox" name="allowComments" checked>
                                                <br>Display comments after live video&nbsp;&nbsp;<input type="checkbox" name="displayComments"></div>
                                        </div>
                                        <div class="row">
                                        <div class="col-sm-9" style="margin-top: 1rem">  Download video links:</div>
                                        <div class="form-group col-md-12 d-flex"> <label class="control-label col-sm-2">  360p: </label><g:textField id="downloadlink1" class="form-control" name="downloadlink1"/></div>
                                        <div class="form-group col-sm-12 d-flex"><label class="control-label col-sm-2">  540p: </label><g:textField id="downloadlink2" class="form-control" name="downloadlink2"/></div>
                                        <div class="form-group col-sm-12 d-flex"><label class="control-label col-sm-2">  720p: </label><g:textField id="downloadlink3" class="form-control" name="downloadlink3"/></div>
                                        </div>
                                        <div class="row">
                                              <div class="col-sm-3 bukadds">
                                                   <button type="button" onclick="javascript:uploadVideoLinks()" class="btn btn-primary">Add</button>
                                              </div>
                                        </div>
                                    </g:uploadForm>

                            </div>
                            <div id="quizupload" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Select the file from your system and upload.</b></div>
                                </div>
                                <div class="row">
                                    <g:uploadForm name="resource6Form" url="[action:'addFIBByFile',controller:'wonderpublish']"  method="post">
                                        <div class="d-flex justify-content-between">

                                        <div class="form-group col-sm-6 col-xs-12"><g:textField id="quizresourceName" class="form-control" name="resourceName"  placeholder="Name" /></div>
                                        <div class="form-group col-sm-3 col-xs-12 filelink"><input id="file4" class="form-control" type="file"  name="file"  accept=".xls,.xlsx,.xml" /></div>
                                        <div class="buk3">
                                            <button type="button" onclick="javascript:uploadQuiz()" class="btn btn-primary">Add</button>
                                        </div>
                                        </div>

                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="quizUploadAlert">
                                            ** Enter a name for this quiz.
                                        </div>
                                        <input type="hidden" name="resourceType" value="">
                                        <input type="hidden" name="useType" value="quiz">
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                    </g:uploadForm>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-12"><br>
                                    <table border="1" width="100%" id="addedContents">
                                    </table><br><br>
                                </div>
                            </div>
                            <div id="quizcopy" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Paste the Quiz number you want to copy from</b></div>
                                </div>
                                <div class="row">
                                    <g:uploadForm name="resource8Form" url="[action:'copyQuiz',controller:'resourceCreator']"  method="post">

                                        <div class="form-group col-sm-3 "><g:textField id="resId"    name="resId"  placeholder="Quiz number" /></div>
                                        <div class="col-sm-3">
                                            <button type="button" onclick="javascript:copyQuiz()" class="btn btn-primary">Copy</button>
                                        </div>

                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="quizCopyAlert">
                                            ** Enter the quiz number.
                                        </div>
                                        <input type="hidden" name="resourceType" value="">
                                        <input type="hidden" name="useType" value="quiz">
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                    </g:uploadForm>
                                </div>
                            </div>
                            <div id="readcopy" style="display: none"><br><br>
                                <div class="row">
                                    <div class="col-md-12"><b>Paste the Read material number you want to copy from</b></div>
                                </div>
                                <div class="row">
                                    <g:uploadForm name="resource9Form" url="[action:'copyQuiz',controller:'resourceCreator']"  method="post">

                                        <div class="form-group col-sm-3 col-xs-12"><g:textField id="readResId"  name="resId"  placeholder="Read Material number" /></div>
                                        <div class="col-sm-3 ">
                                            <button type="button" onclick="javascript:copyRead()" class="btn btn-primary">Copy</button>
                                        </div>

                                        <div class="alert-thin alert alert-warning col-sm-12 text-left red" style="display: none;" id="readCopyAlert">
                                            ** Enter the read material number.
                                        </div>
                                        <input type="hidden" name="resourceType" value="">
                                        <input type="hidden" name="useType" value="notes">
                                        <input type="hidden" name="chapterId">
                                        <input type="hidden" name="bookId">
                                    </g:uploadForm>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <%}%>
            <div class="row">
                <div class="col-md-5 col-md-offset-4 red smallText" style="display: none"  id="publisherrors"></div>
            </div>
            <div class="row dflex">
                <button type="button" id="back-btns" class="btn btn-primary " style="margin-left:3rem;border-radius: 0px;border-width: 0px;">Back</button>
                <sec:ifAnyGranted roles="ROLE_WS_CONTENT_CREATOR,ROLE_BOOK_CREATOR">
                    <div class="col-md-5 text-right" id="notpublished" style="display: none;"><a href="javascript:publish()"><button type="button" class="btn btn-primary " style="border-radius: 0px;border-width: 0px;"><i class="material-icons">shopping_cart</i>&nbsp;&nbsp;  PUBLISH</button></a></div>
                </sec:ifAnyGranted>
                <div class="col-md-5 text-right" id="published" style="display: none;"><b>&nbsp;&nbsp;  PUBLISHED</b>
                    <sec:ifAnyGranted roles="ROLE_WS_CONTENT_ADMIN,ROLE_BOOK_CREATOR">
                        <a href="javascript:unPublishBook()"><button type="button" class="btn btn-primary " style="border-radius: 0px;border-width: 0px;"><i class="material-icons">shopping_cart</i>&nbsp;&nbsp;  UNPUBLISH</button></a>
                    </sec:ifAnyGranted>
                </div>
                <div class="col-md-2 orange" id="booksaving" style="display: none">
                    Saving...<i class="fa fa-spinner fa-3x fa-spin"></i>
                </div>
            </div>
        </div>
    </div>
</div>
</div>
<div class="container-fluid whitebackground">
</div>
<form name="updateDescriptionForm" id="updateDescriptionForm" action="/wonderpublish/updateDescription" method="post">
    <input type="hidden" name="bookId" id="bookId">
    <input type="hidden" name="description" >

</form>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<g:render template="/funlearn/topicscripts"></g:render>
<asset:javascript src="bootstrap-select.js"/>
<asset:javascript src="generic.js"/>
<asset:javascript src="topic.js"/>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="clock.js"/>
<asset:javascript src="moment.min.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>
<script type="text/javascript" async
                 src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<script type="text/x-mathjax-config">
  MathJax.Hub.Config({tex2jax: {inlineMath: [['$','$'], ['\\(','\\)']]}});
</script>
<script>
    $('*[name=resourceStartDate]').appendDtpicker({
        "futureOnly": true,
        "autodateOnStart": false,
        "minuteInterval": 5,
        // "dateOnly":true
    });
    $('*[name=resourceEndDate]').appendDtpicker({
        "futureOnly": true,
        "autodateOnStart": false,
        "minuteInterval": 5,
        // "dateOnly":true
    });
    $(function(){
        $('*[name=testStartDate]').appendDtpicker({
           "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 15,
            "onHide": function(handler){
                 bookDtlUpdate(document.getElementById("testStartDate"))
            }
        });

        $('*[name=modalTestStartDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            // "dateOnly":true
        });
        $('*[name=modalTestEndDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            // "dateOnly":true
        });

        $('*[name=testEndDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "onHide": function(handler){
                bookDtlUpdate(document.getElementById("testEndDate"))
            }

        });
        $('*[name=testResultDate]').appendDtpicker({
            "futureOnly": true,
            "autodateOnStart": false,
            "minuteInterval": 5,
            "onHide": function(handler){
                bookDtlUpdate(document.getElementById("testResultDate"))
            }

        });

        //here
<%  if(booksMst!=null && booksMst.testStartDate!=null && booksMst.testStartDate!="") { %>
        $("#testStartDate").val(moment.utc('<%=booksMst.testStartDate%>').local().format("YYYY-MM-DD HH:mm"));
<%  } %>
<%  if(booksMst!=null && booksMst.testEndDate!=null && booksMst.testEndDate!="") { %>
        $("#testEndDate").val(moment.utc('<%=booksMst.testEndDate%>').local().format("YYYY-MM-DD HH:mm"));
<%  } %>
    });

    CKEDITOR.replace( 'description', {
        height: 50,
        customConfig: '/assets/ckeditor/customConfig.js',
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',


    });

    CKEDITOR.instances.description.on('blur', function() {
        bookDtlUpdate(document.getElementById("description"));
    });

<%if(booksMst!=null&&booksMst.description!=null&&!"".equals(booksMst.description)){%>
    document.getElementById("description").innerHTML="${booksMst.description.replace("\n", "").replace("\r", "")}";
<%}%>
    var loggedInUser = false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedInUser = true;
    </script>
</sec:ifLoggedIn>
<script>


    <%if(booksMst!=null){%>
    var isbn= "${booksMst.isbn}";

    <%}%>
    var bookId="${bookId}";
    var titleAlertShown=false;
    var isbnShown=false;
    var priceErrorShown=false;
    var pubDtErrorShown=false;
    var chapterId=-1;
    var previousChapterId=-1;
    var noOfChapters = ${chaptersMst!=null?chaptersMst.size():0};
    var syllabusList;
    var pageType='';
    var loggedIn='';
    var noOfTags=0;
    var published=false;
    var receivedResId=null;
    <%if(booksTagDtl!=null){%>
    noOfTags = ${booksTagDtl.size()};
    <%}%>
    <%if(booksMst!=null&&"published".equals(booksMst.status)){%>
     $("#notpublished").hide();
     $("#published").show();
     published=true;
    <%}else{%>
    $("#notpublished").show();
    $("#published").hide();
    <%}%>
    $( "input[type='text']" ).keyup(function(e){
        if(e.keyCode == 13) {
           $(this).blur();

        }
    });

    $('#authors').on('changed.bs.select', function (e, clickedIndex, isSelected, previousValue) {

        newAuthorUpdate(clickedIndex,isSelected);
    });
  function videoPlayerChanged(field){
      if("custom"==field.value){
          $("#allowCommentsSection").show();
      }else{
          $("#allowCommentsSection").hide();
      }
  }
    function newAuthorUpdate(clickedIndex,isSelected){
      var authorMode = isSelected?"add":"remove";
       var authorId = document.getElementById("authors").options[clickedIndex].value;

        <g:remoteFunction controller="wonderpublish" action="updateAuthorNew" onSuccess='updateBookId(data);'
            params="'bookId='+bookId+'&authorId='+authorId+'&authorMode='+authorMode"></g:remoteFunction>
    }

    function publish(){
        document.getElementById("publisherrors").innerHTML="";
        $("#publisherrors").hide(500);
        var errorStr="";

        if(document.getElementById('title').value=="") {
            errorStr+="- Book Title/s<br>";
        }

        if(document.getElementById('authors').selectedIndex==-1){
            errorStr+="- Author/s<br>";
        }
        <%if(smartEbook){%>
        if(document.getElementById('price').value==""){
            errorStr+="- Sell Price<br> ";
        }
        if(document.getElementById('bookTypeId').selectedIndex==0){
            errorStr+="- Book type<br> ";
        }
        <%}else{%>
        if(document.getElementById('buylink1').value==""&&document.getElementById('buylink2').value==""){
            errorStr+="- Amazon or Flipkart buy link<br> ";
        }
        <%}%>

        var isbn="";
        <%   if("evidya".equals(session["entryController"])|| "etexts".equals(session["entryController"])) { %>
            if(document.getElementById('isbn').value==""){
                errorStr+="- Enter ISBN<br> ";
            }
        <%}%>

        if(document.getElementById("addedtags").rows.length==0){
            errorStr+="- Category tags<br>";
        }

        if(errorStr.length>0){
            errorStr = "\nPlease enter the following information before publishing <br>"+errorStr;
            document.getElementById("publisherrors").innerHTML=errorStr;
            $("#publisherrors").show(500);
        }else{
            $("#booksaving").show(500);
            <%if(smartEbook){%>
            <g:remoteFunction controller="wonderpublish" action="getChaptersListWithResourceCount" onSuccess='continueValidating(data);
                    'params="'bookId='+bookId"></g:remoteFunction>
            <%}else{%>
            <g:remoteFunction controller="wonderpublish" action="publishBook" onSuccess='publishSucess(data)' params="'bookId='+bookId"></g:remoteFunction>
            <%}%>
        }
    }

    function unPublishBook(){
        if(confirm("Unpublishing this book will remove it from store. It will not remove from user's library. Are you sure to continue?")) {
            $("#booksaving").show(500);
            <g:remoteFunction controller="wonderpublish" action="unpublishBook" onSuccess='unpublishSucess(data)' params="'bookId='+bookId"></g:remoteFunction>
        }
    }
    function unpublishSucess(data){
        $("#booksaving").hide();
        $("#notpublished").show();
        $("#notpublished").addClass('d-inline-flex');
        $("#published").removeClass('d-inline-flex');
    }
   function publishSucess(data){
       $("#booksaving").hide();
       $("#notpublished").hide();
       $("#notpublished").removeClass('d-inline-flex');
       $("#published").show();
       $("#published").addClass('d-inline-flex');
   }
    function continueValidating(data){
        var chapters = data.results;
        var errorStr="";
        var testSeries = data.testSeries;
        $("#booksaving").hide();
        if("Nothing present"==data.status){
            errorStr+="- Add atleast one chapter<br>";
        }else{
            for(i=0;i<chapters.length;i++){
                if(chapters[i].resourceCount=="0"){
                    errorStr+="- Chapter "+chapters[i].chapterName+" does not have any content.<br>";
                }
            }
            if(document.getElementById('bookTypeId')[document.getElementById('bookTypeId').selectedIndex].value=='test'){
                if(chapters.length>"1"){
                    errorStr+="- You should have only one chapter for the test series.<br>";
                }
                if(document.getElementById('testStartDate').value==""){
                    errorStr += "- Test start date<br>";
                }
                if(document.getElementById('testEndDate').value==""){
                    errorStr += "- Test end date<br>";
                }
                if(testSeries.length==0){
                    errorStr += "- Please select the exam for the quiz<br>";
                }
            }else {
                if (document.getElementById('previewChapter').selectedIndex == 0) {
                    errorStr += "- Preview Chapter<br>";
                }
            }
        }

        if(errorStr.length>0){
            $("#booksaving").hide();
            errorStr = "\nPlease enter the following information before publishing <br>"+errorStr;
            document.getElementById("publisherrors").innerHTML=errorStr;
            $("#publisherrors").show();
        }else{
            $("#booksaving").show();
            <g:remoteFunction controller="wonderpublish" action="publishBook" onSuccess='publishSucess(data)' params="'bookId='+bookId"></g:remoteFunction>
        }

    }
    function getCreateSyllabus(level){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show(1000);
            titleAlertShown=true;

        }else {
            $('#createSyllabus').hide();
            $('#createGrade').hide();
            $('#createSubject').hide();
            $("#booksaving").show();
            <g:remoteFunction controller="wonderpublish" action="getSyllabus"  onSuccess='initializeCreateSyllabus(data);'
        params="'level='+level"/>
        }
    }

    function initializeCreateSyllabus(data){

         syllabusList = data.results;
        var select = document.getElementById("createSyllabus");
        select.options.length = 1;
        for(var i=0;i< syllabusList.length; i++) {

            el = document.createElement("option");
            el.textContent = syllabusList[i].syllabus;
            el.value = syllabusList[i].syllabus;
            select.appendChild(el);
        }

        select.focus();
        $('#createSyllabus').show();
        $("#booksaving").hide();
    }

    function getCreateGrade(syllabus){
        $('#createGrade').hide();
        $('#createSubject').hide();
       var level = document.getElementById("createLevel");
        if("School"==level[level.selectedIndex].value&&syllabus!='NIOS'){
            var select = document.getElementById("createGrade");
            select.options.length = 1;
            for(var i=1;i< 13; i++) {

                el = document.createElement("option");
                el.textContent = i;
                el.value = i;
                select.appendChild(el);
            }

            select.focus();
            $('#createGrade').show();
        }else{
            var seperate=true;
            for(var i=0;i< syllabusList.length; i++) {
                if(syllabus==syllabusList[i].syllabus){
                    if(syllabusList[i].gradeType=="Semester"){
                        seperate=false;
                        var select = document.getElementById("createGrade");
                        select.options.length = 1;
                        for(var j=syllabusList[i].startSemester;j< (1+syllabusList[i].endSemester); j++) {

                            el = document.createElement("option");
                            el.textContent = 'Semester '+j;
                            el.value = 'Semester '+j;
                            select.appendChild(el);
                        }

                        select.focus();
                        $('#createGrade').show();

                    }
                    break;
                }
            }
            if(seperate){
                $("#booksaving").show(500);
                <g:remoteFunction controller="wonderpublish" action="getGrades"  onSuccess='initializeCreateGrades(data);'
        params="'syllabus='+syllabus"/>
            }
        }
    }

    function initializeCreateGrades(data){

        var  grades = data.results;
        var select = document.getElementById("createGrade");
        select.options.length = 1;
        for(var i=0;i< grades.length; i++) {

            el = document.createElement("option");

            el.textContent = grades[i].grade+(grades[i].state!=null?" ( "+grades[i].state+" )":"");
            el.value = grades[i].grade;
            select.appendChild(el);
        }

        select.focus();
        $('#createGrade').show();
        $("#booksaving").hide();
    }

    function getCreateSubject(value){
        $('#createSubject').hide();

        var level = document.getElementById("createLevel");
        var syllabusList = document.getElementById("createSyllabus");
        var syllabus=syllabusList[syllabusList.selectedIndex].value;
        if("School"==level[level.selectedIndex].value) syllabus="School";
        $("#booksaving").show(500);
        <g:remoteFunction controller="wonderpublish" action="getSubjects"  onSuccess='initializeCreateSubjects(data);'
        params="'syllabus='+syllabus"/>
    }
    function initializeCreateSubjects(data){

        var subjects = data.results;
        var select = document.getElementById("createSubject");
        select.options.length = 1;
        for(var i=0;i<  subjects.length; i++) {

            el = document.createElement("option");
            el.textContent =  subjects[i].subject;
            el.value =  subjects[i].subject;
            select.appendChild(el);
        }

        select.focus();
        $('#createSubject').show();
        $("#booksaving").hide();
    }

    function addTag(subject){
        var level = document.getElementById("createLevel");
        var syllabus = document.getElementById("createSyllabus");
        var grade = document.getElementById("createGrade");
        $("#booksaving").show(500);
        <g:remoteFunction controller="wonderpublish" action="addBookTags" onSuccess='updateBookTagTable(data);'
        params="'bookId='+bookId+'&level='+level[level.selectedIndex].value+'&syllabus='+syllabus[syllabus.selectedIndex].value+'&grade='+grade[grade.selectedIndex].value+'&subject='+subject"/>
        $('#createSyllabus').hide();
        $('#createGrade').hide();
        $('#createSubject').hide();

    }

    function updateBookTagTable(data){
        var tags =data.results;
        var str = "<tr>"+
                        "<td width='25%'>&nbsp;"+tags.level+"</td>"+
                        "<td width='25%'>&nbsp;"+tags.syllabus+"</td>"+
                        "<td width='25%'>&nbsp;"+tags.grade+"</td>"+
                        "<td width='25%'>&nbsp;"+tags.subject+"</td>";
        %{--<sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">--}%
                        str +="<td width='3%'>&nbsp;<a href='javascript:deleteBookTag("+tags.id+")'>Delete</a></td>";
        %{--</sec:ifAllGranted>--}%

                  str +=  "</tr>";
        document.getElementById("addedtags").innerHTML = document.getElementById("addedtags").innerHTML +str;
        $("#addedtags").show();
        $("#booksaving").hide();
        noOfTags +=1;
    }

    function updateChapterPdf(){
        document.resourceForm.bookId.value=bookId;
        document.resourceForm.chapterId.value=chapterId;
        $("#booksaving").show(500);
        document.resourceForm.submit();
    }

    function updatetoc(){
        document.resource2Form.bookId.value=bookId;
        document.resource2Form.chapterId.value=chapterId;
        $("#booksaving").show(500);
        document.resource2Form.submit();
    }

    function updateChapter(){
        document.resource1Form.bookId.value=bookId;
        document.resource1Form.chapterId.value=chapterId;
        $("#booksaving").show(500);
        document.resource1Form.submit();
    }

    function showNotesUpload(){
        $("#notesupload").show(500);
    }

    function showQuizUpload(resourceType){
        $("#quizupload").show(500);
        document.resource6Form.resourceType.value=resourceType;

    }

    function showQuizCopy(resourceType){
        $("#quizcopy").show(500);
        document.resource8Form.resourceType.value=resourceType;
    }

    function showReadCopy(resourceType){
        $("#readcopy").show(500);
        document.resource9Form.resourceType.value=resourceType;
    }
    var validFileSize = true;
    $('#file3').change(function(event) {
        var _size = this.files[0].size;
        if(_size >= 25000000){
            validFileSize = false;
        }else{
            validFileSize = true;
        }
    });
    function uploadNotes(){
        $("#notesUploadAlert").hide(500);
        $("#UploadSizeAlert").hide(500);
        if(document.resource3Form.resourceName.value==""){
            $("#notesUploadAlert").show(500);
        }else if(!validFileSize) $("#UploadSizeAlert").show(500);
        else {
            document.resource3Form.bookId.value = bookId;
            document.resource3Form.chapterId.value = chapterId;
            $("#booksaving").show(500);
            document.resource3Form.submit();
        }
    }
    function showImagesZipUpload(){
        $("#imagezipupload").show(500);
    }

    function uploadImageZip(){
        $("#zipUploadAlert").hide(500);

        if(document.getElementById("file5").value == "") {
            $("#zipUploadAlert").show(500);
        } else {
            document.resource7Form.bookId.value = bookId;
            document.resource7Form.chapterId.value = chapterId;
            $("#booksaving").show(500);
            document.resource7Form.submit();
        }
    }

    function showVideosUpload(){
        $("#paidvideoupload").show(500);
    }

    function uploadPaidVideo(){
        $("#paidVideoAlert").hide(500);

        if(document.getElementById("file10").value == "") {
            $("#paidVideoAlert").show(500);
        } else {
            document.resource10Form.bookId.value = bookId;
            document.resource10Form.chapterId.value = chapterId;
            $("#booksaving").show(500);
            document.resource10Form.submit();
        }
    }



    function uploadQuiz(){
        $("#quizUploadAlert").hide(500);
        if(document.getElementById("quizresourceName").value==""){
            $("#quizUploadAlert").show(500);
        } else {
            document.resource6Form.bookId.value = bookId;
            document.resource6Form.chapterId.value = chapterId;
            $("#booksaving").show(500);
            document.resource6Form.submit();
        }
    }

    function copyQuiz(){
        $("#quizCopyAlert").hide(500);

        if(document.getElementById("resId").value==""){
            $("#quizCopyAlert").show(500);
        } else {
            document.resource8Form.bookId.value = bookId;
            document.resource8Form.chapterId.value = chapterId;
            $("#booksaving").show(500);
            document.resource8Form.submit();
        }
    }

    function copyRead(){
        $("#readCopyAlert").hide(500);

        if(document.getElementById("readResId").value==""){
            $("#readCopyAlert").show(500);
        } else {
            document.resource9Form.bookId.value = bookId;
            document.resource9Form.chapterId.value = chapterId;
            $("#booksaving").show(500);
            document.resource9Form.submit();
        }
    }
    function showLinksUpload(){
        $("#linksupload").show(500);
    }

    function uploadLinks(){
        $("#linksUploadAlert").hide(500);

        if(document.getElementById("linksresourceName").value==""||document.getElementById("weblink").value==""){
            $("#linksUploadAlert").show(500);
        } else {
            document.resource4Form.bookId.value = bookId;
            document.resource4Form.chapterId.value = chapterId;
            $("#booksaving").show(500);
            document.resource4Form.submit();
        }
    }

    function showVideoLinksUpload(){
        $("#videolinksupload").show(500);
    }

    function uploadVideoLinks(){
          $("#videolinksUploadAlert").hide(500);
        if(document.getElementById("videolinksresourceName").value==""||document.getElementById("videolink").value==""||!validateYouTubeUrl(document.getElementById("videolink").value)){
            $("#videolinksUploadAlert").show(500);
        } else {
            document.resource5Form.bookId.value = bookId;
            document.resource5Form.chapterId.value = chapterId;
            var restitlename =document.getElementById("videolinksresourceName").value;
            document.resource5Form.videolinksresourceName.value = encodeURIComponent(restitlename)
            document.getElementById("videolink").value = getIdFromYouTubeURL(document.getElementById("videolink").value);
            $("#booksaving").show(500);
           document.resource5Form.submit();
        }
    }

    function validateYouTubeUrl(url) {
        if (url != undefined || url != '') {
            var regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|\&v=|\?v=)([^#\&\?]*).*/;
            var match = url.match(regExp);

            if (match && match[2].length == 11) {
                // Do anything for being valid
                // if need to change the url to embed url then use below line
                //$('#ytplayerSide').attr('src', 'https://www.youtube.com/embed/' + match[2] + '?autoplay=0');
                return true;
            } else {
                // Do anything for not being valid
                return false;
            }
        }

        return false;
    }

    function getIdFromYouTubeURL(url) {
        if(url.match(/(youtu.be)/)){
            var split_c = "/";
            var split_n = 3;
        }

        if(url.match(/(youtube.com)/)){
            var split_c = "v=";
            var split_n = 1;
        }

        var getYouTubeVideoID = url.split(split_c)[split_n];
        return getYouTubeVideoID.replace(/(&)+(.*)/, "");
    }

    function createMCQ(){
        window.location="/wonderpublish/quizcreator?page=notes&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Multiple Choice Questions&useType=quiz&mode=create";
    }

    function createFlashCards(){
        window.location="/wonderpublish/studySet?chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Flash cards&mode=create";
    }

    function createMCQBulk(){
        window.location="/wonderpublish/quizcreatorbulkinput?page=notes&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Multiple Choice Questions&useType=quiz&mode=create";
    }

    function createHTML(quizMode){
        window.location="/wonderpublish/notescreator?chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Notes&page=notes&mode=create&quizMode="+quizMode;
    }

    function createVideoHTML(quizMode){
        window.location="/wonderpublish/videoExplanation?chapterId="+chapterId+"&bookId="+bookId+"&resourceType=videoexplanation&useType=videoexplanation&mode=create";
    }

    function createQandA(resourceType){
        window.location="/wonderpublish/qandaCreator?chapterId="+chapterId+"&bookId="+bookId+"&resourceType="+resourceType+"&useType=QA&mode=create";
    }

    function editHTML(quizMode,resId){
        window.location="/wonderpublish/notescreator?page=notes&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Notes&useType=notes&mode=edit&id="+resId+"&quizMode="+quizMode;
    }

    function editMCQ(resId){
        window.location="/wonderpublish/quizcreator?page=notes&id="+resId+"&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=Multiple Choice Questions&useType=quiz&mode=edit";
    }

    function editVideoHTML(resId){
        window.location="/wonderpublish/videoExplanation?id="+resId+"&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=videoexplanation&useType=videoexplanation&mode=edit";
    }

    function editQA(resId){
        window.location="/wonderpublish/qandaCreator?id="+resId+"&chapterId="+chapterId+"&bookId="+bookId+"&resourceType=QA&useType=QA&mode=edit";
    }

    function editFlashCards(resId){
        window.location="/wonderpublish/studySet?chapterId="+chapterId+"&resId="+resId+"&bookId="+bookId+"&resourceType=Flash cards&mode=edit";
    }

    function delResource(resId){
        if(confirm("Are you sure to delete this resource?")) {
            <g:remoteFunction controller="Wonderpublish" action="deleteResource" params="'id='+resId" onSuccess = "alert('Deleted Successfully');window.location.reload();"/>
            $('.loading-icon').removeClass('hidden');
        }
    }

    var deleteResId;
    function delResourceVideo(resId,testStartDate){
        deleteResId = resId;
        if(confirm("Are you sure to delete this resource?")) {
           var sendNotification = false;
            if (testStartDate != '' && testStartDate != 'Invalid date'){
                if(confirm("Do you want to send class cancelled notification?")){
                    deleteClassNotification(resId);
                }
            }
            <g:remoteFunction controller="wonderpublish" action="deleteResource" params="'id='+resId" onSuccess = 'window.location.reload();'/>
        }
    }

    function deleteClassNotification(resId){
        <g:remoteFunction controller="log" action="deleteLiveVideoAutoNotifications" params="'mode=delete&resId='+resId" onSuccess = 'deleteNotificationSent(data)'/>
    }

    function deleteNotificationSent(data){
        <g:remoteFunction controller="wonderpublish" action="deleteResource" params="'id='+deleteResId" onSuccess = 'window.location.reload();'/>
    }




    function submitSearch(){
        if(document.getElementById("restitle").value==""||document.getElementById("resname").value==""||!validateYouTubeUrl(document.getElementById("resname").value)){
            alert("Enter valid Youtube link and the name.");
        }else {
            document.getElementById("resname").value = getIdFromYouTubeURL(document.getElementById("resname").value);
            var modelresName = $("#restitle").val();
            //modelresName=modelresName.replace(/~/g,"'").replace(/"/g,'~').replace(/&/g,'and').replace(/%/g,' ');
            modelresName=encodeURIComponent(modelresName)
            var modelresId = $("#resid").val();
            var modelresLink = $("#resname").val();
            var modalTestStartDate = $("#modalTestStartDate").val();
            var modalTestEndDate = $("#modalTestEndDate").val();
            var modalAllowComments=null;
            var modalDisplayComments=null;
            var downloadlink1 = $("#downloadlink1").val();
            var downloadlink2 = $("#downloadlink2").val();
            var downloadlink3 = $("#downloadlink3").val();
            downloadlink1=downloadlink1.replace(/&/g,"~");
            downloadlink2=downloadlink2.replace(/&/g,"~");
            downloadlink3=downloadlink3.replace(/&/g,"~");
            if(document.getElementById("modalAllowComments").checked) modalAllowComments="on";
            if(document.getElementById("modalDisplayComments").checked) modalDisplayComments="on";
            <g:remoteFunction controller="wonderpublish" action="updateResourceData"  params="'resId='+modelresId+'&reslink='+modelresLink+'&restitle='+modelresName+'&allowComments='+modalAllowComments+'&displayComments='+modalDisplayComments+
            '&testStartDate='+modalTestStartDate+'&testEndDate='+modalTestEndDate+'&downloadlink1='+downloadlink1+'&downloadlink2='+downloadlink2+'&downloadlink3='+downloadlink3"
       onSuccess = "alert('updated Successfully');window.location.reload();"/>

        }
    }

    function submitSearch1(){
            var modelresId = $("#weblinkresid").val();
            var weblinktitleedit = encodeURIComponent($("#weblinktitleedit").val());
            var weblinkurledit = $("#weblinkurledit").val();
            <g:remoteFunction controller="Wonderpublish" action="updateResourceData"  params="'resId='+modelresId+'&restitle='+weblinktitleedit+'&reslink='+weblinkurledit"
       onSuccess = "alert('updated Successfully');window.location.reload();"/>
    }

    function deleteChapter(chapterId){
        if(confirm("Are you sure to delete this chapter?")) {
            <g:remoteFunction controller="Wonderpublish" action="deleteChapter" params="'chapterId='+chapterId" onSuccess = "window.location.reload();"/>
        }
    }

    function deleteBookTag(id){
        if(published&&noOfTags==1){
            alert("Cannot delete the last tag of the published book");
        }else{
            if(confirm("Are you sure to delete this tag?")){
                <g:remoteFunction controller="wonderpublish" action="deleteBookTag" params="'id='+id" onSuccess = "window.location.reload();"/>
            }
        }
    }

    function addNewChapter(){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show(1000);
            titleAlertShown=true;
        } else {
            chapterId = -1;
            //hideDetails();
            document.getElementById('chaptername').value="";
            document.getElementById('chapterDesc').value="";
            $("#chapterdetails").show(500);
            $("#chapterdetailsdetails").hide();
        }
    }

    function chapterDtlUpdate(field){
        let fieldValue=encodeURIComponent(field.value);
        $("#booksaving").show(500);
            <g:remoteFunction controller="wonderpublish" action="chapterUpdate" onSuccess='updateChapterId(data);'
            params="'columnName='+field.name+'&columnValue='+fieldValue+'&bookId='+bookId+'&chapterId='+chapterId"></g:remoteFunction>

    }

    function previewChapterUpdate(field){
        $("#booksaving").show(500);
        <g:remoteFunction controller="wonderpublish" action="previewChapterUpdate" onSuccess='previewChapterUpdated(data);'
            params="'bookId='+bookId+'&chapterId='+field[field.selectedIndex].value"></g:remoteFunction>
    }

    function previewChapterUpdated(data){
        $("#booksaving").hide(500);
    }

    function checkPrice(field){
        if(priceErrorShown){
            priceErrorShown=false;
            $("#priceError").hide();
        }
        if(isNumeric(field.value)){
            bookDtlUpdate(field);
        }
        else{
            priceErrorShown=true;
            $("#priceError").show(500);
        }
    }

    function displayNewAuthorBox(){
        $("#newAuthorLabel").hide(500);
        $("#newauthor").show(500);
        $("#newauthor").focus();
    }

    function addNewAuthor(){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show(1000);
            $('.booktitlealert').show(1000);
            titleAlertShown=true;
        } else {
            $("#newauthor").hide(500);
            $("#newAuthorLabel").show(500);
            if(document.getElementById('newauthor').value==""){
            } else {
                $("#authorsaving").show();
                <g:remoteFunction controller="wonderpublish" action="addAuthor" onSuccess='authorAdded(data);'
            params="'bookId='+bookId+'&author='+document.getElementById('newauthor').value+'&publisherId='+document.getElementById('publisherId').value"></g:remoteFunction>

                document.getElementById('newauthor').value="";
            }

        }
    }

    function authorAdded(data){
        $("#authorsaving").hide();
        var select = document.getElementById("authors");
        var el = document.createElement("option");
        el.textContent = data.author;
        el.value = data.authorId;
        select.appendChild(el);
        updateAuthorSelect(data.authorId);
        $(select).selectpicker('refresh');
        authorSelected(select);
    }

    function authorSelected(field){
        var optionString="";
        var optionAuthors="";
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show(1000);
            titleAlertShown=true;

        }else {
            for (i = 0; i < field.options.length; i++) {
                if (field.options[i].selected) {
                    optionString = optionString + field.options[i].value+",";
                    optionAuthors = optionAuthors + field.options[i].text+",";
                }
            }

            if(optionString.length>0) {
                optionString = optionString.substr(0, (optionString.length - 1));
                optionAuthors = optionAuthors.substr(0, (optionAuthors.length - 1));
            }
            $("#booksaving").show(500);
            <g:remoteFunction controller="wonderpublish" action="updateAuthors" onSuccess='updateBookId(data);'
            params="'bookId='+bookId+'&authors='+optionString+'&optionAuthors='+optionAuthors"></g:remoteFunction>
        }
    }

    function displayNewPublisherBox(){
        $("#newPublisherLabel").hide(500);
        $("#newpublisher").show(500);
        $("#newpublisher").focus();
    }

    function addNewPublisher(){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show(1000);
            $('.booktitlealert').show(1000);
            titleAlertShown=true;

        }else {
            $("#newpublisher").hide(500);
            $("#newPublisherLabel").show(500);
            if(document.getElementById('newpublisher').value==""){

            }
            else{

                $("#publishersaving").show();
                <g:remoteFunction controller="wonderpublish" action="addPublisher" onSuccess='publisherAdded(data);'
            params="'publisher='+document.getElementById('newpublisher').value"></g:remoteFunction>

                document.getElementById('newpublisher').value="";
            }

        }
    }

    function publisherAdded(data){
        $("#publishersaving").hide();
        if(data.status=="Created") {
            var select = document.getElementById("publisherId");
            var el = document.createElement("option");
            el.textContent = data.publisher;
            el.value = data.publisherId;
            select.appendChild(el);
            updatePublisherSelect(data.publisherId);
        }
    }

    function updatePublisherSelect(publisherId){
        var field = document.getElementById("publisherId");
        for (i = 0; i < field.options.length; i++) {
            if(field.options[i].value==publisherId){

                field.options[i].selected=true;
            }
        }
    }

    function updatePackageBooks(){
        var field = document.getElementById("packageBookIds");
        bookDtlUpdate(field);
    }

    function updateIsbnKeyword(){
        var isbnkeywordsIds = document.getElementById("isbnkeywordsIds").value;
        var isbn = document.getElementById("isbn").value;
        <g:remoteFunction controller="institute" action="addKeywordtoIsbn" params="'keyword='+isbnkeywordsIds+'&isbn='+isbn" onSuccess='successupdateIsbnKeyword(data);'/>

    }

    function successupdateIsbnKeyword(data) {
        if(data.valid=="error"){
            alert("please enter ISBN to add the keywords");
        }else{
            alert("keywords updated successfully");
        }
    }

   function bookDtlUpdate(field){
        var isbnValue=document.getElementById('isbn').value;
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show(1000);
            titleAlertShown =true;
            document.getElementById("publisherId").selectedIndex=-1;

        }
        <% if("evidya".equals(session["entryController"])||"etexts".equals(session["entryController"])) { %>
        var fieldValue=field.value;
        if(field.name=="isbn"){
        if((isbnValue.length< 13) && (isbnValue!="")){
            isbnShown=true;
            alert("Please enter 13 digit ISBN number");
            document.getElementById('isbn').value="";

        }
        else if((isbnValue.length === 13) && (isbnValue!="")){
            var isbnNew=document.getElementById('isbn').value;
            document.getElementById('isbnerror').innerText = '';
            <g:remoteFunction controller="wonderpublish" action="validateIsbn" onSuccess='updateTest(data)'
                  params="'isbn='+isbnValue+'&bookId='+bookId"></g:remoteFunction>

            function updateTest(data) {
                if (data.valid == "error") {
                    isbnShown=true;
                    alert("ISBN already present");
                    document.getElementById('isbn').value="";
                }


            }

        }
        }
            <%}%>

    else {
            if(titleAlertShown){
                $('.booktitlealert').hide(1000);
                titleAlertShown=false;
            }
            <% if("evidya".equals(session["entryController"])|| "etexts".equals(session["entryController"])) { %>
            if(isbnShown){
                isbnShown=false;
                document.getElementById('isbnerror').innerText='';
            }
            <%}%>

            $("#booksaving").show(500);

            var fieldValue=encodeURIComponent(field.value);
            if(field.name=="tags") fieldValue = $('#'+field.name).val();
            if(field.name=="description") {
                document.updateDescriptionForm.description.value=CKEDITOR.instances.description.getData();
                document.updateDescriptionForm.bookId.value=bookId;
                document.updateDescriptionForm.submit();
            }else{

                <g:remoteFunction controller="wonderpublish" action="bookUpdate" onSuccess='updateBookId(data);'
                    params="'columnName='+field.name+'&bookId='+bookId+'&printBook=${params.printBooks}&columnValue='+fieldValue"></g:remoteFunction>

            }
        }

    }
    $('#language').on('change', function() {
        document.getElementById('isbnerror').innerText = '';
       $('#isbnerror').hide();
    });


    function bookPubDtUpdate(){
        if(document.getElementById('dtPublishedId').value=="") {
            $('#monthError').hide(1000);
            $('#yearError').show(1000);
            pubDtErrorShown =true;
            document.getElementById("dtPublishedId").selectedIndex=0;

        } else if(document.getElementById('dtPublished1Id').value=="") {
            $('#yearError').hide(1000);
            $('#monthError').show(1000);
            pubDtErrorShown =true;
            document.getElementById("dtPublished1Id").selectedIndex=0;

        } else {
            if(pubDtErrorShown){
                $('#yearError').hide(1000);
                $('#monthError').hide(1000);
                pubDtErrorShown=false;
            }

            $("#booksaving").show(500);
            <g:remoteFunction controller="wonderpublish" action="bookPubDtUpdate" onSuccess='updateBookPubDt(data)'
                    params="'pubYear='+document.getElementById('dtPublishedId').value+'&pubMonth='+document.getElementById('dtPublished1Id').value+'&bookId='+bookId"></g:remoteFunction>
        }
    }

    function updateBookPubDt(data) {
        $("#booksaving").hide(500);
    }

    function updateBookId(data) {
        bookId = data.bookId;
        $("#booksaving").hide(500);
        if("true"==data.newBook) displayDetails();
        displaychapterssection();

        if(data.authors!=null){
            var select = document.getElementById("authors");
            var length = select.options.length;

            for(i = select.options.length - 1 ; i >= 0 ; i--)
            {
                select.remove(i);
            }
            for(i=0;i<data.authors.length;i++){
               var el = document.createElement("option");
                el.textContent = data.authors[i].name;
                el.value = data.authors[i].id;
                select.appendChild(el);
            }
            $(select).selectpicker('refresh');

        }
    }
    function updateChapterId(data) {
        $("#booksaving").hide(500);
        $("#chapterdetailsdetails").show(500);
        chapterId = data.chapterId;
        if("true"==data.newChapter){
            noOfChapters++;
            var str = "<div class='row'>"+
                    "<div class='col-md-12'>"+
                        "<a href='javascript:getChapterDetails("+chapterId+")' class='greytext' id='chapter"+chapterId+"'> "+noOfChapters+". "+document.getElementById("chaptername").value+"</a>"+
                    "</div>"+
                    "</div><hr>";
            document.getElementById("chaptersList").innerHTML=document.getElementById("chaptersList").innerHTML+str;
            if(previousChapterId!=-1) $("#chapter"+previousChapterId).addClass('greytext');
            $("#chapter"+chapterId).removeClass('greytext');
            previousChapterId=chapterId;
            document.getElementById("addedContents").innerHTML="";
        }
    }

    function updateBookCover(imgType){
        if(document.getElementById('title').value=="") {
            $('.booktitlealert').show(1000);
            titleAlertShown=true;
        } else {
            if(titleAlertShown){
                $('.booktitlealert').hide(1000);
                titleAlertShown=false;
            }

            $("#booksaving").show(500);

            if(imgType=='cover') {
                document.uploadbookcover.bookId.value=bookId;
                document.uploadbookcover.submit();
            } else {
                document.uploadbookheader.bookId.value=bookId;
                document.uploadbookheader.submit();
            }
        }
    }

    <%if(selectedAuthors!=null){%>
    <g:each in="${selectedAuthors}" var="selectedAuthor">
     updateAuthorSelect('${selectedAuthor.authorId}');
    </g:each>
    <%}%>

    function updateAuthorSelect(authorId){
        var field = document.getElementById("authors");
        for (i = 0; i < field.options.length; i++) {
            if(field.options[i].value==authorId){

                field.options[i].selected=true;
            }
        }
    }

    function getChapterDetails(chapterIdFromList){
        var $iframe = $('#htmlreadingcontent');
        $iframe.contents().find('body').html("");
        chapterId = chapterIdFromList;

        if(previousChapterId!=-1) $("#chapter"+previousChapterId).addClass('greytext');
        $("#chapter"+chapterId).removeClass('greytext');
        previousChapterId=chapterId;

        <g:remoteFunction controller="wonderpublish" action="chapterMstDetails" onSuccess='updateChapterDesc(data);'
            params="'chapterId='+chapterIdFromList"></g:remoteFunction>
        getTopicDetails(chapterIdFromList,'bookauthor');
        hideDetails();
    }

    function getHtmlsData(resId){

        var $iframe = $('#htmlreadingcontent');
        $iframe.contents().find('body').html("");
        <g:remoteFunction controller="funlearn" action="getHtmlsForWeb"  onSuccess='displayHtmls(data);'
                    params="'resId='+resId" />
    }

    function displayHtmls(data){
        var htmls = data.htmlsContent;

        document.getElementById("htmlreadingcontent").height=screen.availHeight;
        var $iframe = $('#htmlreadingcontent');
        $iframe.contents().find('body').html("");
        $iframe.ready(function() {
            if(data.onlineHtml) {
                htmls = htmls.replace(/\\\\/g , '\\');

                $iframe.contents().find('body').html(htmls);

                var script = $iframe.contents()[0].createElement('script');
                script.type = 'text/javascript';
                script.text = 'renderMathInElement(document.body)';
                $iframe.contents().find('body')[0].appendChild(script);
            } else
                $iframe.contents().find('body').html(htmls.substring(0,htmls.indexOf("<head>")+6)+'<style type="text/css">'+data.cssString+'</style>'+htmls.substring(htmls.indexOf("<head>")+7));
        });
    }

    function updateChapterDesc(data){
        document.getElementById("chaptername").value=data.chapterName;
        document.getElementById("chapterDesc").value=data.chapterDesc;

        $("#chapterdetailsdetails").show(500);
    }
<% if(chapterId!=null) { %>
    getChapterDetails('${chapterId}');
<% } %>
   function displaychapterssection(){
       if(document.getElementById('title').value==""){
          // $("#showdetails").hide();
       } else {
          $("#chapterssection").show(500);
       }
   }

    function hideDetails(){
        // $("#bookdetails").hide(500);
        $("#showdetails").show();

    }

    function displayDetails(){
        $("#bookdetails").show(500);
        $("#showdetails").hide();
    }

    displaychapterssection();
    $('#nxt-btn').on('click', function() {
        $('a[href="#addChapters"]').tab('show');
    });

    $('#back-btns').on('click', function() {

        $('a[href="#createBook"]').tab('show');
    });
    $('#videoModal').on('hidden.bs.modal', function () {
        $(".modal-body iframe").attr('src', '');
    });
    $('.nav-tabs a[href="#createBook"]').tab('show');
    $("#videolink").blur(function() {
        var apiKey = "AIzaSyAT5Kuo5eyO_ChJ2ruyihKL0bNo7lqyI3s";
        var videoId=  getIdFromYouTubeURL(document.getElementById("videolink").value);
        var gUrl = "https://www.googleapis.com/youtube/v3/videos?id=" + videoId + "&key=" + apiKey + "&part=snippet";
        $.get(gUrl, function (data) {
             var youtubetitle =data.items[0].snippet.title;
            youtubetitle = youtubetitle.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, ' ').replace(/&/g,'and').replace(/%/g,' ');
             document.getElementById("videolinksresourceName").value=youtubetitle;

        });

    });

    $("#resname").blur(function() {

        var apiKey = "AIzaSyAT5Kuo5eyO_ChJ2ruyihKL0bNo7lqyI3s";
        var videoId=  getIdFromYouTubeURL(document.getElementById("resname").value);
        var gUrl = "https://www.googleapis.com/youtube/v3/videos?id=" + videoId + "&key=" + apiKey + "&part=snippet";
        $.get(gUrl, function (data) {
            var youtubetitle =data.items[0].snippet.title;
            youtubetitle = youtubetitle.replace(/([\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g, ' ').replace(/&/g,'and').replace(/%/g,' ');


            %{--string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')--}%
             document.getElementById("restitle").value=youtubetitle;

        });

    });


    function getDeepLink(chapterId,resId,resType){
        var firebaseKey="${siteMst!=null?siteMst.fbFirebaseWebAPI:""}";
        var parameters="bookId="+bookId;

        if(chapterId!=null) parameters +="&chapterId="+chapterId;
        if(resId!=null) parameters +="&resId="+resId;
        if(resType!=null) parameters +="&resType="+resType;
         var params = {
            "dynamicLinkInfo": {
                "domainUriPrefix": "${siteMst!=null?siteMst.domainUriPrefix:"none"}",
                "androidInfo": {
                    "androidPackageName": "${siteMst!=null?siteMst.androidPackageName:"none"}"
                },
                "iosInfo": {
                    "iosBundleId": "${siteMst!=null?siteMst.iosBundleId:"none"}"
                },
                "link": getServerPath()+"/wonderpublish/bookdtl?"+parameters,
            },

            "suffix": {
                "option": "SHORT"
            }
        };

        $('.loading-icon').removeClass('hidden');
        $.ajax({
            url: 'https://firebasedynamiclinks.googleapis.com/v1/shortLinks?key='+firebaseKey,
            type: 'POST',
            data: JSON.stringify(params) ,
            contentType: "application/json",
            success: function (response) {
                $('.loading-icon').addClass('hidden');
                alert(response.shortLink);
            },
            error: function () {
                $('.loading-icon').addClass('hidden');
                alert("error");
            }
        });

    }
    function getServerPath() {

        var localPath = window.location.href;
        var thirdIndex = localPath.indexOf('/', 8);
        var serverPath = localPath.substring(0, thirdIndex);
        serverPath = serverPath.replace("publish.","");
        return serverPath;
    }

    <%if("fail".equals(request.getParameter("quizCopyMode"))){%>
    alert("Quiz copy failed");
    <%}%>

</script>

</html>
