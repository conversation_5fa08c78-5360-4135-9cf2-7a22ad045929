<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${chapterName ?: 'Chapter'} - PDF</title>

    <!-- KaTeX for math rendering - Load synchronously for Puppeteer -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.css" integrity="sha384-nB0miv6/jRmo5UMMR1wu3Gz6NLsoTkbqJghGIsx//Rlm+ZU03BU6SQNC66uf4l5+" crossorigin="anonymous">
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/katex.min.js" integrity="sha384-7zkQWkzuo3B5mTepMUcHkMB5jZaolc2xDwL6VFqjFALcbeS9Ggm/Yr2r3Dy4lfFg" crossorigin="anonymous"></script>
    <script src="https://cdn.jsdelivr.net/npm/katex@0.16.11/dist/contrib/auto-render.min.js" integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk" crossorigin="anonymous"></script>

    <style>
        /* Print-optimized CSS */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Noto Sans", "Segoe UI", "Roboto", sans-serif;
            font-size: 1em;
            line-height: 1.6;
            color: #000;
            margin: 2cm 1.5cm;
            padding: 0;
            background: white;
        }

        /* Page break controls */
        .page-break-before {
            page-break-before: always;
        }

        .page-break-after {
            page-break-after: always;
        }

        .page-break-inside-avoid {
            page-break-inside: avoid;
        }

        /* Typography */
        .chapter-title {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        h1 {
            font-size: 2.5em;
            font-weight: bold;
            margin: 1em 0;
            text-align: center;
            color: #1a237e;
            border-bottom: 3px solid #3498db;
            padding-bottom: 0.5em;
        }

        .section-heading {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        h2 {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        h3 {
            font-size: 1.5em;
            font-weight: bold;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
        }

        p {
            margin: 0.8em 0;
            text-align: justify;
        }

        /* Question and Answer Styling */
        .question {
            font-weight: 600;
            margin-top: 1em;
            margin-bottom: 0.5em;
            color: #333;
        }

        .answer {
            background: #f9f9f9;
            border-left: 4px solid #ccc;
            padding: 1em;
            margin-left: 1em;
            margin-bottom: 1em;
        }

        /* Formula Styling */
        .formula {
            text-align: center;
            font-size: 1.2em;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        /* Chapter Overview - Kindle Compatible Table Structure */
        .chapter-overview {
            border: 2px solid #333;
            padding: 1.5em;
            margin: 2em 0;
            page-break-inside: avoid;
        }

        .overview-title {
            font-size: 1.3em;
            font-weight: bold;
            margin: 0 0 1em 0;
            color: #333;
            text-align: center;
            border-bottom: 1px solid #333;
            padding-bottom: 0.5em;
        }

        .overview-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 1em;
        }

        .overview-table th {
            border: 1px solid #333;
            padding: 0.8em;
            text-align: left;
            font-weight: bold;
            background: #f5f5f5;
        }

        .overview-table td {
            border: 1px solid #333;
            padding: 0.8em;
            text-align: left;
        }

        .overview-table .question-type-col {
            width: 70%;
            font-weight: 600;
        }

        .overview-table .count-col {
            width: 30%;
            text-align: center;
            font-weight: bold;
        }

        .overview-table tr:nth-child(even) {
            background: #f9f9f9;
        }

        /* Content sections */
        .content-section {
            margin: 3em 0;
            page-break-inside: avoid;
        }

        .section {
            page-break-before: always;
        }

        .section-header {
            margin-bottom: 2em;
            padding-bottom: 0.5em;
            border-bottom: 1px solid #ccc;
        }

        /* Question styles - printed book style */
        .question-item {
            margin: 2em 0;
            page-break-inside: avoid;
        }

        .question-number {
            font-weight: bold;
            color: #333;
            margin-right: 0.5em;
        }

        .question-text {
            font-weight: 600;
            margin-top: 1em;
            margin-bottom: 0.5em;
            color: #333;
            display: inline;
        }

        .answer-text {
            background: #f9f9f9;
            border-left: 4px solid #ccc;
            padding: 1em;
            margin-left: 1em;
            margin-top: 0.5em;
            line-height: 1.8;
            color: #333;
        }

        .answer-label {
            font-weight: bold;
            margin-right: 0.5em;
            color: #333;
        }

        /* Question type sections - printed book style */
        .question-type-section {
            margin: 3em 0;
            page-break-inside: avoid;
        }

        .question-type-header {
            font-weight: bold;
            font-size: 1.5em;
            margin-top: 2em;
            margin-bottom: 1em;
            color: #1a237e;
            border-bottom: 2px solid #3498db;
            padding-bottom: 0.3em;
            display: block;
        }

        .question-type-content {
            margin-left: 0;
        }

        /* MCQ specific styles - printed book style */
        .mcq-options {
            margin: 0.5em 0 0 2em;
        }

        .mcq-option {
            margin: 0.3em 0;
            display: flex;
            align-items: flex-start;
            gap: 0.5em;
            page-break-inside: avoid;
        }

        .mcq-option.correct {
            font-weight: bold;
            background: #f5f5f5;
            border: 1px solid #d0d0d0;
            border-radius: 0.2em;
            padding: 0.5em;
        }

        .option-label {
            font-weight: bold;
            min-width: 1.5em;
            flex-shrink: 0;
        }

        .option-text {
            flex: 1;
        }

        /* MCQ Answer explanation */
        .mcq-answer-explanation {
            margin: 1em 0 0 2em;
            padding: 1em;
            background: #f8f9fa;
            border-left: 3px solid #007bff;
            border-radius: 0.2em;
        }

        .mcq-answer-explanation .explanation-label {
            font-weight: bold;
            color: #007bff;
            margin-bottom: 0.5em;
            display: block;
        }

        /* Math formulas */
        .katex-display {
            text-align: center;
            font-size: 1.2em;
            margin: 1em 0;
            page-break-inside: avoid;
        }

        .katex {
            page-break-inside: avoid;
        }

        /* Print media queries */
        @media print {
            body {
                font-size: 0.9em;
                margin: 2cm 1.5cm;
                padding: 0;
            }

            .page-break-before {
                page-break-before: always;
            }

            .page-break-after {
                page-break-after: always;
            }

            .page-break-inside-avoid {
                page-break-inside: avoid;
            }
        }

        /* No data message */
        .no-data-message {
            text-align: center;
            padding: 3em 1em;
            color: #666;
            font-style: italic;
        }

        /* AI Footer */
        .ai-footer {
            margin-top: 4em;
            padding: 2em 0;
            text-align: center;
            border-top: 2px solid #3498db;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 8px;
            page-break-inside: avoid;
        }

        .ai-footer-content {
            font-size: 1.1em;
            color: #1a237e;
            font-weight: 600;
            margin-bottom: 0.5em;
        }

        .ai-footer-link {
            font-size: 1.2em;
            color: #3498db;
            text-decoration: none;
            font-weight: bold;
            border: 2px solid #3498db;
            padding: 0.5em 1.5em;
            border-radius: 25px;
            display: inline-block;
            background: white;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(52, 152, 219, 0.2);
        }

        .ai-footer-link:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }

        .ai-footer-icon {
            margin-right: 0.5em;
            font-size: 1.3em;
        }
    </style>
</head>
<body>
    <!-- Chapter Title -->
    <h1 class="chapter-title">${chapterName ?: 'Chapter'}</h1>

    <!-- Chapter Overview -->
    <div class="chapter-overview">
        <div class="overview-title">Chapter Overview</div>
        <table class="overview-table">
            <thead>
                <tr>
                    <th class="question-type-col">Question Type</th>
                    <th class="count-col">Count</th>
                </tr>
            </thead>
            <tbody>
                <!-- Exercise Solutions Count -->
                <g:if test="${exerciseSolutions?.size() > 0}">
                    <tr>
                        <td class="question-type-col">Exercise Solutions</td>
                        <td class="count-col">${exerciseSolutions.size()}</td>
                    </tr>
                </g:if>

                <!-- Question Type Counts -->
                <g:set var="enhancedQuestionTypeCounts" value="${questionTypeCounts ?: [:]}"/>
                <g:if test="${questionBank?.mcqQuestions?.size() > 0}">
                    <g:set var="enhancedQuestionTypeCounts" value="${enhancedQuestionTypeCounts + ['Multiple Choice Questions': questionBank.mcqQuestions.size()]}"/>
                </g:if>

                <g:each in="${['LongAnswer', 'ShortAnswer', 'VeryShortAnswer', 'AssertionReason', 'Problem', 'Multiple Choice Questions', 'FillBlank', 'TrueFalse', 'MatchFollowing', 'ArrangeSequence']}" var="qType">
                    <g:if test="${enhancedQuestionTypeCounts[qType] > 0}">
                        <tr>
                            <td class="question-type-col">
                                <g:if test="${qType == 'LongAnswer'}">Long Answer Type</g:if>
                                <g:elseif test="${qType == 'ShortAnswer'}">Short Answer Type</g:elseif>
                                <g:elseif test="${qType == 'VeryShortAnswer'}">Very Short Answer Type</g:elseif>
                                <g:elseif test="${qType == 'AssertionReason'}">Assertion / Reasoning Type</g:elseif>
                                <g:elseif test="${qType == 'Problem'}">Problem</g:elseif>
                                <g:elseif test="${qType == 'Multiple Choice Questions'}">Multiple Choice Questions</g:elseif>
                                <g:elseif test="${qType == 'FillBlank'}">Fill in the Blanks</g:elseif>
                                <g:elseif test="${qType == 'TrueFalse'}">True or False</g:elseif>
                                <g:elseif test="${qType == 'MatchFollowing'}">Match the Following</g:elseif>
                                <g:elseif test="${qType == 'ArrangeSequence'}">Arrange in Right Sequence</g:elseif>
                                <g:else>${qType}</g:else>
                            </td>
                            <td class="count-col">${enhancedQuestionTypeCounts[qType]}</td>
                        </tr>
                    </g:if>
                </g:each>
            </tbody>
        </table>
    </div>

    <!-- Exercise Solutions Section -->
    <g:if test="${exerciseSolutions?.size() > 0}">
        <div class="content-section page-break-before">
            <div class="section-header">
                <h3 class="section-heading">📚 Exercise Solutions</h3>
            </div>
            <div class="questions-container">
                <g:each in="${exerciseSolutions}" var="question" status="index">
                    <div class="question-item page-break-inside-avoid">
                        <div class="question">
                            <span class="question-number">${index + 1}.</span>
                            ${raw(question.question?.replaceAll('<br>', '<br/>'))}
                        </div>
                        <div class="answer">
                            <span class="answer-label">✅ Answer:</span>
                            ${raw(question.answer?.replaceAll('<br>', '<br/>'))}
                        </div>
                    </div>
                </g:each>
            </div>
        </div>
    </g:if>

    <!-- Question Bank Section -->
    <g:if test="${questionBank}">
        <div class="content-section page-break-before">
            <div class="section-header">
                <h3 class="section-heading">📝 Question Bank</h3>
            </div>

            <!-- QnA Questions by Type -->
            <g:if test="${questionBank.qnaQuestions}">
                <g:each in="${['Question and Answers', 'LongAnswer', 'ShortAnswer', 'VeryShortAnswer', 'AssertionReason', 'Problem', 'FillBlank', 'TrueFalse', 'MatchFollowing', 'ArrangeSequence']}" var="qType">
                    <g:if test="${questionBank.qnaQuestions[qType]?.size() > 0}">
                        <div class="question-type-section page-break-inside-avoid">
                            <div class="question-type-header">
                                <g:if test="${qType == 'Question and Answers'}">❓ Question and Answers</g:if>
                                <g:elseif test="${qType == 'LongAnswer'}">📝 Long Answer Type Questions</g:elseif>
                                <g:elseif test="${qType == 'ShortAnswer'}">✏️ Short Answer Type Questions</g:elseif>
                                <g:elseif test="${qType == 'VeryShortAnswer'}">📄 Very Short Answer Type Questions</g:elseif>
                                <g:elseif test="${qType == 'AssertionReason'}">⚖️ Assertion / Reasoning Type Questions</g:elseif>
                                <g:elseif test="${qType == 'Problem'}">🧮 Problem Questions</g:elseif>
                                <g:elseif test="${qType == 'FillBlank'}">📋 Fill in the Blanks Questions</g:elseif>
                                <g:elseif test="${qType == 'TrueFalse'}">❓ True or False Questions</g:elseif>
                                <g:elseif test="${qType == 'MatchFollowing'}">🔗 Match the Following Questions</g:elseif>
                                <g:elseif test="${qType == 'ArrangeSequence'}">🔢 Arrange in Right Sequence Questions</g:elseif>
                                <g:else>${qType} Questions</g:else>
                                (${questionBank.qnaQuestions[qType].size()} questions)
                            </div>
                            <div class="question-type-content">
                                <g:each in="${questionBank.qnaQuestions[qType]}" var="question" status="qIndex">
                                    <div class="question-item page-break-inside-avoid">
                                        <div class="question">
                                            <span class="question-number">${qIndex + 1}.</span>
                                            ${raw(question.question?.replaceAll('<br>', '<br/>'))}
                                        </div>
                                        <div class="answer">
                                            <span class="answer-label">✅ Answer:</span>
                                            ${raw(question.answer?.replaceAll('<br>', '<br/>'))}
                                        </div>
                                    </div>
                                </g:each>
                            </div>
                        </div>
                    </g:if>
                </g:each>
            </g:if>

            <!-- MCQ Questions -->
            <g:if test="${questionBank.mcqQuestions?.size() > 0}">
                <div class="question-type-section page-break-inside-avoid">
                    <div class="question-type-header">
                        ✅ Multiple Choice Questions (${questionBank.mcqQuestions.size()} questions)
                    </div>
                    <div class="question-type-content">
                        <g:each in="${questionBank.mcqQuestions}" var="question" status="mcqIndex">
                            <div class="question-item page-break-inside-avoid">
                                <div class="question">
                                    <span class="question-number">${mcqIndex + 1}.</span>
                                    ${raw(question.question?.replaceAll('<br>', '<br/>'))}
                                </div>
                                <div class="mcq-options">
                                    <g:if test="${question.option1}">
                                        <div class="mcq-option ${question.answer1 == 'Yes' ? 'correct' : ''}">
                                            <span class="option-label">A.</span>
                                            <span class="option-text">${raw(question.option1?.replaceAll('<br>', '<br/>'))}</span>
                                        </div>
                                    </g:if>
                                    <g:if test="${question.option2}">
                                        <div class="mcq-option ${question.answer2 == 'Yes' ? 'correct' : ''}">
                                            <span class="option-label">B.</span>
                                            <span class="option-text">${raw(question.option2?.replaceAll('<br>', '<br/>'))}</span>
                                        </div>
                                    </g:if>
                                    <g:if test="${question.option3}">
                                        <div class="mcq-option ${question.answer3 == 'Yes' ? 'correct' : ''}">
                                            <span class="option-label">C.</span>
                                            <span class="option-text">${raw(question.option3?.replaceAll('<br>', '<br/>'))}</span>
                                        </div>
                                    </g:if>
                                    <g:if test="${question.option4}">
                                        <div class="mcq-option ${question.answer4 == 'Yes' ? 'correct' : ''}">
                                            <span class="option-label">D.</span>
                                            <span class="option-text">${raw(question.option4?.replaceAll('<br>', '<br/>'))}</span>
                                        </div>
                                    </g:if>
                                    <g:if test="${question.option5}">
                                        <div class="mcq-option ${question.answer5 == 'Yes' ? 'correct' : ''}">
                                            <span class="option-label">E.</span>
                                            <span class="option-text">${raw(question.option5?.replaceAll('<br>', '<br/>'))}</span>
                                        </div>
                                    </g:if>
                                </div>

                                <!-- MCQ Answer Explanation -->
                                <g:if test="${question.gptExplanation || question.answerDescription}">
                                    <div class="mcq-answer-explanation">
                                        <span class="explanation-label">Explanation:</span>
                                        ${raw((question.gptExplanation ?: question.answerDescription)?.replaceAll('<br>', '<br/>'))}
                                    </div>
                                </g:if>
                            </div>
                        </g:each>
                    </div>
                </div>
            </g:if>
        </div>
    </g:if>

    <!-- No content message -->
    <g:if test="${(!exerciseSolutions || exerciseSolutions.size() == 0) && (!questionBank || (!questionBank.qnaQuestions && !questionBank.mcqQuestions))}">
        <div class="no-data-message">
            <p>No content available for this chapter.</p>
        </div>
    </g:if>

    <!-- AI Footer -->
    <div class="ai-footer">
        <div class="ai-footer-content">
            <span class="ai-footer-icon">🤖</span>
            AI version available at
        </div>
        <a href="https://www.gptsir.ai" class="ai-footer-link" target="_blank">
            <span class="ai-footer-icon">🌐</span>
            www.gptsir.ai
        </a>
    </div>

    <!-- JavaScript for math rendering -->
    <script>
        // Math formula rendering function
        function renderMathFormulas() {
            if (typeof renderMathInElement === "function") {
                renderMathInElement(document.body, {
                    delimiters: [
                        {left: "$$", right: "$$", display: true},
                        {left: "$", right: "$", display: false},
                        {left: "\\(", right: "\\)", display: false},
                        {left: "\\[", right: "\\]", display: true}
                    ]
                });

                // Wrap display math in formula class
                document.querySelectorAll('.katex-display').forEach(function(element) {
                    if (!element.parentElement.classList.contains('formula')) {
                        var wrapper = document.createElement('div');
                        wrapper.className = 'formula';
                        element.parentNode.insertBefore(wrapper, element);
                        wrapper.appendChild(element);
                    }
                });

                // Signal that math rendering is complete for Puppeteer
                window.mathRenderingDone = true;
            }
        }

        // Render math on page load
        document.addEventListener("DOMContentLoaded", function() {
            renderMathFormulas();
        });
    </script>
</body>
</html>
