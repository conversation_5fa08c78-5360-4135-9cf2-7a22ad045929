<%
	String requestURL = request.getRequestURL().toString();
	String servletPath = request.getServletPath();
%>

<div class="modal fade" id="signin-modal" data-backdrop="static" tabindex="-1" role="dialog" aria-labelledby="signIn">
  <div class="modal-dialog sign-in-modal-dialog" role="document" id="sign-in">
    <div class="modal-content sign-in-modal-content">
      <button type="button" class="close-signin-signup" data-dismiss="modal" aria-label="Close">
        Close
      </button>
      <div id="social-login" style="display: none;">
        <div class="modal-header sign-in-modal-header">
          Welcome back!
        </div>
        <div class="modal-body sign-in-modal-body">
          <p class="signin-modal-credentials">Sign in to access next-gen smart eBooks which contains reading material, practices, videos, weblinks, and much more.</p>
          <div class="social-login-btns">

              <button class="social-login social-google waves-effect" id="googleSignInButton" onclick="googleSignInCalled();">Sign in with Google</button>

<!--
            <oauth2:connect provider="facebook" id="facebook-connect-link">
              <button class="social-login social-facebook waves-effect">Sign in with Facebook</button>
            </oauth2:connect>
-->
            <button class="social-login social-email waves-effect" onclick="javascript:hideSocialSignIn();">Sign in with Email</button>
          </div>

          <div class="term-condition">
            <p class="terms-text">By clicking Sign in, I agree to Wonderslates’s
              <a href="/smartebook/termsandconditions">Terms and Conditions</a>
            </p>
          </div>
          <hr />
          <div class="create-account">
            <p class="create-account-btn">New to Wonderslate? <a href="javascript:showSignup();" id="create-account">Create Account</a></p>
          </div>
        </div>
      </div>

      <div id="social-signup" style="display: none;">
        <div class="modal-header sign-in-modal-header">
          Join Us
        </div>
        <div class="modal-body sign-in-modal-body">
          <p class="signin-modal-credentials">Home of next-gen smart eBooks which contains reading material, practices, videos, weblinks, and much more</p>
          <div class="social-login-btns">

              <button class="social-login social-google waves-effect" id="googleSignInButton1" onclick="googleSignInCalled();">Sign up with Google</button>

<!--
            <oauth2:connect provider="facebook" id="facebook-connect-link">
              <button class="social-login social-facebook waves-effect">Sign up with Facebook</button>
            </oauth2:connect>
-->
            <button class="social-login social-email waves-effect" onclick="javascript:hideSocialSignUp();">Sign up with Email</button>
          </div>

          <div class="term-condition">
            <p class="terms-text">By clicking Sign up, I agree to Wonderslates’s
              <a href="/smartebook/termsandconditions">Terms and Conditions</a>
            </p>
          </div>
          <hr />
          <div class="create-account">
            <p class="create-account-btn">Already have an account? <a href="javascript:showSignIn();">Sign in</a></p>
          </div>
        </div>
      </div>

      <div id="sign-in-div" style="display: none;">
        <div class="modal-header sign-in-modal-header">
          Sign in with email
        </div>
        <div class="modal-body sign-in-modal-body">
          <p class="signin-modal-credentials">Enter the credentials assosicated with your Wonderslate account and enter the world of next-gen smart eBooks.</p>
          <form id="signin-form" class="form-horizontal" method="post" action="/login/authenticate">
            <div class="sign-in-inputs">
              <input type="text" name="username" id="email" class="form-control login-signup-input email-input" placeholder="Your Email Address" required>
              <input type="password" name="password" id="password" class="form-control login-signup-input" placeholder="Enter Password" required>
            </div>
              <div id="loginFailed" style="display:none; color: #F05A2A; margin-top: 15px;">Login failed. Please try again!</div>
            <div class="forgot-password">
              <a href="javascript:showFGpassword();" class="forgot-pwd-btn">Forgot Password?</a>
            </div>
            <div class="submit-btn">
              <input type="submit" id="sign-in" onclick="javascript:userSignIn();" class="btn btn-block login-signup-btn waves-effect waves-ripple" value="Sign in">
            </div>
          </form>
          <div class="email-error" id="signInError" style="display: none;">
            <p class="email-error-text">All fields are mandatory.</p>
          </div>
          <div class="term-condition">
            <p class="terms-text">By clicking Sign in, I agree to Wonderslates’s
              <a href="/smartebook/termsandconditions">Terms and Conditions</a>
            </p>
          </div>
          <hr />
          <div class="create-account">
            <p class="create-account-btn"><a href="javascript:showSignIn();"><i class="icon-back" aria-hidden="true" style="vertical-align: middle; display: inline-block; margin-top: 2px;"></i> Back to sign in options</a></p>
          </div>
        </div>
      </div>

      <div id="sign-up-div" style="display: none;">
        <div class="modal-header sign-in-modal-header">
          Sign up with email
        </div>
        <div class="modal-body sign-in-modal-body">
          <p class="signin-modal-credentials">Home of next-gen smart eBooks.</p>
          <g:form class="form-horizontal" name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
            <div class="sign-in-inputs">
              <input type="text" class="form-control login-signup-input" id="name" name="name" placeholder="Your Name" required>
              <input type="email" id="username" class="form-control login-signup-input email-input" name="username" placeholder="Your Email Address" required>
              <input type="password" id="signup-password" class="form-control login-signup-input" name="password" placeholder="Choose Password" required>
              <input type="text" class="form-control login-signup-input mobile-input" name="mobile" id="mobile" placeholder="Your Mobile No" required>
              %{-- <span class="input-error-tooltip" style="display: none;">Please enter 10 digit mobile number.</span> --}%
              <input type="hidden" name="email">
              <div class="input-error-tooltip" style="display: none;">
                <div class="input-error-tooltip-inner">Please enter 10 digit mobile number.</div>
              </div>
            </div>
            <div class="submit-btn">
              <input type="button" id="signup" onclick="javascript:formSubmit();" class="btn btn-block login-signup-btn waves-effect waves-ripple" value="Sign up">
            </div>
            <div class="email-error" id="emailexists" style="display: none;">
              <p class="email-error-text">This email is already taken. Please sign in.</p>
            </div>
          </g:form>
          <div class="term-condition">
            <p class="terms-text">By clicking Sign up, I agree to Wonderslates’s
              <a href="/smartebook/termsandconditions">Terms and Conditions</a>
            </p>
          </div>
          <hr />
          <div class="create-account">
            <p class="create-account-btn"><a href="javascript:showSignup();"><i class="icon-back" aria-hidden="true" style="vertical-align: middle; display: inline-block; margin-top: 2px;"></i> Back to sign up options</a></p>
          </div>
        </div>
      </div>

      <div id="forgot-password-div" style="display: none;">
        <div class="modal-header sign-in-modal-header">
          Forgot your password?
        </div>
        <div class="modal-body sign-in-modal-body">
          <p class="signin-modal-credentials">Fill in your email address that you used to register, then click the submit button.</p>
          <br>
          <p class="signin-modal-credentials">We’ll email you a link to your reset password page, to reset your password.</p><br>
          <p class="signin-modal-credentials">You may need to check your spam folder or add “<EMAIL>” to your safe senders list.</p>
          <br>
          <g:form name="forgotpassword" class="form-horizontal" method="post" autocomplete="off">
            <div class="sign-in-inputs">
              <input type="text" name="username" id="fPemail" class="form-control login-signup-input email-input" placeholder="Your Email Address" required>
            </div>
            <div class="submit-btn">
              <input type="button" id="fPbtn" onclick="javascript:formFPSubmit();" class="btn btn-block login-signup-btn waves-effect waves-ripple" value="Send reset link">
            </div>
          </g:form>
          <hr />
          <div class="create-account">
            <p class="login-back-btn"><i class="icon-back" aria-hidden="true" style="vertical-align: middle; display: inline-block; margin-top: 2px;"></i> <a href="javascript:bckLogin();">Back to sign in</a></p>
          </div>
        </div>
      </div>

      <div id="connecting-div" class="connecting-user" style="display: none;">
        <p class="connecting-text">Connecting...</p>
      </div>

      <div id="google-password-div" style="display: none;">
        <div class="modal-header sign-in-modal-header">
          Password reset not possible!
        </div>
        <div class="modal-body sign-in-modal-body">
          <p class="signin-modal-credentials">We see that you have registered using Google, so it will not possible to change the password for your account (<span id="fp-user-email1"></span>) with us. <br><br>Kindly continue to login using Google</p>
          <br>
          <hr />
          <div class="create-account">
            <p class="login-back-btn"><i class="icon-back" aria-hidden="true" style="vertical-align: middle; display: inline-block; margin-top: 2px;"></i> <a href="javascript:bckLogin();">Back to sign in</a></p>
          </div>
        </div>
      </div>

      <div id="reset-password-div" style="display: none;">
          <div class="modal-header sign-in-modal-header">
              Password reset link sent.
          </div>
          <div class="modal-body sign-in-modal-body">
              <p class="signin-modal-credentials">We’ve sent instructions on how to reset your password to <span id="fp-user-email"></span>. If you haven’t received an email from Wonderslate within a couple of minutes, please check your spam folder.</p>
              <br>
              <hr />
              <div class="create-account">
                  <p class="login-back-btn"><i class="icon-back" aria-hidden="true" style="vertical-align: middle; display: inline-block; margin-top: 2px;"></i> <a href="javascript:bckLogin();">Back to sign in</a></p>
              </div>
          </div>
      </div>

    </div>
  </div>
</div>

<script>
	var flds = new Array (
		'name',
		'username',
		'signup-password',
		'mobile'
	);

	function userSignIn() {
		if($('#email').val()=="" || $('#password').val()=="") {
			$('#signInError').show();
		} else {
			$('#sign-in-div').hide();
			$('#connecting-div').show();
		}
	}

	function formSubmit() {
		if (validate()) {
			document.adduser.username.value = document.adduser.username.value.toLowerCase();
			document.adduser.email.value = document.adduser.username.value.toLowerCase();
			checkUsernameExists();
		}
	}

	function validate(){
		var allFilled=true
		document.getElementById('emailexists').style.display = 'none';

		for (i=0; i<flds.length; i++) {
			if( !$("#"+flds[i]).val() ) {
				//actual code to check all fields needs to be entered. use the array of fields
				$("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
				$("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
				allFilled = false;
			} else {
				$("#"+flds[i]).css('border', 'none');
				$("#"+flds[i]).css('border', 'none');
			}
		}

		if(!allFilled) {
			$('.alert').show();
		} else {
			var email = $("#username").val();
			var atpos = email.indexOf("@");
			var dotpos = email.lastIndexOf(".");

			if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
				document.getElementById('username').style.border = 'rgba(240, 90, 40, 0.5)';
				return false;
			}
		}

		return allFilled;
    }

	function checkUsernameExists() {
		var username = $("#username").val();
		<g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
	}

	function userNameExistsResult(data) {
		if(data=="0") {
			$('#connecting-div').show();
			document.adduser.submit();
			$('#sign-up-div').hide();
		} else {
			document.getElementById('emailexists').style.display = 'block';
		}
	}

	function formFPSubmit() {
		$("#emailidnf").hide();

		if( !$("#fPemail").val() ) {
			//actual code to check all fields needs to be entered. use the array of fields
			$("#email").addClass('has-error');
			$("#email").closest('.input-group').addClass('has-error');
		} else {
			var email = $("#fPemail").val();
			var atpos = email.indexOf("@");
			var dotpos = email.lastIndexOf(".");

			if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
				return false;
			} else {
				$("#loader").show();
				<g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
			}
		}
    }

	function displayFPResults(data) {
		var userEmail = $('#fPemail').val();

		if("OK"==data.status) {
			$('#forgot-password-div').hide();
            $('#google-password-div').hide();
			$('#reset-password-div').show();
			$('#fp-user-email').html("“"+userEmail+"”");
		} else if("Google"==data.status) {
            $('#forgot-password-div').hide();
            $('#reset-password-div').hide();
            $('#google-password-div').show();
            $('#fp-user-email1').html("“"+userEmail+"”");
        }
	}
</script>