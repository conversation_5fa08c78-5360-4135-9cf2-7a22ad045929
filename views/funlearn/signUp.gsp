<%
  String requestURL = request.getRequestURL().toString();
  String servletPath = request.getServletPath();
  String appURL = requestURL.substring(0, requestURL.indexOf(servletPath));
  session.setAttribute("servername", appURL);
%>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader"></g:render>
<div class="login-signup-container">
  <div class="login-signup-form">
    <p class="login-signup-headline">Sign Up</p>
    <p class="easy-login">Easily Using</p>
    <div class="social-btns">
      <oauth2:connect provider="google" id="google-connect-link">
        <button class="google btns">Google</button>
      </oauth2:connect>
      <oauth2:connect provider="facebook" id="facebook-connect-link">
        <button class="facebook btns">Facebook</button>
      </oauth2:connect>
    </div>
    <p class="using-email">OR USING EMAIL</p>
    <div class="form-container">
      <g:form class="form-horizontal" name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
        <div class="form-inputs">
          <input type="text" class="form-control login-signup-input" id="name" name="name" placeholder="Your Name" required>
          <input type="email" id="username" class="form-control login-signup-input" name="username" placeholder="Your Email Address" required>
          <input type="text" class="form-control login-signup-input" name="mobile" id="mobile" placeholder="Your Mobile No" required>
          <input type="password" id="password" class="form-control login-signup-input" name="password" placeholder="Choose Password" required>
          <a href="#" class="show-password" id="show-password">Show passwors</a>
          <input type="hidden" name="email">
        </div>

        <div class="red" style="display: none" id="emailexists">** This email is already registered.</div>

        <div class="submit-btn">
          <button type="button" id="signup" onclick="javascript:formSubmit();" class="btn btn-block login-signup-btn">SIGN UP</button>
        </div>
      </g:form>
      <div class="row">
        <div class="already-user col-md-12">
          <p class="have-an-account pull-right">Already have an account? <a href="/funlearn/signIn">Login</a></p>
        </div>
      </div>
    </div>
  </div>
</div>
<g:render template="/${session['entryController']}/footer"></g:render>

<script>
  var flds = new Array (
    'name',
    'username',
    'password',
    'mobile'
  );

  function formSubmit() {
    if (validate()) {
      document.adduser.username.value = document.adduser.username.value.toLowerCase();
      document.adduser.email.value = document.adduser.username.value.toLowerCase();
      checkUsernameExists();
    }
  }

  function validate(){
    var allFilled=true
    document.getElementById('emailexists').style.display = 'none';
    for (i=0; i<flds.length; i++) {
      if( !$("#"+flds[i]).val() ) {
          //actual code to check all fields needs to be entered. use the array of fields
          $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
          $("#"+flds[i]).css('border', '1px solid rgba(240, 90, 40, 0.5)');
          allFilled = false;
      } else {
          $("#"+flds[i]).css('border', 'none');
          $("#"+flds[i]).css('border', 'none');
        }
    }
    if(!allFilled) {
      $('.alert').show();
    } else {
      var email = $("#username").val();
      var atpos = email.indexOf("@");
      var dotpos = email.lastIndexOf(".");
        if (atpos<1 || dotpos<atpos+2 || dotpos+2>=email.length) {
            document.getElementById('username').style.border = 'rgba(240, 90, 40, 0.5)';
            return false;
          }
        }
      return allFilled;
    }

  function checkUsernameExists() {
    var username = $("#username").val();
    <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);'
              params="'username='+username" />
  }
  function userNameExistsResult(exists) {
    "0"==exists?document.adduser.submit():(document.getElementById('emailexists').style.display = 'block');
  }
  showHidePassword();
  $('#name').focus();
</script>