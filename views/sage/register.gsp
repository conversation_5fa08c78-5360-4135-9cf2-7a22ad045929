<%
  String requestURL = request.getRequestURL().toString();
  String servletPath = request.getServletPath();
%>

<g:render template="/sage/navheader"></g:render>
<div class="container-fluid top-menus-wrapper">
  <div class="row">
    <div class="col-md-12 col-xs-12 top-menus">
        <%if(request.getParameter("mode")=="student"){%>
          <p class="resource-label" style="padding: 0; position: absolute; bottom: 0; left: 21px;">Student Resources</p>
        <%} else {%>
          <p class="resource-label" style="padding: 0; position: absolute; bottom: 0; left: 21px;">Instructor Resources</p>
        <%}%>
      <div class="col-md-5 col-md-offset-7 col-xs-12 top-menus-item">
        <a href="/sage/index" class="menu-btn home-btn">Home</a>
        <a href="javascript:ShowSageLogin();" class="menu-btn login-btn">Login</a>
      </div>
    </div>
  </div>
</div>
<div class="container-fluid sage-content-container">
  
    <div class="row">
      <div class="col-md-12 col-sm-12 sage-registration">
        <p class="sage-text-logo pull-right">
          <img src="${assetPath(src: 'sage/logo.png')}" class="img-responsive sage-text-logo-img" alt="">
        </p>
        <div class="col-md-12 col-sm-12" style="padding: 0;">
          <img src="${assetPath(src: 'sage/full-books.png')}" class="img-responsive full-books" alt="">
        </div>
      </div>
    </div>






    <div class="row" id="user-register" style="margin-top: 16px;">
      <%if(request.getParameter("mode")=="student"){%>
      <div class="col-md-5 col-sm-12 col-xs-12 student-reg" style="padding-left: 0;">
        <img src="${assetPath(src: 'sage/colm-title-student.png')}" class="img-responsive" alt="">
        <div class="student-sec" style="padding: 8px;">
          <p class="instructor-text">Are you looking for the much-needed miracle?</p>
          <p class="instructor-bottom">Let us simplify your every learning challange!</p>
          <p class="instructor-bottom" style="margin-bottom: 0;">Access these impressive arrays of free tools and resources by registering in just 20 seconds!</p>
          <ul class="colm-list">
            <div class="col-md-7">
              <li><span>•</span> Synoptic Study-notes</li>
              <li><span>•</span> Short answer questions</li>
              <li><span>•</span> Descriptive answer questions</li>
            </div>
            <div class="col-md-5">
              <li><span>•</span> Objective type questions</li>
              <li><span>•</span> Case studies and data sets</li>
              <li><span>•</span> Case studies and data sets</li>
            </div>
          </ul>
        </div>
      </div>
      <%} else {%>
          <div class="col-md-5 col-sm-12 col-xs-12" style="padding-left: 0;">
            <img src="${assetPath(src: 'sage/colm-title-instructor.png')}" class="img-responsive" alt="">
            <div class="instructor-sec" style="border: 0;">
              <p class="instructor-text inst-login" style="font-size: 14px;">Elevate your engagement with your students with Digital Resources for Instructors & Students — invaluable pedagogical tools to help you enhance your teaching experience!</p>
              <p class="instructor-bottom">Access these impressive arrays of free tools and resources by registering in just 20 seconds!</p>
              <ul class="colm-list instructor-lis">
                <div class="col-md-7">
                  <li><span>•</span> Synoptic Study-notes</li>
                  <li><span>•</span> Chapter-based PowerPoint slides</li>
                  <li><span>•</span> Instructors manual</li>
                </div>
                <div class="col-md-5">
                  <li><span>•</span> Teaching notes</li>
                  <li><span>•</span> Ideas on essay topics</li>
                  <li><span>•</span> Off-the- rack test papers</li>
                </div>
              </ul>
            </div>
          </div>
      <%}%>

      <div class="col-md-4 col-sm-12 col-xs-12 col-md-offset-3" style="padding-left: 0;">
        <img src="${assetPath(src: 'sage/reg-user.png')}" alt="">
        <form id="signin-form" method="post" action="/login/authenticate" class="sage-form form-horizontal register-form">
          <div class="form-input-wrapper form-group">
            <label>USERNAME</label>
            <input type="email" name="username" id="email" value="" placeholder="">
          </div>

          <div class="form-input-wrapper form-group">
            <label>PASSWORD</label>
            <input type="password" name="password" id="password" placeholder="">
          </div>

          <div class="form-input-wrapper form-group">
            <a href="javascript:userSignIn();" id="sign-in" class="pull-right sage-login">Login</a>
          </div>
          <div class="form-input-wrapper form-group">
            <a href="javascript:showSageRegistration();" class="pull-right not-user">Not a User? Create a new account</a>
          </div>

          <div class="form-input-wrapper form-group">
            <a href="#" class="pull-right not-user">Recover my password</a>
          </div>
          <div class="col-md-12 col-sm-12 col-xs-12" id="login-error" style="margin-top: 16px; display: none;">
            <p class="pull-right" style="color: red;">*All fields are mandatory*</p>
          </div>
        </form>
      </div>
    </div>
















    <div class="row" id="user-login" style="margin-top: 16px; display: none;">
      <%if(request.getParameter("mode")=="student"){%>
      <div class="col-md-4 col-sm-12 col-xs-12 student-reg" style="padding-left: 0;">
        <img src="${assetPath(src: 'sage/colm-title-student.png')}" class="img-responsive" alt="">
        <div class="student-sec student-sec-login" style="padding: 24px 16px 48px;">
          <p class="instructor-text">Are you looking for the much-needed miracle?</p>
          <p class="instructor-bottom">Let us simplify your every learning challange!</p>
          <p class="instructor-bottom" style="margin-bottom: 0;">Access these impressive arrays of free tools and resources by registering in just 20 seconds!</p>
          <ul class="colm-list">
            <div class="col-md-6">
              <li><span>•</span> Synoptic Study-notes</li>
              <li><span>•</span> Short answer questions</li>
              <li><span>•</span> Descriptive answer questions</li>
            </div>
            <div class="col-md-6">
              <li><span>•</span> Objective type questions</li>
              <li><span>•</span> Case studies and data sets</li>
              <li><span>•</span> Case studies and data sets</li>
            </div>
          </ul>
        </div>
      </div>
      <%} else {%>
          <div class="col-md-4 col-sm-12 col-xs-12" style="padding-left: 0;">
            <img src="${assetPath(src: 'sage/colm-title-instructor.png')}" class="img-responsive" alt="">
            <div class="instructor-sec instructor-sec-login">
              <p class="instructor-text" style="font-size: 14px;">Elevate your engagement with your students with Digital Resources for Instructors & Students — invaluable pedagogical tools to help you enhance your teaching experience!</p>
              <p class="instructor-bottom" style="margin-bottom: 0;">Access these impressive arrays of free tools and resources by registering in just 20 seconds!</p>
              <ul class="colm-list">
                <div class="col-md-6">
                  <li><span>•</span> Synoptic Study-notes</li>
                  <li><span>•</span> Chapter-based PowerPoint slides</li>
                  <li><span>•</span> Instructors manual</li>
                </div>
                <div class="col-md-6">
                  <li><span>•</span> Teaching notes</li>
                  <li><span>•</span> Ideas on essay topics</li>
                  <li><span>•</span> Off-the- rack test papers</li>
                </div>
              </ul>
            </div>
          </div>
      <%}%>
    <g:form id="register-user-form" class="form-horizontal" name="adduser" url="[action:'addUser',controller:'creation']" method="post" autocomplete="off">
      <div class="col-md-4 col-sm-12 col-xs-12 registeration-user-form" style="padding-left: 48px; padding-right: 0;">
        <img src="${assetPath(src: 'sage/acc-info.png')}" alt="" style="width: 85%;">
        <div  class="sage-form form-horizontal">
          <div class="form-input-wrapper form-group">
            <label>USERNAME</label>
            <input type="email" id="username" class="register-user-input" name="username" value="" placeholder="" required>
            <div class="mandatory">*</div>
          </div>

          <div class="form-input-wrapper form-group">
            <label>PASSWORD</label>
            <input type="password" id="user-password" class="register-user-input" name="password" value="" placeholder="" required>
            <div class="mandatory">*</div>
          </div>

          <div class="form-input-wrapper form-group">
            <label>First Name</label>
            <input type="text" id="first-name" class="register-user-input" name="name" value="" placeholder="" required>
            <div class="mandatory">*</div>
          </div>

          <div class="form-input-wrapper form-group">
            <label>Last Name</label>
            <input type="text" id="last-name" class="register-user-input" name="lastName" value="" placeholder="" required>
            <div class="mandatory">*</div>
          </div>

          <div class="form-input-wrapper form-group">
            <label>Role</label>
            <select>
              <option>Please select you role</option>
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </select>
            <div class="mandatory">*</div>
          </div>

          <div class="form-input-wrapper form-group">
            <label>Phone/Mobile</label>
            <input type="text" id="user-mobile" class="register-user-input" name="mobile" value="" placeholder="" required>
            <div class="mandatory">*</div>
          </div>

          <div class="form-input-wrapper form-group">
            <label>Area of interests</label>
            <select>
              <option>Your area of interests</option>
              <option>Option 1</option>
              <option>Option 2</option>
              <option>Option 3</option>
            </select>
            <div class="mandatory">*</div>
          </div>
        </div>
      </div>

      <div class="col-md-4 col-sm-12 col-xs-12" style="padding-right: 0;">
        <img src="${assetPath(src: 'sage/ins-info.png')}" class="img-responsive ins-info" alt="">
        <div class="sage-form form-horizontal" style="padding-bottom: 15px;">
          <div class="form-input-wrapper form-group">
            <label>Country</label>
            <input type="text" name="country" value="" placeholder="">
          </div>

          <div class="form-input-wrapper form-group">
            <label>Instituition</label>
            <input type="text" name="institute" value="" placeholder="">
          </div>

          <div class="form-input-wrapper form-group">
            <label>Department</label>
            <input type="text" name="department" value="" placeholder="">
          </div>

          <div class="form-input-wrapper form-group">
            <label>Address 1</label>
            <input type="text" name="address1" value="" placeholder="">
          </div>

          <div class="form-input-wrapper form-group">
            <label>Address 2</label>
            <input type="text" name="address2" value="" placeholder="">
          </div>

          <div class="form-input-wrapper form-group city-postal">
            <label>City</label>
            <input type="text" name="city" value="" placeholder="">

            <label style="width: 17%;">Postal Code</label>
            <input type="text" name="postal" value="" placeholder="">
          </div>

          <div class="form-input-wrapper form-group">
            <label>State/Province</label>
            <input type="text" name="state" value="" placeholder="">
          </div>
        </div>
        </g:form>
      </div>
      <div class="col-md-12 col-sm-12 col-xs-12" style="margin-top: 16px;">
        <input type="button" id="signup" onclick="javascript:submitRegistration();" class="pull-right sage-submit pull-right">
      </div>

      <div class="col-md-12 col-sm-12 col-xs-12" id="form-error" style="margin-top: 16px; display: none;">
        <p class="pull-right" style="color: red;">*All fields are mandatory*</p>
      </div>
      
      <div class="col-md-12 col-sm-12 col-xs-12" id="emailexists" style="margin-top: 16px; display: none;">
        <p class="pull-right" style="color: red;">*Email is already registered*</p>
      </div>
    </div>
</div>
<g:render template="/sage/footer"></g:render>
<script>
var flds = new Array (
  'name',
  'username',
  'signup-password',
  'mobile'
);

function userSignIn() {
  if($('#email').val()=="") {
    $('#login-error').show();
    $('#email').css({
      'border' : '1px solid red'
    });
  }
  if($('#password').val()=="") {
    $('#login-error').show();
    $('#password').css({
      'border' : '1px solid red'
    });
  } else {
    $('#login-error').hide();
    $('#signin-form').submit();
  }
}
function showSageRegistration() {
  document.getElementById('user-login').style.display ='block';
  document.getElementById('user-register').style.display ='none';
}

function ShowSageLogin() {
  document.getElementById('user-register').style.display ='block';
  document.getElementById('user-login').style.display ='none';
}

function submitRegistration() {
  if($('#email').val()=="") {
    $('#form-error').show();
    $('#email').css({
      'border' : '1px solid red'
    });
  }
  if($('#user-password').val()=="") {
    $('#form-error').show();
    $('#user-password').css({
      'border' : '1px solid red'
    });
  }
  if($('#first-name').val()=="") {
    $('#form-error').show();
    $('#first-name').css({
      'border' : '1px solid red'
    });
  }
  if($('#last-name').val()=="") {
    $('#form-error').show();
    $('#last-name').css({
      'border' : '1px solid red'
    });
  }
  if($('#user-mobile').val()=="") {
    $('#form-error').show();
    $('#user-mobile').css({
      'border' : '1px solid red'
    });
  } else {
    $('#form-error').hide();
    $('.register-user-input').css({
      'border' : '0'
    });
    document.adduser.username.value = document.adduser.username.value.toLowerCase();
    //document.adduser.email.value = document.adduser.username.value.toLowerCase();
    checkUsernameExists();
  }
}

function checkUsernameExists() {
  var username = $("#username").val();
  console.log("username="+username);
  <g:remoteFunction controller="creation" action="checkUserNameExists"  onSuccess='userNameExistsResult(data);' params="'username='+username" />
}

function userNameExistsResult(data) {
    console.log("Data="+data);
  if(data=="0") {
    document.adduser.submit();
  } else {
    document.getElementById('emailexists').style.display = 'block';
  }
}
</script>