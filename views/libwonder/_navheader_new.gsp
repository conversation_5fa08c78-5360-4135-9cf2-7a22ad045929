<%@ page import="com.wonderslate.institute.InstituteIpAddress; com.wonderslate.data.UtilService; com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <%if(header!=null&&!"notadded".equals(header)){%>
    <%= header %>
    <%}else{%>
    <title><%= title!=null?title:"Lib Wonder"%></title>
    <meta name="description" content="Lib Wonder">
    <%}%>
    <link rel="icon"  href="${assetPath(src: 'landingpageImages/favicon.ico')}" type="image/x-icon">
    <link rel="android-touch-icon" href="${assetPath(src: 'landingpageImages/wsmetalogo.png')}"/>
    <link rel="windows-touch-icon" href="icon.png" />
    <meta name="theme-color" content="#358EF0" />
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@100;200;300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.6.0/css/all.min.css" rel="stylesheet">
    <link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <asset:stylesheet href="wonderslate/material.css" async="true"/>
    <asset:stylesheet href="landingpage/bootstrap.min.css"/>
    <asset:stylesheet href="landingpage/sageEvidya.css"/>
    <asset:stylesheet href="landingpage/fonts/flaticon.css"/>
    <asset:stylesheet href="landingpage/libwonderStyles.css"/>

    <%if("true".equals(commonTemplate)){%>
    <asset:stylesheet href="landingpage/libwonderTemplate.css" async="true"/>
    <% }%>

    <script nomodule="" src="https://unpkg.com/ionicons@5.2.3/dist/ionicons.js"></script>
    <style type="text/css" media="print">
    body { visibility: hidden; display: none }
    </style>
    <style>
    .btco-hover-menu .show > .dropdown-toggle::after{
        transform: rotate(-90deg);
    }
    .btco-hover-menu ul li {
        position:relative;
        /*border-left: 1px solid white;*/
    }
    .btco-hover-menu ul li.dropdown:last-child {
        /*border-left: none;*/
    }
    .btco-hover-menu ul li:first-child {
        /*border-left: none;*/
    }
    .btco-hover-menu ul ul li {
        position:relative;
    }
    .btco-hover-menu ul ul li:hover> ul {
        display:block;
    }
    .btco-hover-menu ul ul ul {
        position:absolute;
        top:0;
        left:-100% !important;
        min-width:220px;
        display:none;
    }
    .btco-hover-menu ul li.login_button {
        border-right: none;
    }
    </style>

    <sec:ifNotLoggedIn>
        <style>
            @media screen and (max-width: 767px) {
                .libwonder .ws-menu-start {
                    display: none;
                }
            }
        </style>

    </sec:ifNotLoggedIn>

</head>
<%session['siteId'] = new Integer(25);%>


<body class="libwonder" data-spy="scroll" data-target=".ws-header" data-offset="50">

<sec:ifNotLoggedIn>
    <g:render template="/books/signIn"></g:render>
</sec:ifNotLoggedIn>

<header class="LibWonder row justify-content-between m-0 py-3 px-4 px-md-5">

    <%if(params.tokenId==null){%>
    <div class="libwonder-logo text-center">
        <a href="/libwonder/index" class="row m-0 align-items-center">
            <%if(session['publisherLogoId']!=null){%>
            <span class="logo"><img class="publisher-logo${session['publisherLogoId']}" src="/publisherManagement/showPublisherImage?id=${session['publisherLogoId']}"></span>
            <%} else { %>   <span class="logo"><img src="${assetPath(src: 'libwonder/LibWonder_logo.svg')}" alt="LibWonder Logo"></span>
            <%}%>

            <span class="divider mx-3 mx-md-4"><img src="${assetPath(src: 'ws/chapter-line.png')}"></span>
            <span class="wslogo"><img src="${assetPath(src: 'libwonder/libwonder_wslogo.svg')}" alt="Wonderslate Logo"></span>
        </a>
    </div>
    <div class="ws-menu-start">
        <nav class="ws-header navbar navbar-expand-md navbar-default btco-hover-menu p-0">
            <ul class="navbar-nav right-menu align-items-center">
            <% if(showLibrary){%>
                <li class="nav-item">
                    <a class="nav-link" href="/wsLibrary/myLibrary">My Library</a>
                </li>
            <%}%>
            <sec:ifNotLoggedIn>
                <li class="nav-item ml-4">
                    <a class="nav-link loginButton d-none d-md-block" onclick="javascript:loginOpen()">Sign Up or Login</a>
                </li>
                %{--<li class="nav-item">
                    <a class="nav-link signupbutton" onclick="signupModal()" >Sign up</a>
                </li>--}%
            </sec:ifNotLoggedIn>
                <sec:ifLoggedIn>
                    <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
                        <li class="nav-item dropdown d-none d-md-block">
                            <a class="nav-link dropdown-toggle" href="#" id="publishing" data-toggle="dropdown">
                                Publishing
                            </a>
                            <div class="dropdown-menu">
                                <a class="dropdown-item" href="/publishing-desk">Publishing Desk</a>
                                <a class="dropdown-item" href="/wonderpublish/manageTabs">Manage Tags</a>
                                <a class="dropdown-item" href="/wonderpublish/manageExams">Manage MCQs Templates</a>
                                <%if(session.getAttribute("userdetails")!=null&&session.getAttribute("userdetails").publisherId!=null){%>
                                <a class="dropdown-item" href="/publisherManagement/addPublisher?pubId=${session.getAttribute("userdetails").publisherId}">Publisher Profile</a>
                                <%}%>
                            </div>
                        </li>
                    </sec:ifAllGranted>

                    <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_WS_CONTENT_ADMIN,ROLE_NOTIFICATION,ROLE_USER_LOGIN_RESET_MANAGER,ROLE_APP_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER,ROLE_WEB_CHAT,ROLE_WS_EDITOR,ROLE_LIBRARY_ADMIN,ROLE_MASTER_LIBRARY_ADMIN">

                        <li class="nav-item dropdown d-none d-md-block">
                            <a class="nav-link dropdown-toggle" href="#" id="ad" data-toggle="dropdown">
                                Admin
                            </a>
                            <div class="dropdown-menu">
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/admin/priceList">Price List</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/excel/fileUploader">File Uploader</a>
                                </sec:ifAllGranted>

                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/admin/managePublishers">Publisher Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_DELETE_USER">
                                    <a class="dropdown-item" href="/admin/deleteuser">Delete User</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_INSTITUTE_ADMIN">
                                    <a class="dropdown-item" href="/institute/admin">eClass+</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/institute/libAdmin">Library Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAnyGranted roles="ROLE_INSTITUTE_ADMIN,ROLE_INSTITUTE_REPORT_MANAGER">
                                    <a class="dropdown-item" href="/reports/instituteReport">Institute reports</a>
                                </sec:ifAnyGranted>
                                <sec:ifAnyGranted roles="ROLE_MASTER_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                                </sec:ifAnyGranted>
                                <sec:ifAllGranted roles="ROLE_PUBLISHER">
                                    <a class="dropdown-item" href="/log/quizissues">Quiz Issues</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                                    <a class="dropdown-item" href="/log/notification">Notification</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                                    <a class="dropdown-item" href="/log/userManagement">Force Logout</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_USER_LOGIN_RESET_MANAGER">
                                    <a class="dropdown-item" href="/log/migrateuser">Migrate User</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_ACCESS_CONTROLL">
                                    <a class="dropdown-item" href="/log/userAccess">User Access</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_APP_ADMIN">
                                    <a class="dropdown-item" href="/log/appVersionManagement">App Version Management</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_WS_EDITOR">
                                    <a class="dropdown-item" href="/wonderpublish/wseditor">WS Editor</a>
                                </sec:ifAllGranted>
%{--                                <sec:ifAnyGranted roles="ROLE_WEB_CHAT,ROLE_WS_CONTENT_ADMIN">--}%
%{--                                    <a class="dropdown-item" href="/comments/chatLogin">Live Web Chat</a>--}%
%{--                                </sec:ifAnyGranted>--}%
%{--                                <sec:ifAnyGranted roles="ROLE_WEB_CHAT,ROLE_WS_CONTENT_ADMIN">--}%
%{--                                    <a class="dropdown-item" href="/comments/downloadWebChat">Download Web Chat</a>--}%
%{--                                </sec:ifAnyGranted>--}%
                                <sec:ifAllGranted roles="ROLE_NOTIFICATION">
                                    <a class="dropdown-item" href="/log/notificationManagement">Notification Management</a>
                                </sec:ifAllGranted>
                                <%if(session.getAttribute("userdetails")!=null&&session.getAttribute("userdetails").publisherId==null){%>
                                <sec:ifAllGranted roles="ROLE_WS_CONTENT_ADMIN">
                                    <a class="dropdown-item" href="/discussionBoardAdmin">Doubts Admin</a>
                                </sec:ifAllGranted>
                                <sec:ifAllGranted roles="ROLE_LIBRARY_ADMIN">
                                    <a class="dropdown-item" href="/institute/userManagement">Library Management</a>
                                    <a class="dropdown-item" href="/institute/manageInstitutePage">Institute Page Management</a>
                                    <a class="dropdown-item" href="/institute/usageReportInstituteAdmin">Usage Statistics Report</a>
                                </sec:ifAllGranted>
                                <%}%>
                            </div>
                        </li>
                    </sec:ifAnyGranted>

                    %{--<li class="nav-item notification">
                        <a class="nav-link" onclick=""><i class="material-icons">notifications</i></a>
                    </li>--}%
                    <li class="nav-item dropdown">
                        <a class="nav-link" class="dropdown-toggle" data-toggle="dropdown">
                            <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                            <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle">
                            <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="rounded-circle">
                            <%}%>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right">
                            <div class="media p-3">
                                <a href="/creation/userProfile">
                                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                                    <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="mr-3 rounded-circle drop-profile">
                                    <%} else { %> <img src="${assetPath(src: 'landingpageImages/img_avatar3.png')}" alt="" class="mr-3 rounded-circle drop-profile">
                                    <%}%>
                                    <a href="/creation/userProfile" class="edit-btn"><i class="material-icons">edit</i></a>
                                </a>
                                <div class="media-body">
                                    <p class="user-name">Hello,<span id="loggedin-user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                                    <p class="user-mail"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                                </div>
                            </div>
                            <a class="dropdown-item order-pr" id="order-pr" href="\creation/userProfile#orders">Your Orders</a>
                            %{--<a class="dropdown-item" href="#">Wishlist</a>--}%
                            <a class="dropdown-item" href="/logoff" id="logout">Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <span>Sign out</span></a>
                        </div>
                    </li>
                </sec:ifLoggedIn>
            </ul>

        </nav>

    </div>
    <%}%>
</header>





