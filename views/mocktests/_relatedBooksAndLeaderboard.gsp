
<section class="booksList" style="display:none;">
    <div class="d-flex align-items-center justify-content-between">
        <h3 class="suggestedBooksTitle">Suggested Books</h3>
    </div>
    <div class="booksList__items" id="booksListItems">

    </div>
    <a href="/store" target="_blank" class="text-dark showMoreText">Show More</a>
</section>


<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.18.1/moment.min.js"></script>
<script>
    function latestBooksReceived(data){
        if(data.books == null || data.books == undefined){
            return
        }
        const booksList = JSON.parse(data.books).slice(0,4);
        const booksListItems = document.getElementById('booksListItems');
        let bookItemsHTML = "";
        let bookCoverImg;
        if (booksList.length>0){
            booksList.forEach(book=>{
                if (book.coverImage === null || book.coverImage === "null" || book.coverImage === undefined){
                    bookCoverImg = "/assets/resource/cover_dummy.webp";
                }else if (book.coverImage!=null && book.coverImage.startsWith("https")){
                    bookCoverImg = books.coverImage;
                    bookCoverImg = bookCoverImg.replace("~", ":");
                }else{
                    bookCoverImg = "/funlearn/showProfileImage?id=" + book.id + "&fileName=" + book.coverImage + "&type=books&imgType=passport";
                }
                let bookLink  = "https://www.wonderslate.com/"+book.title.replaceAll(' ','-') + "/ebook-details?siteName=${session["entryController"]}"+"&bookId="+book.id+"&"+book.publisher.replaceAll(' ','-')+"&preview=true";
                let publisherLink =  "https://www.wonderslate.com/books/store?linkSource=bookDetail&publisher="+book.publisher.replaceAll(' ','-');
                bookItemsHTML += "<div class='booksList__items-item'>"+
                    "<div class='booksListItem__img'>"+
                    "<img src='"+bookCoverImg+"' alt='"+book.title+"' width='100px' height='140px'>"+
                    "</div>"+
                    "<div class='booksListItem__details'>"+
                    "<a href='"+bookLink+"' target='_blank'><p>"+book.title+"</p></a>"+
                    "<a href='"+publisherLink+"' target='_blank'><span>By "+book.publisher+"</span></a>"+
                    "<div class='booksListItem__img-price'>"+
                    "<p><strike>&#x20b9 "+book.listPrice+"</strike></p>"+
                    "<p><strong>&#x20b9 "+book.offerPrice+"</strong></p>"+
                    "</div>"+
                    "</div>"+
                    "</div>";
            })
            booksListItems.innerHTML = bookItemsHTML;
            let showMoreURL = "https://www.wonderslate.com/store?level="+level.replaceAll(" ","-")+"&syllabus="+syllabus.replaceAll(" ","-")+"&";
            document.querySelector('.showMoreText').setAttribute('href',showMoreURL);
            document.querySelector('.booksList').style.display="block";
        }

    }
    function getBooks(level,syllabus,grade,subject,pageNo,publisherId,subscriptionBooks){
        let bookSearchParams = 'fromApp=false&categories=true&'+
            'level='+level+'&syllabus='+syllabus+'&' +
            'grade='+grade+'&subject='+subject+'&pageNo='+pageNo+'&' +
            'publisherId='+publisherId+'&getSubscription='+ subscriptionBooks;
        <g:remoteFunction controller="wonderpublish" action="getNewBooksList" params="'fromApp=false&categories=true&level='+level+'&syllabus='+syllabus+'&grade='+grade+'&subject='+subject+'&pageNo='+pageNo+'&publisherId='+publisherId+'&getSubscription='+subscriptionBooks"  onSuccess='latestBooksReceived(data);'/>
    }
    document.addEventListener('DOMContentLoaded',function (){
        getBooks(level,syllabus,grade,subject,pageNo,publisherId,subscriptionBooks);
    })
    var selectedDate = "";

    var today = moment().format('DD MMMM YYYY');
    if(document.getElementById("selectedMonthYear")){
        document.getElementById("selectedMonthYear").innerHTML = today;
    }

    function selectMonth() {
        $('#datePicker').datepicker({
            maxViewMode: 0,
            format: 'dd-mm-yyyy',
            todayHighlight: true,
            maxDate:'0',
            endDate: "today",
        });
        $("#datePickerModal").modal('show');
    }

    $('#datePicker').on('changeDate', function(event) {
        var selectedCurrentDate = event.format('dd MM yyyy');
        document.getElementById("selectedMonthYear").innerHTML = selectedCurrentDate;
        selectedDate = $('#datePicker').datepicker('getFormattedDate');``
        $("#datePickerModal").modal('hide');

        currentDate = selectedDate.split('-').reverse().join('-');
        getDailyRank();
    });

    function openDailyTest(){
        window.location.href = '/prepjoy/dailyTest';
    }

</script>
