<div id="billingShipping" style="display: none">

    <form id="billingShippingForm" name="billingShippingForm" class="mt-3 mb-0">

        <!-- Shipping Address Section -->
        <div class="shippingAdressWrapper">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h3>Shipping Address </h3>
                <a href="javascript:editShippingAddress()">Edit</a>
            </div>

            <div class="form-group">
                <label for="shipName">Name</label>
                <input type="text" class="form-control" id="shipName" name="shipName" required value="${billShip!=null?billShip.shipFirstName:""}">
            </div>
            <div class="form-group">
                <label for="shipMobile">Mobile</label>
                <input type="number" class="form-control" id="shipMobile" name="shipMobile" required value="${billShip!=null?billShip.shipMobile:""}">
            </div>
            <div class="form-group">
                <label for="shipEmail">Email</label>
                <input type="email" class="form-control" id="shipEmail" name="shipEmail" required value="${billShip!=null?billShip.shipEmail:""}">
            </div>



            <div class="form-group">
                <label for="shipAddressLine1">Address Line 1</label>
                <input type="text" class="form-control" id="shipAddressLine1" name="shipAddressLine1" required value="${billShip!=null?billShip.shipAddressLine1:""}">
            </div>

            <div class="form-group">
                <label for="shipAddressLine2">Address Line 2</label>
                <input type="text" class="form-control" id="shipAddressLine2" name="shipAddressLine2" value="${billShip!=null?billShip.shipAddressLine2:""}">
            </div>

            <div class="form-group">
                <label for="shipPincode">Pincode</label>
                <input type="text" class="form-control" id="shipPincode" name="shipPincode" required value="${billShip!=null?billShip.shipPincode:""}">
                <div id="pincodeErrShip" class="alert alert-danger d-none">Please enter valid pincode.</div>
            </div>

            <div class="form-group">
                <label for="shipCity">City</label>
                <input type="text" class="form-control" id="shipCity"  name="shipCity"  required value="${billShip!=null?billShip.shipCity:""}">
            </div>

            <div class="form-group">
                <label for="shipState">State</label>
                <select class="form-control" id="shipState" name="shipState">
                    <option value="">-- Select State --</option>
                    <option value="Andaman and Nicobar Islands" ${billShip!=null&&"Andaman and Nicobar Islands".equals(billShip.shipState)?"selected":""}>Andaman and Nicobar Islands</option>
                    <option value="Andhra Pradesh" ${billShip!=null&&"Andhra Pradesh".equals(billShip.shipState)?"selected":""}>Andhra Pradesh</option>
                    <option value="Arunachal Pradesh" ${billShip!=null&&"Arunachal Pradesh".equals(billShip.shipState)?"selected":""}>Arunachal Pradesh</option>
                    <option value="Assam" ${billShip!=null&&"Assam".equals(billShip.shipState)?"selected":""}>Assam</option>
                    <option value="Bihar" ${billShip!=null&&"Bihar".equals(billShip.shipState)?"selected":""}>Bihar</option>
                    <option value="Chandigarh" ${billShip!=null&&"Chandigarh".equals(billShip.shipState)?"selected":""}>Chandigarh</option>
                    <option value="Chhattisgarh" ${billShip!=null&&"Chhattisgarh".equals(billShip.shipState)?"selected":""}>Chhattisgarh</option>
                    <option value="Dadra and Nagar Haveli and Daman and Diu" ${billShip!=null&&"Dadra and Nagar Haveli and Daman and Diu".equals(billShip.shipState)?"selected":""}>Dadra and Nagar Haveli and Daman and Diu</option>
                    <option value="Delhi" ${billShip!=null&&"Delhi".equals(billShip.shipState)?"selected":""}>Delhi</option>
                    <option value="Goa" ${billShip!=null&&"Goa".equals(billShip.shipState)?"selected":""}>Goa</option>
                    <option value="Gujarat" ${billShip!=null&&"Gujarat".equals(billShip.shipState)?"selected":""}>Gujarat</option>
                    <option value="Haryana" ${billShip!=null&&"Haryana".equals(billShip.shipState)?"selected":""}>Haryana</option>
                    <option value="Himachal Pradesh" ${billShip!=null&&"Himachal Pradesh".equals(billShip.shipState)?"selected":""}>Himachal Pradesh</option>
                    <option value="Jammu and Kashmir" ${billShip!=null&&"Jammu and Kashmir".equals(billShip.shipState)?"selected":""}>Jammu and Kashmir</option>
                    <option value="Jharkhand" ${billShip!=null&&"Jharkhand".equals(billShip.shipState)?"selected":""}>Jharkhand</option>
                    <option value="Karnataka" ${billShip!=null&&"Karnataka".equals(billShip.shipState)?"selected":""}>Karnataka</option>
                    <option value="Kerala" ${billShip!=null&&"Kerala".equals(billShip.shipState)?"selected":""}>Kerala</option>
                    <option value="Ladakh" ${billShip!=null&&"Ladakh".equals(billShip.shipState)?"selected":""}>Ladakh</option>
                    <option value="Lakshadweep" ${billShip!=null&&"Lakshadweep".equals(billShip.shipState)?"selected":""}>Lakshadweep</option>
                    <option value="Madhya Pradesh" ${billShip!=null&&"Madhya Pradesh".equals(billShip.shipState)?"selected":""}>Madhya Pradesh</option>
                    <option value="Maharashtra" ${billShip!=null&&"Maharashtra".equals(billShip.shipState)?"selected":""}>Maharashtra</option>
                    <option value="Manipur" ${billShip!=null&&"Manipur".equals(billShip.shipState)?"selected":""}>Manipur</option>
                    <option value="Meghalaya" ${billShip!=null&&"Meghalaya".equals(billShip.shipState)?"selected":""}>Meghalaya</option>
                    <option value="Mizoram" ${billShip!=null&&"Mizoram".equals(billShip.shipState)?"selected":""}>Mizoram</option>
                    <option value="Nagaland" ${billShip!=null&&"Nagaland".equals(billShip.shipState)?"selected":""}>Nagaland</option>
                    <option value="Odisha" ${billShip!=null&&"Odisha".equals(billShip.shipState)?"selected":""}>Odisha</option>
                    <option value="Puducherry" ${billShip!=null&&"Puducherry".equals(billShip.shipState)?"selected":""}>Puducherry</option>
                    <option value="Punjab" ${billShip!=null&&"Punjab".equals(billShip.shipState)?"selected":""}>Punjab</option>
                    <option value="Rajasthan" ${billShip!=null&&"Rajasthan".equals(billShip.shipState)?"selected":""}>Rajasthan</option>
                    <option value="Sikkim" ${billShip!=null&&"Sikkim".equals(billShip.shipState)?"selected":""}>Sikkim</option>
                    <option value="Tamil Nadu" ${billShip!=null&&"Tamil Nadu".equals(billShip.shipState)?"selected":""}>Tamil Nadu</option>
                    <option value="Telangana" ${billShip!=null&&"Telangana".equals(billShip.shipState)?"selected":""}>Telangana</option>
                    <option value="Tripura" ${billShip!=null&&"Tripura".equals(billShip.shipState)?"selected":""}>Tripura</option>
                    <option value="Uttarakhand" ${billShip!=null&&"Uttarakhand".equals(billShip.shipState)?"selected":""}>Uttarakhand</option>
                    <option value="Uttar Pradesh" ${billShip!=null&&"Uttar Pradesh".equals(billShip.shipState)?"selected":""}>Uttar Pradesh</option>
                    <option value="West Bengal" ${billShip!=null&&"West Bengal".equals(billShip.shipState)?"selected":""}>West Bengal</option>
                </select>
            </div>
            <div class="form-group">
                <label>
                    <input type="checkbox" id="differentAddress"> Billing address different from Shipping Address
                </label>
            </div>

        <div id="billing-address-section" style="display: none;">
            <h3>Billing Address</h3>
            <div class="form-group">
                <label for="billName">Name</label>
                <input type="text" class="form-control" id="billName" name="billName" value="${billShip!=null?billShip.billFirstName:""}">
            </div>
            <div class="form-group">
                <label for="billMobile">Mobile</label>
                <input type="number" class="form-control" id="billMobile" name="billMobile" value="${billShip!=null?billShip.billMobile:""}">
            </div>
            <div class="form-group">
                <label for="billEmail">Email</label>
                <input type="email" class="form-control" id="billEmail" name="billEmail" value="${billShip!=null?billShip.billEmail:""}">
            </div>
            <div class="form-group">
                <label for="billAddressLine1">Address Line 1</label>
                <input type="text" class="form-control" id="billAddressLine1" name="billAddressLine1" value="${billShip!=null?billShip.billAddressLine1:""}">
            </div>

            <div class="form-group">
                <label for="billAddressLine2">Address Line 2</label>
                <input type="text" class="form-control" id="billAddressLine2" name="billAddressLine2" value="${billShip!=null?billShip.billAddressLine2:""}">
            </div>

            <div class="form-group">
                <label for="billPincode">Pincode</label>
                <input type="text" class="form-control" id="billPincode" name="billPincode" value="${billShip!=null?billShip.billPincode:""}">
                <div id="pincodeErrBill" class="alert alert-danger d-none">Please enter valid pincode.</div>
            </div>

            <div class="form-group">
                <label for="billCity">City</label>
                <input type="text" class="form-control" id="billCity"  name="billCity" value="${billShip!=null?billShip.billCity:""}">
            </div>

            <div class="form-group">
                <label for="billState">State</label>
                <select class="form-control" id="billState" name="billState">
                    <option value="">-- Select State --</option>
                    <option value="Andaman and Nicobar Islands" ${billShip!=null&&"Andaman and Nicobar Islands".equals(billShip.billState)?"selected":""}>Andaman and Nicobar Islands</option>
                    <option value="Andhra Pradesh" ${billShip!=null&&"Andhra Pradesh".equals(billShip.billState)?"selected":""}>Andhra Pradesh</option>
                    <option value="Arunachal Pradesh" ${billShip!=null&&"Arunachal Pradesh".equals(billShip.billState)?"selected":""}>Arunachal Pradesh</option>
                    <option value="Assam" ${billShip!=null&&"Assam".equals(billShip.billState)?"selected":""}>Assam</option>
                    <option value="Bihar" ${billShip!=null&&"Bihar".equals(billShip.billState)?"selected":""}>Bihar</option>
                    <option value="Chandigarh" ${billShip!=null&&"Chandigarh".equals(billShip.billState)?"selected":""}>Chandigarh</option>
                    <option value="Chhattisgarh" ${billShip!=null&&"Chhattisgarh".equals(billShip.billState)?"selected":""}>Chhattisgarh</option>
                    <option value="Dadra and Nagar Haveli and Daman and Diu" ${billShip!=null&&"Dadra and Nagar Haveli and Daman and Diu".equals(billShip.billState)?"selected":""}>Dadra and Nagar Haveli and Daman and Diu</option>
                    <option value="Delhi" ${billShip!=null&&"Delhi".equals(billShip.billState)?"selected":""}>Delhi</option>
                    <option value="Gujarat" ${billShip!=null&&"Gujarat".equals(billShip.billState)?"selected":""}>Gujarat</option>
                    <option value="Haryana" ${billShip!=null&&"Haryana".equals(billShip.billState)?"selected":""}>Haryana</option>
                    <option value="Himachal Pradesh" ${billShip!=null&&"Himachal Pradesh".equals(billShip.billState)?"selected":""}>Himachal Pradesh</option>
                    <option value="Jammu and Kashmir" ${billShip!=null&&"Jammu and Kashmir".equals(billShip.billState)?"selected":""}>Jammu and Kashmir</option>
                    <option value="Jharkhand" ${billShip!=null&&"Jharkhand".equals(billShip.billState)?"selected":""}>Jharkhand</option>
                    <option value="Karnataka" ${billShip!=null&&"Karnataka".equals(billShip.billState)?"selected":""}>Karnataka</option>
                    <option value="Kerala" ${billShip!=null&&"Kerala".equals(billShip.billState)?"selected":""}>Kerala</option>
                    <option value="Ladakh" ${billShip!=null&&"Ladakh".equals(billShip.billState)?"selected":""}>Ladakh</option>
                    <option value="Lakshadweep" ${billShip!=null&&"Lakshadweep".equals(billShip.billState)?"selected":""}>Lakshadweep</option>
                    <option value="Madhya Pradesh" ${billShip!=null&&"Madhya Pradesh".equals(billShip.billState)?"selected":""}>Madhya Pradesh</option>
                    <option value="Maharashtra" ${billShip!=null&&"Maharashtra".equals(billShip.billState)?"selected":""}>Maharashtra</option>
                    <option value="Manipur" ${billShip!=null&&"Manipur".equals(billShip.billState)?"selected":""}>Manipur</option>
                    <option value="Meghalaya" ${billShip!=null&&"Meghalaya".equals(billShip.billState)?"selected":""}>Meghalaya</option>
                    <option value="Mizoram" ${billShip!=null&&"Mizoram".equals(billShip.billState)?"selected":""}>Mizoram</option>
                    <option value="Nagaland" ${billShip!=null&&"Nagaland".equals(billShip.billState)?"selected":""}>Nagaland</option>
                    <option value="Odisha" ${billShip!=null&&"Odisha".equals(billShip.billState)?"selected":""}>Odisha</option>
                    <option value="Puducherry" ${billShip!=null&&"Puducherry".equals(billShip.billState)?"selected":""}>Puducherry</option>
                    <option value="Punjab" ${billShip!=null&&"Punjab".equals(billShip.billState)?"selected":""}>Punjab</option>
                    <option value="Rajasthan" ${billShip!=null&&"Rajasthan".equals(billShip.billState)?"selected":""}>Rajasthan</option>
                    <option value="Sikkim" ${billShip!=null&&"Sikkim".equals(billShip.billState)?"selected":""}>Sikkim</option>
                    <option value="Tamil Nadu" ${billShip!=null&&"Tamil Nadu".equals(billShip.billState)?"selected":""}>Tamil Nadu</option>
                    <option value="Telangana" ${billShip!=null&&"Telangana".equals(billShip.billState)?"selected":""}>Telangana</option>
                    <option value="Tripura" ${billShip!=null&&"Tripura".equals(billShip.billState)?"selected":""}>Tripura</option>
                    <option value="Uttarakhand" ${billShip!=null&&"Uttarakhand".equals(billShip.billState)?"selected":""}>Uttarakhand</option>
                    <option value="Uttar Pradesh" ${billShip!=null&&"Uttar Pradesh".equals(billShip.billState)?"selected":""}>Uttar Pradesh</option>
                    <option value="West Bengal" ${billShip!=null&&"West Bengal".equals(billShip.billState)?"selected":""}>West Bengal</option>
                </select>
            </div>
        </div>

        </div>

        <!-- Billing Address Section (Initially hidden) -->
        <div class="form-group mb-0">
            <div id="billing-address-error" class="alert alert-danger d-none"></div>
        </div>
        <!-- Checkbox to toggle billing address visibility -->

    </form>
</div>

<script src="https://code.jquery.com/jquery-3.3.1.min.js"></script>
<script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/js/bootstrap.min.js"></script>
<script>
    // Get the different address checkbox and the billing address section
    const differentAddressCheckbox = document.getElementById('differentAddress');
    const billingAddressSection = document.getElementById('billing-address-section');

    // Function to show or hide the billing address section based on the checkbox state
    function toggleBillingAddressSection() {
        if (differentAddressCheckbox.checked) {
            $('#billing-address-section').slideToggle()
            enableBillingAddressValidation();
        } else {
            $('#billing-address-section').slideToggle()
            disableBillingAddressValidation();
        }
    }

    // Function to enable billing address validation
    function enableBillingAddressValidation() {
        const billAddressLine1 = document.getElementById('billAddressLine1');
        const billPincode = document.getElementById('billPincode');
        const billCity = document.getElementById('billCity');
        const billState = document.getElementById('billState');

        billAddressLine1.setAttribute('required', '');
        billPincode.setAttribute('required', '');
        billCity.setAttribute('required', '');
        billState.setAttribute('required', '');
    }

    // Function to disable billing address validation
    function disableBillingAddressValidation() {
        const billAddressLine1 = document.getElementById('billAddressLine1');
        const billPincode = document.getElementById('billPincode');
        const billCity = document.getElementById('billCity');
        const billState = document.getElementById('billState');

        billAddressLine1.removeAttribute('required');
        billPincode.removeAttribute('required');
        billCity.removeAttribute('required');
        billState.removeAttribute('required');
    }

    // Add event listener to the different address checkbox
    differentAddressCheckbox.addEventListener('change', function() {
        toggleBillingAddressSection();
    });

    // Function to fetch city and state based on pincode
    function fetchCityAndState(pincode, targetCityElement, targetStateElement,type) {
        fetch(`https://api.postalpincode.in/pincode/`+pincode)
            .then(response => response.json())
            .then(data => {
                if (data[0].Status === 'Success') {
                    const city = data[0].PostOffice[0].District;
                    const state = data[0].PostOffice[0].State;
                    targetCityElement.value = city;
                    targetStateElement.value = state;
                } else {
                    targetStateElement.value = '';
                    targetCityElement.value = '';
                    if (type == 'shipPin'){
                        document.getElementById('pincodeErrShip').classList.remove('d-none');
                        setTimeout(function (){
                            document.getElementById('pincodeErrShip').classList.add('d-none');
                        },2000)
                    }else{
                        document.getElementById('pincodeErrBill').classList.remove('d-none');
                        setTimeout(function (){
                            document.getElementById('pincodeErrBill').classList.add('d-none');
                        },2000)
                    }
                }
            })
            .catch(error => {
                console.log('An error occurred while fetching city and state:', error);
            });
    }






    function submitShippingBilling() {

        let isValid = true;

        // Validate the shipping address fields
        const name = document.getElementById('shipName');
        const email = document.getElementById('shipEmail');
        const mobile = document.getElementById('shipMobile');
        const shipAddressLine1 = document.getElementById('shipAddressLine1');
        const shipPincode = document.getElementById('shipPincode');
        const shipCity = document.getElementById('shipCity');
        const shipState = document.getElementById('shipState');
        const billingAddressError = document.getElementById('billing-address-error');

        if (name.value.trim() === '') {
            isValid = false;
            showHideError(billingAddressError,name,'Enter valid name');
        }


        if (mobile.value.trim() === '') {
            isValid = false;
            showHideError(billingAddressError,mobile,'Enter valid mobile');
        }

        if (email.value.trim() === '' || !email.value.includes('@')) {
            isValid = false;
            showHideError(billingAddressError,email,'Enter valid email');
        }

        if (shipAddressLine1.value.trim() === '') {
            isValid = false;
            showHideError(billingAddressError,shipAddressLine1,'Enter valid address');
        }

        if (shipPincode.value.trim() === '') {
            isValid = false;
            showHideError(billingAddressError,shipPincode,'Enter valid pincode');
        }

        if (shipCity.value.trim() === '') {
            isValid = false;
            showHideError(billingAddressError,shipCity,'Enter valid city');
        }

        if (shipState.value.trim() === '') {
            isValid = false;
            showHideError(billingAddressError,shipState,'Enter valid state');
        }

        // Validate the billing address fields if different address checkbox is checked
        const differentAddressCheckbox = document.getElementById('differentAddress');
        if (differentAddressCheckbox.checked) {
            const billAddressLine1 = document.getElementById('billAddressLine1');
            const billPincode = document.getElementById('billPincode');
            const billCity = document.getElementById('billCity');
            const billState = document.getElementById('billState');
            const billEmail = document.getElementById('billEmail');

            if (billAddressLine1.value.trim() === '') {
                isValid = false;
                showHideError(billingAddressError,billAddressLine1,'Enter valid address');
            }

            if (billPincode.value.trim() === '') {
                isValid = false;
                showHideError(billingAddressError,billPincode,'Enter valid pincode');
            }

            if (billCity.value.trim() === '') {
                isValid = false;
                showHideError(billingAddressError,billCity,'Enter valid city');
            }

            if (billState.value.trim() === '') {
                isValid = false;
                showHideError(billingAddressError,billState,'Enter valid state');
            }
            if (billEmail.value.trim() === '' || !billEmail.value.includes('@')) {
                isValid = false;
                showHideError(billingAddressError,billEmail,'Enter valid email');
            }
        }
        if (isValid) {
            billingAddressError.classList.add('d-none');
            // Submit the form if validation is successful
            document.getElementById('billCity').removeAttribute('disabled');
            document.getElementById('billState').removeAttribute('disabled');
            document.getElementById('billCity').setAttribute('readOnly','');
            document.getElementById('billState').setAttribute('readOnly','');
            shipCity.removeAttribute('disabled');
            shipState.removeAttribute('disabled');

            shipState.setAttribute('readOnly','');
            shipCity.setAttribute('readOnly','');
            var oData = new FormData(document.forms.namedItem("billingShippingForm"));
            var url = "${createLink(controller:'wsshop',action:'addBillShipAddressToHolder')}";
            if (!prepjoySite){
                $('.loading-icon').removeClass('hidden');
            }else{
                $('#loading').show();
            }
            $.ajax({
                url: url,
                type: 'POST',
                data: oData,
                processData: false,  // tell jQuery not to process the data
                contentType: false,
                success: function (req) {
                    if (!prepjoySite){
                        $('.loading-icon').addClass('hidden');
                    }else{
                        $('#loading').hide();
                    }
                    shippingDeliveryUpdated(req);
                }
            });
        }
    }


    function enableDisableEditField(state){
        const shipName = document.getElementById('shipName');
        const shipMobile = document.getElementById('shipMobile');
        const shipEmail = document.getElementById('shipEmail');
        const shipAddressLine1 = document.getElementById('shipAddressLine1');
        const shipAddressLine2 = document.getElementById('shipAddressLine2');
        const shipPincode = document.getElementById('shipPincode');
        const differentAddress = document.getElementById('differentAddress');
        const billName = document.getElementById('billName');
        const billMobile = document.getElementById('billMobile');
        const billEmail = document.getElementById('billEmail');
        const billAddressLine1 = document.getElementById('billAddressLine1');
        const billAddressLine2 = document.getElementById('billAddressLine2');
        const billPincode = document.getElementById('billPincode');
        const billCity = document.getElementById('billCity');
        const billState = document.getElementById('billState');
        if (state=='disable'){
            shipName.setAttribute('readOnly','');
            shipMobile.setAttribute('readOnly','');
            shipEmail.setAttribute('readOnly','');
            shipAddressLine1.setAttribute('readOnly','');
            shipAddressLine2.setAttribute('readOnly','');
            shipPincode.setAttribute('readOnly','');
            differentAddress.setAttribute('disabled','disabled');
            billName.setAttribute('readOnly','');
            billMobile.setAttribute('readOnly','');
            billEmail.setAttribute('readOnly','');
            billAddressLine1.setAttribute('readOnly','');
            billAddressLine2.setAttribute('readOnly','');
            billPincode.setAttribute('readOnly','');
            billCity.setAttribute('readOnly','');
            billState.setAttribute('readOnly','');
        }else if (state=='enable'){
            shipName.removeAttribute('readOnly');
            shipMobile.removeAttribute('readOnly');
            shipEmail.removeAttribute('readOnly');
            shipAddressLine1.removeAttribute('readOnly');
            shipAddressLine2.removeAttribute('readOnly');
            shipPincode.removeAttribute('readOnly');
            differentAddress.removeAttribute('disabled');
            billName.removeAttribute('readOnly');
            billMobile.removeAttribute('readOnly');
            billEmail.removeAttribute('readOnly');
            billAddressLine1.removeAttribute('readOnly');
            billAddressLine2.removeAttribute('readOnly');
            billPincode.removeAttribute('readOnly');
            billCity.removeAttribute('readOnly');
            billState.removeAttribute('readOnly');
        }
    }

    function editShippingAddress(){
        document.getElementById("proceedToBuyButton").href="javascript:submitShippingBilling();";
        document.getElementById("proceedToBuyButton").textContent = 'Confirm Shipping Address';
        enableDisableEditField('enable');
    }

    function showHideError(billingAddressError,element,msg){
        billingAddressError.innerHTML = msg;
        billingAddressError.classList.remove('d-none');
        element.focus();
        setTimeout(function (){
            billingAddressError.classList.add('d-none');
            billingAddressError.innerHTML = ''
        },2000)
    }
</script>
