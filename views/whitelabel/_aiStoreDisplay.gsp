<%
    String levelLabel = "Select Level"
    String syllabusLabel = "Select Board"
    String gradeLabel = "Select Grade"
    String subjectLabel = "Select Subject"
    if(session["levelLabel"]!=null) levelLabel = session["levelLabel"]
    if(session["syllabusLabel"]!=null) syllabusLabel = session["syllabusLabel"]
    if(session["gradeLabel"]!=null) gradeLabel = session["gradeLabel"]
    if(session["subjectLabel"]!=null) subjectLabel = session["subjectLabel"]
%>

<div class="ai-store-container">
    <div class="container">
        <!-- Modern Header Section -->
        <div class="ai-store-header">
            <h1 class="ai-store-title">AI Inside Every Book</h1>
            <p class="ai-store-subtitle">Smarter Books, Better Scores</p>
        </div>

        <!-- Compact Search and Filters Row -->
        <div class="ai-search-filters-row">
            <!-- Search Container -->
            <div class="ai-search-container">
                <input type="text" class="ai-search-input typeahead"
                       name="search" id="search-book-store" autocomplete="off"
                       placeholder="Search books, authors, subjects..."
                       onfocus="expandSearch(this)" onblur="contractSearch(this)">
                <i class="material-icons ai-search-icon" onclick="submitSearchTop()">search</i>
            </div>

            <!-- Horizontal Filters -->
            <div class="ai-filters-horizontal" id="filters-horizontal">
                <%if(showPublishers!=null&&"true".equals(showPublishers)){%>
                <div class="ai-filter-item">
                    <label class="ai-filter-label">Publisher</label>
                    <select id="publisher" onchange="publisherChanged(this);" class="ai-filter-select">
                        <option value="" selected="selected">All Publishers</option>
                    </select>
                </div>
                <%}%>

                <%if(!"55".equals(""+session["siteId"])){%>
                <div class="ai-filter-item">
                    <label class="ai-filter-label">Level</label>
                    <select id="level" onchange="levelChanged(this);" class="ai-filter-select">
                        <option value="" selected="selected">${levelLabel}</option>
                    </select>
                </div>
                <%}%>

                <div class="ai-filter-item">
                    <label class="ai-filter-label">Board</label>
                    <select id="syllabus" onchange="syllabusChanged(this)" class="ai-filter-select">
                        <option value="" selected="selected">${syllabusLabel}</option>
                    </select>
                </div>

                <%if(!session['wileySite']){%>
                <div class="ai-filter-item" id="grade-filter" style="display: none;">
                    <label class="ai-filter-label">Grade</label>
                    <select id="grade" onchange="gradeChanged(this)" class="ai-filter-select">
                        <option value="" selected="selected">${gradeLabel}</option>
                    </select>
                </div>

                <div class="ai-filter-item" id="subject-filter" style="display: none;">
                    <label class="ai-filter-label">Subject</label>
                    <select id="subject" onchange="subjectChanged(this)" class="ai-filter-select">
                        <option value="" selected="selected">${subjectLabel}</option>
                    </select>
                </div>
                <%}%>

                <button onclick="resetFilters()" id="resetFilter" class="ai-clear-filters">
                    Clear All
                </button>
            </div>
        </div>

        <sec:ifAllGranted roles="ROLE_WS_SALES_TEAM">
            <div class="text-center mb-4">
                <button class="ai-btn-primary" onclick="downloadCatalogue();">
                    <i class="material-icons" style="font-size: 16px;">download</i>
                    Download Catalogue
                </button>
            </div>
        </sec:ifAllGranted>

        <div class="row">
            <!-- Hidden Old Filters Sidebar -->
            <div class="col-12 col-lg-3 mb-4" style="display: none;">
                <div class="ai-filters-container d-flex flex-column" id="filters">
                    <div class="ai-filter-title">
                        <div class="ai-filter-icon">
                            <i class="material-icons" style="font-size: 12px;">tune</i>
                        </div>
                        Filters
                        <button onclick="resetFilters()" id="resetFilter" class="ai-clear-filters ms-auto">
                            Clear All
                        </button>
                    </div>

                    <%if(showPublishers!=null&&"true".equals(showPublishers)){%>
                    <div class="mb-3">
                        <select id="publisher" onchange="publisherChanged(this);" 
                                class="form-control ai-form-control">
                            <option value="" selected="selected">Select Publishers</option>
                        </select>
                    </div>
                    <%}else{%>
                    <div id="publisher" style="display: none"></div>
                    <%}%>

                    <%if("55".equals(""+session["siteId"])){%>
                    <div class="mb-3" style="display: none">
                    <%}else{%>
                    <div class="mb-3">
                    <%}%>
                        <select id="level" onchange="levelChanged(this);" 
                                class="form-control ai-form-control">
                            <option value="" selected="selected">${levelLabel}</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <select id="syllabus" onchange="syllabusChanged(this)" 
                                class="form-control ai-form-control">
                            <option value="" selected="selected">${syllabusLabel}</option>
                        </select>
                    </div>

                    <%if(session['wileySite'] ){%>
                    <div class="mb-3" style="display: none">
                        <select id="grade" onchange="gradeChanged(this)" 
                                class="form-control ai-form-control">
                            <option value="" selected="selected">${gradeLabel}</option>
                        </select>
                    </div>
                    <div class="mb-3" style="display: none">
                        <select id="subject" onchange="subjectChanged(this)" 
                                class="form-control ai-form-control">
                            <option value="" selected="selected">${subjectLabel}</option>
                        </select>
                    </div>
                    <%} else {%>
                    <div class="mb-3">
                        <select id="grade" onchange="gradeChanged(this)" 
                                class="form-control ai-form-control">
                            <option value="" selected="selected">${gradeLabel}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <select id="subject" onchange="subjectChanged(this)" 
                                class="form-control ai-form-control">
                            <option value="" selected="selected">${subjectLabel}</option>
                        </select>
                    </div>
                    <%}%>
                </div>
            </div>

            <!-- Books Grid Section -->
            <div class="col-12">
                <!-- No Results Message -->
                <div class="ai-no-results searchShowHide" style="display: none;" id="noResultsFound">
                    <div class="ai-no-results-icon">
                        <i class="material-icons">sentiment_dissatisfied</i>
                    </div>
                    <div class="ai-no-results-title">No Results Found</div>
                    <div class="ai-no-results-text">
                        We couldn't find any books matching your search criteria. Please try adjusting your filters.
                    </div>
                </div>

                <!-- Amazon Books Title -->
                <div id="amazonBooksTitle" class="mb-4" style="display: none;">
                    <div class="ai-store-header">
                        <h3 class="text-center mb-0"></h3>
                    </div>
                </div>

                <!-- Books Grid Container -->
                <div class="ai-books-grid" id="content-data-books-ebooks"></div>

                <!-- Load More Button -->
                <div id="load-more-button" class="ai-load-more" style="display: none;">
                    <button class="ai-load-more-btn" id="view-more">
                        <i class="material-icons me-2" style="font-size: 18px;">expand_more</i>
                        Load More Books
                    </button>
                </div>
            </div>
        </div>

        <!-- About Sections -->
        <%if(aboutTitle!=null){%>
        <div class="ai-store-header mt-5">
            <h2 class="ai-store-title" style="font-size: 2rem;">About ${(""+aboutTitle).toUpperCase()}</h2>
            <div class="ai-store-subtitle" id="aboutDescription" style="text-align: left; font-size: 1rem; line-height: 1.6;">
                ${aboutDescription}
            </div>
        </div>
        <%}%>

        <%if(publisherDescription!=null&&!"".equals(publisherDescription)){%>
        <div class="ai-store-header mt-4">
            <h2 class="ai-store-title" style="font-size: 2rem;">About ${publisherName}</h2>
            <div class="ai-store-subtitle" id="publisherDescription" style="text-align: left; font-size: 1rem; line-height: 1.6;">
                ${publisherDescription}
            </div>
        </div>
        <%}%>

        <%if("1".equals(""+session["siteId"]) || "27".equals(""+session["siteId"])){%>
        <div class="ai-store-header mt-4">
            <g:render template="/resources/ebookFeatures"></g:render>
        </div>
        <%}%>
    </div>
</div>

<!-- Loading Spinner -->
<div class="loading-icon hidden">
    <div class="ai-loading">
        <div class="ai-loading-spinner"></div>
    </div>
</div>

<%if(aboutTitle!=null){%>
<script>
    document.getElementById("aboutDescription").innerHTML=document.getElementById("aboutDescription").innerText;
</script>
<%}%>

<%if(publisherDescription!=null&&!"".equals(publisherDescription)){%>
<script>
    document.getElementById("publisherDescription").innerHTML=document.getElementById("publisherDescription").innerText;
</script>
<%}%>

<style>
/* Additional styles for dummy covers in grid layout */
.dummy_cover {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    border: 1px solid #e9ecef;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
    border-radius: 0 6px 6px 0;
    background: white;
    position: relative;
    z-index: 1;
}

.dummy_cover-section {
    padding: 8px 12px;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: block !important;
}

.dummy_cover .top {
    flex: 1;
    font-weight: 600;
    font-size: 11px;
    color: #495057;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid #dee2e6;
}

.dummy_cover .middle {
    flex: 3;
    font-size: 13px;
    line-height: 1.3;
    padding: 12px;
    flex-direction: column;
    white-space: normal;
    overflow: hidden;
    text-align: center;
    display: flex;
    justify-content: center;
    align-items: center;
    color: white;
    font-weight: 600;
}

.dummy_cover .bottom {
    flex: 1;
    font-size: 11px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    color: #495057;
    display: flex;
    justify-content: center;
    align-items: center;
    font-weight: 600;
    padding: 8px 12px;
    border-top: 1px solid #dee2e6;
}

.dummy_cover_brand {
    font-size: 10px;
    font-weight: 500;
}

.dummy_cover_brand .gpt {
    color: #dc3545;
    font-weight: 700;
}

.dummy_cover_titleText{
    position: relative;
    font-weight: 600;
    line-height: 1.2;
}
</style>
