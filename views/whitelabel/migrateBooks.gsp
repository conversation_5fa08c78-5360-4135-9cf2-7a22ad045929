<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center">Migrate books</h3>
                    <div class="form-group" id="intrst-area">
                         <div class="form-group">
                            <label for="bookId"><strong>Book Ids (comma seperated)</strong></label>
                            <textarea id="bookId" class="form-control"></textarea>
                        </div>

                        <div class="form-group col-md-3">
                            <label for="siteId"><strong>Sites</strong></label>
                            <g:select id="siteId" class="form-control w-100" optionKey="id" optionValue="clientName"
                                      value="" name="siteId" from="${sitesList}" noSelection="['':'All']"/>
                        </div>
                        <button onclick="transferBooks()">Transfer</button>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<script>
    function transferBooks() {
        var bookIds = $('#bookId').val();
        var siteId = $('#siteId').val();
        if (bookIds == '') {
            alert('Please enter book ids');
            return;
        }
        if (siteId == '') {
            alert('Please select site');
            return;
        }
        $('.loading-icon').removeClass('hidden');
        var data = {
            bookIds: bookIds,
            siteId: siteId,
            mode:"submit"
        };
        $.ajax({
            url: '/whitelabel/migrateBooks',
            type: 'POST',
            data: data,
            success: function (response) {
                $('.loading-icon').addClass('hidden');
                alert(response.status);
            }
        });
    }
</script>
