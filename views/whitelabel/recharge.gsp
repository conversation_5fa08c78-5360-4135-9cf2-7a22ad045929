<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}

@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
</style>
<style>
.pricing-card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
    height: 100%;
}

.pricing-card:hover {
    transform: translateY(-10px);
}

.card-header {
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
}

.card-price {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.card-price small {
    font-size: 1rem;
}

.btn-add-to-cart {
    background-color: #5a67d8;
    color: #fff;
}

.btn-add-to-cart:hover {
    background-color: #434190;
}

.pricing-card .list-group-item {
    border: none;
}

.card-header-free {
    background-color: #3b82f6; /* Light Blue */
}

.card-header-lite {
    background-color: #a78bfa; /* Light Purple */
}

.card-header-pro {
    background-color: #fb7185; /* Light Coral */
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <h3 class="text-center rechargeTitle">Recharge Doubt Tokens</h3>
                    <div class="container my-5">
                        <div class="row">
                            <%String[] headerColor=["#3b82f6","#a78bfa","#fb7185","#3b82f6","#a78bfa","#fb7185"]
                             int i=0
                            rechargeOptions.each{ rechargeOption->%>
                            <!-- Free Plan -->
                            <div class="col-lg-4 mb-4">
                                <div class="card pricing-card">
                                    <div class="card-header text-center" style="background-color: ${headerColor[i]}">
                                        ${(""+rechargeOption.description).replaceAll("<p>","").replaceAll("</p>","")}
                                    </div>
                                    <div class="card-body text-center">
                                        <p class="card-price">₹${rechargeOption.sell_price} <small>/ ${rechargeOption.free_chat_tokens} doubts</small></p>

                                        <a href="javascript:addToCartFromDtl('${rechargeOption.id}','recharge')" class="btn btn-add-to-cart mt-3">Add to cart</a>
                                    </div>
                                </div>
                            </div>
                            <%i++
                            }%>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>




<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>

<g:render template="/wsshop/cartScripts"></g:render>
<g:render template="/wsshop/searchScripts"></g:render>
