<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<section class="page-main-wrapper mdl-js homepage pt-4">
    <div class="container main-content mb-5">

        <div class="row main-heading pt-5 pb-md-4 mb-3">
            <div class="col-md-12 text-md-center">
                <h1 class="mb-3">eBooks Revolution Partners</h1>
            </div>
        </div>

        <div class="row products-list home-institutes-list mx-0" id="recentInstitutes">

        </div>

    </div>
</section>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<script>
    function getLatestReleasedInstitutes(){
        <g:remoteFunction controller="institute" action="getLatestReleasedInstitutes"  onSuccess='displayInstitutes(data);'
            params="'limit=max'"     />
    }

    function displayInstitutes(data){
        var institutes = JSON.parse(data.institutes);
        var htmlStr="";
        for(var i=0;i<institutes.length;i++){
            htmlStr +="<div class=\"col-md-6 col-lg-3\">\n" +
                "      <a href='https://"+institutes[i].urlname+".wonderslate.com' target='_blank'>   <div class=\"product-info px-4 py-5\">\n" +
                "<img class=\"pub-logo\" width=\"auto\" height=\"100\" src=\"/institute/instituteImage?instituteId="+institutes[i].id+"\" />"+
                "                    <h5 class=\"mb-3\">"+institutes[i].name+"</h5>\n" +
                "                    <p>"+institutes[i].town+"</p>\n" +
                "                </div></a>\n" +
                "            </div>";
        }
        document.getElementById("recentInstitutes").innerHTML =  htmlStr;
    }

    $(document).ready(function(){
        getLatestReleasedInstitutes();
    })
</script>
<g:render template="/books/footer_new"></g:render>
