<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>

<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js p-md-5 order-management">
    <div class="container">
        <div class="d-flex justify-content-center align-items-center mt-5 mt-md-0">
            <h3><strong>User Management</strong></h3>
        </div>
        <form class="card card-modifier card-shadow border-0 col-md-8 mx-auto mt-4 p-4 p-lg-5" id="orderManagement" name="orderManagement">

            <div class="form-group col-md-10 mx-auto">
                <input type="text" class="form-control" name="userName" id="userName" placeholder="Name">
            </div>
            <div class="form-group col-md-10 mx-auto">
                <input type="tel" class="form-control" name="mobile" id="mobile" placeholder="Mobile Number" minlength="10" maxlength="10" pattern="[0-9]*" oninput="numberOnly(this.id)">
            </div>
            <div class="form-group col-md-10 mx-auto">
                <input type="text" class="form-control" name="email" id="userEmail" placeholder="Email">
            </div>
            <div class="form-group col-md-10 mx-auto">
                <input type="text" class="form-control" name="city" id="city" placeholder="Town / Village">
            </div>
            <div class="form-group col-md-10 mx-auto">
                <input type="text" class="form-control" name="pincode" id="pincode" placeholder="Pin code" oninput="numberOnly(this.id)">
            </div>


            <div class="form-group col-md-10 mx-auto">
                <a class="btn btn-primary btn-primary-modifier mb-3" href="javascript:addOrder();">Add User</a>&nbsp;&nbsp;&nbsp;
                <a class="mb-3" href="javascript:clearFields();">Clear</a>
            </div>
            <div class="form-group col-md-10 mx-auto">
                <div class="alert alert-sm alert-primary p-2" id="addOrderResult" style="display: none"></div>
            </div>
            <div class="form-group col-md-10 mx-auto">
                <a class="btn btn-primary btn-primary-modifier mb-3" href="javascript:showAllUsers();">Show Users</a>&nbsp;&nbsp;&nbsp;

            </div>
        </form>
        <div id="batchUsers" style="display: none"></div>
        <div style="margin-right: 25px; display: none;" id="download">
            <div class="form-group">
                <button type="button" id="download-btn" class="btn btn-primary " style="border-width: 0px;"  onclick="downloadUsers()">Download</button>
            </div>
        </div>
    </div>
</section>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>


    function addOrder() {
        $("#addOrderResult").html("").hide();
       if (document.getElementById("userName").value == "") {
            alert("Please enter name.");
            document.getElementById("userName").focus();
            $("#orderName").addClass("input-error");
        } else if (document.getElementById("mobile").value == "") {
            alert("Please enter mobile number.");
            document.getElementById("mobile").focus();
            $("#mobile").addClass("input-error");
        } else {
            var name = document.getElementById("userName").value;
            var mobile = document.getElementById("mobile").value;
            var email = document.getElementById("userEmail").value;
            var city = document.getElementById("city").value;
            var pincode = document.getElementById("pincode").value;

            <g:remoteFunction controller="institute" action="createUserAndAddToLibrary" params="'name='+name+'&mobile='+mobile+'&email='+email+'&city='+city+'&pincode='+pincode+'&instituteId=${instituteId}' " onSuccess = "userAdded(data);"/>
            $('.loading-icon').removeClass('hidden');
        }
    }

    function userAdded(data){
        $('.loading-icon').addClass('hidden');
        document.getElementById("addOrderResult").innerHTML="<p> User added. Login id="+data.user+" and password="+data.user+"</p>";
        $("#addOrderResult").show();
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }


    $('#orderName').on('keyup', function () {
        $(this).removeClass('input-error');
        $("#addOrderResult").html("").hide();
    });
    $('#mobile').on('keyup', function () {
        $(this).removeClass('input-error');
        $("#addOrderResult").html("").hide();
    });


    function clearFields(){

        document.getElementById("userName").value="";
        document.getElementById("mobile").value="";
        document.getElementById("userEmail").value="";
        document.getElementById("city").value="";
        document.getElementById("pincode").value="";

    }

    function showAllUsers(){

        $('.loading-icon').removeClass('hidden');
        <g:remoteFunction controller="institute" action="showAllUsersForBatch" params="'instituteId=${instituteId}&batchId=${defaultBatchId}'+'&userType=student'" onSuccess = "showUsersForBatch(data);"/>

    }

    function showUsersForBatch(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr="<h3>Users of this library</h3><br>\n" +
            "<table class='table table-striped table-bordered'>\n" +
            "<tr>\n" +
            "<th>Name</th>\n" +
            "<th>Email</th>\n"
            htmlStr+="<th>Mobile</th>\n"
            htmlStr+=" <th>Delete</th>\n"
        htmlStr+= " </tr>\n" ;

        if(data.status=="OK"){
            var users = data.users;
            for(i=0;i<users.length;i++){
                var username =users[i].username;
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+(users[i].email?users[i].email: '')+"</td>"+
                "<td>"+(users[i].mobile?users[i].mobile: '')+"</td>"
                htmlStr+=    "<td><a href='javascript:deleteUser("+users[i].userId+","+data.batchId+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt='wonderslate'></a></td>"

                htmlStr+=    "</tr>";
            }
            htmlStr +="</table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
            $('#download').show();
        }else{
            $('#download').hide();
            document.getElementById("batchUsers").innerHTML= "No students added to this institute yet";
        }
        $("#batchUsers").show();

    }

    function deleteUser(userId,batchId){
        if(confirm("Do you want to go ahead and delete this user from the library?")) {
            $('.loading-icon').removeClass('hidden');
            <g:remoteFunction controller="institute" action="removeUserFromBatch" params="'batchId='+batchId+'&userId='+userId" onSuccess = "userDeleted(data);"/>
        }
    }


    function userDeleted(data){
        $('.loading-icon').addClass('hidden');
        $("#addOrderResult").hide();
        document.getElementById("batchUsers").innerHTML= "";
        alert("Deleted Successfully");

    }

    function downloadUsers(){
        window.location.href = "/institute/getBooksForBatch?download=true&batchId=${defaultBatchId}";
    }

</script>
