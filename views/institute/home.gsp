<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/books/navheader_new"></g:render>
<asset:stylesheet href="groups/groupsStyle.css" async="true"/>
<style>
.notification{
    position: relative;
}
.notificationCount{
    position: absolute;
    left: 70%;
    top: 10%;
    background: red;
    width: 15px;
    height: 15px;
    text-align: center;
    border-radius: 50%;
    color: #fff;
    font-size: 10px;
}
.post-card{
    transition: all .2s ease;
}

.post-card:hover{
    transform: scale(1.05);

}
.backgrouncard {
    height: 71px;
    width: 90%;
    background-size: cover;
    background-repeat: no-repeat;
    margin:10px;
    border-radius:5px;
}
</style>
<script>
    $('link[data-role="baseline"]').attr('href', '');
</script>

<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>

<section class="page-main-wrapper mdl-js pb-5 pt-0 db-main">
    <!-- General -->
    <div class="container pt-4 pt-md-5 mt-3 mt-md-0">
        <div class="d-flex align-items-start welcome-user mb-3 mb-lg-4">
            <button id="goBack" class="material-icons border-0 mr-2 go-back-btn" onclick="javascript:window.history.back();">keyboard_backspace</button>
            <div class="mdl-tooltip" data-mdl-for="goBack">Back</div>
            <h3>
                <strong>${instituteName}</strong>
            </h3>
        </div>
        <!-- Mobile Top Menus -->
        <div class="top_main_mobile__menu d-flex d-md-none">
            <a href="/test-generator" class="mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col">
                <img src="${assetPath(src: 'ws/icon-menu-create-test.svg')}"> Create Test
            </a>
            <a href="/usermanagement/orders" class="mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col">
                <img src="${assetPath(src: 'ws/icon-menu-order-history.svg')}"> Orders History
            </a>
            <a href="/doubts" class="mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col">
                <img src="${assetPath(src: 'ws/icon-menu-doubts.svg')}"> Doubts
            </a>
            <a href="/ebooks" class="mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect col">
                <img src="${assetPath(src: 'ws/icon-menu-search.svg')}"> Search
            </a>
        </div>

        <div class="card card-modifier card-shadow border-0 db-common-info db-general col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <sec:ifAllGranted roles="ROLE_INSTITUTION_DEMO">
                    <%if(isLibrarian){%>
                    <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                        <a href="/instituteDashboard" target="_blank">
                            <asset:image src="/ws/icon-db-notification-management.svg"></asset:image>
                            <p>Dashboard</p>
                        </a>
                    </div>
                    <%}%>
                </sec:ifAllGranted>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/wsLibrary/myLibrary?instituteId=${instituteId}">
                        <asset:image src="/ws/icon-db-my-books.svg"></asset:image>
                        <p>Library</p>
                    </a>
                </div>
                 <%if(isEduWonder){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/doubts?instituteId=${instituteId}">
                        <asset:image src="/ws/Doubts_icon.png"></asset:image>
                        <p>Doubts</p>
                    </a>
                </div>
                <%}%>
                <%if(isLibrarian||(isInstructor&&isEduWonder)){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publishing-desk">
                        <asset:image src="/ws/icon-db-publishing-desk.svg"></asset:image>
                        <p>Publishing Desk</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/resources/createPdfBook">
                        <asset:image src="/ws/icon-db-current-affairs.svg"></asset:image>
                        <p>Create PDF book</p>
                    </a>
                </div>
                <%}%>
                <%if(isLibrarian){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/publishing-sales">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Sales</p>
                    </a>
                </div>
                <%}%>

            </div>
        </div>
    </div>
    <%if(isLibrarian){%>
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Manage</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-admin col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">
                <%if(!ipRestricted){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/userManagement?accessMode=users">
                        <asset:image src="/ws/icon-db-migrate-user.svg"></asset:image>
                        <p>Students</p>
                    </a>
                </div>
                <%}%>
                <%if(isEduWonder){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/userManagement?userType=Instructors&accessMode=users">
                        <asset:image src="/ws/icon-db-doubts-admin.svg"></asset:image>
                        <p>Instructors</p>
                    </a>
                </div>
                <%}%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/userManagement?accessMode=books">
                        <asset:image src="/ws/icon-db-library-management.svg"></asset:image>
                        <p>Books</p>
                    </a>
                </div>
                <%if(!ipRestricted){%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/manageClasses?instituteId=${params.instituteId}">
                        <asset:image src="/ws/icon-db-eclass-plus.svg"></asset:image>
                        <p>Classes</p>
                    </a>
                </div>
                <%}%>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/userManagement?accessMode=waitingList">
                        <asset:image src="/ws/icon-db-todo-list.svg"></asset:image>
                        <p>Waiting List</p>
                    </a>
                </div>
                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/manageInstitutePage?instituteId=${params.instituteId}">
                        <asset:image src="/ws/icon-db-institutes.svg"></asset:image>
                        <p>Institute Page</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <%}%>


    <!-- Report -->
    <%if(isLibrarian){%>
    <div class="container mt-5">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Reports</strong></h5>
        </div>
        <div class="card card-modifier card-shadow border-0 db-common-info db-admin db-report col-12 py-4 px-2 px-md-4">
            <div class="row mx-0">

                <div class="card text-center col-6 col-md-4 col-lg-3 col-xl-2">
                    <a href="/institute/usageReportInstituteAdmin">
                        <asset:image src="/ws/icon-db-sales.svg"></asset:image>
                        <p>Report</p>
                    </a>
                </div>
            </div>
        </div>
    </div>
    <%}%>

    <div class="container mt-5" id="classrooms" style="display:none;">
        <div class="db-section-title my-3">
            <h5 class="text-secondary text-secondary-modifier"><strong>Classrooms</strong></h5>
        </div>
    <div id="groupsList">

    </div>
    <div id="showMoreBtn" class="text-center mt-4">
        <a id="showMoreMyGroups" href="javascript:showMoreMyGroups();" class="btn btn-success" style="display: none;">Show More</a>

    </div>
    </div>
    
</section>

<section class="groups-listing">
    <div class="container width-size">


    </div>
</section>
<g:render template="/books/footer_new"></g:render>

<script>
    $(document).ready(function () {
        $(".loading-icon").removeClass("hidden");
        $(".db-common-info").each(function () {
            var cardsCount = $(this).find('.card').size();
            if(cardsCount === 1) {
                $(this).removeClass("col-12").addClass("col-6 col-md-4 col-lg-3 col-xl-2 single-card");
                $(this).find(".card").removeClass("col-6 col-md-4 col-lg-3 col-xl-2").addClass("col-12");
            } else if (cardsCount === 2) {
                $(this).addClass("col-md-8 col-lg-6 col-xl-4 two-cards");
                $(this).find(".card").removeClass("col-md-4 col-lg-3 col-xl-2").addClass("col-md-6");
            } else if (cardsCount === 3) {
                $(this).addClass("col-md-12 col-lg-9 col-xl-6 three-cards");
                $(this).find(".card").removeClass("col-lg-3 col-xl-2");
            } else if (cardsCount === 4) {
                $(this).addClass("col-md-12 col-lg-12 col-xl-8 four-cards");
                $(this).find(".card").removeClass("col-xl-2");
            } else {
                $(this).addClass("col-12 above-four-cards");
            }
        });
        $(".db-common-info .card a, .top_main_mobile__menu a").on('click', function () {
            $(".loading-icon").removeClass("hidden");
        });
    });

    window.addEventListener('load', function() {
        $(".loading-icon").addClass("hidden");
    }, false);

    window.addEventListener( "pageshow", function () {
        $(".loading-icon").addClass("hidden");
    }, false);
</script>
<script>
    function myGroups(){
        $(".loading-icon").removeClass("hidden");
        <g:remoteFunction controller="groups" action="getMyGroupsList" params="'start=0&length=20'" onSuccess='showGroupsList(data);'/>
    }

    function showGroupsList(data) {
        $(".loading-icon").addClass("hidden");
        var list = data.groupsList;



        var htmlStr = "";
        var onlyChannel= "true";
        if (data.status == "success") {
            htmlStr += "<div class='posts-cards mt-4'>";
            for(var i=0;i<list.length;i++) {
                if (list[i].groupType == "channel") {
                    htmlStr += "<div class='post-card' >";
                    onlyChannel="false";
                    if (list[i].postCount > 0) {
                        htmlStr += "<span class=\"post-pending\">" + list[i].postCount + "</span>";
                    }


                    htmlStr += "<div class=\"post-card-body d-flex justify-content-between\">\n" +
                        "<p onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='cursor: pointer;'>" + list[i].groupName + "</p>\n";


                    htmlStr += "</div>" +
                        "<div class=\"divider\"onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='cursor: pointer;'></div>";

                    htmlStr += "<div class=\"post-card-footer d-flex align-items-center justify-content-between\" onclick=\"javascript:goInsideGroup('/groups/groupDtl','" + list[i].id + "');\" class=\"mr-2 group-title\" style='cursor: pointer;'>\n" +
                        "<div class=\"members d-flex align-items-center\">" +
                        "<img src=\"${assetPath(src: 'groups/people_black_24dp.svg')}\" class=\"mr-2\">\n" +
                        "<p>" + list[i].membersCount + "</p>" +
                        "</div>" +
                        "<div class=\"pb-pr d-flex align-items-center\">\n";



                    htmlStr +=  "</div>" +
                        "</div>";

                    htmlStr += "</div>";

                }
            }
            htmlStr += "</div>";
            document.getElementById("groupsList").innerHTML = htmlStr;
            $("#classrooms").show();
            $("#topcreateBtn").show();
            if(onlyChannel=="true" && list.length>0){
                document.getElementById("groupsList").innerHTML = noGroupsUILayer();
                $("#topcreateBtn").hide();
            }
        } else {
            document.getElementById("groupsList").innerHTML = noGroupsUILayer();
            $("#topcreateBtn").hide();
        }

        if(list.length<20) {
            $("#showMoreMyGroups").hide();
        } else {

                $("#showMoreMyGroups").show();
        }
    }
    function noGroupsUILayer() {
        var htmlStr = "<div class='create-group mt-5 pb-5'><p>Currently you are not part of any classroom. <br>Contact your institute admin for help.</p>\n"+
            "</div>";
        return htmlStr;
    }

    function goInsideGroup(url,id) {
        window.location.href = '/classroom/${instituteId}/'+id;
    }
    <%if(isEduWonder){%>
    myGroups();
    <%}%>
</script>

</body>
</html>
