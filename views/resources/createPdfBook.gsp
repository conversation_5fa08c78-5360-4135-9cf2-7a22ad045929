<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:stylesheet href="wonderslate/ebookDtl.css" async="true"/>
<section class="dashboard_section">
    <div class="container">
        <div class="pt-5 pb-4" id="relatedBooksContainer">
            <h4>Create eBook from pdf</h4>
        </div>
          <g:uploadForm name="resource3Form" url="[action:'addFile',controller:'resourceCreator']"  method="post">
         <div class=" align-items-center form-fields" id="titleSection" >

            <div class="form-group">

                <label>EBOOK TITLE</label>
                <g:textField id="resourceName" class="form-control" name="resourceName"  placeholder="Enter Resource Name" size="50" autocomplete="off" />
                <div class="alert-thin col-12 text-left red invalid-feedback "  id="notesUploadAlert">
                  Enter eBook title.
                </div>
            </div>
        </div>
        <div class="align-items-center form-fields" id="fileSection" >
            <div class="form-group">
                <label>UPLOAD PDF</label>

                <input id="file3" type="file" class="form-control " name="file"  accept=".pdf" />
            </div>
            <div class="alert-thin col-12 text-left red invalid-feedback" id="UploadSizeAlert">
                File size exceeds 25mb.
            </div>
            <div class="alert-thin col-12 text-left red invalid-feedback" id="UploadPdfFile">
               Select the PDF File.
            </div>
        </div>

        <div class="mt-2 pl-4 pt-4" id="actionButtons">
            <button type="button" onclick="javascript:history.back();" class="btn btn-light col-md-2 col-lg-1 col-6" id="cancelBtn" style="background-color: lightgray;"  >Cancel</button>
            <button type="button" onclick="javascript:uploadNotes()" class="btn btn-primary col-md-2 col-lg-1  col-6">Submit</button>
        </div>


        <input type="hidden" name="resourceType" value="Notes">
        <input type="hidden" name="useType" value="notes">
        <input type="hidden" name="chapterId">
        <input type="hidden" name="bookId">
        <input type="hidden" name="from" value="pdfBookCreationPage">
      </g:uploadForm>
    </div>
</section>



<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>




<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>

    var validFileSize = true;
    var bookId;
    $('#file3').change(function(event) {
        var _size = this.files[0].size;
        if(_size >= 25000000){
            validFileSize = false;
        }else{
            validFileSize = true;
        }
    });
    function uploadNotes(){
        $("#notesUploadAlert").hide(500);
        $("#UploadSizeAlert").hide(500);
        if(document.resource3Form.resourceName.value==""){
            document.getElementById("notesUploadAlert").innerHTML="Enter eBook title.";
            $("#notesUploadAlert").show(500);
            $('#resourceName').removeClass('input-success').addClass('input-error').focus();
            $('#resourceName').on('keyup keypress', function () {
                $('#notesUploadAlert').hide();
                $(this).removeClass('input-error').addClass('input-success');
            });
        }else if( document.getElementById("file3").files.length == 0 ){
            document.getElementById("UploadPdfFile").innerHTML="Select the PDF file.";
            $("#UploadPdfFile").show(500);
            $('#file3').removeClass('input-success').addClass('input-error').focus();
            $('#file3').on('keyup keypress', function () {
                $('#UploadPdfFile').hide();
                $(this).removeClass('input-error').addClass('input-success');
            });
        } else if(!validFileSize) {
            $("#UploadSizeAlert").show(500);
            $('#file3').removeClass('input-success').addClass('input-error').focus();
            $('#file3').on('keyup keypress', function () {
                $('#UploadSizeAlert').hide();
                $(this).removeClass('input-error').addClass('input-success');
            });
        }
        else {

            $('.loading-icon').removeClass('hidden');
            var title = encodeURIComponent(document.resource3Form.resourceName.value);
            <g:remoteFunction controller="wonderpublish" action="pdfBookCreate" params="'publisherId=${session["userdetails"].publisherId}&columnValue='+title" onSuccess="bookCreated(data)"/>
        }
    }

    function bookCreated(data){


        document.resource3Form.bookId.value = data.bookId;
        document.resource3Form.chapterId.value = data.chapterId;
        bookId = data.bookId;
        var form = $("#resource3Form");
        console.log("which one");
        var formData = new FormData(document.resource3Form);
        console.log("which two");
        var url = form.attr('action');

        $.ajax({
            type: "POST",
            url: url,
            data: formData,
            processData: false,
            contentType: false,
            success: function(data)
            {
                $("#titleSection").hide();
                $("#fileSection").hide();
                document.getElementById("notesUploadAlert").innerHTML="Congratulations, your eBook has been created.";
                $("#notesUploadAlert").show(500);
                document.getElementById("actionButtons").innerHTML="<button type=\"button\" onclick=\"goBack();\" class=\"btn btn-light\">Close</button>\n" +
                    "            <button type=\"button\" onclick=\"javascript:openBook("+bookId+")\" class=\"btn btn-primary\">Open Book</button>";
                $('.loading-icon').addClass('hidden');
            }
        });

    }

    function goBack(){
        if ('referrer' in document) {
            window.location = document.referrer;
            /* OR */
            //location.replace(document.referrer);
        } else {
            window.history.back();
        }
    }

    function openBook(bookId){
      window.location.href="/resources/ebook?bookId="+bookId;
    }

    $(document).ready(function(){
        document.getElementById("resourceName").focus();
    });
</script>
