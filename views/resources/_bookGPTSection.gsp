    <style>
    body {
        background-color: #f8f9fa;
    }
    .bookDetails__container{
        width:100% !important;
    }
    .pricing-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
        transition: transform 0.3s;
        display: flex;
        flex-direction: column;
        background-color: #fff;
        margin: 10px;
        padding: 20px;
        min-height: 450px;
        flex: 1;
    }
    .pricing-card:hover {
        transform: scale(1.05);
    }
    .pricing-card h4 {
        margin-top: 15px;
        margin-bottom: 20px;
        font-size: 16px;
        font-weight: 600;
    }
    .pricing-card .card-body {
        padding: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
    }
    .pricing-card .card-footer {
        background: none;
        border-top: none;
        text-align: center;
        margin-top: auto;
    }
    .book-section {
        background-color: #fff;
        padding: 40px 20px;
        border-radius: 10px;
        margin-bottom: 20px;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
    }
    .book-description p {
        margin: 0;
    }
    .show-more {
        color: blue;
        cursor: pointer;
    }
    .most-popular {
        border: 2px solid orange;
        position: relative;
    }
    .most-popular::before {
        content: "Most Popular";
        position: absolute;
        top: -20px;
        left: 50%;
        transform: translateX(-50%);
        background-color: orange;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 14px;
    }
    .btn-preview {
        display: block;
        margin: 20px auto;
        background-color: #F79420;
        color: white;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        text-align: center;
        cursor: pointer;
    }
    .video-section {
        text-align: center;
        margin: 40px 0;
    }
    .video-section h2 {
        margin-bottom: 20px;
    }
    .breadcrumbs {
        font-size: 14px;
        margin-bottom: 20px;
        color: grey;
    }
    .breadcrumbs a {
        color: grey;
        text-decoration: none;
    }
    .breadcrumbs a:hover {
        text-decoration: underline;
    }
    .publisher-link {
        color: grey;
        text-decoration: none;
    }
    .publisher-link:hover {
        text-decoration: underline;
    }
    .highlight {
        background-color: yellow;
        font-weight: bold;
    }
    .pricing-card ul {
        padding-left: 20px;
    }
    .pricing-card ul li {
        margin-bottom: 10px;
    }
    .related-books {
        margin: 40px 0;
    }
    .related-books h2 {
        margin-bottom: 20px;
        text-align: center;
    }
    .related-books .book-cover {
        margin: 10px;
    }
    .digital-icon {
        color: #007bff;
        font-size: 24px;
        margin-bottom: 5px;
    }
    .physical-icon {
        color: #28a745;
        font-size: 24px;
        margin-bottom: 5px;
    }
    .testimonial-section {
        background-color: #f8f9fa;
        padding: 40px 20px;
        text-align: center;
    }
    .testimonial-section h2 {
        margin-bottom: 40px;
    }
    .testimonial-container{
        display: flex;
        overflow-x: auto;
        padding-bottom: 10px;
        scroll-behavior: smooth;
    }
    .testimonial-card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        margin: 10px;
        padding: 20px;
        background-color: #fff;
        display: inline-block;
        flex: 0 0 500px;
    }
    .testimonial-card img {
        border-radius: 50%;
        width: 50px;
        height: 50px;
        object-fit: cover;
        margin-bottom: 10px;
        border: 1px solid rgba(0, 0, 0, 0.1);
        padding: 5px;
    }
    .testimonial-card p {
        font-style: italic;
        color: #555;
    }
    .testimonial-card .user {
        margin-top: 10px;
        font-weight: bold;
    }
    .price-details {
        text-align: center;
        margin-top: auto;
        margin-bottom: 20px;
    }
    .list-price {
        text-decoration: line-through;
        color: #999;
        font-size: 14px;
    }
    .price {
        color: #000;
        font-weight: bold;
        font-size: 24px;
        display: block;
    }
    .discount {
        color: red;
        font-weight: bold;
        font-size: 16px;
    }
    .card-footer {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    @media (max-width: 767px) {
        .pricing-card {
            width: 100%;
        }
        .testimonial-card {
            width: 100%;
            margin-bottom: 20px;
        }
    }
    </style>
    <style>
    .copy-container {
        display: flex;
        align-items: center;
        margin-top: 20px;
    }

    .affiliation-label {
        font-weight: bold;
        margin-right: 10px;
    }

    #affiliation-text {
        padding: 5px;
        background-color: #f5f5f5;
        border: 1px solid #ccc;
        border-radius: 4px;
        margin-right: 10px;
        user-select: text; /* Allows the text to be selected */
    }

    .copy-btn {
        cursor: pointer;
        color: #007bff;
        font-size: 20px;
    }
    </style>
    <%
        String bookCoverImage = ""
        if(booksMst.coverImage!=null) {
            if(booksMst.coverImage.startsWith("http")){
                bookCoverImage = booksMst.coverImage
            }else{
                bookCoverImage = "/funlearn/showProfileImage?id="+booksMst.id+"&fileName="+booksMst.coverImage+"&type=books&imgType=webp"
            }
        }
        String bookDtlSEOTitle = (publisherNameForTitle!=null?publisherNameForTitle:"")+" "+booksMst.title
        String pulisherPageURL = ""
        if("true".equals(session["commonWhiteLabel"])){
            pulisherPageURL= "/sp/"+session["siteName"]+"/store?linkSource=bookDetail&publisher="+publisherName.replaceAll(" ","-")
        }else if("true".equals(session["prepjoySite"])){
            pulisherPageURL= "/"+session["entryController"]+"/eBooks?linkSource=bookDetail&publisher="+publisherName.replaceAll(" ","-")
        }else {
            pulisherPageURL= "/"+session["entryController"]+"/store?linkSource=bookDetail&publisher="+publisherName.replaceAll(" ","-")
        }

    %>

<div class="container">
    <!-- Book Section -->
    <div class="row book-section">
    <%if(session["userdetails"]!=null&&session["userdetails"].affliationCd!=null){%>
    <div class="copy-container">
        <span class="affiliation-label">Affiliation Link:</span>
        <div id="affiliation-text">${affiliationUrl}</div>
        <i class="far fa-copy copy-btn" id="copy-btn" title="Copy to clipboard"></i>
    </div>
    <%}%>
        <div class="col-md-12 breadcrumbs">
            <a href="javascript:openBreadcrumbLink('${booksTagDtl.level}','','');">${booksTagDtl.level}</a> >
            <a href="javascript:openBreadcrumbLink('${booksTagDtl.level}','${booksTagDtl.syllabus}','');">${booksTagDtl.syllabus}</a> >
            <a href="javascript:openBreadcrumbLink('${booksTagDtl.level}','${booksTagDtl.syllabus}','${booksTagDtl.grade}');">${booksTagDtl.grade}</a>
        </div>
        <div class="col-md-4 text-center">
            <% if(booksMst.coverImage!=null){%>
            <picture>
                <source media="(min-width: 768px)" srcset="${bookCoverImage}">
                <source media="(max-width: 767px)" srcset="${bookCoverImage}">
                <img src="${bookCoverImage}" alt="${booksMst.title}" width="276" height="375" style="border: 1px solid rgba(0,0,0,0.1);border-radius: 2px">
            </picture>
            <%}else{%>
            <div class="unknownCoverImg">
                <div class="unknownCoverImgTitle">
                    <p>${bookDtlSEOTitle}</p>
                </div>
            </div>
            <%}%>
            <a href="/prompt/bookgpt?bookId=${booksMst.id}" class="btn btn-preview" target="_blank">Try for Free</a>
        </div>
        <div class="col-md-8 gptBookInfoWrapper">
            <h2>${booksMst.title}</h2>
            <p><a href="${pulisherPageURL}" target="_blank" class="publisher-link">${publisherName}</a></p>
            <p>ISBN: ${booksMst.isbn}</p>
            <div class="book-description">
                <input value="${booksMst.description}" id="bookDescHidden" class="form-control hidden">
                <p id="description"></p>
                <span id="show-more" class="show-more" style="display:none;">Show More</span>
            </div>
        </div>
    </div>

    <!-- Pricing Section -->
    <div class="row">
    <%
        String bookType
        String finalPerVal
        String bookPrice=""
        String listPrice=""
        String noOfFreeTokens="250"
        String noOfEbookFreeToken = "10"
        bookPriceDtls.each{ bookPriceDtl ->
            if("eBook".equals(bookPriceDtl.bookType)){
                Float offerPrice = bookPriceDtl.sellPrice
                Float actualPrice = bookPriceDtl.listPrice!=null?bookPriceDtl.listPrice:bookPriceDtl.sellPrice;
                Double calculatedVal = actualPrice - offerPrice
                Double percentageVal = calculatedVal * 100 / actualPrice
                if(offerPrice>100) finalPerVal="Rs."+String.format("%.0f",calculatedVal)
                else finalPerVal=String.format("%.0f",percentageVal) +" %"
                if (offerPrice % 1 != 0) {
                    bookPrice=String.format("%.2f",offerPrice)
                } else {
                    bookPrice=String.format("%.0f",offerPrice)
                }
                if (actualPrice % 1 != 0) {
                    listPrice=String.format("%.2f",actualPrice)
                } else {
                    listPrice=String.format("%.0f",actualPrice)
                }
                if(bookPriceDtl.freeChatTokens!=null){
                    noOfEbookFreeToken=bookPriceDtl.freeChatTokens
                }
        %>
        <div class="col-md-4 d-flex">
            <div class="card pricing-card">
                <div class="card-body">
                    <i class="fas fa-tablet-alt digital-icon text-center"></i>
                    <h4 class="text-center">eBook with AI Doubts Solver</h4>
                    <ul class="text-left">
                        <li>Solve doubts (<span class="highlight">${noOfEbookFreeToken} free</span>)</li>
                        <li>Digital version of the book</li>
                        <li>Highlight and take notes directly in the text</li>
                        <li>Accessible on laptops, tablets, and mobile phones</li>
                        <li>Convenient and portable for on-the-go learning</li>
                    </ul>
                </div>
                <div class="price-details">
                    <span class="list-price">₹${listPrice}</span>
                    <span class="price">₹${bookPrice}</span>
                    <span class="discount">(${finalPerVal} off)</span>
                </div>
                <div class="card-footer">
                    <a href="javascript:addToCartFromDtl('${params.bookId}','eBook')" class="btn btn-primary">Add to cart</a>
                </div>
            </div>
        </div>
    <%}}
    bookPriceDtls.each{ bookPriceDtl ->
        if("bookGPT".equals(bookPriceDtl.bookType)){
            if(bookPriceDtl.freeChatTokens!=null){
                noOfFreeTokens=bookPriceDtl.freeChatTokens
            }
            Float offerPrice = bookPriceDtl.sellPrice
            Float actualPrice = bookPriceDtl.listPrice!=null?bookPriceDtl.listPrice:bookPriceDtl.sellPrice;
            Double calculatedVal = actualPrice - offerPrice
            Double percentageVal = calculatedVal * 100 / actualPrice
            if(offerPrice>100) finalPerVal="Rs."+String.format("%.0f",calculatedVal)
            else finalPerVal=String.format("%.0f",percentageVal) +" %"
            if (offerPrice % 1 != 0) {
                bookPrice=String.format("%.2f",offerPrice)
            } else {
                bookPrice=String.format("%.0f",offerPrice)
            }
            if (actualPrice % 1 != 0) {
                listPrice=String.format("%.2f",actualPrice)
            } else {
                listPrice=String.format("%.0f",actualPrice)
            }%>
        <div class="col-md-4 d-flex">
            <div class="card pricing-card most-popular">
                <div class="card-body">
                    <i class="fas fa-laptop-code digital-icon text-center"></i>
                    <h4 class="text-center">iBookGPT</h4>
                    <ul class="text-left">
                        <li>AI-powered interactive book</li>
                        <li>Explain concepts in detail</li>
                        <li>Solve doubts (<span class="highlight">${noOfFreeTokens} free</span>)</li>
                        <li>Create MCQs, Q&A, and flashcards</li>
                        <li>Generate question papers</li>
                        <li>Provide real-world examples</li>
                        <li>Offer tips and tricks for memorization</li>
                        <li>Unlimited use of GPT-powered options</li>
                    </ul>
                </div>
                <div class="price-details">
                    <span class="list-price">₹${listPrice}</span>
                    <span class="price">₹${bookPrice}</span>
                    <span class="discount">(${finalPerVal} off)</span>
                </div>
                <div class="card-footer">
                    <a href="javascript:addToCartFromDtl('${params.bookId}','bookGPT')" class="btn btn-primary">Add to cart</a>
                </div>
            </div>
        </div>
    <%}}
    bookPriceDtls.each{ bookPriceDtl ->
        if("printbook".equals(bookPriceDtl.bookType)){
            Float offerPrice = bookPriceDtl.sellPrice
            Float actualPrice = bookPriceDtl.listPrice!=null?bookPriceDtl.listPrice:bookPriceDtl.sellPrice;
            Double calculatedVal = actualPrice - offerPrice
            Double percentageVal = calculatedVal * 100 / actualPrice
            if(offerPrice>100) finalPerVal="Rs."+String.format("%.0f",calculatedVal)
            else finalPerVal=String.format("%.0f",percentageVal) +" %"
            if (offerPrice % 1 != 0) {
                bookPrice=String.format("%.2f",offerPrice)
            } else {
                bookPrice=String.format("%.0f",offerPrice)
            }
            if (actualPrice % 1 != 0) {
                listPrice=String.format("%.2f",actualPrice)
            } else {
                listPrice=String.format("%.0f",actualPrice)
            }%>
        <div class="col-md-4 d-flex">
            <div class="card pricing-card">
                <div class="card-body">
                    <i class="fas fa-book physical-icon text-center"></i>
                    <h4 class="text-center">Print Book (Paperback)</h4>
                    <ul class="text-left">
                        <li>Physical copy of the book</li>
                        <li>High-quality print for easy reading</li>
                        <li>Durable and portable</li>
                        <li>Perfect for those who prefer the traditional reading experience</li>
                    </ul>
                </div>
                <div class="price-details">
                    <span class="list-price">₹${listPrice}</span>
                    <span class="price">₹${bookPrice}</span>
                    <span class="discount">(${finalPerVal} off)</span>
                </div>
                <div class="card-footer">
                    <a href="javascript:addToCartFromDtl('${params.bookId}','printbook')" class="btn btn-primary">Add to cart</a>
                </div>
            </div>
        </div>
    <%}}%>
    <%bookPriceDtls.each{ bookPriceDtl ->
        if("ibookgptpro".equals(bookPriceDtl.bookType)){
            Float offerPrice = bookPriceDtl.sellPrice
            Float actualPrice = bookPriceDtl.listPrice!=null?bookPriceDtl.listPrice:bookPriceDtl.sellPrice;
            Double calculatedVal = actualPrice - offerPrice
            Double percentageVal = calculatedVal * 100 / actualPrice
            if(offerPrice>100) finalPerVal="Rs."+String.format("%.0f",calculatedVal)
            else finalPerVal=String.format("%.0f",percentageVal) +" %"
            if (offerPrice % 1 != 0) {
                bookPrice=String.format("%.2f",offerPrice)
            } else {
                bookPrice=String.format("%.0f",offerPrice)
            }
            if (actualPrice % 1 != 0) {
                listPrice=String.format("%.2f",actualPrice)
            } else {
                listPrice=String.format("%.0f",actualPrice)
            }%>
    <div class="col-md-4 d-flex">
        <div class="card pricing-card">
            <div class="card-body">
                <i class="fas fa-laptop-code digital-icon text-center"></i>
                <h4 class="text-center">iBookGPT Pro</h4>
                <ul class="text-left">
                    <li><span class="highlight">Unlimited doubts</span></li>
                    <li>AI-powered interactive book</li>
                    <li>Explain concepts in detail</li>
                    <li>Create MCQs, Q&A, and flashcards</li>
                    <li>Generate question papers</li>
                    <li>Provide real-world examples</li>
                    <li>Offer tips and tricks for memorization</li>
                    <li>Unlimited use of GPT-powered options</li>
                </ul>
            </div>
            <div class="price-details">
                <span class="list-price">₹${listPrice}</span>
                <span class="price">₹${bookPrice}</span>
                <span class="discount">(${finalPerVal} off)</span>
            </div>
            <div class="card-footer">
                <a href="javascript:addToCartFromDtl('${params.bookId}','ibookgptpro')" class="btn btn-primary">Add to cart</a>
            </div>
        </div>
    </div>
    <%}}%>
    </div>

    <!-- Video Section -->
    <div class="video-section">
        <h2>What is Book GPT</h2>
        <div class="embed-responsive embed-responsive-16by9">
            <iframe style="border-radius: 30px" class="embed-responsive-item" src="https://www.youtube.com/embed/lZ-WQjUdAV4?controls=1" allowfullscreen></iframe>
        </div>
    </div>

    <!-- Testimonials Section -->
    <div class="testimonial-section">
        <h2>What Our Users Say</h2>
        <div class="testimonial-container">
            <div class="testimonial-card">
                <img src='${assetPath(src: 'resource/userIcon.png')}' alt='user'>
                <p>"Book GPT has completely transformed my study routine. The AI-powered features make learning so much easier and more interactive."</p>
                <div class="user">- Aman Sharma</div>
            </div>
            <div class="testimonial-card">
                <img src='${assetPath(src: 'resource/userIcon.png')}' alt='user'>
                <p>"I love how Book GPT helps me create MCQs and flashcards on the go. It's a must-have for any student looking to enhance their learning experience."</p>
                <div class="user">- Priya Gupta</div>
            </div>
            <div class="testimonial-card">
                <img src='${assetPath(src: 'resource/userIcon.png')}' alt='user'>
                <p>"The doubt-solving feature is amazing! Having 250 free doubt resolutions has saved me so much time and stress."</p>
                <div class="user">- Rajesh Kumar</div>
            </div>
        </div>
    </div>

    <!-- Related Books Section -->
    <g:render template="/resources/relatedBooks"></g:render>
</div>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    var siteName = "";

    <%if("true".equals(session["commonWhiteLabel"])){%>
    siteName = "${session['siteName']}";
    <%}else{%>
    siteName = "${session['entryController']}";
    <%}%>
    $(document).ready(function() {
        const descriptionElement = document.getElementById('bookDescHidden')
        document.getElementById('description').innerHTML = descriptionElement.value;
    });

    function openBreadcrumbLink(level,syllabus,grade) {
        var paramLevel = replaceAll(level.replace('&', '~'),' ','-');
        var paramSyllabus = replaceAll(syllabus.replace('&', '~'),' ','-');
        var paramGrade = replaceAll(grade.replace('&', '~'),' ','-');

        <%if("true".equals(""+session["commonWhiteLabel"])){%>
        if(grade!="" && grade!=null) {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/sp/${session["siteName"]}/store?level="+paramLevel;
        }
        <%}else if (!"true".equals(session['prepjoySite'])){%>
        if(grade!="" && grade!=null) {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/${session["entryController"]}/store?level="+paramLevel;
        }
        <%} else {%>
        if(grade!="" && grade!=null) {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel+"&syllabus="+paramSyllabus+"&grade="+paramGrade;
        } else if(syllabus!="" && syllabus!=null) {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel+"&syllabus="+paramSyllabus;
        } else {
            window.location.href = "/"+siteName+"/eBooks?level="+paramLevel;
        }
        <%}%>
    }
    function getGPTRelatedBooks(){
        <g:remoteFunction controller="wsshop" action="getRelatedBooks" params="'bookId=${params.bookId}&fromApp=false&relatedBooks=true'" onSuccess='showGPTRelatedBooks(data);'/>
    }
    function showGPTRelatedBooks(data){
        var books = JSON.parse(data.books);
        var imgSrc = "";

        var colors=['#2EBAC6','#0D5FCE','#6FCF97','#F2C94C','#C20232','#FC7753','#E40039','#1abc9c','#FD7272','#55E6C1','#17c0eb'];

        for(var i=0; i<books.length&& i<=9; i++){
            imgSrc = books[i].coverImage;
            var ebookHtmlStr = "";
            ebookHtmlStr +=    "/" + replaceAll(books[i].title,' ','-') + "/ebook-details?siteName="+siteName+"&bookId=" + books[i].id + "&preview=true&publisher="+books[i].publisher ;
            if (books[i].coverImage!=null && books[i].coverImage.startsWith("https")) {
                imgSrc = books[i].coverImage;
                imgSrc = imgSrc.replace("~", ":");
            } else {
                imgSrc = "/funlearn/showProfileImage?id=" + books[i].id + "&fileName=" + books[i].coverImage + "&type=books&imgType=passport";
            }
            var bookCover=""
            if (books[i].coverImage === null || books[i].coverImage == "null" || books[i].coverImage == "") {
                bookCover = "<a href=\""+ebookHtmlStr+"\" target=\"_blank\">" +
                    "<div class='uncover'>"+
                    "<p style='color: #000 !important;'>"+books[i].title+"</p>"+
                    "</div>"+ "</a>";
            }
            else {
                bookCover = "<a href=\""+ebookHtmlStr+"\" target=\"_blank\"><img src='" + imgSrc + "' alt=\"Related Book\" class=\"img-fluid book-cover\" style=\"max-width: 150px;\"></a>";
            }
             $("#related-books-list").append(bookCover);
        }
    }
    <%if("true".equals(params.addToCart)){%>
    addToCartFromDtl('${booksMst.id}','${params.bookType}');
    <%}%>

    document.getElementById("copy-btn").addEventListener("click", function() {
        // Get the div text
        var copyText = document.getElementById("affiliation-text").innerText;

        // Create a temporary textarea to copy the text
        var tempInput = document.createElement("textarea");
        tempInput.value = copyText;
        document.body.appendChild(tempInput);

        // Select and copy the text
        tempInput.select();
        document.execCommand("copy");

        // Remove the temporary textarea
        document.body.removeChild(tempInput);

        // Optionally: Change the icon to indicate success (e.g., show a check mark)
        alert("Affiliation link copied to clipboard!");
    });
</script>
    <g:render template="/wsshop/cartScripts"></g:render>
    <g:render template="/wsshop/searchScripts"></g:render>


