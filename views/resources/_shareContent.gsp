<asset:javascript src="sharer.min.js"/>
<asset:stylesheet href="wonderslate/shareContent.css" async="true"/>
<!-- Modal -->
<div class="modal modal-modifier fade" id="shareContentModal" tabindex="-1" role="dialog" aria-labelledby="shareContentModalCenterTitle">
    <div class="modal-dialog modal-dialog-modifier modal-dialog-centered modal-dialog-share-content"  role="document">
        <div class="modal-content modal-content-modifier modal-share-content">

            <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">x</span>
            </button>

            <!-- Modal body -->
            <div class="modal-body modal-body-modifier d-flex align-items-center justify-content-center flex-column">
                <i class="material-icons-round text-success p-4" style="font-size: 50px;">check_circle_outline</i>
                <p class="linkShown" id="linkGenerated">http://tiny.cc/fx31tz</p>
                <p class="text-success pb-3 text-center" id="shareMsg">Your link to share has been copied.</p>
                <p style="color: rgba(68, 68, 68, 0.48);" class="pb-2">or share directly on</p>
                <div id="shareLinksContainer">
                    <a id="fbShare" data-sharer="facebook" target="_blank"><img class="p-2" src="${assetPath(src: 'ws/sharefb.svg')}"></a>
                    <a id="lnShare" data-sharer="linkedin" target="_blank"><img class="p-2" src="${assetPath(src: 'ws/linkedinshare.svg')}"></a>
                    <a id="twShare" data-sharer="twitter"  target="_blank"><img class="p-2" style="width:60px; height:60px;" src="${assetPath(src: 'ws/twShare.svg')}"></a>
                    <a id="waShare" data-sharer="whatsapp" target="_blank"><img class="p-2" src="${assetPath(src: 'ws/whatsappshare.svg')}"></a>
                </div>
%{--                <div id="studyGroupShare" class="text-center">--}%
%{--                    <p style="color: rgba(68, 68, 68, 0.48)" class="pb-2">or share it on</p>--}%
%{--                    <a id="groupShare" data-sharer="studygroup"><img class="p-2" src="${assetPath(src: 'groups/gpIcon.png')}"></a>--}%
%{--                    <p>Study groups</p>--}%
%{--                </div>--}%

                <div class="d-flex justify-content-end col-12 py-3 mt-3">
                    <button type="button" class="btn btn-lg btn-secondary btn-secondary-modifier btn-shadow mdl-button mdl-button-modifier mdl-js-button mdl-js-ripple-effect" data-dismiss="modal" aria-label="Close">Close</button>
                </div>

            </div>
        </div>
    </div>
</div>


<script>
    var appType = "${session['appType']}";

    function copyToClipboard (str){
            var el = document.createElement('textarea');
            el.value = str;
            el.setAttribute('readonly', '');
            el.style.position = 'absolute';
            el.style.left = '-9999px';
            document.body.appendChild(el);
            el.select();
            document.execCommand('copy');
            document.body.removeChild(el);
        };



    function openShareContentModal(source, url){
        url+="&shared=true";
        $("#shareContentModal").modal('show');
        $("#linkGenerated").text(url);
        $('#linkToCopy').attr("value", url);
        copyToClipboard(url)

        if(source === 'ebooks'){
            //var gpUrl = 'groups/index'
            $("#shareMsg").text("Your link to share eBooks has been copied.");

            if(appType == "" || appType == undefined || appType == null){
                $("#fbShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting eBooks found. Please click on the link to see the books!"});
                $("#lnShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting eBooks found. Please click on the link to see the books!"});
                $("#twShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting eBooks found. Please click on the link to see the books!"});
                $("#waShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting eBooks found. Please click on the link to see the books!"});
                //$("#groupShare").attr({"href":gpUrl,"data-url":gpUrl,"data-web":true,"data-title":"Interesting eBooks found. Please click on the link to see the books!"})
            } else if(appType == 'android') {

                $("#fbShare").attr("onclick", "callAndroidShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callAndroidShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callAndroidShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callAndroidShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"whatsapp\","+url+")");
            } else if(appType == 'ios'){
                $("#fbShare").attr("onclick", "callIosShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callIosShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callIosShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callIosShare(\"Interesting eBooks found. Please click on the link to see the books!\",\"whatsapp\","+url+")");
            }

        } else  if(source === 'flashcard'){
            //var gpUrl = 'groups/index'
            $("#shareMsg").text("Your link to share flashcard has been copied.");

            if(appType == "" || appType == undefined || appType == null){
                $("#fbShare").attr({"data-url": url, "data-web": true, "data-title": "Found this useful flashcard. Please click on the link to see the flashcard!"});
                $("#lnShare").attr({"data-url": url, "data-web": true, "data-title": "Found this useful flashcard. Please click on the link to see the flashcard!"});
                $("#twShare").attr({"data-url": url, "data-web": true, "data-title": "Found this useful flashcard. Please click on the link to see the flashcard!"});
                $("#waShare").attr({"data-url": url, "data-web": true, "data-title": "Found this useful flashcard. Please click on the link to see the flashcard!"});
                //$("#groupShare").attr({"href":gpUrl,"data-url":gpUrl,"data-web":true,"data-title":"Found this useful flashcard. Please click on the link to see the flashcard!"})
            } else if(appType == 'android') {

                $("#fbShare").attr("onclick", "callAndroidShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callAndroidShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callAndroidShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callAndroidShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"whatsapp\","+url+")");
            } else if(appType == 'ios'){
                $("#fbShare").attr("onclick", "callIosShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callIosShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callIosShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callIosShare(\"Found this useful flashcard. Please click on the link to see the flashcard!\",\"whatsapp\","+url+")");
            }

        } else  if(source === 'doubts'){
            //var gpUrl = 'groups/index'
            $("#shareMsg").text("Your link to share the discussion has been copied.");

            if(appType == "" || appType == undefined || appType == null){

                $("#fbShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting discussion found. Please click on the link to see the discussion!"});
                $("#lnShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting discussion found. Please click on the link to see the discussion!"});
                $("#twShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting discussion found. Please click on the link to see the discussion!"});
                $("#waShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting discussion found. Please click on the link to see the discussion!"});
                //$("#groupShare").attr({"href":gpUrl,"data-url":gpUrl,"data-web":true,"data-title":"Interesting discussion found. Please click on the link to see the discussion!"})
            }else if(appType == 'android') {
                $("#fbShare").attr("onclick", "callAndroidShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callAndroidShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callAndroidShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callAndroidShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"whatsapp\","+url+")");
            } else if(appType == 'ios'){
                $("#fbShare").attr("onclick", "callIosShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callIosShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callIosShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callIosShare(\"Interesting discussion found. Please click on the link to see the discussion!\",\"whatsapp\","+url+")");
            }
        }
        else  if(source === 'MCQ'){
            //var gpUrl = 'groups/index'
            $("#shareMsg").text("Your link to share the MCQ has been copied.");

            if(appType == "" || appType == undefined || appType == null){

                $("#fbShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting MCQ found. Please click on the link to see the MCQ!"});
                $("#lnShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting MCQ found. Please click on the link to see the MCQ!"});
                $("#twShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting MCQ found. Please click on the link to see the MCQ!"});
                $("#waShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting MCQ found. Please click on the link to see the MCQ!"});
                //$("#groupShare").attr({"href":gpUrl,"data-url":gpUrl,"data-web":true,"data-title":"Interesting mcq found.Please click on the link to see the mcq!"})
            }else if(appType == 'android') {
                $("#fbShare").attr("onclick", "callAndroidShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callAndroidShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callAndroidShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callAndroidShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"whatsapp\","+url+")");
            } else if(appType == 'ios'){
                $("#fbShare").attr("onclick", "callIosShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callIosShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callIosShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callIosShare(\"Interesting MCQ found. Please click on the link to see the MCQ!\",\"whatsapp\","+url+")");
            }
        }
        else  if(source === 'Notes'){
            //var gpUrl = 'groups/index'
            $("#shareMsg").text("Your link to share the Notes has been copied.");

            if(appType == "" || appType == undefined || appType == null){

                $("#fbShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Notes found. Please click on the link to see the Notes!"});
                $("#lnShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Notes found. Please click on the link to see the Notes!"});
                $("#twShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Notes found. Please click on the link to see the Notes!"});
                $("#waShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Notes found. Please click on the link to see the Notes!"});
                //$("#groupShare").attr({"href":gpUrl,"data-url":gpUrl,"data-web":true,"data-title":"Interesting Notes found. Please click on the link to see the Notes!"})
            }else if(appType == 'android') {
                $("#fbShare").attr("onclick", "callAndroidShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callAndroidShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callAndroidShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callAndroidShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"whatsapp\","+url+")");
            } else if(appType == 'ios'){
                $("#fbShare").attr("onclick", "callIosShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callIosShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callIosShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callIosShare(\"Interesting Notes found. Please click on the link to see the Notes!\",\"whatsapp\","+url+")");
            }
        }
        else  if(source === 'Videos'){
            //var gpUrl = 'groups/index'
            $("#shareMsg").text("Your link to share the Videos has been copied.");

            if(appType == "" || appType == undefined || appType == null){

                $("#fbShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Videos found. Please click on the link to see the Videos!"});
                $("#lnShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Videos found. Please click on the link to see the Videos!"});
                $("#twShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Videos found. Please click on the link to see the Videos!"});
                $("#waShare").attr({"data-url": url, "data-web": true, "data-title": "Interesting Videos found. Please click on the link to see the Videos!"});
                //$("#groupShare").attr({"href":gpUrl,"data-url":gpUrl,"data-web":true,"data-title":"Interesting Videos found. Please click on the link to see the Videos!"})
            }else if(appType == 'android') {
                $("#fbShare").attr("onclick", "callAndroidShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callAndroidShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callAndroidShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callAndroidShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"whatsapp\","+url+")");
            } else if(appType == 'ios'){
                $("#fbShare").attr("onclick", "callIosShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"facebook\","+url+")");
                $("#lnShare").attr("onclick", "callIosShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"linkedin\","+url+")");
                $("#twShare").attr("onclick", "callIosShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"twitter\","+url+")");
                $("#waShare").attr("onclick", "callIosShare(\"Interesting Videos found. Please click on the link to see the Videos!\",\"whatsapp\","+url+")");
            }
        }
        window.Sharer.init();
    };



    function callAndroidShare(messsage,socialMediaSite,dataUrl){
        JSInterface.shareEvent(messsage,socialMediaSite,dataUrl);
    }

    function callIosShare(messsage,socialMediaSite,dataUrl){
        var json = {'messsage':messsage,'socialMediaSite':socialMediaSite,'dataUrl':dataUrl};
        webkit.messageHandlers.shareEvent.postMessage(JSON.stringify(json));
    }

    function openShareContentModalGeneric(message, url) {
        $("#shareContentModal").modal('show');
        $("#linkGenerated").text(url);
        $("#linkGenerated").text(url);
        $('#linkToCopy').attr("value", url);
        $("#studyGroupShare").hide()
        copyToClipboard(url);

            $("#shareMsg").text("Your link to share has been copied.");

            if (appType == "" || appType == undefined || appType == null) {
                $("#fbShare").attr({
                    "data-url": url,
                    "data-web": true,
                    "data-title": message,
                });
                $("#lnShare").attr({
                    "data-url": url,
                    "data-web": true,
                    "data-title": message,
                });
                $("#twShare").attr({
                    "data-url": url,
                    "data-web": true,
                    "data-title": message,
                });
                $("#waShare").attr({
                    "data-url": url,
                    "data-web": true,
                    "data-title": message,
                });
            } else if (appType == 'android') {

                $("#fbShare").attr("onclick", "callAndroidShare(\""+message+"\",\"facebook\"," + url + ")");
                $("#lnShare").attr("onclick", "callAndroidShare(\""+message+",\"linkedin\"," + url + ")");
                $("#twShare").attr("onclick", "callAndroidShare(\""+message+"\",\"twitter\"," + url + ")");
                $("#waShare").attr("onclick", "callAndroidShare(\""+message+"\",\"whatsapp\"," + url + ")");
            } else if (appType == 'ios') {
                $("#fbShare").attr("onclick", "callIosShare(\""+message+"\",\"facebook\"," + url + ")");
                $("#lnShare").attr("onclick", "callIosShare(\""+message+"\",\"linkedin\"," + url + ")");
                $("#twShare").attr("onclick", "callIosShare(\""+message+"\",\"twitter\"," + url + ")");
                $("#waShare").attr("onclick", "callIosShare(\""+message+"\",\"whatsapp\"," + url + ")");
            }


        window.Sharer.init();
    }
</script>
