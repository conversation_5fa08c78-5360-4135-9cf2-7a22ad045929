<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<div class="container">
<h2>Delete Chapters</h2>
<label>BookID:</label>
<input type="text" id="bookId" name="bookId" placeholder="Enter book id"/>

<p>OR</p>
<label>ChapterID:</label>
<input type="text" id="chapterIds" name="chapterIds" placeholder="Comma seperated chapter ids" size="100"/>
</br>
<button id="submit" onclick="submit();">Submit</button>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    function submit() {
        var bookId = document.getElementById("bookId").value;
        var chapterIds = document.getElementById("chapterIds").value;
        if (!bookId && !chapterIds) {
            alert("Please enter either book id or chapter ids.");
        } else {
            <g:remoteFunction controller="resources" action="deleteBookChapters"  onSuccess='chapterDeleted(data);'
        params="'bookId='+bookId+'&chapterIds='+chapterIds"/>
        }

    }

    function chapterDeleted(data){
        alert(data.status);

    }

</script>

