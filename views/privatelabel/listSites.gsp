<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>
<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <div class="form-group">
                    <div class="container mt-5">
                        <h2>List of Sites</h2>
                        <hr/>

                        <div class="mb-3">
                            <!-- Link to create a new site -->
                            <g:link controller="privatelabel" action="addSite" class="btn btn-primary">Create New Site</g:link>
                        </div>

                        <table class="table table-bordered table-striped table-hover">
                            <thead>
                            <tr>
                                <th>
                                <!-- Clicking on Site Name header toggles sorting by 'siteName' -->
                                    <g:link controller="privatelabel" action="listSites" params="${[sort: 'siteName', order: currentSort == 'siteName' && currentOrder == 'asc' ? 'desc' : 'asc']}">
                                        Site Name
                                        <g:if test="${currentSort == 'siteName'}">
                                            <span class="ml-1">
                                                <g:if test="${currentOrder == 'asc'}">&uarr;</g:if>
                                                <g:if test="${currentOrder == 'desc'}">&darr;</g:if>
                                            </span>
                                        </g:if>
                                    </g:link>
                                </th>
                                <th>
                                <!-- Clicking on Client Name header toggles sorting by 'clientName' -->
                                    <g:link controller="privatelabel" action="listSites" params="${[sort: 'clientName', order: currentSort == 'clientName' && currentOrder == 'asc' ? 'desc' : 'asc']}">
                                        Client Name
                                        <g:if test="${currentSort == 'clientName'}">
                                            <span class="ml-1">
                                                <g:if test="${currentOrder == 'asc'}">&uarr;</g:if>
                                                <g:if test="${currentOrder == 'desc'}">&darr;</g:if>
                                            </span>
                                        </g:if>
                                    </g:link>
                                </th>
                                <th>
                                <!-- Clicking on ID header toggles sorting by 'id' -->
                                    <g:link controller="privatelabel" action="listSites" params="${[sort: 'id', order: currentSort == 'id' && currentOrder == 'asc' ? 'desc' : 'asc']}">
                                        ID
                                        <g:if test="${currentSort == 'id'}">
                                            <span class="ml-1">
                                                <g:if test="${currentOrder == 'asc'}">&uarr;</g:if>
                                                <g:if test="${currentOrder == 'desc'}">&darr;</g:if>
                                            </span>
                                        </g:if>
                                    </g:link>
                                </th>
                                <th>Actions</th>
                            </tr>
                            </thead>
                            <tbody>
                            <g:if test="${!siteList || siteList.size() == 0}">
                                <tr>
                                    <td colspan="4" class="text-center">No sites found.</td>
                                </tr>
                            </g:if>
                            <g:each in="${siteList}" var="site">
                                <tr>
                                    <td>${site.siteName}</td>
                                    <td>${site.clientName}</td>
                                    <td>${site.id}</td>
                                    <td>
                                        <!-- Edit link -->
                                        <g:link controller="privatelabel" action="addSite" params="[id: site.id]" class="btn btn-sm btn-secondary">Edit</g:link>
                                    </td>
                                </tr>
                            </g:each>
                            </tbody>
                        </table>
                    </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
