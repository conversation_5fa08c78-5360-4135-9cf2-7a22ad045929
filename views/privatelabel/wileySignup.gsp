<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>

<% if ("1".equals("" + session["siteId"])) { %>
<asset:stylesheet href="wonderslate/signup.css" async="true"/>
<% } else { %>
<asset:stylesheet href="${session['entryController']}/signup.css" async="true"/>
<% } %>

<% String siteName = session.getAttribute("siteName") != null ? session.getAttribute("siteName") : grailsApplication.config.grails.appServer.siteName; %>

<%
    boolean mobileOnlyLogin = "mobile".equals("" + session.getAttribute("loginType"))
%>
<% if (!(("android".equals(session["appType"])) || ("ios".equals(session["appType"])))) { %>
<style>
/* Removing Input Field Arrows */
#loginOpen input::-webkit-outer-spin-button,
#loginOpen input::-webkit-inner-spin-button,
#signup input::-webkit-outer-spin-button,
#signup input::-webkit-inner-spin-button,
#forgotPasswordmodal input::-webkit-outer-spin-button,
#forgotPasswordmodal input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}
#loginOpen input[type=number],
#signup input[type=number],
#forgotPasswordmodal input[type=number] {
    -moz-appearance: textfield;
}

.modal-text-content{
    border-radius: 0 0 50px 50px;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 4px #00000040;
}
#signup .modal-body{
    background: #fff!important;
}
#termsCondition:focus {
    box-shadow: none !important;
}
#otherInterestsField:focus {
    box-shadow: none !important;
}

@media (min-width: 768px) {
    .modal-form-content{
        padding: 10px 30px !important;
        background: #fff !important;
    }
    .modal-text-content h1{
        font-size: 1.5rem;
    }
    #signup .head-title{
        font-size: 20px;
    }

}
</style>

<div class="loading-icon">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<!--------------    LOGIN MODAL  ------------->
<div class="modal fade" id="loginOpen" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body d-lg-flex p-0">
                <button type="button" class="close" data-dismiss="modal">X</button>
                <div class="modal-text-content col-lg-6 p-4 p-lg-5 d-flex">
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_jcikwtux.json" background="transparent" speed="1" loop autoplay></lottie-player>
                    <h1 id="loginMessage">The Joy of <span>Learning</span></h1>
                </div>
                <div class="modal-form-content col-12 col-lg-6 p-4 p-md-5">
                    <h4 class="modal-title head-title">Login</h4>
                    <form class="mt-3 mb-4 text-center" method="post" action="/login/authenticate" name="signin" id="login-form">
                        <input type="hidden" name="username" value="">
                        <input class="form-control" placeholder="Mobile No./Email"
                               type=<%=mobileOnlyLogin ? "number" : "text"%> id="number" name="username_temp"
                               autocomplete="mobile-number" required onfocus="javascript:resetError();">
                        <p id="usernameError" class="text-left error-text"></p>
                        <div class="position-relative text-right mt-3">
                            <input class="form-control" placeholder="Password" type="password" id="password"
                                   name="password" autocomplete="current-password" required onfocus="javascript:resetError();">
                            <a href="javascript:void(0);" class="hide-password material-icons">visibility</a>
                            <p id="loginPasswordError" class="text-left error-text"></p>
                            <a href="javascript:forgotPasswordOpen()" class="forgot mt-2 d-inline-block">Forgot password?</a>
                        </div>

                        <p id="username-empty" class="error-msg mt-2"></p>
                        <p id="loginFailed" class="error-text mt-3" style="display:none;">Login failed. Please try again!</p>
                        <p class="error-text mt-3" id="limit-signin" style="display:none;"></p>

                        <input type="button" class="mt-4 btn login-btn" value="LOGIN" id="nrmlSignin"
                               onclick="submitSignIn();">

                    </form>

                    <div class="modal-footer justify-content-center border-0 pt-0">
                        <p>New User? <a href="/privatelabel/wileySignup?signUpPage=true">Sign-up</a></p>
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>

<!--------------    SIGNUP MODAL  ------------>
<div class="modal fade" id="signup" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body p-0">
                <button type="button" class="close" id="closeButton">X</button>

                <div class="modal-text-content col-12 col-lg-12 p-4 p-lg-4 d-flex">
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_jcikwtux.json" background="transparent" speed="1" loop autoplay style="display:none;"></lottie-player>
                    <h1 id="signupMessage">The Joy of <span>Learning</span></h1>
                </div>
                <div class="modal-form-content col-12 col-lg-12 p-4">
                    <h4 class="modal-title head-title" id="signUpTitle">Sign-up</h4>
                    <p id="signUpSubTitle"></p>
                    <g:form name="adduser" url="[action: 'addUser', controller: 'creation']" method="post"
                            autocomplete="off" id="signInId" class="mt-3 mb-4">

                        <input type="text" class="form-control" placeholder="First Name" id="firstName" name="firstName" required value="">
                        <p id="firstNameError" class="text-left error-text"></p>

                        <input type="text" class="form-control mt-3" placeholder="Last Name" id="lastName" name="lastName" required value="">
                        <p id="lastNameError" class="text-left error-text"></p>

                        <input type="number" name="migrateMobile" id="migrateMobile" class="form-control mt-3" placeholder="Mobile" autocomplete="off" required>
                        <p id="migrateMobileError" class="text-left error-text"></p>

                        <select name="state" id="state" required class="form-control mt-3">
                            <option value="">Select State</option>
                        </select>
                        <p id="stateError" class="text-left error-text"></p>

                        <select name="district" id="district" required class="form-control mt-3" >
                            <option value="">Select City</option>
                        </select>
                        <p id="cityError" class="text-left error-text"></p>

                        <select id="countrySelect" required class="form-control mt-3" name="country"></select>
                        <p id="countryError" class="text-left error-text"></p>

                        <input class="form-control mt-3" type="email" id="email" name="email" placeholder="Email" required>
                        <p id="emailError" class="text-left error-text"></p>

                        <div class="position-relative mt-3">
                            <input type="password" class="form-control" placeholder="Password" id="signup-password"
                                   name="password" autocomplete="off" required>
                            <a href,="javascript:void(0);" class="hide-password material-icons">visibility</a>
                            <p id="signupPasswordError" class="text-left error-text"></p>
                        </div>

                        <select id="institutes" required class="form-control mt-3" name="institutionName">
                            <option value="">Select Institute</option>
                        </select>
                        <input type="hidden" id="institution" name="institution">
                        <input type="text" id="otherInstituteName" class="form-control mt-3 d-none" required placeholder="Insitute Name">
                        <p id="instituteError" class="text-left error-text"></p>

                        <input class="form-control mt-3" type="text" id="course" name="department" placeholder="Course" required>
                        <p id="courseError" class="text-left error-text"></p>

                        <select id="semesters" required class="form-control mt-3" name="studentDiscipline">
                            <option value="">Select Semester</option>
                        </select>
                        <p id="semestersError" class="text-left error-text"></p>

                        <input type="number" class="form-control mt-3" placeholder="Enter OTP" name="signupOtp" id="signupOtp" required maxlength="10" minlength="10" style="display: none;">
                        <p id="signupOTPError" class="text-left error-text"></p>

                        <input type="hidden" name="username">
                        <input type="hidden" name="mobile">
                        <input type="hidden" name="otp_finished" value="true">
                        <input type="hidden" name="name" id="name">
                        <input type="hidden" name="otherInterests" id="otherInterests">

                        <div class="d-flex align-items-center" style="gap: 6px">
                            <input type="checkbox" name="termsCondition" id="termsCondition" >
                            <label for="termsCondition" class="mb-0 text-justify">
                                I have read and agree to the
                                <a href="${session["termsCondition"] ? session["termsCondition"] : "https://www.wonderslate.com/funlearn/termsandconditions"}" target="_blank">terms and conditions</a> and
                                <a href="${session["privacyPolicy"] ? session["privacyPolicy"] : "https://www.wonderslate.com/funlearn/privacy"}" target="_blank">privacy policy.</a>
                            </label>
                        </div>
                        <p id="termsError" class="text-left error-text"></p>
                        <div class="d-flex align-items-center mt-4" style="gap: 6px">
                            <input type="checkbox" name="otherInterestsField" id="otherInterestsField" >
                            <label for="otherInterestsField" class="mb-0 text-justify">
                                I wish to receive information regarding special offers and products. My information will be treated in accordance with Wonderslate's
                                <a href="${session["privacyPolicy"] ? session["privacyPolicy"] : "https://www.wonderslate.com/funlearn/privacy"}" target="_blank">privacy policy.</a>
                            </label>
                        </div>
                        <input type="button" onclick="javascript:getOTPForSignup();" class="mt-4 btn login-btn" value="GET OTP" id="migrateOtp">

                        <div class="verification-code mt-3" style="display: none;">

                            <input type="button" onclick="javascript:verifyMobileOTP();" class="mt-2 btn login-btn"
                                   value="Verify OTP" id="verifybtn" style="display: none">
                            <p class="mt-3">Verification code is sent to <b><span id="sentMobileNumbers"></span></b></p>

                            <div>
                                <span class="timer">
                                    <span class="time"></span>
                                </span>
                            </div>

                            <p class="resendotp mt-2" style="display: none;">Did not receive the OTP? <a
                                    href="javascript:getOTPForSignup(true);">Resend</a></p>

                        </div>

                        <button class="mt-4 btn login-btn" type="button" onclick="javascript:formSubmit();"
                                style="display: none;" id="register">Register</button>
                    </g:form>
                    <div class="modal-footer justify-content-center border-0 pt-0">
                        <p>Already registered? <a href="javascript:loginOpen()">Login</a></p>
                    </div>

                </div>

            </div>

        </div>
    </div>
</div>

<!--------------    FORGOT-PASSWORD MODAL  ------------>
<div class="modal fade" id="forgotPasswordmodal" data-keyboard="false" data-backdrop="static">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="login_signup_loader mdl-progress mdl-js-progress mdl-progress__indeterminate"></div>

            <!-- Modal body -->
            <div class="modal-body d-lg-flex p-0">
                <button type="button" class="close" data-dismiss="modal">X</button>
                <div class="modal-text-content col-12 col-lg-6 p-4 p-lg-5 d-flex">
                    <lottie-player src="https://assets1.lottiefiles.com/packages/lf20_jcikwtux.json" background="transparent" speed="1" loop autoplay></lottie-player>
                    <h1>The Joy of <span>Learning</span></h1>
                </div>
                <div class="modal-form-content col-12 col-lg-6 p-4 p-md-5">
                    <h4 class="modal-title head-title" id="forgotTitle">Forgot Password</h4>
                    <p id="forgotSubTitle">Don't worry. We'll fix this for you!</p>
                    <form class="mt-4 text-center" id="forgot-form">

                        <div class="otp-wrapper">

                            <input type="number" class="form-control mt-3" placeholder="Confirming OTP..." name="otpConfirm"
                                   id="otpConfirm" required maxlength="10" minlength="10">
                            <p id="ForgotPasswordOTPError" class="text-left error-text"></p>
                            <input type="button" onclick="javascript:verifyOTP();" class="mt-4 btn login-btn"
                                   value="Verify OTP">

                            <p class="mt-3">Verification code has been sent to <b><span id="sentMobileNumber"></span></b></p>
                            <div>
                                <span class="timer">
                                    <span class="time"></span>
                                </span>
                            </div><br>

                            <p class="resendotp" style="display: none;">Did not receive the OTP? <a
                                    href="javascript:getOTPForForgottenPassword(true);">Resend</a></p>

                        </div>

                        <p id="user-exist" class="error-msg"></p>

                        <span class="error-msg otperror-msg" style="display: none;">OTP failed. OTP is Incorrect.</span>

                        <p class="error-msg" id="limit-signin-verify"></p>

                    </form>
                    <g:form name="adduserFromForgot" url="[action: 'addUser', controller: 'creation']" method="post"
                            autocomplete="off" id="signUpFromForgot" class="mt-3 mb-4" style="display: none;">
                        <input type="text" class="form-control" placeholder="Name" id="nameFromForgot" name="name" required
                               value="" autocomplete="off">
                        <p id="nameErrorFromForgot" class="text-left error-text"></p>
                        <div class="position-relative mt-3">
                            <input type="password" class="form-control" placeholder="Password" id="passwordFromForgot"
                                   name="password" autocomplete="off" required>
                            <a href="javascript:void(0);" class="hide-password material-icons">visibility</a>
                            <p id="passwordFromForgotError" class="text-left error-text"></p>
                        </div>

                        <input type="hidden" name="username">
                        <input type="hidden" name="mobile">
                        <input type="hidden" name="email">
                        <input type="hidden" name="otp_finished" value="true">
                        <input type="button" onclick="javascript:formSubmitForgot();" class="mt-4 btn login-btn"
                               value="CONTINUE">
                    </g:form>
                </div>
            </div>

        </div>
    </div>
</div>

<% } %>

<g:render template="/privatelabel/footer_new"></g:render>
<asset:javascript src="jquery-1.11.2.min.js"/>
<asset:javascript src="wonderslate/vendors.min.js"/>
<script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-player/1.4.3/lottie-player.min.js"></script>


<script>
    document.querySelector('.login_signup_loader').addEventListener('mdl-componentupgraded', function() {
        this.MaterialProgress.setProgress(44);
    });

    var functionNameToCall = "";
    var loginHappened = false;
    var siteIds = "<%=session.getAttribute("siteId")%>";
    var otpReg = "${otpReg}";
    var operation;
    var mobileNumber;
    var nextDestination = null;
    var userCheck = true;
    var validation;
    var forceRegisterMode=null;
    var registerBookId=null;
    var registerBookTitle=null;
    var returnFunctionCallForQuiz=null;
    var userName;
    var email;
    var userState;
    var userCity;
    var userCountry;
    var userInstitute;
    var userCourse;
    var userSemester;
    var otherInteres;


    //FORGOT PASSWORD SUBMISSION
    function formFPSubmit() {
        $("#emailidnf").hide();

        if (!$("#fPemail").val()) {
            //actual code to check all fields needs to be entered. use the array of fields
            $("#email").addClass('has-error');
            $("#email").closest('.input-group').addClass('has-error');
        } else {

            var email = $("#fPemail").val();
            var atpos = email.indexOf("@");
            var dotpos = email.lastIndexOf(".");

            if (atpos < 1 || dotpos < atpos + 2 || dotpos + 2 >= email.length) {
                return false;
            } else {
                $("#loader").show();
                <g:remoteFunction controller="creation" action="forgottenPassword"  onSuccess='displayFPResults(data);'
						params="'email='+email" />
            }
        }
    }

    //DISPLAYING FORGOT PASSWORD DETAILS
    function displayFPResults(data) {
        var userEmail = $('#fPemail').val();

        if ("OK" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-completed');
            $('#fp-user-email').html("“" + userEmail + "�?");
        } else if ("Google" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'reset-google-paswd');
            $('#fp-user-email1').html("“" + userEmail + "�?");
        } else if ("Fail" == data.status) {
            $('#loginSignup').modal('show');
            $('#loginSignup').attr('data-page', 'account-exist');
        }
    }
</script>


<script>
    //P.R
    var loginName = document.getElementById("number");
    var institutionNameVal = "";
    var otherInstitute=false;

    //OPENING SIGNUP MODAL
    function signupModal() {
        forceRegisterMode=null;
        $('#signup').modal('show');
        document.getElementById("signUpTitle").innerText="Sign Up";
        document.getElementById("signUpSubTitle").innerText="";
        $('#loginOpen').modal('hide');
        $('p.error-msg').text('');
        $('#loginPasswordError').text('');
        $('#nameError').text('');
        $('#migrateMobileError').text('');
        $('.verification-code').hide();
        $('#migrateOtp').show();
        $("#signupOtp").hide();
        operation = "signup";
    }
    signupModal();
    //Sign up Submit
    function formSubmit() {

        if ($('#firstName,#lastName,#email,#mobile,#signup-password').val().length != 0) {
            var fullName = document.getElementById('firstName').value +" " + document.getElementById('lastName').value
            document.adduser.username.value = mobileNumber;
            document.adduser.name.value = fullName
            document.adduser.email.value = document.getElementById('email').value;
            document.adduser.mobile.value = mobileNumber;

            document.adduser.otherInterests.value = document.getElementById('otherInterestsField').checked;

            if (otherInstitute){
                institutionNameVal = document.getElementById('otherInstituteName').value;
            }else{
                institutionNameVal = document.getElementById('institutes').value;
            }
            document.adduser.institution.value = institutionNameVal;

            var oData = new FormData(document.forms.namedItem("adduser"));
            oData.append("site", "Wonderslate");

            var url = "${createLink(controller:'creation',action:'addUser')}";

            var correctMobile = false;
            var correctEmail = false;
            var emailCheck = /^[A-Z0-9._%+-]+@([A-Z0-9-]+\.)+[A-Z]{2,4}$/i;
            if (/^\d{10}$/.test($("#mobile").val()) && $("#mobile").val() != "") {
                correctMobile = true;
                document.adduser.mobile.value = $("#mobile").val();
            }
            if ($("#email").val() != "" && $("#email").val().includes("@")) {
                if (emailCheck.test($("#email").val()))
                    correctEmail = true;
                document.adduser.email.value = $("#email").val();
            }
            if(forceRegisterMode=="quiz"){
                oData.append("forceRegisterMode", "quiz");
            }

            ajaxFunction(url, oData);

        }
    }

    function formSubmitForgot() {
        var fullname = document.getElementById("nameFromForgot");
        var password = document.getElementById("passwordFromForgot");
        if (fullname.value == '') {
            $('#nameErrorFromForgot').text('Please enter your name.');
            $(fullname).focus().addClass('input-error');
            $('#passwordFromForgotError').text('');
            $(password).removeClass('input-error');
        } else if (password.value == '') {
            $('#passwordFromForgotError').text('Please enter password.');
            $(password).focus().addClass('input-error');
        } else {
            if ($('#passwordFromForgot,#passwordFromForgot').val().length != 0) {
                $('.login_signup_loader').show();
                document.adduserFromForgot.username.value = mobileNumber;
                if (mobileNumber.includes("@")) {
                    document.adduserFromForgot.email.value = mobileNumber;
                } else {
                    document.adduserFromForgot.mobile.value = mobileNumber;
                }

                var oData = new FormData(document.forms.namedItem("adduserFromForgot"));
                oData.append("site", "Wonderslate");

                var url = "${createLink(controller:'creation',action:'addUser')}";

                ajaxFunction(url, oData);
            }
        }
    }

    function ajaxFunction(url, oData) {
        $.ajax({
            url: url,
            type: 'POST',
            data: oData,
            processData: false,  // tell jQuery not to process the data
            contentType: false,
            success: function (req) {
                loginDone(req);
            }
        });
    }

    //LOGIN CODES
    //OPEN Login modal
    function loginOpen() {
        $('#loginOpen').modal('show');

        $('#signup').modal('hide');
        $('#login-form').trigger('reset');
        $('p.error-msg').text('');
        $('#loginPasswordError').text('');
        $('#nameError').text('');
        $('#migrateMobileError').text('');

        if ($('.mega_menu__wrapper').hasClass('menu-showing')) {
            $(".navbar-hamburger").toggleClass("menu-actives");
            $(".mega_menu__overlay_bg").toggleClass("active");
            $(".mega_menu__wrapper").toggleClass("menu-showing");
        }

    }


    //FORGOT PASSWORD CODES

    function checkUserExist() {
        document.signin.username.value = document.signin.username_temp.value;
        var username = document.signin.username.value;
        var siteId = "${session["siteId"]}";
        <g:remoteFunction controller="creation" action="checkUserExistForSite"  onSuccess='userExistSuccess(data);'
         params="'siteId='+siteId+'&username='+username" />
    }

    //Get OTP
    function userExistSuccess(data) {
        if (data.status == 'No user') {
            $('#username-empty').text('There is no user with this username..');
        } else {
            $('#loginOpen').modal('hide');
            getOTPForSignup();
            $('#forgotPasswordmodal').modal('show');
        }
    }


    function loginOpenWithFunction(functionName, message) {
        functionNameToCall = functionName;
        if (loginHappened) {
            window[functionNameToCall]();
        } else {
            document.getElementById("loginMessage").innerText = message;
            document.getElementById("signupMessage").innerText = message;
            loginOpen();
        }
    }

    function signupWithFunction(functionName, message) {
        functionNameToCall = functionName;
        if (loginHappened) {
            window[functionNameToCall]();
        } else {
            $('#signup').modal('show');
            $('#loginOpen').modal('hide');
            $('p.error-msg').text('');
            $('#migrateMobileError').text('');
            $('#nameError').text('');
            $('#loginPasswordError').text('');

            document.getElementById("loginMessage").innerText = message;
            document.getElementById("signupMessage").innerText = message;
            operation = "signup";
        }
    }

    $('#login-form input').keypress(function (e) {
        if (e.which == 13) {
            submitSignIn();
            return false;    //<---- Add this line
        }
    });

    $('#adduser input').keypress(function (e) {
        if (e.which == 13) {
            getOTPForSignup();
            return false;    //<---- Add this line
        }
    });
    $('#adduser input').keypress(function (e) {
        if (e.which == 13) {
            verifyMobileOTP();
            return false;    //<---- Add this line
        }
    });

    //Submit Login
    function submitSignIn() {
        document.signin.username.value = "${session["siteId"]}_" + document.signin.username_temp.value;
        var username = document.signin.username.value;
        var password = document.signin.password.value;
        if (loginName.value == '') {
            $('#usernameError').text('Please enter mobile number or email.');
            $(loginName).focus().addClass('input-error');
            $('#loginPasswordError').text('');
            $('#login-form #password').removeClass('input-error');
        } else if (password == '') {
            $('#loginPasswordError').text('Please enter password.');
            $('#login-form #password').addClass('input-error');
        } else {
            $('.login_signup_loader').show();
            <g:remoteFunction controller="log" action="login"  onSuccess="loginDone(data);" params="'source=web&username='+username+'&password='+password" />
        }
    }

    //Success Login/Signup
    var urlSeparate = location.search.split('?')[location.search.split('?').length - 1];
    var getGroup = urlSeparate.split('=')[0];
    if (getGroup == 'groupId') {
        $('.modal-dialog.modal-dialog-centered button.close').hide();
    }

    function loginDone(data) {
        $('.login_signup_loader').hide();
        if ('failed' == data.status) {
            alert(data.message);
        }
        if ("ok" == data.status || "OK" == data.status) {
            $('.loading-icon').removeClass('hidden');
            if(forceRegisterMode=="free"||forceRegisterMode=="paid"){
                window.location.href = "/"+registerBookTitle+"/ebook?bookId="+registerBookId+"&siteName=${params.siteName}";
            }else  if(forceRegisterMode=="quiz"){

                userId=data.username;
                $('#loginOpen').modal('hide');
                $('#signup').modal('hide');
                $("#signupnav").hide();
                document.getElementById("myhomeref").href="/books/home";
                window[returnFunctionCallForQuiz]();
            } else {
                if (functionNameToCall != "") {
                    loginHappened = true;
                    $('#signup').modal('hide');
                    $('#loginOpen').modal('hide');
                    window[functionNameToCall]();
                }
                else if (siteIds != 1) {
                    window.location.href = "/wsLibrary/myLibrary";
                } else if (getGroup == 'groupId') {
                    $('.modal-dialog.modal-dialog-centered button.close').hide();
                    window.location.reload();
                } else {
                    loginHappened = true;
                    window.location.href = "/books/myHome";
                }
            }
        } else {
            $("#loginFailed").show();
        }
    }


    //FORGOT PASSWORD CODES
    //OPENING FORGOT PASSWORD MODAL
    function forgotPasswordOpen() {
        if (loginName.value == "") {
            $(loginName).focus().addClass('input-error');
            $('#usernameError').text('Please enter registered mobile number or email to retrieve the password.');
            $('#login-form #password').removeClass('input-error');
            $('#loginPasswordError').text('');
        } else {
            operation = "forgotpassword";
            $('#forgot-form').trigger('reset');
            getOTPForForgottenPassword(false);
        }
    }

    //CHECKING USER IS EXISTS OR NOT
    function userExistSuccess(data) {
        if (operation == "forgotpassword") {
            $('#forgotPasswordmodal').modal('show');
            $('#loginOpen').modal('hide');
            $('#signup').modal('hide');
        }
    }

    //CALLING GENERATE OTP API
    function getOTPForSignup(resend=false) {
        counter = 60;
        userCheck = false;
        mobileNumber = document.getElementById("migrateMobile").value;
        email = document.getElementById('email').value
        if (validateFormFields()) {
            if ($('#name,#email,#mobile,#signup-password').val().length != 0) {
                if (/^\d{10}$/.test(mobileNumber) && email.includes("@")) {
                    document.getElementById("sentMobileNumbers").innerHTML = mobileNumber;
                    $('.login_signup_loader').show();
                    if (mobileNumber.includes("@")) {
                        <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);'
                        params="'email='+mobileNumber+'&source=web'" />
                        validation = "mobile";
                    } else {
                        <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='OTPReceived(data);'
                        params="'mobile='+mobileNumber+'&resend='+resend+'&userCheck='+userCheck+'&source=web'" />
                        validation = "email";
                    }
                } else {
                    $('#migrateMobileError').text('Please enter 10 digit mobile number.');
                    $('#migrateMobile').focus().addClass('input-error');
                    return false
                }
            }
        }
    }


    // Field validation data structure
    const fieldValidations = [
        {
            field: '#firstName',
            errorField: '#firstNameError',
            errorMessage: 'Please enter first name.',
        },
        {
            field: '#lastName',
            errorField: '#lastNameError',
            errorMessage: 'Please enter last name.',
        },
        {
            field: '#migrateMobile',
            errorField: '#migrateMobileError',
            errorMessage: 'Please enter 10 digit mobile number.',
            condition: () => forceRegisterMode === 'free' || forceRegisterMode === 'paid' || forceRegisterMode === 'quiz',
        },
        {
            field: '#state',
            errorField: '#stateError',
            errorMessage: 'Please select state.',
        },
        {
            field: '#district',
            errorField: '#cityError',
            errorMessage: 'Please select district.',
        },
        {
            field: '#email',
            errorField: '#emailError',
            errorMessage: 'Please enter email.',
        },
        {
            field: '#signup-password',
            errorField: '#signupPasswordError',
            errorMessage: 'Please enter password.',
        },
        {
            field: '#institutes',
            errorField: '#instituteError',
            errorMessage: 'Please select Institute.',
        },
        {
            field: '#course',
            errorField: '#courseError',
            errorMessage: 'Please enter course.',
        },
        {
            field: '#semesters',
            errorField: '#semestersError',
            errorMessage: 'Please select semester.',
        },
        {
            field: '#termsCondition',
            errorField: '#termsError',
            errorMessage: 'Please check terms & condition to continue.',
        },
    ];

    // Clear error messages and remove 'input-error' class
    function clearErrorMessage(errorField, field) {
        setTimeout(function () {
            errorField.text('');
            field.focus().removeClass('input-error');
        }, 2000);
    }

    // Validate form fields
    function validateFormFields() {
        for (const validation of fieldValidations) {
            const fieldValue = $(validation.field).val();
            if (fieldValue === '' || (validation.condition && validation.condition())) {
                $(validation.errorField).text(validation.errorMessage);
                $(validation.field).focus().addClass('input-error');
                clearErrorMessage($(validation.errorField), $(validation.field));
                return false; // Stop validation on the first error
            }
            var regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            var isEmailValid = regex.test(email);
            if (!isEmailValid){
                $('#emailError').text('Please enter a valid email');
                $('#email').focus().addClass('input-error');
                clearErrorMessage($('#emailError'), $('#email'));
                return false
            }
            var terms = document.getElementById('termsCondition');
            if (!terms.checked){
                $('#termsError').text('Please check the terms and condition to continue.');
                $('#termsCondition').focus().addClass('input-error');
                clearErrorMessage($('#termsError'), $('#termsCondition'));
                return false
            }
            var institutesListElement = document.getElementById('institutes').value
            if (institutesListElement == 'Others' || institutesListElement == 'others'){
                if($('#otherInstituteName').val() == ''){
                    $('#instituteError').text('Please enter your institute name');
                    $('#otherInstituteName').focus().addClass('input-error');
                    clearErrorMessage($('#instituteError'), $('#otherInstituteName'));
                    return false
                }
            }
        }
        return true; // All fields are valid
    }
    //Timer
    var interval;
    var counter = 60;

    function resendTimer() {
        clearInterval(interval);
        interval = setInterval(function () {
            counter--;
            // Display 'counter' wherever you want to display it.
            if (counter <= 0) {
                clearInterval(interval);
                $('.timer').html("");
                $('.resendotp').show();
                return;
            } else {
                $('.timer').html("<br>Resend OTP option available in <b>" + counter + "</b> seconds");
            }
        }, 1000);
    }

    //AFTER SUCCESSFULLY RECEIVING OTP
    function OTPReceived(data) {
        var userExist = data.userExist;
        userExistSuccess(userExist);
        $('.resendotp').hide();
        $('.login_signup_loader').hide();
        if (data.status == "Failed") {
            $('#user-exist').html('User already exists. Please use a different mobile number or email.');
        } else {
            resendTimer();
            if (operation == 'signup') {
                $('#signupOtp,.verification-code,#verifybtn').show();
                $('#signupOtp').focus();
                $('#migrateOtp').hide();
            }
        }
    }

    //VERIFYING OTP
    function verifyOTP() {
        if (document.getElementById("otpConfirm").value == "") {
            $('#otpConfirm').focus().addClass('input-error');
            $('#ForgotPasswordOTPError').text('Please enter the OTP to proceed.');
        } else {
            $('.login_signup_loader').show();
            var mobileOTP = document.getElementById("otpConfirm").value;
            if (mobileNumber.includes("@"))
                <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);'
    params="'email='+mobileNumber+'&email_otp='+mobileOTP" />
            else
            <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);'
    params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP" />

        }
    }

    //Submit otp for Signup

    function verifyMobileOTP() {
        if (document.getElementById("signupOtp").value == "") {
            $('#signupOtp').focus().addClass('input-error');
            $('#signupOTPError').text('Please enter the OTP to proceed.');
        } else {
            $('.login_signup_loader').show();
            var mobileOTP = document.getElementById("signupOtp").value;

            if (mobileNumber.includes("@"))
                <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);'
         params="'email='+mobileNumber+'&email_otp='+mobileOTP" />
            else
            <g:remoteFunction controller="creation" action="checkOTP"  onSuccess='otpVerified(data);'
         params="'mobile='+mobileNumber+'&mobile_otp='+mobileOTP" />
        }

    }

    //OTP verified

    function otpVerified(data) {
        $('.login_signup_loader').hide();
        $('#signup .close').hide();
        if ("OK" == data.status) {
            if (operation == "signup") {
                if (data.userExists) {
                    if (data.allowLogin) {
                        if(forceRegisterMode=="quiz"){
                            userId=data.username;
                            $('#loginOpen').modal('hide');
                            $('#signup').modal('hide');
                            $("#signupnav").hide();
                            document.getElementById("myhomeref").href="/books/home";
                            window[returnFunctionCallForQuiz]();
                        }else {
                            if (functionNameToCall != "") {
                                loginHappened = true;
                                $('#signup').modal('hide');
                                $('#signup').modal('hide');
                                $('#loginOpen').modal('hide');
                                window[functionNameToCall]();
                            }else {
                                window.location.href = "/security/loginmanager";
                                $('.loading-icon').removeClass('hidden');
                            }
                        }
                    } else {
                        document.getElementById('limit-signin-verify').innerText = 'The user cannot login as he already logged in from multiple devices';
                    }
                } else {
                    $('.verification-code').html("<p class='register-success'>Your registration is successful.<br>Taking you inside the application now.</p>");

                    $('#migrateOtp,#signupOtp').hide();
                    $('#migrateMobile').attr('disabled', 'disabled');
                    $('.loading-icon').removeClass('hidden');
                    formSubmit();

                }

            } else if (operation == "forgotpassword") {
                if (data.userExists) {
                    //temporary solution

                    if (data.allowLogin) {
                        if (functionNameToCall != "") {
                            loginHappened = true;
                            $('#signup').modal('hide');
                            $('#signup').modal('hide');
                            $('#loginOpen').modal('hide');
                            $('#forgotPasswordmodal').modal('hide');
                            window[functionNameToCall]();
                        }
                        else if(forceRegisterMode=="quiz"){
                            userId=data.username;
                            $('#loginOpen').modal('hide');
                            $('#signup').modal('hide');
                            $('#forgotPasswordmodal').modal('hide');
                            $("#signupnav").hide();
                            document.getElementById("myhomeref").href="/books/home";
                            window[returnFunctionCallForQuiz]();
                        }else {
                            window.location.href = "/security/loginmanager";
                            $('.loading-icon').removeClass('hidden');
                        }
                    } else {
                        document.getElementById('limit-signin-verify').innerText = 'The user cannot login as he already logged in from multiple devices';
                    }
                } else {
                    $("#forgotTitle").text("Get started!");
                    $("#forgot-form, #forgotSubTitle").hide();
                    $("#signUpFromForgot").show();
                }
            } else if (operation == "migrate") {
                $('.set-password').show();
                $('.otp-wrapper').hide();
            }
        } else {
            if (operation == "signup") {
                $('#signupOtp').focus().addClass('input-error');
                $('#signupOTPError').text('Incorrect OTP. Please try again.');
            } else if (operation == "forgotpassword") {
                $('#otpConfirm').focus().addClass('input-error');
                $('#ForgotPasswordOTPError').text('Incorrect OTP. Please try again.');
            }
        }
    }

    //ReSet Password
    function setPassword() {

        if (document.getElementById("setPassword1").value != document.getElementById("setPassword2").value) {
            $('#passwd-error').html('Please make sure password and confirm password are same.');
        } else if (document.getElementById("setPassword1").value == "") {
            $('#passwd-error').html('Please enter the password.')

        } else {
            $('.login_signup_loader').show();
            var oldPassword = "<%= session['userdetails']!=null?session['userdetails'].password:"" %>";
            var password = document.getElementById("setPassword1").value;
            <g:remoteFunction controller="creation" action="updateMigrateUserPassword"  onSuccess='passwordSetCompleted(data);'
    params="'oldPassword='+oldPassword+'&password='+password" />
        }
    }

    //Password set success
    function passwordSetCompleted(data) {
        $('.login_signup_loader').hide();
        window.location.href = "/security/loginmanager";
    }

    function checkLoginAndProceed(destination) {
        <sec:ifLoggedIn>
        $(".loading-icon").removeClass("hidden");
        window.location.href = destination;
        </sec:ifLoggedIn>
        <sec:ifNotLoggedIn>
        nextDestination = destination;
        </sec:ifNotLoggedIn>
    }

    function numberOnly(id) {
        var element = document.getElementById(id);
        var regex = /[^0-9]/gi;
        element.value = element.value.replace(regex, "");
    }

    function getOTPForForgottenPassword(resend) {
        counter = 60;
        mobileNumber = document.getElementById("number").value;

        if (/^\d{10}$/.test(mobileNumber) || mobileNumber.includes("@")) {
            document.getElementById("sentMobileNumber").innerHTML = mobileNumber;
            $('.login_signup_loader').show();
            if (mobileNumber.includes("@")) {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='forgotPasswordOTPReceived(data);'
            params="'email='+mobileNumber+'&source=web'" />
                validation = "mobile";
            } else {
                <g:remoteFunction controller="creation" action="generateOTP"  onSuccess='forgotPasswordOTPReceived(data);'
            params="'mobile='+mobileNumber+'&resend='+resend+'&userCheck=true&source=web'" />
                validation = "email";
            }

        } else {
            $(loginName).focus().addClass('input-error');
            $('#usernameError').text('Please enter 10 digit mobile number or email.');
            $('#login-form #password').removeClass('input-error');
            $('#loginPasswordError').text('');
            return false
        }

    }

    function forgotPasswordOTPReceived(data) {
        var userExist = data.userExist;
        userExistSuccess(userExist);
        $('#otpConfirm').focus();
        $('.resendotp').hide();
        $('.login_signup_loader').hide();
        resendTimer();
    }

    //setting the default focus
    $('#loginOpen').on('shown.bs.modal', function () {
        $('#number').focus();
    })

    $('#forgotPasswordmodal').on('shown.bs.modal', function () {
        document.getElementById("signInId").reset();
        $('#otpConfirm').focus();
    })

    $('#signup').on('shown.bs.modal', function () {
        $('#name').focus();
        $('#signupOTPError').text('');
    })

    $(loginName).on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#usernameError').text('');
        $('#loginFailed').hide();
    });
    $('#login-form #password').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#loginPasswordError').text('');
        $('#loginFailed').hide();
    });
    $('#name').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#nameError').text('');
    });
    $('#migrateMobile').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#migrateMobileError').text('');
    });
    $('#signup-password').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#signupPasswordError').text('');
    });
    $('#signupOtp').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#signupOTPError').text('');
    });
    $('#otpConfirm').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#ForgotPasswordOTPError').text('');
    });
    $('#nameFromForgot').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#nameErrorFromForgot').text('');
    });
    $('#passwordFromForgot').on('keyup keypress', function () {
        $(this).removeClass('input-error');
        $('#passwordFromForgotError').text('');
    });

    //hide or show password
    $('.hide-password').on('click', function(){
        var $this= $(this), $password_field = $this.prev('input');
        ( 'password' == $password_field.attr('type') ) ? $password_field.attr('type', 'text') : $password_field.attr('type', 'password');
        ( 'visibility' == $this.text() ) ? $this.text('visibility_off') : $this.text('visibility');
    });

    function openRegister(bookType,inputBookTitle,inputBookId){
        registerBookTitle = replaceAll(inputBookTitle,' ','-');
        registerBookId = inputBookId;

        if(bookType=="free"){
            signupModal();
            forceRegisterMode = "free";
            document.getElementById("signUpTitle").innerText="Get this eBook for free!";
            document.getElementById("migrateMobile").type="number";
            document.getElementById("migrateMobile").placeholder="Mobile no";
            document.getElementById("signUpSubTitle").innerText="Enter the following information to get this eBook.";

        }
        else if(bookType=="paid"){
            signupModal();
            forceRegisterMode = "paid";
            document.getElementById("migrateMobile").type="number";
            document.getElementById("migrateMobile").placeholder="Mobile no";
            document.getElementById("signUpTitle").innerText="Get a chapter for free!";
            document.getElementById("signUpSubTitle").innerText="Enter the following information to get a free chapter.";

        }
    }

    function openRegisterForQuiz(returnFunctionCall){
        returnFunctionCallForQuiz = returnFunctionCall;
        $('.loading-icon').addClass('hidden');
        signupModal();
        forceRegisterMode = "quiz";
        document.getElementById("signUpTitle").innerText="Well done. You are almost there!";
        document.getElementById("migrateMobile").type="number";
        document.getElementById("migrateMobile").placeholder="Mobile no";
        document.getElementById("signUpSubTitle").innerText="Enter the following information and check your quiz results.";



    }

    function resetError(){
        $("#loginFailed").hide();
    }

    $('#loginOpen').on('hidden.bs.modal', function () {
        $('#number, #login-form #password').removeClass('input-error');
        $('#usernameError, #loginPasswordError').text('');
        $('#loginFailed').hide();
    });

    $('#forgotPasswordmodal').on('hidden.bs.modal', function () {
        $('#otpConfirm, #nameFromForgot, #passwordFromForgot').removeClass('input-error');
        $('#ForgotPasswordOTPError, #nameErrorFromForgot, #passwordFromForgotError').text('');
        $("#forgotTitle").text("Forgot Password");
        $("#forgotSubTitle, #forgot-form").show();
        $("#signUpFromForgot").hide();
    });

    $('#signup').on('hidden.bs.modal', function () {
        $('#name, #migrateMobile, #signup-password, #signupOtp').removeClass('input-error');
        $('#nameError, #migrateMobileError, #signupPasswordError, #signupOTPError').text('');
    });


    var stateSel = document.getElementById("state"),
        districtSel = document.getElementById("district");

    //STATES & DIST OBJ
    var stateObjectNew = {
        "Andhra Pradesh": [
            "Anantapur",
            "Chittoor",
            "East Godavari",
            "Guntur",
            "Krishna",
            "Kurnool",
            "Nellore",
            "Prakasam",
            "Srikakulam",
            "Visakhapatnam",
            "Vizianagaram",
            "West Godavari",
            "YSR Kadapa"
        ],
        "Arunachal Pradesh": [
            "Tawang",
            "West Kameng",
            "East Kameng",
            "Papum Pare",
            "Kurung Kumey",
            "Kra Daadi",
            "Lower Subansiri",
            "Upper Subansiri",
            "West Siang",
            "East Siang",
            "Siang",
            "Upper Siang",
            "Lower Siang",
            "Lower Dibang Valley",
            "Dibang Valley",
            "Anjaw",
            "Lohit",
            "Namsai",
            "Changlang",
            "Tirap",
            "Longding"
        ],
        "Assam": [
            "Baksa",
            "Barpeta",
            "Biswanath",
            "Bongaigaon",
            "Cachar",
            "Charaideo",
            "Chirang",
            "Darrang",
            "Dhemaji",
            "Dhubri",
            "Dibrugarh",
            "Goalpara",
            "Golaghat",
            "Hailakandi",
            "Hojai",
            "Jorhat",
            "Kamrup Metropolitan",
            "Kamrup",
            "Karbi Anglong",
            "Karimganj",
            "Kokrajhar",
            "Lakhimpur",
            "Majuli",
            "Morigaon",
            "Nagaon",
            "Nalbari",
            "Dima Hasao",
            "Sivasagar",
            "Sonitpur",
            "South Salmara-Mankachar",
            "Tinsukia",
            "Udalguri",
            "West Karbi Anglong"
        ],
        "Bihar": [
            "Araria",
            "Arwal",
            "Aurangabad",
            "Banka",
            "Begusarai",
            "Bhagalpur",
            "Bhojpur",
            "Buxar",
            "Darbhanga",
            "East Champaran (Motihari)",
            "Gaya",
            "Gopalganj",
            "Jamui",
            "Jehanabad",
            "Kaimur (Bhabua)",
            "Katihar",
            "Khagaria",
            "Kishanganj",
            "Lakhisarai",
            "Madhepura",
            "Madhubani",
            "Munger (Monghyr)",
            "Muzaffarpur",
            "Nalanda",
            "Nawada",
            "Patna",
            "Purnia (Purnea)",
            "Rohtas",
            "Saharsa",
            "Samastipur",
            "Saran",
            "Sheikhpura",
            "Sheohar",
            "Sitamarhi",
            "Siwan",
            "Supaul",
            "Vaishali",
            "West Champaran"
        ],
        "Chandigarh (UT)": [
            "Chandigarh"
        ],
        "Chhattisgarh": [
            "Balod",
            "Baloda Bazar",
            "Balrampur",
            "Bastar",
            "Bemetara",
            "Bijapur",
            "Bilaspur",
            "Dantewada (South Bastar)",
            "Dhamtari",
            "Durg",
            "Gariyaband",
            "Janjgir-Champa",
            "Jashpur",
            "Kabirdham (Kawardha)",
            "Kanker (North Bastar)",
            "Kondagaon",
            "Korba",
            "Korea (Koriya)",
            "Mahasamund",
            "Mungeli",
            "Narayanpur",
            "Raigarh",
            "Raipur",
            "Rajnandgaon",
            "Sukma",
            "Surajpur  ",
            "Surguja"
        ],
        "Dadra and Nagar Haveli (UT)": [
            "Dadra & Nagar Haveli"
        ],
        "Daman and Diu (UT)": [
            "Daman",
            "Diu"
        ],
        "Delhi (NCT)": [
            "Central Delhi",
            "East Delhi",
            "New Delhi",
            "North Delhi",
            "North East  Delhi",
            "North West  Delhi",
            "Shahdara",
            "South Delhi",
            "South East Delhi",
            "South West  Delhi",
            "West Delhi"
        ],
        "Goa": [
            "North Goa",
            "South Goa"
        ],
        "Gujarat": [
            "Ahmedabad",
            "Amreli",
            "Anand",
            "Aravalli",
            "Banaskantha (Palanpur)",
            "Bharuch",
            "Bhavnagar",
            "Botad",
            "Chhota Udepur",
            "Dahod",
            "Dangs (Ahwa)",
            "Devbhoomi Dwarka",
            "Gandhinagar",
            "Gir Somnath",
            "Jamnagar",
            "Junagadh",
            "Kachchh",
            "Kheda (Nadiad)",
            "Mahisagar",
            "Mehsana",
            "Morbi",
            "Narmada (Rajpipla)",
            "Navsari",
            "Panchmahal (Godhra)",
            "Patan",
            "Porbandar",
            "Rajkot",
            "Sabarkantha (Himmatnagar)",
            "Surat",
            "Surendranagar",
            "Tapi (Vyara)",
            "Vadodara",
            "Valsad"
        ],
        "Haryana": [
            "Ambala",
            "Bhiwani",
            "Charkhi Dadri",
            "Faridabad",
            "Fatehabad",
            "Gurgaon",
            "Hisar",
            "Jhajjar",
            "Jind",
            "Kaithal",
            "Karnal",
            "Kurukshetra",
            "Mahendragarh",
            "Mewat",
            "Palwal",
            "Panchkula",
            "Panipat",
            "Rewari",
            "Rohtak",
            "Sirsa",
            "Sonipat",
            "Yamunanagar"
        ],
        "Himachal Pradesh": [
            "Bilaspur",
            "Chamba",
            "Hamirpur",
            "Kangra",
            "Kinnaur",
            "Kullu",
            "Lahaul &amp; Spiti",
            "Mandi",
            "Shimla",
            "Sirmaur (Sirmour)",
            "Solan",
            "Una"
        ],
        "Jammu and Kashmir": [
            "Anantnag",
            "Bandipore",
            "Baramulla",
            "Budgam",
            "Doda",
            "Ganderbal",
            "Jammu",
            "Kargil",
            "Kathua",
            "Kishtwar",
            "Kulgam",
            "Kupwara",
            "Leh",
            "Poonch",
            "Pulwama",
            "Rajouri",
            "Ramban",
            "Reasi",
            "Samba",
            "Shopian",
            "Srinagar",
            "Udhampur"
        ],
        "Jharkhand": [
            "Bokaro",
            "Chatra",
            "Deoghar",
            "Dhanbad",
            "Dumka",
            "East Singhbhum",
            "Garhwa",
            "Giridih",
            "Godda",
            "Gumla",
            "Hazaribag",
            "Jamtara",
            "Khunti",
            "Koderma",
            "Latehar",
            "Lohardaga",
            "Pakur",
            "Palamu",
            "Ramgarh",
            "Ranchi",
            "Sahibganj",
            "Seraikela-Kharsawan",
            "Simdega",
            "West Singhbhum"
        ],
        "Karnataka": [
            "Bagalkot",
            "Ballari (Bellary)",
            "Belagavi (Belgaum)",
            "Bengaluru (Bangalore) Rural",
            "Bengaluru (Bangalore) Urban",
            "Bidar",
            "Chamarajanagar",
            "Chikballapur",
            "Chikkamagaluru (Chikmagalur)",
            "Chitradurga",
            "Dakshina Kannada",
            "Davangere",
            "Dharwad",
            "Gadag",
            "Hassan",
            "Haveri",
            "Kalaburagi (Gulbarga)",
            "Kodagu",
            "Kolar",
            "Koppal",
            "Mandya",
            "Mysuru (Mysore)",
            "Raichur",
            "Ramanagara",
            "Shivamogga (Shimoga)",
            "Tumakuru (Tumkur)",
            "Udupi",
            "Uttara Kannada (Karwar)",
            "Vijayapura (Bijapur)",
            "Yadgir"
        ],
        "Kerala": [
            "Alappuzha",
            "Ernakulam",
            "Idukki",
            "Kannur",
            "Kasaragod",
            "Kollam",
            "Kottayam",
            "Kozhikode",
            "Malappuram",
            "Palakkad",
            "Pathanamthitta",
            "Thiruvananthapuram",
            "Thrissur",
            "Wayanad"
        ],
        "Lakshadweep (UT)": [
            "Agatti",
            "Amini",
            "Androth",
            "Bithra",
            "Chethlath",
            "Kavaratti",
            "Kadmath",
            "Kalpeni",
            "Kilthan",
            "Minicoy"
        ],
        "Madhya Pradesh": [
            "Agar Malwa",
            "Alirajpur",
            "Anuppur",
            "Ashoknagar",
            "Balaghat",
            "Barwani",
            "Betul",
            "Bhind",
            "Bhopal",
            "Burhanpur",
            "Chhatarpur",
            "Chhindwara",
            "Damoh",
            "Datia",
            "Dewas",
            "Dhar",
            "Dindori",
            "Guna",
            "Gwalior",
            "Harda",
            "Hoshangabad",
            "Indore",
            "Jabalpur",
            "Jhabua",
            "Katni",
            "Khandwa",
            "Khargone",
            "Mandla",
            "Mandsaur",
            "Morena",
            "Narsinghpur",
            "Neemuch",
            "Panna",
            "Raisen",
            "Rajgarh",
            "Ratlam",
            "Rewa",
            "Sagar",
            "Satna",
            "Sehore",
            "Seoni",
            "Shahdol",
            "Shajapur",
            "Sheopur",
            "Shivpuri",
            "Sidhi",
            "Singrauli",
            "Tikamgarh",
            "Ujjain",
            "Umaria",
            "Vidisha"
        ],
        "Maharashtra": [
            "Ahmednagar",
            "Akola",
            "Amravati",
            "Aurangabad",
            "Beed",
            "Bhandara",
            "Buldhana",
            "Chandrapur",
            "Dhule",
            "Gadchiroli",
            "Gondia",
            "Hingoli",
            "Jalgaon",
            "Jalna",
            "Kolhapur",
            "Latur",
            "Mumbai City",
            "Mumbai Suburban",
            "Nagpur",
            "Nanded",
            "Nandurbar",
            "Nashik",
            "Osmanabad",
            "Palghar",
            "Parbhani",
            "Pune",
            "Raigad",
            "Ratnagiri",
            "Sangli",
            "Satara",
            "Sindhudurg",
            "Solapur",
            "Thane",
            "Wardha",
            "Washim",
            "Yavatmal"
        ],
        "Manipur": [
            "Bishnupur",
            "Chandel",
            "Churachandpur",
            "Imphal East",
            "Imphal West",
            "Jiribam",
            "Kakching",
            "Kamjong",
            "Kangpokpi",
            "Noney",
            "Pherzawl",
            "Senapati",
            "Tamenglong",
            "Tengnoupal",
            "Thoubal",
            "Ukhrul"
        ],
        "Meghalaya": [
            "East Garo Hills",
            "East Jaintia Hills",
            "East Khasi Hills",
            "North Garo Hills",
            "Ri Bhoi",
            "South Garo Hills",
            "South West Garo Hills ",
            "South West Khasi Hills",
            "West Garo Hills",
            "West Jaintia Hills",
            "West Khasi Hills"
        ],
        "Mizoram": [
            "Aizawl",
            "Champhai",
            "Kolasib",
            "Lawngtlai",
            "Lunglei",
            "Mamit",
            "Saiha",
            "Serchhip"
        ],
        "Nagaland": [
            "Dimapur",
            "Kiphire",
            "Kohima",
            "Longleng",
            "Mokokchung",
            "Mon",
            "Peren",
            "Phek",
            "Tuensang",
            "Wokha",
            "Zunheboto"
        ],
        "Odisha": [
            "Angul",
            "Balangir",
            "Balasore",
            "Bargarh",
            "Bhadrak",
            "Boudh",
            "Cuttack",
            "Deogarh",
            "Dhenkanal",
            "Gajapati",
            "Ganjam",
            "Jagatsinghapur",
            "Jajpur",
            "Jharsuguda",
            "Kalahandi",
            "Kandhamal",
            "Kendrapara",
            "Kendujhar (Keonjhar)",
            "Khordha",
            "Koraput",
            "Malkangiri",
            "Mayurbhanj",
            "Nabarangpur",
            "Nayagarh",
            "Nuapada",
            "Puri",
            "Rayagada",
            "Sambalpur",
            "Sonepur",
            "Sundargarh"
        ],
        "Puducherry (UT)": [
            "Karaikal",
            "Mahe",
            "Pondicherry",
            "Yanam"
        ],
        "Punjab": [
            "Amritsar",
            "Barnala",
            "Bathinda",
            "Faridkot",
            "Fatehgarh Sahib",
            "Fazilka",
            "Ferozepur",
            "Gurdaspur",
            "Hoshiarpur",
            "Jalandhar",
            "Kapurthala",
            "Ludhiana",
            "Mansa",
            "Moga",
            "Muktsar",
            "Nawanshahr (Shahid Bhagat Singh Nagar)",
            "Pathankot",
            "Patiala",
            "Rupnagar",
            "Sahibzada Ajit Singh Nagar (Mohali)",
            "Sangrur",
            "Tarn Taran"
        ],
        "Rajasthan": [
            "Ajmer",
            "Alwar",
            "Banswara",
            "Baran",
            "Barmer",
            "Bharatpur",
            "Bhilwara",
            "Bikaner",
            "Bundi",
            "Chittorgarh",
            "Churu",
            "Dausa",
            "Dholpur",
            "Dungarpur",
            "Hanumangarh",
            "Jaipur",
            "Jaisalmer",
            "Jalore",
            "Jhalawar",
            "Jhunjhunu",
            "Jodhpur",
            "Karauli",
            "Kota",
            "Nagaur",
            "Pali",
            "Pratapgarh",
            "Rajsamand",
            "Sawai Madhopur",
            "Sikar",
            "Sirohi",
            "Sri Ganganagar",
            "Tonk",
            "Udaipur"
        ],
        "Sikkim": [
            "East Sikkim",
            "North Sikkim",
            "South Sikkim",
            "West Sikkim"
        ],
        "Tamil Nadu": [
            "Ariyalur",
            "Chennai",
            "Coimbatore",
            "Cuddalore",
            "Dharmapuri",
            "Dindigul",
            "Erode",
            "Kanchipuram",
            "Kanyakumari",
            "Karur",
            "Krishnagiri",
            "Madurai",
            "Nagapattinam",
            "Namakkal",
            "Nilgiris",
            "Perambalur",
            "Pudukkottai",
            "Ramanathapuram",
            "Salem",
            "Sivaganga",
            "Thanjavur",
            "Theni",
            "Thoothukudi (Tuticorin)",
            "Tiruchirappalli",
            "Tirunelveli",
            "Tiruppur",
            "Tiruvallur",
            "Tiruvannamalai",
            "Tiruvarur",
            "Vellore",
            "Viluppuram",
            "Virudhunagar"
        ],
        "Telangana": [
            "Adilabad",
            "Bhadradri Kothagudem",
            "Hyderabad",
            "Jagtial",
            "Jangaon",
            "Jayashankar Bhoopalpally",
            "Jogulamba Gadwal",
            "Kamareddy",
            "Karimnagar",
            "Khammam",
            "Komaram Bheem Asifabad",
            "Mahabubabad",
            "Mahabubnagar",
            "Mancherial",
            "Medak",
            "Medchal",
            "Nagarkurnool",
            "Nalgonda",
            "Nirmal",
            "Nizamabad",
            "Peddapalli",
            "Rajanna Sircilla",
            "Rangareddy",
            "Sangareddy",
            "Siddipet",
            "Suryapet",
            "Vikarabad",
            "Wanaparthy",
            "Warangal (Rural)",
            "Warangal (Urban)",
            "Yadadri Bhuvanagiri"
        ],
        "Tripura": [
            "Dhalai",
            "Gomati",
            "Khowai",
            "North Tripura",
            "Sepahijala",
            "South Tripura",
            "Unakoti",
            "West Tripura"
        ],
        "Uttarakhand": [
            "Almora",
            "Bageshwar",
            "Chamoli",
            "Champawat",
            "Dehradun",
            "Haridwar",
            "Nainital",
            "Pauri Garhwal",
            "Pithoragarh",
            "Rudraprayag",
            "Tehri Garhwal",
            "Udham Singh Nagar",
            "Uttarkashi"
        ],
        "Uttar Pradesh": [
            "Agra",
            "Aligarh",
            "Allahabad",
            "Ambedkar Nagar",
            "Amethi (Chatrapati Sahuji Mahraj Nagar)",
            "Amroha (J.P. Nagar)",
            "Auraiya",
            "Azamgarh",
            "Baghpat",
            "Bahraich",
            "Ballia",
            "Balrampur",
            "Banda",
            "Barabanki",
            "Bareilly",
            "Basti",
            "Bhadohi",
            "Bijnor",
            "Budaun",
            "Bulandshahr",
            "Chandauli",
            "Chitrakoot",
            "Deoria",
            "Etah",
            "Etawah",
            "Faizabad",
            "Farrukhabad",
            "Fatehpur",
            "Firozabad",
            "Gautam Buddha Nagar",
            "Ghaziabad",
            "Ghazipur",
            "Gonda",
            "Gorakhpur",
            "Hamirpur",
            "Hapur (Panchsheel Nagar)",
            "Hardoi",
            "Hathras",
            "Jalaun",
            "Jaunpur",
            "Jhansi",
            "Kannauj",
            "Kanpur Dehat",
            "Kanpur Nagar",
            "Kanshiram Nagar (Kasganj)",
            "Kaushambi",
            "Kushinagar (Padrauna)",
            "Lakhimpur - Kheri",
            "Lalitpur",
            "Lucknow",
            "Maharajganj",
            "Mahoba",
            "Mainpuri",
            "Mathura",
            "Mau",
            "Meerut",
            "Mirzapur",
            "Moradabad",
            "Muzaffarnagar",
            "Pilibhit",
            "Pratapgarh",
            "RaeBareli",
            "Rampur",
            "Saharanpur",
            "Sambhal (Bhim Nagar)",
            "Sant Kabir Nagar",
            "Shahjahanpur",
            "Shamali (Prabuddh Nagar)",
            "Shravasti",
            "Siddharth Nagar",
            "Sitapur",
            "Sonbhadra",
            "Sultanpur",
            "Unnao",
            "Varanasi"
        ],
        "West Bengal": [
            "Alipurduar",
            "Bankura",
            "Birbhum",
            "Burdwan (Bardhaman)",
            "Cooch Behar",
            "Dakshin Dinajpur (South Dinajpur)",
            "Darjeeling",
            "Hooghly",
            "Howrah",
            "Jalpaiguri",
            "Kalimpong",
            "Kolkata",
            "Malda",
            "Murshidabad",
            "Nadia",
            "North 24 Parganas",
            "Paschim Medinipur (West Medinipur)",
            "Purba Medinipur (East Medinipur)",
            "Purulia",
            "South 24 Parganas",
            "Uttar Dinajpur (North Dinajpur)"
        ]

    }

    // Change State and District Function
    for (var state in stateObjectNew) {
        stateSel.options[stateSel.options.length] = new Option(state, state);
    }
    stateSel.onchange = function () {
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObjectNew[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    // stateSel.onchange(); // reset in case page is reloaded
    stateSel.onload = function () {
        districtSel.length = 1; // remove all options bar first
        if (this.selectedIndex < 1) return; // done
        var district = stateObjectNew[stateSel.value];
        for (var i = 0; i < district.length; i++) {
            districtSel.options[districtSel.options.length] = new Option(district[i], district[i]);
        }
    }
    stateSel.onload();

    // All countries
    // length 252
    const countries = [
        {name: "Afghanistan",code: "AF"},
        {name: "Åland Islands",code: "AX"},
        {name: "Albania",code: "AL"},
        {name: "Algeria",code: "DZ"},
        {name: "American Samoa",code: "AS"},
        {name: "Andorra",code: "AD"},
        {name: "Angola",code: "AO"},
        {name: "Anguilla",code: "AI"},
        {name: "Antarctica",code: "AQ"},
        {name: "Antigua & Barbuda",code: "AG"},
        {name: "Argentina",code: "AR"},
        {name: "Armenia",code: "AM"},
        {name: "Aruba",code: "AW"},
        {name: "Australia",code: "AU"},
        {name: "Austria",code: "AT"},
        {name: "Azerbaijan",code: "AZ"},
        {name: "Bahamas",code: "BS"},
        {name: "Bahrain",code: "BH"},
        {name: "Bangladesh",code: "BD"},
        {name: "Barbados",code: "BB"},
        {name: "Belarus",code: "BY"},
        {name: "Belgium",code: "BE"},
        {name: "Belize",code: "BZ"},
        {name: "Benin",code: "BJ"},
        {name: "Bermuda",code: "BM"},
        {name: "Bhutan",code: "BT"},
        {name: "Bolivia",code: "BO"},
        {name: "Caribbean Netherlands",code: "BQ"},
        {name: "Bosnia & Herzegovina",code: "BA"},
        {name: "Botswana",code: "BW"},
        {name: "Bouvet Island",code: "BV"},
        {name: "Brazil",code: "BR"},
        {name: "British Indian Ocean Territory",code: "IO"},
        {name: "Brunei",code: "BN"},
        {name: "Bulgaria",code: "BG"},
        {name: "Burkina Faso",code: "BF"},
        {name: "Burundi",code: "BI"},
        {name: "Cambodia",code: "KH"},
        {name: "Cameroon",code: "CM"},
        {name: "Canada",code: "CA"},
        {name: "Cape Verde",code: "CV"},
        {name: "Cayman Islands",code: "KY"},
        {name: "Central African Republic",code: "CF"},
        {name: "Chad",code: "TD"},
        {name: "Chile",code: "CL"},
        {name: "China",code: "CN"},
        {name: "Christmas Island",code: "CX"},
        {name: "Cocos (Keeling) Islands",code: "CC"},
        {name: "Colombia",code: "CO"},
        {name: "Comoros",code: "KM"},
        {name: "Congo - Brazzaville",code: "CG"},
        {name: "Congo - Kinshasa",code: "CD"},
        {name: "Cook Islands",code: "CK"},
        {name: "Costa Rica",code: "CR"},
        {name: "Côte d’Ivoire",code: "CI"},
        {name: "Croatia",code: "HR"},
        {name: "Cuba",code: "CU"},
        {name: "Curaçao",code: "CW"},
        {name: "Cyprus",code: "CY"},
        {name: "Czechia",code: "CZ"},
        {name: "Denmark",code: "DK"},
        {name: "Djibouti",code: "DJ"},
        {name: "Dominica",code: "DM"},
        {name: "Dominican Republic",code: "DO"},
        {name: "Ecuador",code: "EC"},
        {name: "Egypt",code: "EG"},
        {name: "El Salvador",code: "SV"},
        {name: "Equatorial Guinea",code: "GQ"},
        {name: "Eritrea",code: "ER"},
        {name: "Estonia",code: "EE"},
        {name: "Ethiopia",code: "ET"},
        {name: "Falkland Islands (Islas Malvinas)",code: "FK"},
        {name: "Faroe Islands",code: "FO"},
        {name: "Fiji",code: "FJ"},
        {name: "Finland",code: "FI"},
        {name: "France",code: "FR"},
        {name: "French Guiana",code: "GF"},
        {name: "French Polynesia",code: "PF"},
        {name: "French Southern Territories",code: "TF"},
        {name: "Gabon",code: "GA"},
        {name: "Gambia",code: "GM"},
        {name: "Georgia",code: "GE"},
        {name: "Germany",code: "DE"},
        {name: "Ghana",code: "GH"},
        {name: "Gibraltar",code: "GI"},
        {name: "Greece",code: "GR"},
        {name: "Greenland",code: "GL"},
        {name: "Grenada",code: "GD"},
        {name: "Guadeloupe",code: "GP"},
        {name: "Guam",code: "GU"},
        {name: "Guatemala",code: "GT"},
        {name: "Guernsey",code: "GG"},
        {name: "Guinea",code: "GN"},
        {name: "Guinea-Bissau",code: "GW"},
        {name: "Guyana",code: "GY"},
        {name: "Haiti",code: "HT"},
        {name: "Heard & McDonald Islands",code: "HM"},
        {name: "Vatican City",code: "VA"},
        {name: "Honduras",code: "HN"},
        {name: "Hong Kong",code: "HK"},
        {name: "Hungary",code: "HU"},
        {name: "Iceland",code: "IS"},
        {name: "India",code: "IN"},
        {name: "Indonesia",code: "ID"},
        {name: "Iran",code: "IR"},
        {name: "Iraq",code: "IQ"},
        {name: "Ireland",code: "IE"},
        {name: "Isle of Man",code: "IM"},
        {name: "Israel",code: "IL"},
        {name: "Italy",code: "IT"},
        {name: "Jamaica",code: "JM"},
        {name: "Japan",code: "JP"},
        {name: "Jersey",code: "JE"},
        {name: "Jordan",code: "JO"},
        {name: "Kazakhstan",code: "KZ"},
        {name: "Kenya",code: "KE"},
        {name: "Kiribati",code: "KI"},
        {name: "North Korea",code: "KP"},
        {name: "South Korea",code: "KR"},
        {name: "Kosovo",code: "XK"},
        {name: "Kuwait",code: "KW"},
        {name: "Kyrgyzstan",code: "KG"},
        {name: "Laos",code: "LA"},
        {name: "Latvia",code: "LV"},
        {name: "Lebanon",code: "LB"},
        {name: "Lesotho",code: "LS"},
        {name: "Liberia",code: "LR"},
        {name: "Libya",code: "LY"},
        {name: "Liechtenstein",code: "LI"},
        {name: "Lithuania",code: "LT"},
        {name: "Luxembourg",code: "LU"},
        {name: "Macao",code: "MO"},
        {name: "North Macedonia",code: "MK"},
        {name: "Madagascar",code: "MG"},
        {name: "Malawi",code: "MW"},
        {name: "Malaysia",code: "MY"},
        {name: "Maldives",code: "MV"},
        {name: "Mali",code: "ML"},
        {name: "Malta",code: "MT"},
        {name: "Marshall Islands",code: "MH"},
        {name: "Martinique",code: "MQ"},
        {name: "Mauritania",code: "MR"},
        {name: "Mauritius",code: "MU"},
        {name: "Mayotte",code: "YT"},
        {name: "Mexico",code: "MX"},
        {name: "Micronesia",code: "FM"},
        {name: "Moldova",code: "MD"},
        {name: "Monaco",code: "MC"},
        {name: "Mongolia",code: "MN"},
        {name: "Montenegro",code: "ME"},
        {name: "Montserrat",code: "MS"},
        {name: "Morocco",code: "MA"},
        {name: "Mozambique",code: "MZ"},
        {name: "Myanmar (Burma)",code: "MM"},
        {name: "Namibia",code: "NA"},
        {name: "Nauru",code: "NR"},
        {name: "Nepal",code: "NP"},
        {name: "Netherlands",code: "NL"},
        {name: "Curaçao",code: "AN"},
        {name: "New Caledonia",code: "NC"},
        {name: "New Zealand",code: "NZ"},
        {name: "Nicaragua",code: "NI"},
        {name: "Niger",code: "NE"},
        {name: "Nigeria",code: "NG"},
        {name: "Niue",code: "NU"},
        {name: "Norfolk Island",code: "NF"},
        {name: "Northern Mariana Islands",code: "MP"},
        {name: "Norway",code: "NO"},
        {name: "Oman",code: "OM"},
        {name: "Pakistan",code: "PK"},
        {name: "Palau",code: "PW"},
        {name: "Palestine",code: "PS"},
        {name: "Panama",code: "PA"},
        {name: "Papua New Guinea",code: "PG"},
        {name: "Paraguay",code: "PY"},
        {name: "Peru",code: "PE"},
        {name: "Philippines",code: "PH"},
        {name: "Pitcairn Islands",code: "PN"},
        {name: "Poland",code: "PL"},
        {name: "Portugal",code: "PT"},
        {name: "Puerto Rico",code: "PR"},
        {name: "Qatar",code: "QA"},
        {name: "Réunion",code: "RE"},
        {name: "Romania",code: "RO"},
        {name: "Russia",code: "RU"},
        {name: "Rwanda",code: "RW"},
        {name: "St. Barthélemy",code: "BL"},
        {name: "St. Helena",code: "SH"},
        {name: "St. Kitts & Nevis",code: "KN"},
        {name: "St. Lucia",code: "LC"},
        {name: "St. Martin",code: "MF"},
        {name: "St. Pierre & Miquelon",code: "PM"},
        {name: "St. Vincent & Grenadines",code: "VC"},
        {name: "Samoa",code: "WS"},
        {name: "San Marino",code: "SM"},
        {name: "São Tomé & Príncipe",code: "ST"},
        {name: "Saudi Arabia",code: "SA"},
        {name: "Senegal",code: "SN"},
        {name: "Serbia",code: "RS"},
        {name: "Serbia",code: "CS"},
        {name: "Seychelles",code: "SC"},
        {name: "Sierra Leone",code: "SL"},
        {name: "Singapore",code: "SG"},
        {name: "Sint Maarten",code: "SX"},
        {name: "Slovakia",code: "SK"},
        {name: "Slovenia",code: "SI"},
        {name: "Solomon Islands",code: "SB"},
        {name: "Somalia",code: "SO"},
        {name: "South Africa",code: "ZA"},
        {name: "South Georgia & South Sandwich Islands",code: "GS"},
        {name: "South Sudan",code: "SS"},
        {name: "Spain",code: "ES"},
        {name: "Sri Lanka",code: "LK"},
        {name: "Sudan",code: "SD"},
        {name: "Suriname",code: "SR"},
        {name: "Svalbard & Jan Mayen",code: "SJ"},
        {name: "Eswatini",code: "SZ"},
        {name: "Sweden",code: "SE"},
        {name: "Switzerland",code: "CH"},
        {name: "Syria",code: "SY"},
        {name: "Taiwan",code: "TW"},
        {name: "Tajikistan",code: "TJ"},
        {name: "Tanzania",code: "TZ"},
        {name: "Thailand",code: "TH"},
        {name: "Timor-Leste",code: "TL"},
        {name: "Togo",code: "TG"},
        {name: "Tokelau",code: "TK"},
        {name: "Tonga",code: "TO"},
        {name: "Trinidad & Tobago",code: "TT"},
        {name: "Tunisia",code: "TN"},
        {name: "Turkey",code: "TR"},
        {name: "Turkmenistan",code: "TM"},
        {name: "Turks & Caicos Islands",code: "TC"},
        {name: "Tuvalu",code: "TV"},
        {name: "Uganda",code: "UG"},
        {name: "Ukraine",code: "UA"},
        {name: "United Arab Emirates",code: "AE"},
        {name: "United Kingdom",code: "GB"},
        {name: "United States",code: "US"},
        {name: "U.S. Outlying Islands",code: "UM"},
        {name: "Uruguay",code: "UY"},
        {name: "Uzbekistan",code: "UZ"},
        {name: "Vanuatu",code: "VU"},
        {name: "Venezuela",code: "VE"},
        {name: "Vietnam",code: "VN"},
        {name: "British Virgin Islands",code: "VG"},
        {name: "U.S. Virgin Islands",code: "VI"},
        {name: "Wallis & Futuna",code: "WF"},
        {name: "Western Sahara",code: "EH"},
        {name: "Yemen",code: "YE"},
        {name: "Zambia",code: "ZM"},
        {name: "Zimbabwe",code: "ZW"}
    ];

    const institutes = {
            "InstituteName": [
                "ABESIT",
                "ABV-Indian Institute Of Information Technology & Management IIIT",
                "Academy of Technology ((AOT)",
                "ACE Engineering College",
                "Adamas University",
                "Ajay Kumar Garg Engineering College - [AKGEC]",
                "Amal Jyothi College of Engineering,Kanjirappally",
                "Amity",
                "Bansal Institute of Science & Technology, Bhopal",
                "Bellari Institute of Technology & Management",
                "Bhartiya Vidyapeeth Institute of Technology",
                "Bhilai Institute Of Technology",
                "Birla Institute of Technology & Science",
                "BML Munjal University",
                "BMS College of Engineering",
                "BMS Institute of Technology & Management ",
                "Brainware University",
                "Christ University",
                "Cummins College of Engineering",
                "DAV University",
                "Dayananda Sagar College of Engineering",
                "Dit University",
                "Dr. Sudhir Chandra Sur Degree Engineering College",
                "Dr.B. C. Roy Engineering Colege",
                "Dronacharya Institute of Engineering & Technology",
                "Federal Institute of Science and Technology",
                "Future Institute of Technology ",
                "G.L Bajaj Institute Of Tech",
                "Galgotia College Engineering & Technology",
                "GH Raisoni College of Engineering",
                "Global Academy of Technology",
                "Guru Tegh Bahadur Institute of Technology",
                "Gurunanak Institute of Technology (GNIT)",
                "Gyan Ganga Institute of Technology ",
                "Haldia Institute of Technology",
                "Hitkarini College of Engineering & Technology",
                "IMS Engineering College",
                "Institute of Engineering & Management (IEM)",
                "Jaypee Institute of Information Technology",
                "JIS College of Engineering",
                "JNTUGV  College of Engineering",
                "K. K. Wagh Institute Of Engineering Education And Research",
                "K.R.Mangalam University",
                "KIET",
                "Kongu Engineering college",
                "Laxmi Narayan College of Technology, Bhopal",
                "Laxmi Narayan College of Technology, Bhopal, Indore",
                "Mahakal Institute of Technology & Science, ",
                "Maharaj Agrasen Institute of Technology",
                "Maharaja Surajmal Institute of Technology",
                "Matoshri College of Engineering & Research Centre",
                "MCKV Institute of Engineering",
                "Medicaps University, Indore",
                "Meerut Institute of Engineering and Technology - [MIET]",
                "Meghnad Saha Institute of Technology",
                "Modi  University Rajasthan",
                "Narula Institute of Technology",
                "Noida Intitute of Engineering & Technology",
                "OP Jindal Institute Of Technology",
                "Oriental College Of Technology, Bhopal",
                "Oriental Institute Of Science & Technology, Bhopal",
                "PES University",
                "Prestige Institute of Engineering and Science",
                "PVP Siddhartha Engineering College",
                "R M K Engineering college",
                "R V R & J C College",
                "Rajagiri School of Engineering, Kakkanad",
                "Ramaiah Institute of Technology",
                "Rao Bahadur Y Mahabaleswarappa Engineering College",
                "RV College of Engineering",
                "Saintgits Engineering College, Pathamuttom",
                "Samrat Ashok Technological Institute SATI",
                "SB Jain Institute of Technology, Management and Research, Nagpur",
                "Shiv Nadar University",
                "Shri Govindram Seksaria Institute of Technology & Science SGSITS",
                "Shri Ramdeobaba College of Engineering and Management, Nagpur",
                "Shri Shankaracharya Engineering College",
                "Shri Vaishnav Institute of Technology & Science,",
                "Sister Nivedita University",
                "Sona college of Technology",
                "SRI Indu College of Engineering",
                "Sri Sivasubramaniya Nadar College of Engineering",
                "SRM Institute of Science and Technology",
                "St. Vincent Pallotti College of Engineering and Technology, Nagpur",
                "Techno India Main Salt Lake",
                "Techno India University",
                "Techno International",
                "Technocrats Institute of Technology & Science, Bhopal",
                "Thapar Institute of Engineering & Technology",
                "The Heritage Academy,",
                "TKM Engineering College",
                "Truba College of Science & Technology, Bhopal ",
                "University of Engineering & Management (UEM)",
                "University of Petroleum and Energy Studies - [UPES]",
                "Vel Tech Rangarajan Dr. Sagunthala R&D Institute of Science and Technology",
                "Vellore institute of Technology",
                "Vishwakarma Institute of Information Technology",
                "Vishwakarma Institute of Technology",
                "Vivekanand Institute of Professional Studies",
                "Yeshwantrao Chavan College of Engineering, Nagpur",
                "Others"
            ]
        }

    const semester = {
            "semesters": [
                "I Semester",
                "II Semester",
                "III Semester",
                "IV Semester",
                "V Semester",
                "VI Semester",
                "VII Semester",
                "VIII Semester"
            ]
        }

    countries.map(country=>{
        if (country.name=='India'){
            document.getElementById('countrySelect').innerHTML += "<option value='"+country.name+"' selected>"+country.name+"</option>";
        }else{
            <%if(session['wileySite'] == false || session['wileySite'] == 'false' || session['wileySite'] == null){%>
            document.getElementById('countrySelect').innerHTML += "<option value='"+country.name+"'>"+country.name+"</option>";
            <%}%>
        }
    })

    institutes.InstituteName.map(institute=>{
        document.getElementById('institutes').innerHTML += "<option value='"+institute+"'>"+institute+"</option>"
    })
    semester.semesters.map(semester=>{
        document.getElementById('semesters').innerHTML += "<option value='"+semester+"'>"+semester+"</option>"
    })

    document.getElementById('closeButton').addEventListener('click',function (){
        $('.loading-icon').removeClass('hidden');
        window.location.href = '/sp/${session['siteName']}'
    })
    document.addEventListener('load',function (){
        $('.loading-icon').removeClass('hidden');
    })

    document.getElementById('institutes').addEventListener('change',function (e){
        if (e.target.value == 'Others' || e.target.value == 'others'){
            otherInstitute = true;
            document.getElementById('otherInstituteName').classList.remove('d-none');
        }else{
            otherInstitute=false;
            document.getElementById('otherInstituteName').value = "";
            document.getElementById('otherInstituteName').classList.add('d-none');
        }
    })
</script>

