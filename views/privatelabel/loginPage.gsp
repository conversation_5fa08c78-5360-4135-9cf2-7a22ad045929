<%@ page import="javax.servlet.http.Cookie" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/privatelabel/navheader_new"></g:render>
<asset:stylesheet href="landingpage/libwonderStyles.css"/>
<asset:stylesheet href="whitelabel/additionalStyles.css"/>

<%if("160".equals(""+session["siteId"])){%>
<style>
    .banner_person img{
        width: 350px;
    }
    .banner_person{
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: -120px;
    }
    .titleRowThree{
        text-align: center;
        font-size: 24px !important;
    }
    .loginbuttonsDiv{
        justify-content: center !important;
    }
    @media (max-width: 767px) {
        .banner_person{
            margin-top: 0;
        }
        .banner_person img{
            width: 250px;
        }
        .titleRowThree{
            font-size: 18px !important;
        }
    }

    @media (min-width: 768px) and (max-width: 1024px) {
        .titleRowThree{
            font-size: 25px !important;
        }
        .cuetAcademics{
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
        .banner_wrap{
            padding-left: 0 !important;
            padding-right: 0 !important;
        }
        .banner_person img{
            width: 250px;
        }
    }
</style>
<%}%>
<div class="page-main-wrapper mdl-js information-admin p-5 cuetAcademics">
    <div class="container-fluid">
        <div class="curve-bg">
            <img src="${assetPath(src: 'libwonder/curve-bg.svg')}">
        </div>
        <section class="banner_wrap p-4 p-md-5 my-5">
            <div class="row mt-5">
                <div class="banner_info col-12 col-md-6">
                    <%if("160".equals(""+session["siteId"])){%>
                    <div class="banner_person">
                        <img src="/assets/gptsir/tmv-person.jpg" alt="TMV" />
                    </div>
                    <%}%>
                    <h1 class="titleRowThree">
                        Welcome to ${session["clientName"]}
                    </h1>
                    <div class="loginbuttonsDiv d-flex align-items-center justify-content-center justify-content-md-start mt-3 pt-3">
                        <% if(showLibrary||loggedIn){
                            if("true".equals(""+session["digitalLibraryLandingPage"])){
                        %>
                        <a class="btn btn-lg signup-btn" href="/digitalLibrary">Go to Digital Library</a>
                        <%}else{%>
                        <a class="btn btn-lg signup-btn" href="/wsLibrary/myLibrary?mode=mybooks">Go to My Books</a>
                        <%}
                        }else if(!loggedIn){%>
                        <button class="btn login-btn mr-3 cuetLogin" onclick="javascript:loginOpen()">Login</button>
                        <button class="btn signup-btn mr-3 cuetLogin" onclick="javascript:signupModal()">Signup</button>
                        <%}%>

                    </div>
                    <sec:ifNotLoggedIn>
                        <div class="d-flex align-items-center justify-content-center d-md-none mt-4">

                        </div>
                    </sec:ifNotLoggedIn>
                </div>
                <div class="banner_img col-12 col-md-6">
                    <img src="${assetPath(src: 'libwonder/lib-banner.png')}" style="width: 75%;">
                </div>
            </div>
        </section>
    </div>
</div>
<g:render template="/privatelabel/footer_new"></g:render>
<script>
    var base_url = location.protocol + '//' + location.host;
    window.history.replaceState("", "", base_url);

    if("${params.mode}"=="login"){
        document.addEventListener('DOMContentLoaded',loginOpen)
    }
</script>


