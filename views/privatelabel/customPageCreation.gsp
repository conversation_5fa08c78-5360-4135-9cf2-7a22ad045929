<g:render template="/wonderpublish/loginChecker"></g:render>

<g:render template="/privatelabel/navheader_new"></g:render>

<asset:stylesheet href="material-input.css"/>
<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<asset:stylesheet href="wonderslate/adminCommonStyle.css" async="true"/>

<style>
.sage-body .sage-banner {
    display: none;
}
.image-upload > input {
    display: none;
}

.cke_textarea_inline {
    height: 80px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}

.form-group .cke_textarea_inline{
    height: 35px;
    overflow: auto;
    border: 1px solid gray;
    -webkit-appearance: textfield;
}
</style>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="notesCreator">
    <div class="container" >
        <div id="static-content" width="90%" class="rounded">
            <div class="row ">
                <div class="col-md-12">
                    <h4>Create Page</h4><br>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">

                    <div>
                        <div class="form-group float-label-control">
                            <div class="cktext d-flex align-items-center" style="gap: 1rem">
                                <input type="text" class="form-control" id="pageName" name="pageName" placeholder="Name of the page"  maxlength="255" value="">
                                <button class='btn btn-outline-primary' id="addName">Add Name</button>
                            </div>
                        </div>
                    </div>

                    <form name="pageCreation" class="mt-3" id="pageCreation" enctype="multipart/form-data" role="form" action="/pages/addPageDetails" method="post">
                        <div class="">
                            <label class="mb-0 mt-1 mr-2">Show in :</label>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="headerTrue" name="showInHeaderCheck" disabled>
                                <label class="form-check-label" for="headerTrue">Header</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="checkbox" id="footerTrue" name="showInFooterCheck" disabled>
                                <label class="form-check-label" for="footerTrue">Footer</label>
                            </div>
                        </div>
                        <input type="hidden" name="pageId" id="pageId">
                        <input type="hidden" name="showInHeader" id="showInHeader">
                        <input type="hidden" name="showInFooter" id="showInFooter">
                        <input type="hidden" name="siteId" id="siteId">
                        <textarea name="page" id="page" rows="10" cols="80">
                        </textarea>
                        <div class="alert alert-danger col-sm-12" id="alertbox" style="display: none">
                            Please enter all the fields.
                        </div>
                        <button class="btn btn-primary submitPage mt-3" id="submitPage" onclick="validateMyForm(event)" disabled>Create Page</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
<asset:javascript src="jquery-1.11.2.min.js"/>
<script type="text/javascript" async
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<asset:javascript src="bootstrap.min.js"/>
<asset:javascript src="material-input.js"/>
<script src="https://cdn.ckeditor.com/4.7.1/full-all/ckeditor.js"></script>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<g:render template="/privatelabel/footer_new"></g:render>
</div>

<% if(request.getRequestURL().toString().indexOf("wonderslate.com")>-1){ %>
<asset:javascript src="analytics.js"/>
<% } %>
<script>
    CKEDITOR.replace( 'page', {
        height: 600,
        customConfig: '/assets/ckeditor/customConfig.js',
        extraPlugins: 'mathjax,uploadimage,image2,font,colorbutton,colordialog,autolink',
        mathJaxLib: 'https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML',

        // Upload images to a CKFinder connector (note that the response type is set to JSON).
        uploadUrl: '/pages/uploadContent?command=QuickUpload&type=Files&responseType=json&pageId='+pageID,

        // Configure your file manager integration. This example uses CKFinder 3 for PHP.
        filebrowserUploadUrl: '/pages/uploadContent?command=QuickUpload&type=Files&responseType=json&pageId='+pageID,
        filebrowserImageUploadUrl: '/pages/uploadContent?command=QuickUpload&type=Files&responseType=json&pageId='+pageID,

        // The following options are not necessary and are used here for presentation purposes only.
        // They configure the Styles drop-down list and widgets to use classes.

        stylesSet: [
            {name: 'Narrow image', type: 'widget', widget: 'image', attributes: {'class': 'image-narrow'}},
            {name: 'Wide image', type: 'widget', widget: 'image', attributes: {'class': 'image-wide'}}
        ],

        // Load the default contents.css file plus customizations for this sample.
        contentsCss: [CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css', 'http://fonts.googleapis.com/css?family=Merriweather'],
        //contentsCss: [ CKEDITOR.basePath + 'contents.css', 'assets/css/widgetstyles.css','http://fonts.googleapis.com/css?family=Merriweather','http://fonts.googleapis.com/css?family=MerriweatherSans'],


        // Configure the Enhanced Image plugin to use classes instead of styles and to disable the
        // resizer (because image size is controlled by widget styles or the image takes maximum
        // 100% of the editor width).
        image2_alignClasses: ['image-align-left', 'image-align-center', 'image-align-right'],
    });
    $(document).ready(function() {
        CKEDITOR.config.readOnly = true;
    });

    var pageName = document.getElementById('pageName');
    var addPageBtn = document.getElementById('addName');
    var submitPage = document.getElementById('submitPage');
    var footerTrue = document.getElementById('footerTrue');
    var headerTrue = document.getElementById('headerTrue');
    var pageID;
    var pageNameVal

    addPageBtn.addEventListener('click',function (){
        $('.loading-icon').removeClass('hidden')
        pageNameVal = pageName.value;
        var siteId = ${session['siteId']}
        <g:remoteFunction controller="pages" action="addPage" params="'pageName='+pageNameVal+'&siteId='+siteId" onSuccess="enablePageCreation(data)" />
    });

    function enablePageCreation(data){
        pageID = data.pageId;
        $('.loading-icon').addClass('hidden')
        if (pageID!='exists' && pageID!=undefined && pageID!=null){
            pageID = parseInt(data.pageId)
            CKEDITOR.instances['page'].setReadOnly(false);
            submitPage.removeAttribute('disabled');
            footerTrue.removeAttribute('disabled');
            headerTrue.removeAttribute('disabled');
            pageName.setAttribute('disabled','disabled');
            addPageBtn.setAttribute('disabled','disabled');
            document.getElementById('pageId').value = pageID
        }else{
            alert("THIS PAGE IS ALREADY EXISTS")
        }

    }

    function validateMyForm(e){
        var value = CKEDITOR.instances['page'].getData();
        document.pageCreation.page.value = value;
        document.pageCreation.pageId.value = pageID;
        document.pageCreation.showInHeader.value = headerTrue.checked;
        document.pageCreation.showInFooter.value = footerTrue.checked;
        document.pageCreation.siteId.value = parseInt(${session['siteId']});

        if (value!=""){
            $('.loading-icon').removeClass('hidden')
            document.pageCreation.submit();
        }else{
            event.preventDefault()
            $('.alert').show();
            setTimeout(function (){
                $('.alert').hide();
            },2000)
        }
    }

    function pageCreated(data){
        console.log(data)
    }


</script>
<script>

    function replaceAll(str, find, replace) {
        return str.replace(new RegExp(escapeRegExp(find), 'g'), replace);
    }

    function escapeRegExp(str) {
        return str.replace(/([.*+?^=!:$\{\}()|\[\]\/\\])/g, "\\$1");
    }
    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    };
</script>
</body>
</html>
