<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/navheader"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<%}%>

<asset:stylesheet href="jquery.simple-dtpicker.css"/>
<script>
    var loggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>


<!--<div>-->
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container-fluid adminForm" style="min-height: calc(100vh - 160px);" >
    <div class='row' >
        <div class='col-md-9 main p-4' style=" margin: 40px auto; float: none; padding: 15px;">
            <div id="content-books">
                <label for="userValue" style="display: block;">Get User</label>
                <div class="form-group form-inline">
                    <input type="text" class="form-control col-md-4 mr-4" name="userValue" id="userValue"  placeholder="Email or mobile number">
                    <div class="custom-control custom-radio custom-control-inline mr-4">
                        <input type="radio" id="userMode" name="userMode" value="mobile" class="custom-control-input" checked>
                        <label class="custom-control-label" for="userMode">Mobile number</label>
                    </div>
                    <div class="custom-control custom-radio custom-control-inline mr-4">
                        <input type="radio" id="userMode1" name="userMode" value="email" class="custom-control-input">
                        <label class="custom-control-label" for="userMode1">Email</label>
                    </div>
                    %{--&nbsp;&nbsp;Mobile number&nbsp;<input type="radio" name="userMode" value="mobile" checked>&nbsp;&nbsp;Email&nbsp;<input type="radio" name="userMode" value="email"><br>--}%
                </div>
                <button class="btn btn-primary col-2"  onclick="getUser();">Get User</button>

                <div id="errormsg" class="alert alert-danger has-error mt-4" role="alert" style="display: none; background: none;"></div>
                <div id="successmsg" style="display: none"></div>
                <div id="batchUsers" class="mt-4" style="display: none"></div>
            </div>
        </div>

    </div>
</div>


<% if("sage".equals(session["entryController"])){%>
<g:render template="/${session['entryController']}/footer"></g:render>
<%}else {%>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<%}%>
<!--</div>-->
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>
<asset:javascript src="jquery.simple-dtpicker.js"/>

<script>
    function getUser(){

        if(document.getElementById("userValue").value==="" ){
            document.getElementById("errormsg").innerHTML="Please enter the user email id or mobile number."
            $("#errormsg").show();
        }
        else {
            $("#errormsg").hide();
            $("#successmsg").hide();
            $("#batchUsers").show();
            var userValue = document.getElementById("userValue").value;
            var userMode=$("input[name='userMode']:checked"). val();
            <g:remoteFunction controller="log" action="getUsers" params="'userValue='+userValue+'&userMode='+userMode" onSuccess = "showUsers(data);"/>
        }

    }

    function showUsers(data){
        $('.loading-icon').addClass('hidden');
        var htmlStr= "                    <table class='table table-hover table-bordered'>\n" +
            "                        <tr class='bg-primary text-white'>\n" +
            "                            <th>Name</th>\n" +
            "                            <th>Username</th>\n" +
            "                            <th>Email</th>\n" +
            "                            <th class='text-center'>Force Logout</th>\n" +
            "                        </tr>\n" ;
        if(data.status=="OK"){
            var users = data.userList;
            for(var i=0;i<users.length;i++){
                htmlStr +="<tr><td style='text-transform:capitalize;'>"+users[i].name+"</td>"+
                    "<td>"+users[i].username+"</td>" +
                    "<td>"+users[i].email+"</td>" +
                    "<td class='text-center'><a href='javascript:logoutUser("+users[i].id+");'><img  src='${assetPath(src: 'baseline-delete-24px.svg')}' alt=''></a></td>"+
                    "</tr>";
            }
            htmlStr +="                        \n" +
                "                    </table>";
            document.getElementById("batchUsers").innerHTML= htmlStr;
        }else{
            document.getElementById("batchUsers").innerHTML= "No user found with this information.";
        }
        $("#batchUsers").show();

    }

    function logoutUser(userId){
        if(confirm("Do you want to go ahead and logout this user from all mobile devices?")) {
            <g:remoteFunction controller="log" action="forceLogout" params="'userId='+userId" onSuccess = "userLoggedOut(data);"/>
        }
    }





    function userLoggedOut(data){
        window.location.href = "/log/userManagement";
    }


</script>


</body>
</html>