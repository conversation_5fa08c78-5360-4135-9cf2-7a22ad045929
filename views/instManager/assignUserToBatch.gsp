<%@ page import="java.text.SimpleDateFormat" %>
<g:render template="/wonderpublish/loginChecker"></g:render>
<g:render template="/${session['entryController']}/navheader_new"></g:render>
<asset:javascript src="moment.min.js"/>
<link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap-datepicker/1.9.0/css/bootstrap-datepicker.min.css" rel="stylesheet" type="text/css" />
<asset:javascript src="multiselect.js"/>

<script>
    var loggedIn=false;
</script>
<style>

.table-bordered th,td {
    padding: 10px;
}


@media (min-width: 576px) {
    .modal-dialog-centered {
        min-height: calc(100% - 3.5rem);
    }
}
@media (min-width: 576px) {
    .modal-dialog {
        /*max-width: 500px;*/
        margin: 1.75rem auto;
    }
}
/* Add a border to the table */
table {
    border-collapse: collapse;
    width: 100%;
}

/* Add a border to table cells */
th, td {
    border: 1px solid #ddd;
    padding: 8px;
}

/* Set a light background color for the row headers */
th {
    background-color: #f2f2f2;
    color: black;
}
</style>
<sec:ifLoggedIn>
    <script>
        loggedIn=true;
    </script>
</sec:ifLoggedIn>
<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<div class="container"><br>
    <h2 class="text-center">Assign User to Batch</h2>
    <h4>Batch: ${batchName}</h4>
    <hr/>

        <div class="form-group">
            <label>Search Users:</label>
            <input type="text" id="search" name="search" value="${search}" class="form-control" placeholder="Name,Email,Mobile, Admission No"/>
        </div>
        <div class="form-group">
            <label class="col-sm-2 control-label">User Type:</label>
            <div class="col-sm-10">
                <g:select name="userTypeInst" from="${['Student', 'Instructor','Manager']}" required="true" class="form-control" noSelection="['':'Select User Type']"/>
            </div>
        </div>
        <div id="userResults">
            <!-- AJAX-loaded search results -->
        </div>
        <div class="form-group text-center">
            <g:link action="getBatch" params="[id: batchId]" class="btn btn-default">Cancel</g:link>
        </div>

</div>

<!--   //name, userPrompt, systemPrompt, response, promptType, feedbackType, feedback-->
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<asset:javascript src="searchContents.js"/>


</body>
</html>
<script type="text/javascript">
    $(document).ready(function() {
        // Initialize Autocomplete for Search
        $('#search').autocomplete({
            source: function(request, response) {
                $.ajax({
                    url: '${createLink(controller: 'instManager', action: 'searchUserSuggestions')}',
                    dataType: 'json',
                    data: {
                        term: request.term,
                        batchId: '${defaultBatchId}'
                    },
                    success: function(data) {
                        var html = '';
                        //add table header to the html
                        html = '<table class="table table-bordered">';
                        html += '<tr>';
                        html += '<th>Name</th>';
                        html += '<th>Action</th>';
                        html += '</tr>';
                        for(var i = 0; i < data.length; i++) {
                            html += '<tr>';
                             html += '<td>' + data[i].label + '</td>';
                           // add assign button to call assignUserToBatch function
                            html += '<td><button class="btn btn-primary" onclick="assignUserToBatch(${batchId}, \'' + data[i].value + '\')">Assign</button></td>';
                            html += '</tr>';
                        }
                        html += '</table>';
                        document.getElementById('userResults').innerHTML = html;
                    }
                });
            },
            minLength: 2
        });
    });

    function assignUserToBatch(batchId, username) {
        var userType = $("select[name='userTypeInst']").val();
        if(userType == '') {
            alert('Please select user type.');
            return;
        }

        $.ajax({
            url: '${createLink(controller: 'instManager', action: 'assignUserToBatch')}',
            type: 'POST',
            data: {
                batchId: batchId,
                username: username,
                mode: 'submit',
                userType: userType
            },
            success: function(data) {
                if(data.status == 'success') {
                    alert('User assigned to batch successfully.');
                    window.location.href = '${createLink(controller: 'instManager', action: 'getBatch')}?id=' + batchId;
                } else {
                    alert('Failed to assign user to batch.');
                }
            }
        });
    }
</script>
