<g:render template="/${session['entryController']}/navheader_new"></g:render>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/chartist/0.11.4/chartist.min.css" crossorigin="anonymous" />
<script>var defaultSiteName="${session['entryController']}";
</script>

<style>
.chartist-tooltip {
    position: absolute;
    display: inline-block;
    opacity: 0;
    min-width: 5em;
    padding: .5em;
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid #CCC;
    border-radius: 5px;
    color: #000000;
    /*font-family: Arial,sans-serif;*/
    font-weight: 500;
    font-size: 80%;
    text-align: center;
    pointer-events: none;
    z-index: 1;
    -webkit-transition: opacity .2s linear;
    -moz-transition: opacity .2s linear;
    -o-transition: opacity .2s linear;
    transition: opacity .2s linear;
}
.chartist-tooltip:before {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    width: 0;
    height: 0;
    margin-left: -15px;
    border: 15px solid transparent;
    border-top-color: #FFFFFF;
    display: none;
}
.chartist-tooltip.tooltip-show {
    opacity: 1;
}
.ct-area, .ct-line {
    pointer-events: none;
}
.ct-series-a .ct-bar {
    stroke: #FFCF02;
    stroke-width: 40px;
}
.logo_icon {
    background-image: url("/assets/eutkarsh/utkarsh-logo.svg");
    background-repeat: no-repeat;
    background-position: center;
    background-size: cover;
    width: 32px;
    height: 32px;
}
#imgPopupModal .close {
    position: absolute;
    right: -10px;
    top: -10px;
    width: 25px;
    height: 25px;
    background: #000;
    border: 2px solid #fff;
    color: #FFF;
    border-radius: 50px;
    font-size: 14px;
    font-weight: normal;
    opacity: 1;
    z-index: 999;
}
#imgPopupModal .close:focus {
    outline: 0;
}
#imgPopupModal .modal-body {
    padding: 7px;
}
#imgPopupModal #originalImage {
    width: 100%;
}
#imgPopupModal #imgCaption {
    text-align: center;
    padding: 7px;
    font-size: 14px;
}
.poll-img, .options-img {
    border-radius: 2px;
    cursor: pointer;
}
.poll-img:hover, .options-img:hover {
    opacity: 0.7;
}
#resultsPublish a.btn-success:focus,
#resultsPublish a.btn-success:focus:active{
    color: #28a745;
}
.poll-question-info .card,
.poll-results-chart .card {
    box-shadow: 0px 4px 4px rgba(151, 151, 151, 0.15);
    border-radius: 10px;
    margin-bottom: 30px;
}
.poll-question-info .card h6 {
    line-height: normal;
}
#optionsList .label-text {
    width: 15px;
}
#optionsList .option-list {
    box-shadow: 0px 4px 4px rgba(140, 140, 140, 0.15);
    border-radius: 5px;
}
#optionsList .option-list p {
    margin-bottom: 0;
}
.poll-question-info .card table thead tr th {
    font-weight: normal;
    font-size: 12px;
    border: 0;
    padding: 0.5rem;
}
.poll-results-page .poll-question-info .card table tbody tr td {
    font-weight: bold;
    font-size: 12px;
    border-top: 0;
    border-bottom: 4px solid #fff;
    background: #ffcf021a;
}
#resultsPublish a.btn {
    font-size: 16px;
    background: #FFCF02;
    box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.1);
    border-radius: 10px;
}
#totalSubmission {
    margin-bottom: 20px;
}
#pollsResultChart {
    position: relative;
}
#optionsList .correct-answer-title {
    font-weight: normal;
    color: rgba(68, 68, 68, 0.85);
}
#optionsList .correct-answer {
    border: 3px solid #67CE00;
}
</style>


<link href="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/css/bootstrap4-toggle.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/gh/gitbrent/bootstrap4-toggle@3.6.1/js/bootstrap4-toggle.min.js"></script>

<div class="loading-icon" style="display: none">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<section class="poll-results-page bg-light">
<div class="container py-5">
    <div class="row mx-0 justify-content-center align-items-start">
        <button class="btn btn-sm" onclick="javascript:history.back()">Back</button>
        <%if(params.resId!=null){%>
        <div class="col-md-10 poll-question-info">
            <div class="d-flex justify-content-between align-items-start mb-3">
                <h5 class="mb-0">Rank Holders</h5>
                <div id="consolidatedResultsPublish">
                    <a href="javascript:publishConsolidatedResults();" class="btn btn-lg btn-warning">Publish Consolidated Results</a>
                </div>
            </div>
            <div class="card border-0 p-4 text-left">
                <h6 class="mb-3">Number of Polls: ${pollCount}</h6>
                <table class="table table-rank-holders">
                    <thead style="display: none;">
                    <tr>
                        <th width="10%">RANK</th>
                        <th width="50%">NAME</th>
                        <th width="20%">MOBILE</th>
                        <th width="20%">CORRECT ANSWERS</th>
                    </tr>
                    </thead>
                    <tbody id="rankHoldersListConsolidated">

                    </tbody>
                </table>
            </div>
        </div>
       <% }else{%>
        <div class="col-md-5 poll-question-info">
            <h5 class="mb-3">${polls.name}</h5>
            <div class="card border-0 p-4 text-left">
                <h6 class="mb-0" id="pollResultQuestion"></h6>
                <div id="pollImage" class="mb-3"></div>
                <div id="optionsList"></div>
            </div>
            <div class="card border-0 p-4 text-left">
                <h5 class="mb-4">Rank Holders</h5>
                <table class="table table-rank-holders">
                    <thead style="display: none;">
                        <tr>
                            <th width="20%">RANK</th>
                            <th width="55%">NAME</th>
                            <th width="25%">MOBILE</th>
                        </tr>
                    </thead>
                    <tbody id="rankHoldersList">

                    </tbody>
                </table>

            </div>
        </div>
        <div class="col-md-5 poll-results-chart pt-4">
            <div class="card border-0 p-4 mt-3">
                <h5 class="mb-4">Results</h5>
                <h6 id="totalSubmission"></h6>
                <div id="emptyData" class="jumbotron mb-0" style="display: none;">
                    <div class="d-flex justify-content-center align-items-center">
                        <h6 class="mb-0">No data available!</h6>
                    </div>
                </div>
                <div id="pollsResultChart" style="display: none;"></div>
            </div>
            <div id="resultsPublish">
                <%if("resultsComputed".equals(polls.status)){%>
                <a href="javascript:publishResults();" class="btn btn-lg btn-warning w-100">Publish Results</a>
                <%}else if("published".equals(polls.status)){%>
                <h6 class="text-center btn btn-lg btn-warning w-100 disabled">Published</h6>
                <%}%>
            </div>
        </div>
        <%}%>
    </div>
</div>
</section>

<div class="modal fade image-popup-modal" id="imgPopupModal" data-keyboard="false" data-backdrop="true" >
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content shadow rounded">
            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                <span aria-hidden="true">X</span>
            </button>
            <!-- Modal body -->
            <div class="modal-body">
                <img id="originalImage" src="">
                <div id="imgCaption"></div>
            </div>
        </div>
    </div>
</div>

<g:render template="/${session['entryController']}/footer_new"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/chartist/0.11.4/chartist.min.js" crossorigin="anonymous"></script>
<script src="https://unpkg.com/chartist-plugin-tooltips@0.0.17/dist/chartist-plugin-tooltip.js"></script>
<script type="text/javascript"
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.0/MathJax.js?config=TeX-AMS_HTML-full">
</script>
<script>
    <%if(params.pollId!=null){%>
    function publishResults(){
        <g:remoteFunction controller="polls" action="publishPollResults" onSuccess='pollResultsPublished(data)' params="'pollId=${polls.id}'"/>
    }

    function pollResultsPublished(data){
        <g:remoteFunction controller="comments" action="addQuestion"  onSuccess="resultsNotificationSent(data)"
        params="'siteId=1&resId=${polls.resId}&question=PollResults&pollDuration=-1&pollId=${polls.id}'"/>
    }

    function resultsNotificationSent(data){
        document.getElementById("resultsPublish").innerHTML="<h6 class='text-center btn btn-lg btn-warning w-100 disabled'>Published</h6>";
    }

    function getPollResults(){
        $(".loading-icon").show();
        <g:remoteFunction controller="polls" action="getPollResults" onSuccess='displayPollResults(data)' params="'pollId=${polls.id}'"/>
    }

    var pollImgSrc = "/polls/getPollsImages?resId=${polls.resId}&fileName=${polls.imgName}&pollsId=${polls.id}&imgType=polls";
    if(${polls.imgName != null} && ${polls.imgName != ""}) {
        document.getElementById("pollImage").innerHTML = "<img src="+pollImgSrc+" alt='${polls.imgName}' onclick='openImagePopup(this);' width='70' height='70' class='poll-img'>";
    }

    var questStr = htmlDecode("${polls.question.replaceAll("(\\r|\\n)", "")}");
    document.getElementById("pollResultQuestion").innerHTML = questStr;
    MathJax.Hub.Queue(["Typeset", MathJax.Hub, "pollResultQuestion"]);

    function htmlDecode( html ) {
        var a = document.createElement( 'a' ); a.innerHTML = html;
        return a.textContent;
    }

    function displayPollResults(data){
        $(".loading-icon").hide();
        var optionsStr="";
        var rankHolderStr="";
        var results = JSON.parse(data.results);
        var rankDetails = JSON.parse(data.rankDetails);
        var options = JSON.parse(data.options);

        document.getElementById("totalSubmission").innerHTML = "Total Submissions : "+results[0].total_count;
        var counts = [results[0].option1Count,results[0].option2Count,results[0].option3Count,results[0].option4Count];
        var optionNum = ["a","b","c","d"];

        var arr = [];

        <%for(int i=0;i<details.size();i++){%>
            //countSeries.push(counts[${i}]);
            //label.push("Option ${i+1}");
            var obj = {};
            obj['data'] = counts[${i}];
            obj['label'] = optionNum[${i}];
            arr.push(obj);
        <%}%>
        
        var label = [];
        var countSeries = [];
        for (var i=0;i<arr.length;i++) {
            countSeries.push(arr[i].data);
            label.push(arr[i].label);
        }

        if((/[^0]/).exec(countSeries.join(""))){
            $("#pollsResultChart").show();
            $("#emptyData").hide();
        } else {
            $("#pollsResultChart").hide();
            $("#emptyData").show();
        }

        var chart_data = {
            labels: label,
            series: [
                {meta:"Users Clicked", data: countSeries}
            ]
        };

        var chart_options = {
            height: 300,
            fullWidth: true,
            seriesBarDistance: 30,
            axisX: {
                showGrid:false
            },
            axisY: {
                onlyInteger: true
            },
            plugins: [
                Chartist.plugins.tooltip()
            ]
        };

        new Chartist.Bar('#pollsResultChart', chart_data,chart_options);

        <%for(int i=0;i<details.size();i++){%>
            var optionImgSrc = "/polls/getPollsImages?resId=${polls.resId}&fileName=${details[i].imgName}&pollsId=${details[i].pollsId}&imgType=options";
            optionsStr +="<div class='d-flex align-items-center mb-3'>" +
                "<span class='mb-2 label-text'>"+optionNum[${i}]+".</span>";
            if(${details[i].imgName != null} && ${details[i].imgName != ""}) {
                optionsStr += "<span class='option-list ml-2 p-2 px-3 w-100'>"+htmlDecode("${details[i].optionText.replaceAll("(\\r|\\n)", "")}")+"<img src="+optionImgSrc+" alt='${details[i].imgName}' onclick='openImagePopup(this);' width='40' height='40' class='mt-2 options-img'></span></div>";
            } else {
                optionsStr +="<span class='option-list ml-2 p-2 px-3 w-100'>"+htmlDecode("${details[i].optionText.replaceAll("(\\r|\\n)", "")}")+"</span></div>";
            }
        <%}%>
        <%for(int i=0;i<details.size();i++){%>
            var optionImgSrc = "/polls/getPollsImages?resId=${polls.resId}&fileName=${details[i].imgName}&pollsId=${details[i].pollsId}&imgType=options";
            if(true === ${details[i].correctOption}) {
                optionsStr +="<h6 class='mt-4 text-left correct-answer-title'>Correct Answer</h6>";
                optionsStr +="<div class='d-flex align-items-center mb-4'>" +
                    "<span class='text-success label-text'>"+optionNum[${i}]+".</span>";
                if(${details[i].imgName != null} && ${details[i].imgName != ""}) {
                    optionsStr += "<span class='option-list ml-2 p-1 px-3 w-100 correct-answer'>"+htmlDecode("${details[i].optionText.replaceAll("(\\r|\\n)", "")}")+"<img src="+optionImgSrc+" alt='${details[i].imgName}' onclick='openImagePopup(this);' width='40' height='40' class='mt-2 options-img'></span></div>";
                } else {
                    optionsStr += "<span class='option-list ml-2 p-1 px-3 w-100 correct-answer'>"+htmlDecode("${details[i].optionText.replaceAll("(\\r|\\n)", "")}")+"</span></div>";
                }
            }
        <%}%>
        document.getElementById("optionsList").innerHTML = optionsStr;
        MathJax.Hub.Queue(["Typeset", MathJax.Hub, "optionsList"]);

        if(rankDetails!=null && rankDetails.length != null && rankDetails.length > 0){
            for(i=0;i<rankDetails.length;i++){
                rankHolderStr +="<tr><td>#"+rankDetails[i].rank+"</td><td>"+rankDetails[i].name+"</td><td>"+rankDetails[i].mobile+"</td></tr>";
                $(".table-rank-holders thead").css("display", "table-header-group");
            }
        } else {
            rankHolderStr +="<tr><td colspan='3' class='text-center' style='font-weight: normal;'>No records found!</td></tr>";
            $(".table-rank-holders thead").css("display", "none");
        }
        document.getElementById("rankHoldersList").innerHTML = rankHolderStr;
    }
    getPollResults();

    function openImagePopup(img) {
        var originalImg = document.getElementById("originalImage");
        var imgCaption = document.getElementById("imgCaption");
        originalImg.src = img.src;
        imgCaption.innerHTML = img.alt;
        $("#imgPopupModal").modal("show");
    }

    <%}else{%>
    function getConsolidatedPollResults(){
        $(".loading-icon").show();
        <g:remoteFunction controller="polls" action="getPollResultsAll" onSuccess='displayConsolidatedPollResults(data)' params="'resId=${params.resId}'"/>
    }

    function displayConsolidatedPollResults(data){
        $(".loading-icon").hide();
        var rankDetails = JSON.parse(data.rankDetails);
        var rankHolderStr="";
        if(rankDetails!=null && rankDetails.length != null && rankDetails.length > 0){
            for(i=0;i<rankDetails.length;i++){
                rankHolderStr +="<tr><td>#"+rankDetails[i].rank+"</td><td>"+rankDetails[i].name+"</td><td>"+rankDetails[i].mobile+"</td><td>"+rankDetails[i].correctAnswers+"</td></tr>";
                $(".table-rank-holders thead").css("display", "table-header-group");
            }
        } else {
            rankHolderStr +="<tr><td colspan='4' class='text-center' style='font-weight: normal;'>No records found!</td></tr>";
            $(".table-rank-holders thead").css("display", "none");
        }
        document.getElementById("rankHoldersListConsolidated").innerHTML = rankHolderStr;
    }


        getConsolidatedPollResults();

 function publishConsolidatedResults(){
     <g:remoteFunction controller="comments" action="addQuestion"  onSuccess="consolidatedResultsPublished(data)"
        params="'siteId=1&resId=${params.resId}&question=PollResultsAll&pollDuration=-1'"/>
 }

 function consolidatedResultsPublished(data){
     document.getElementById("consolidatedResultsPublish").innerHTML="<h6 class='text-center btn btn-lg btn-warning w-100 disabled'>Consolidated results published</h6>";
 }

   <% }%>
</script>


