<style>
/* Modal styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal {
    background-color: white;
    border-radius: 16px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px;
    padding: 0;
    overflow: hidden;
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal {
    transform: translateY(0);
}

.modal-header {
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid var(--light-gray);
}

.modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.modal-close {
    background: none;
    border: none;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.modal-close:hover {
    background-color: var(--light-gray);
}

.modal-body {
    padding: 25px;
    max-height: 350px;
    overflow: scroll;
}

.modal-message {
    font-size: 1rem;
    line-height: 1.5;
}

.modal-footer {
    padding: 15px 25px 25px;
    display: flex;
    justify-content: flex-end;
}

.modal-btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s ease;
    border: none;
}

.modal-btn-primary {
    background-color: var(--primary);
    color: white;
}

.modal-btn-primary:hover {
    background-color: var(--secondary);
}

/* Error message styling */
.modal-error .modal-title {
    color: var(--danger);
}

.modal-error .modal-message {
    color: #e63946;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.modal-success .modal-title {
    color: #2ecc71;
}
</style>

<!-- Modal for API messages -->
<div class="modal-overlay" id="message-modal-overlay">
    <div class="modal" id="message-modal">
        <div class="modal-header">
            <div class="modal-title" id="modal-title">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 8V12M12 16H12.01M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
                <span id="modal-title-text">Message</span>
            </div>
            <button class="modal-close" id="modal-close">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
        <div class="modal-body">
            <p class="modal-message" id="modal-message">This is a message from the API.</p>
        </div>
        <div class="modal-footer">
            <button class="modal-btn modal-btn-primary" id="modal-ok-btn">OK</button>
        </div>
    </div>
</div>


<script>
    // Modal functions
    const modalOverlay = document.getElementById('message-modal-overlay');
    const modal = document.getElementById('message-modal');
    const modalTitle = document.getElementById('modal-title-text');
    const modalMessage = document.getElementById('modal-message');
    const modalClose = document.getElementById('modal-close');
    const modalOkBtn = document.getElementById('modal-ok-btn');


    function showModal(message, isError = false) {
        modalMessage.textContent = message;
        modal.classList.remove('modal-error', 'modal-success');

        if (isError) {
            modal.classList.add('modal-error');
            modalTitle.textContent = 'Error';
            document.querySelector('.modal-title svg').innerHTML = "<path d='M12 9V11M12 15H12.01M5.07183 19H18.9282C20.4678 19 21.4301 17.3333 20.6603 16L13.7321 4C12.9623 2.66667 11.0378 2.66667 10.268 4L3.33978 16C2.56998 17.3333 3.53223 19 5.07183 19Z' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/>"
        } else {
            modal.classList.add('modal-success');
            modalTitle.textContent = 'Success';
            document.querySelector('.modal-title svg').innerHTML = "<path d='M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/>"
        }

        modalOverlay.classList.add('active');
    }

    // Hide modal
    function hideModal() {
        modalOverlay.classList.remove('active');
    }

    modalClose.addEventListener('click', hideModal);
    modalOkBtn.addEventListener('click', hideModal);
    modalOverlay.addEventListener('click', (e) => {
        if (e.target === modalOverlay) {
            hideModal();
        }
    });
</script>