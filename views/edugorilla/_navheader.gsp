<%@ page import="com.wonderslate.usermanagement.User; com.wonderslate.data.ResourceType" %>
<!DOCTYPE html>
<html>
<head>
<!-- Basic -->
<meta charset="utf-8">
  <%if(header!=null&&!"notadded".equals(header)){%>
  <%= header %>
  <%}else{%>
  <title><%= title!=null?title:"EduGorilla"%></title>
  <meta name="keywords" content="Wonderslate" />
  <meta name="description" content="Online store for digital text books">
  <%}%>
<link rel="icon"  href="${assetPath(src: '/edugorilla/favicon.png')}">
<!-- Mobile Specific Metas -->
<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">

<!-- Web Fonts  -->
<asset:stylesheet href="font-awesome.min.css"/>
<asset:stylesheet href="bootstrap.css"/>
<link rel="stylesheet" href="/assets/katex.min.css">
<% if(testGenCss){ %>
  <asset:stylesheet href="common.css"/>
  <style>
    body,html {
      padding-top: 0;
    }
  </style>
<%}%>

<link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,500,600,700" rel="stylesheet">
<link href="https://fonts.googleapis.com/css?family=Work+Sans:300,400,500,600,700" rel="stylesheet">
<link href="https://fonts.googleapis.com/css?family=Abril+Fatface" rel="stylesheet">
<asset:stylesheet href="wonderslate-material-design.css"/>
<asset:stylesheet href="edugorilla.css"/>

<asset:javascript src="jquery-1.11.2.min.js"/>
<script src="/assets/katex.min.js"></script>
<script src="/assets/auto-render.min.js"></script>
</head>
<body>

<sec:ifNotLoggedIn>
  <g:render template="/funlearn/signIn"></g:render>
</sec:ifNotLoggedIn>
  <header class="sticky-header">
  <nav id="nav" class="navbar wonderslate-navbar">
    <div class="container-fluid navbar-container">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#main-navbar" aria-expanded="false">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="/edugorilla/store?mode=browse">EduGorilla</a>
      </div>

      <div class="collapse navbar-collapse" id="main-navbar">
        <sec:ifNotLoggedIn>
          <ul class="nav navbar-nav header-menus">
            <li class="header-menu-item">
              <a href="/edugorilla/store?mode=browse" class="header-menu-item-link">Store</a>
            </li>
            <li class="header-menu-item">
              <a href="javascript:showSignUpModal();" class="header-menu-item-link">Library</a>
            </li>
          </ul>

          <ul class="nav navbar-nav navbar-right">
            <li>
              <a href="javascript:showSignInModal();" class="login-btn waves-effect waves-teal waves-ripple">Sign in</a>
            </li>
            <li>
              <a href="javascript:showSignUpModal();" class="signup-btn waves-effect waves-ripple">Sign up</a>
            </li>
          </ul>
        </sec:ifNotLoggedIn>

        <sec:ifLoggedIn>
          <ul class="nav navbar-nav header-menus">

            <li class="header-menu-item">
              <a href="/edugorilla/store?mode=browse" class="header-menu-item-link">Store</a>
            </li>
            <%%>
            <li class="header-menu-item">
              <a href="/library" class="header-menu-item-link">Library</a>
            </li>
            <li class="header-menu-item">
              <a href="/test-generator" class="header-menu-item-link">Test Generator</a>
            </li>
            <sec:ifAllGranted roles="ROLE_BOOK_CREATOR">
              <li class="header-menu-item">
                <a href="/publishing-desk" class="header-menu-item-link">Publishing Desk</a>
              </li>
            </sec:ifAllGranted>

            <sec:ifAllGranted roles="ROLE_PUBLISHER">
              <li class="header-menu-item">
                <a href="/publishing-sales" class="header-menu-item-link">Sales</a>
              </li>
            </sec:ifAllGranted>
          </ul>
          <ul class="nav navbar-nav navbar-right">
            <li class="dropdown" style="padding-bottom: 10px;">
              <a href="#" class="dropdown-toggle user-logged-in" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                  <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport" class="img-responsive">
                  <%} else { %> <img src="${assetPath(src: 'wonderslate/user-icon.png')}" alt="">
                <%}%>
              </a>
              <div class="dropdown-menu profile-dropdown">
                <a href="/creation/userProfile" style="display: block; float: left; width: 100%; font-size: 14px;">
                  <div class="user-image">
                    <%if(session['userdetails']!=null&&session['userdetails'].username!=null&&session['userdetails'].profilepic!=null){%>
                      <img src="/funlearn/showProfileImage?id=${session['userdetails'].id}&fileName=${session['userdetails'].profilepic}&type=user&imgType=passport">
                      <%} else { %> <img src="${assetPath(src: 'wonderslate/user-icon.png')}" alt="">
                    <%}%>
                    <p  class="user-edit-profile">Edit</p>
                  </div>
                  <div class="logged-in-user-details">
                    <p class="loggedin-user-name">Hello, <span id="loggedin-user-name" class="user-name"><%= session["userdetails"]!=null?session["userdetails"].name:"" %></span></p>
                    <p class="loggedin-user-mobile"><%= session["userdetails"]!=null?session["userdetails"].mobile:""%></p>
                    <p class="loggedin-user-email"><%= session["userdetails"]!=null?session["userdetails"].email:""%></p>
                  </div>
                </a>

                <div class="user-orders">
                  <a href="/creation/userProfile#orders">Your Orders</a>
                </div>

                <div class="user-logout">
                  <p>Not <%= session["userdetails"]!=null?session["userdetails"].name:"" %>? <a href="/logoff" id="logout">Sign out</a></p>
                </div>
              </div>
            </li>
          </ul>
        </sec:ifLoggedIn>
      </div>
    </div>
  </nav>
</header>




