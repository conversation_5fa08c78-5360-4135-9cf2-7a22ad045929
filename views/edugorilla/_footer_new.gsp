<g:render template="/wonderpublish/commonfooter_new"></g:render>
<script>
    var defaultSiteName="${grailsApplication.config.grails.appServer.default}";
    var userLoggedIn=false;
</script>
<sec:ifLoggedIn>
    <script>
        userLoggedIn=true;
    </script>
</sec:ifLoggedIn>
<style>
.copy-right-text-footer span{
    color: grey !important;
}
</style>
<% if(params.tokenId==null && !isBookPage){%>
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-4 responsive-none-view">
                <div class="image-wrapper-footer-logo">
                    <img src="${assetPath(src: 'edugorilla/edugorilla-logo.png')}" alt="EduGorilla Logo" />
                </div>
                <div class="text-center-align-here">
                    <ul class="there-social-footer-link-wrp">
                        <li><a href="https://www.facebook.com/edugorilladotcom" target="_blank"><i class="fa fa-facebook"></i></a></li>
                        <li><a href="https://twitter.com/edugorilla" target="_blank"><i class="fa fa-twitter"></i></a></li>
                        <li><a href="https://www.instagram.com/edugorilla/" target="_blank"><i class="fa fa-instagram"></i></a></li>
                        <li><a href="https://www.linkedin.com/company/edugorilla-pvt-ltd" target="_blank"><i class="fa fa-linkedin"></i></a></li>
                        <li><a href="https://www.youtube.com/c/EduGorilladotcom" target="_blank"><i class="fa fa-youtube-play"></i></a></li>
                        <div class="clearfix"></div>
                    </ul>
                </div>
            </div>

            <div class="col-md-8 responsive-view-none">
                <div class="row">


                    <div class="col-lg-4 col-md-6 main-div-box-link-footer">
                        <h3 class="footer-link-title">Categories</h3>
                        <ul class="link-ul-footer" id="footerCategoryLinks">

                        </ul>
                    </div>

                    <div class="col-lg-4 col-md-6 main-div-box-link-footer">
                        <h3 class="footer-link-title">Our Policy</h3>
                        <ul class="link-ul-footer" id="footerPolicyLinks">
                            <li><a href="https://edugorilla.com/privacy-policy-2/" target="_blank">Privacy Policy</a></li>
                            <li><a href="https://edugorilla.com/terms-and-conditions" target="_blank">Terms & Conditions</a></li>
                        </ul>
                    </div>

                    %{--          <div class="col-lg-4 col-md-6 main-div-box-link-footer download-app-links text-center text-md-left">--}%
                    %{--            <h3 class="footer-link-title">Download App From</h3>--}%
                    %{--            <ul class="link-ul-footer mt-3">--}%
                    %{--              <li>--}%
                    %{--                <a href="#" target="_blank" class="d-inline-flex align-items-center justify-content-center android-app-link text-left">--}%
                    %{--                  <img src="${assetPath(src: 'playstore.png')}" alt="EduGorilla Google Play Store">--}%
                    %{--                  <span>--}%
                    %{--                    <small>Google</small><br>Play Store--}%
                    %{--                  </span>--}%
                    %{--                </a>--}%
                    %{--              </li>--}%
                    %{--            </ul>--}%
                    %{--          </div>--}%

                </div>
            </div>


        </div>
    </div>

    <div class="row footer-copyright mt-2 mt-md-4 mx-0">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <p class="copy-right-text-footer"></p>
                </div>
            </div>
        </div>
    </div>
</footer>

<div class="mobile-footer-nav d-md-none" id="mobile-footer-nav"></div>
<%}%>

<script>
    //Dynamic Year in Footer
    var strDate = new Date();
    var shortYear = strDate.getFullYear();
    var nextYear = (new Date().getFullYear()+1);
    var twoDigitYear = nextYear.toString().substr(-2);
    $('.copy-right-text-footer').html('Copyright &copy; ' + shortYear +'-'+twoDigitYear+". <span> Powered by Wonderslate</span> ");

    var footerCategoryLinks="";
    for (var i = 0; i < activeCategories.length; i++) {
        footerCategoryLinks +="<li><a href='/edugorilla/store?level="+replaceAll(activeCategories[i].level.replace('&', '~'),' ','-')+"'>"+activeCategories[i].level+"</a></li>";
    }
    <% if(params.tokenId==null && !isBookPage){%>
    document.getElementById("footerCategoryLinks").innerHTML=footerCategoryLinks;
    <%}%>

    var activeCategoriesSyllabus = JSON.parse("${session["activeCategoriesSyllabus_"+session["siteId"]]}".replace(/&quot;/g,'"').replaceAll('&#92;u0026','&'));

    var displayLevel = "";
    var categoryChanged = true;
    var categorySyllabusHtml="";
    for (var i = 0; i < activeCategoriesSyllabus.length; i++) {
        if(activeCategoriesSyllabus[i].level!=displayLevel){
            categoryChanged = true;
            displayLevel = activeCategoriesSyllabus[i].level;
            //closing tag logic
            if(displayLevel!=""){
                categorySyllabusHtml +=" </ul>\n" +
                    "  </div>\n" +
                    "  </div>";
            }
            categorySyllabusHtml +="<div class=\"col-sm-4\">\n" +
                "          <div class=\"manage-row-fluides-menu-big\">\n" +
                "          <ul class=\"manage-with-all-links-big-menus\">\n" +
                "          <h4>"+activeCategoriesSyllabus[i].level+"</h4>";
        }
        if("Medical Entrances"==activeCategoriesSyllabus[i].level&&"NEET"==activeCategoriesSyllabus[i].syllabus) continue;
        categorySyllabusHtml +="<li><a href='/edugorilla/store?level="+ replaceAll(activeCategoriesSyllabus[i].level.replace('&', '~'),' ','-')+"&syllabus="+replaceAll(activeCategoriesSyllabus[i].syllabus.replace('&', '~'),' ','-')+"&grade=null'>"+activeCategoriesSyllabus[i].syllabus+"</a></li>";

    }
    //last tag close
    categorySyllabusHtml +=" </ul>\n" +
        "  </div>\n" +
        "  </div>";

</script>
<script>



    var mobileFooterNav =
        '        <div class="d-flex row justify-content-around w-100">\n' +
        '            <a href="/edugorilla/store" class="ebooks-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'ws/icon-mobile-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'ws/icon-ebooks-filled.svg')}">\n' +
        '                <p>eBooks Store</p>\n' +
        '            </a>\n' +
        '<sec:ifNotLoggedIn>\n' +
        '            <a href="javascript:loginOpen()" class="home-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'arihant/icon-arihant-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'arihant/icon-arihant-library-filled.svg')}">\n' +
        '                <p>My Books</p>\n' +
        '            </a>\n' +
        '</sec:ifNotLoggedIn>'+
        '<sec:ifLoggedIn>'+
        '            <a href="/wsLibrary/myLibrary" class="home-menu d-flex align-items-center col">\n' +
        '                <img class="mb-1 inactive" src="${assetPath(src: 'arihant/icon-arihant-library.svg')}">\n' +
        '                <img class="mb-1 active d-none" src="${assetPath(src: 'arihant/icon-arihant-library-filled.svg')}">\n' +
        '                <p>My Books</p>\n' +
        '            </a>\n' +
        '</sec:ifLoggedIn>'+
        '        </div>';

    $(document).ready(function(){
        document.getElementById('mobile-footer-nav') ? document.getElementById('mobile-footer-nav').innerHTML = mobileFooterNav : null;

        var url = window.location.href;
        if(url.indexOf("/funlearn/quiz") != -1){
            //$('.mobile-footer-nav').addClass('hide-menus');
        } else if(url.indexOf("/wsLibrary/myLibrary") != -1){
            $('.mobile-footer-nav .home-menu').addClass('active-menu');
            $('.mobile-footer-nav .home-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .home-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .ebooks-menu').addClass('common-footer-nav');
        } else if(url.indexOf("/edugorilla/store") != -1){
            $('.mobile-footer-nav .ebooks-menu').addClass('active-menu');
            $('.mobile-footer-nav .ebooks-menu .active').removeClass('d-none');
            $('.mobile-footer-nav .ebooks-menu .inactive').addClass('d-none');
            $('.mobile-footer-nav .home-menu').addClass('common-footer-nav');
        } else {
            $('.mobile-footer-nav a').addClass('common-footer-nav');
        }

    });

</script>
<asset:javascript src="landingpage/jquery.shorten.js" />
<asset:javascript src="arihant/popper.min.js"/>
<asset:javascript src="arihant/bootstrap.min.js"/>
<asset:javascript src="arihant/jquery-ui.min.js"/>
<asset:javascript src="arihant/waypoints.min.js"/>
<asset:javascript src="arihant/jquery.counterup.min.js"/>
<asset:javascript src="arihant/custom.js"/>
<asset:javascript src="landingpage/slick.js"/>
<asset:javascript src="wonderslate/material.min.js"/>
<asset:javascript src="landingpage/bootstrap-3-typeahead.js"/>
<g:render template="/books/pomodoro"></g:render>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-browser/0.1.0/jquery.browser.min.js"></script>
<script>
    window.addEventListener('keydown', function(event) {
        if (event.keyCode === 80 && (event.ctrlKey || event.metaKey) && !event.altKey && (!event.shiftKey || window.chrome || window.opera)) {
            event.preventDefault();
            if (event.stopImmediatePropagation) {
                event.stopImmediatePropagation();
            } else {
                event.stopPropagation();
            }
            return;
        }
    }, true);


    if($.browser.platform == "win") {
        $("html").addClass("windows");
    }

    if($.browser.name == "chrome") {
        $("html").addClass("chrome");
    } else if($.browser.name == "mozilla") {
        $("html").addClass("mozilla");
    } else if($.browser.name == "safari") {
        $("html").addClass("safari");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
                $('.btn-primary-modifier').css('background-color','#0b65b3 !important');
                $('.btn-success-modifier').css('background-color','#27AE60 !important');
                $('.btn-secondary-modifier').css('background-color','#8E8E8E !important');
                $('.btn-danger-modifier').css('background-color','#FF4B33 !important');
                $('.btn-warning-modifier').css('background-color','#FFD602 !important');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    } else {
        $("html").addClass("others");
        if($(window).width() < 767){
            $(".mdl-button").removeClass("mdl-js-ripple-effect").click(function() {
                $(this).css('background','#eee');
            });
            document.addEventListener('touchmove', function (event) {
                if (event.scale !== 1) { event.preventDefault(); }
            }, { passive: false });
        }
    }
</script>

<g:render template="/wsshop/cartScripts"></g:render>
<g:render template="/wsshop/searchScripts"></g:render>