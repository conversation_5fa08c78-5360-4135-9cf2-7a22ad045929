<g:render template="/${session['entryController']}/navheader_new"></g:render>
    <style>
    .custom_container_new{
        width: calc(100% - 10%);
        margin: 0 auto;
    }
    .priceListTable tr th,
    .priceListTable tr td{
        padding: 10px;
    }
    .priceListTable tr td > *{
        width: 100%;
    }


    .processing {
        background-color: yellow;
    }

    .ok {
        background-color: white;
        color: black;
    }

    .error {
        background-color: red;
        color: white;
    }
    #tableContainer {
        overflow: scroll;
    }
    </style>
    <script src="/assets/xlsx.full.min.js"></script>

<div class="custom_container_new">
    <h4 class="text-center my-4">Books upload manager</h4>
    <div class='row' >
        <div class='col-md-9 main' style=" margin: 20px auto; float: none; padding: 15px;">
            <div id="content-books">

                    <div class="d-flex justify-content-evenly download-content" style="gap: 14px">
                        <div class="d-flex align-items-center justify-content-center flex-column w-100">
                            <g:select id="publisherId" class="form-control" optionKey="id" optionValue="name"
                                      name="publisherId" from="${publishers}" noSelection="${['':'All publisher']}"/>
                        </div>
                        <div class="d-flex align-items-center justify-content-center flex-column w-100">
                            <g:select id="bookType" class="form-control" optionKey="bookType" optionValue="bookType"
                                      name="bookType" from="${bookTypes}" noSelection="${['':'Select Book Type']}"/>
                        </div>

                        <div class="form-group  w-100" >
                            <label for="excelFileInput">Choose Excel File:</label>
                            <input type="file" id="excelFileInput" accept=".xlsx, .xls" class="form-control">
                        </div>
                        <div class="w-100">
                            <div class="text-center">
                                <button type="button" id="download-btn" class="btn btn-primary btn-lg col-4" style="border-width: 0px;" onclick="handleFileUpload()">Upload</button>
                            </div>
                        </div>


                    </div>

            </div>
            </div>


    </div>
</div>
<div class="custom_container_new">
    <div class="row">
        <div id="tableContainer"></div><br><br>

    </div>
    <div class="row" >
        <button id="exportButton" class="btn btn-primary" style="display: none">Export Failed Rows</button>
    </div>
    <div class="row" style="display: none">
        <div id="errorTableContainer"></div>
    </div>
    </div>


<div class="loading-icon hidden">
    <div class="loader-wrapper">
        <div class="loader">Loading</div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>
    var input = document.getElementById('excelFileInput');
    var tableContainer = document.getElementById('tableContainer');
    var table,errorTable;
    var failedRows = [];
    var mandatoryFields = ['Book_Title','Sell_Price','List_Price'];
    var columnNames;

    function handleFileUpload() {
        if (document.getElementById("publisherId").selectedIndex == 0) {
            alert("Select Publisher");
            document.getElementById("publisherId").focus();

        } else if (document.getElementById("bookType").selectedIndex == 0) {
            alert("Select Book Type");
            document.getElementById("bookType").focus();
        }else if(document.getElementById('excelFileInput').files.length==0){
            alert("Please select the file to upload.");
            document.getElementById('excelFileInput').focus();
        }
        else {
            failedRows = [];
            var fileInput = document.getElementById('excelFileInput');
            var file = fileInput.files[0];
            var reader = new FileReader();

            reader.onload = function (e) {
                var data = new Uint8Array(e.target.result);
                var workbook = XLSX.read(data, {type: 'array'});

                var sheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[sheetName];
                var jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});

                 columnNames = jsonData[0]; // Assuming the first row contains column names

                // Create the initial table with column names
                createTable(columnNames);


                // Start the API calling process
                callServerAPI(jsonData, columnNames, 1);
            }


            reader.readAsArrayBuffer(file);
        }
    }

    function createTable(columnNames) {
        document.getElementById("tableContainer").innerHTML="";
        var element = document.getElementById("statusTable");
        if (element !== null) {
            element.remove(); // Removes the element from the DOM
        }
        table = document.createElement('table');
        table.id='statusTable';
        table.classList.add('priceListTable');
        var thead = document.createElement('thead');
        var tr = document.createElement('tr');

        // Create table headers with column names
        for (var i = 0; i < columnNames.length; i++) {
            if(columnNames[i]=='Book_Description') continue;
            var th = document.createElement('th');
            th.textContent = columnNames[i];
            tr.appendChild(th);
        }

        // Add an additional column for status updates
        var statusTh = document.createElement('th');
        statusTh.textContent = 'Status';
        tr.appendChild(statusTh);

        thead.appendChild(tr);
        table.appendChild(thead);

        tableContainer.appendChild(table);
    }



    function updateRowStatus(rowIndex, status,information) {
       var row = table.rows[rowIndex];

        // Clear existing status classes
        row.classList.remove('processing', 'ok', 'error');
        console.log("status is "+status);
        if (status === 'OK') {
            row.classList.add('ok');
            console.log("it is ok man");
        } else {
            console.log("is it entering this?");
            row.classList.add('error');
            failedRows.push(row);
        }

        // Update the status column text
        var statusCell = row.cells[row.cells.length - 1];
       statusCell.textContent = information;
    }

    function callServerAPI(jsonData, columnNames, rowIndex) {
        $("#exportButton").show();
        // Stop the process if the rowIndex exceeds the jsonData length
        if (rowIndex >= jsonData.length) {
            console.log("All rows processed.");
            return;
        }

        var row = jsonData[rowIndex];
        var rowData = {};

        // Iterate through each cell in the row and build the row data object
        for (var j = 0; j < row.length; j++) {
            var columnName = columnNames[j];
            var cellValue = row[j];
            rowData[columnName] = cellValue;
        }

        // Check if all columns are empty in the current row
        var allColumnsEmpty = Object.values(rowData).every(cellValue => cellValue === "");

        // Stop the process if all columns are empty
        if (allColumnsEmpty) {
            console.log("All columns are empty. Stopping the process.");
            return;
        }

        // Update the current row as processing
        var newRow = table.insertRow();
        newRow.classList.add('processing');
        var mandatoryFieldsPresent = true;
        var missingMandatoryFields= "";
        for (var i = 0; i < columnNames.length; i++) {
            if(columnNames[i]=='Book_Description') continue;
            var cell = newRow.insertCell();
            cell.textContent = row[i];
            if(mandatoryFields.indexOf(columnNames[i])>-1){
                if(!cell.textContent){
                    mandatoryFieldsPresent=false;
                    missingMandatoryFields +=" "+columnNames[i];
                }
            }
        }

        // Add the status cell for the processing row
        var statusCell = newRow.insertCell();
        if(mandatoryFieldsPresent) {
            statusCell.textContent = 'Processing';

            // Call the server API with the row data
            callServerAPIInternal(rowData)
                .then(response => {
                    // Handle the server API response as needed
                    console.log(response);

                    // Update the row status based on the API response
                    updateRowStatus(rowIndex, response.status,response.information);

                    // Call the next API immediately
                    callServerAPI(jsonData, columnNames, rowIndex + 1);
                })
                .catch(error => {
                    // Handle any errors that occur during the API call
                    console.error('Error:', error);

                    // Update the row status as error
                    updateRowStatus(rowIndex, 'Error',response.information);

                    // Call the next API immediately even if an error occurred
                    callServerAPI(jsonData, columnNames, rowIndex + 1);
                });
        }else{
            updateRowStatus(rowIndex, 'Error',missingMandatoryFields+" missing");
            callServerAPI(jsonData, columnNames, rowIndex + 1);
        }
    }

    function exportFailedRows() {
        createErrorTable(columnNames);
        console.log("row length="+failedRows.length);
        for(var i=0;i<failedRows.length;i++){
            errorTable.appendChild(failedRows[i]);
        }
        var workbook = XLSX.utils.book_new();
        var worksheet = XLSX.utils.table_to_sheet(document.getElementById('errorStatusTable'));



        XLSX.utils.book_append_sheet(workbook, worksheet, 'Failed Rows');
        XLSX.writeFile(workbook, 'failed_rows.xlsx');
    }

    var exportButton = document.getElementById('exportButton');
    exportButton.addEventListener('click', exportFailedRows);

    function callServerAPIInternal(rowData) {
        // Make an AJAX request or use fetch() to call your server API
        // Adjust the URL and method as per your server implementation
        var url = '/excel/processRow';

        rowData['publisherId']=document.getElementById("publisherId")[document.getElementById("publisherId").selectedIndex].value;
        rowData['bookType']=document.getElementById("bookType")[document.getElementById("bookType").selectedIndex].value;
        return fetch(url, {
            method: 'POST',
            body: JSON.stringify(rowData),
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json());
    }

    function createErrorTable(columnNames) {
        document.getElementById("errorTableContainer").innerHTML="";
        var element = document.getElementById("errorStatusTable");
        if (element !== null) {
            element.remove(); // Removes the element from the DOM
        }
        errorTable = document.createElement('table');
        errorTable.id='errorStatusTable';
        errorTable.classList.add('priceListTable');
        var thead = document.createElement('thead');
        var tr = document.createElement('tr');

        // Create table headers with column names
        for (var i = 0; i < columnNames.length; i++) {
            var th = document.createElement('th');
            th.textContent = columnNames[i];
            tr.appendChild(th);
        }

        // Add an additional column for status updates
        var statusTh = document.createElement('th');
        statusTh.textContent = 'Status';
        tr.appendChild(statusTh);

        thead.appendChild(tr);
        errorTable.appendChild(thead);

        errorTableContainer.appendChild(errorTable);
    }
</script>
</body>
</html>
