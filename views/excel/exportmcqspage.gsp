<g:render template="/${session['entryController']}/navheader_new"></g:render>


<style>
h2 {
    color: #212529;
    margin-bottom: 20px;
    text-align: center;
}

.chapter-container {
    max-width: 600px;
    margin: 0 auto;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 20px;
}

.select-all-container {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.select-all-container input {
    margin-right: 10px;
    transform: scale(1.2);
}

.chapter-list {
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
}

.chapter-item {
    padding: 12px 20px;
    margin: 5px 0;
    border: 1px solid #e3e6f0;
    border-radius: 6px;
    background-color: #f9f9f9;
    display: flex;
    align-items: center;
    transition: all 0.2s ease-in-out;
}

.chapter-item:hover {
    background-color: #f1f3f5;
    cursor: pointer;
    transform: scale(1.02);
}

.chapter-item input {
    margin-right: 10px;
}

.button-container {
    text-align: center;
}

button {
    padding: 12px 30px;
    font-size: 16px;
    background-color: #007bff;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

button:hover {
    background-color: #0056b3;
}

#selectedChapters {
    margin-top: 20px;
    text-align: center;
    font-weight: bold;
    color: #28a745;
}

.message {
    text-align: center;
    color: #dc3545;
    font-weight: bold;
}


.spinner-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 9999;
}

.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>


<div class="container mt-5" style="min-height: 80vh">
    <h2>Export MCQs to Excel</h2>

    <div class="chapter-container">
        <div class="select-all-container">
            <input type="checkbox" id="selectAll" /> <strong>Select All Chapters</strong>
        </div>

        <div class="chapter-list">

        </div>

        <div class="button-container">
            <button onclick="handleExport()">Export MCQs</button>
        </div>

        <div id="selectedChapters"></div>
        <div class="message" id="errorMessage"></div>
    </div>

    <div class="spinner-container" id="spinnerContainer">
        <div class="spinner"></div>
    </div>
</div>
<g:render template="/${session['entryController']}/footer_new"></g:render>
<script>

    const chaptersList = JSON.parse((("${chaptersList}").replace(/&#92;/g,'\\')).replace(/&quot;/g,'"'));

    const chapterListDiv = document.querySelector('.chapter-list');
    chaptersList.forEach(function(chapter) {
        const checkbox = document.createElement("input");
        checkbox.type = "checkbox";
        checkbox.className = "chapterCheckbox";
        checkbox.value = chapter.id;
        checkbox.dataset.name = chapter.name;

        const label = document.createElement("label");
        label.appendChild(document.createTextNode(chapter.name));

        const container = document.createElement("div");
        container.className = "chapter-item";
        container.appendChild(checkbox);
        container.appendChild(label);

        chapterListDiv.appendChild(container);
    });

    const bookId = ${params.bookId};
    let selectedChapters = [];

    document.getElementById("selectAll").addEventListener("change", function() {
        const checkboxes = document.querySelectorAll(".chapterCheckbox");
        checkboxes.forEach(checkbox => checkbox.checked = this.checked);
        updateSelectedChapters();
    });

    function updateSelectedChapters() {
        const checkboxes = document.querySelectorAll(".chapterCheckbox:checked");
        selectedChapters = Array.from(checkboxes).map(checkbox => ({
            id: checkbox.value,
            name: checkbox.dataset.name
        }));
        displaySelectedChapters();
    }

    function displaySelectedChapters() {
        if (selectedChapters.length > 0) {
            document.getElementById("selectedChapters").textContent = "Selected Chapters: " +
                selectedChapters.map(ch => ch.name).join(", ");
            document.getElementById("errorMessage").textContent = '';
        } else {
            document.getElementById("selectedChapters").textContent = '';
        }
    }

    const chapterCheckboxes = document.querySelectorAll(".chapterCheckbox");
    chapterCheckboxes.forEach(checkbox => checkbox.addEventListener("change", updateSelectedChapters));

    async function handleExport() {
        if (selectedChapters.length === 0) {
            document.getElementById("errorMessage").textContent = "Please select at least one chapter!";
            return;
        }

        document.getElementById("errorMessage").textContent = '';
        showLoader(true);

        const paramObj = {
            chapterIds: selectedChapters,
            bookId: bookId
        };

        try {
            const response = await fetch("/excel/exportmcqs", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(paramObj)
            });

            if (!response.ok) {
                throw new Error('Something went wrong');
            }

            const disposition = response.headers.get("Content-Disposition");
            let filename = "download.zip";
            if (disposition && disposition.includes("filename=")) {
                filename = disposition.split("filename=")[1].replace(/"/g, "").trim();
            }

            const blob = await response.blob();
            const downloadUrl = URL.createObjectURL(blob);
            const downloadLink = document.createElement("a");
            downloadLink.href = downloadUrl;
            downloadLink.download = filename;
            document.body.appendChild(downloadLink);
            downloadLink.click();

            URL.revokeObjectURL(downloadUrl);
            downloadLink.remove();
            showLoader(false);
            alert("Export Successful! Download started.");
        } catch (error) {
            showLoader(false);
            document.getElementById("errorMessage").textContent = 'Something went wrong';
        }
    }

    function showLoader(isLoading) {
        const spinnerContainer = document.getElementById('spinnerContainer');
        spinnerContainer.style.display = isLoading ? 'flex' : 'none';
    }
</script>

</body>
</html>
