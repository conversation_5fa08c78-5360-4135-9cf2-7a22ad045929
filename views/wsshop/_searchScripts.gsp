<style>
.error-message {
    color: red;
}
</style>
<script>
    // Search feature scripts
    var searchDataValues;
    var searchStringValues;
    var searchInputSelector = "#search-book-store";
    var siteNamesPrep = "${session['siteName']}";

    $(document).ready(function(){
        $('#search-btn-header, #search-btn-store, #search-btn-home').attr('disabled',true);

        // Navigation search
        $('#search-book-header').keyup(function(){
            <%if("true".equals(session["prepjoySite"])){%>
            prepJoySearchBook = true;
            <%}%>
            if($(this).val().length !=0) {
                searchInputSelector = "#search-book-header";
                searchTypeAhead(searchInputSelector);
                $('#search-btn-header').attr('disabled', false);
            } else {
                $('#search-btn-header').attr('disabled',true);
            }
        });
        $('#search-book-header').on('paste', function () {
            searchInputSelector = "#search-book-header";
            searchTypeAhead(searchInputSelector);
        });

        // Store search
        $('#search-book-store').keyup(function(){
            if($(this).val().length !=0) {
                searchInputSelector = "#search-book-store";
                searchTypeAhead(searchInputSelector);
                $('#search-btn-store').attr('disabled', false);
            } else {
                $('#search-btn-store').attr('disabled',true);
            }
        });
        $('#search-book-store').on('paste', function () {
            searchInputSelector = "#search-book-store";
            searchTypeAhead(searchInputSelector);
        });

        // Home search
        $('#search-book-home').keyup(function(){
            if($(this).val().length !=0) {
                searchInputSelector = "#search-book-home";
                searchTypeAhead(searchInputSelector);
                $('#search-btn-home').attr('disabled', false);
            } else {
                $('#search-btn-home').attr('disabled',true);
            }
        });
        $('#search-book-home').on('paste', function () {
            searchInputSelector = "#search-book-home";
            searchTypeAhead(searchInputSelector);
        });

        $('#librarySearch').keyup(function(){
            if($(this).val().length !=0) {
                searchInputSelector = "#librarySearch";
                searchTypeAheadLibrary(searchInputSelector);
                $('#search-btn-home').attr('disabled', false);
            } else {
                $('#search-btn-home').attr('disabled',true);
            }
        });
        $('#search-book-home').on('paste', function () {
            searchInputSelector = "#librarySearch";
            searchTypeAheadLibrary(searchInputSelector);
        });


        //Library Search
    });

    function searchTypeAhead(searchInputSelector) {
        $(searchInputSelector).typeahead({
            minLength : 3,
            source: function(query, process) {
                $.ajax({
                    url: '/discover/searchList',
                    method: 'GET',
                    data: {query:query},
                    dataType: 'JSON',
                    success: function fetchBooks(data) {
                        process($.map(data.searchList, function(item) {
                            if(query === '') {
                                return(item);
                            } else {
                                return item;
                            }
                        }));
                        searchDataValues = data.searchValues;
                        searchStringValues = data.searchList;
                        <%if("books".equals(""+session["entryController"])){%>
                        if (searchDataValues.length == 0 && searchInputSelector == "#search-book-home") {
                            document.querySelector(".quick-search").classList.replace("hide", "show");
                            document.querySelector("#searchEmpty").setAttribute("style", "display: block");
                        } else {
                            if (document.querySelector(".quick-search")!=null){
                                document.querySelector(".quick-search").classList.replace("show", "hide");
                            }
                        }
                        <%}%>
                    }
                })
            },
            afterSelect: function(){
                submitSearchHeader();
            }
        }).focus();
    }
<%if("true".equals(librarySearch)){%>
    function searchTypeAheadLibrary(searchInputSelector) {
        $(searchInputSelector).typeahead({
            minLength : 3,
            source: function(query, process) {
                $.ajax({
                    url: '/discover/searchList?batchId='+batchId,
                    method: 'GET',
                    data: {query:query},
                    dataType: 'JSON',
                    success: function fetchBooks(data) {
                        process($.map(data.searchList, function(item) {
                            if(query === '') {
                                return(item);
                            } else {
                                return item;
                            }
                        }));
                        searchDataValues = data.searchValues;
                        searchStringValues = data.searchList;
                        if (searchDataValues.length == 0 ) {
                            searchBookId=-1;
                        }

                    }
                })
            },
            afterSelect: function(){
                submitLibrarySearch();
            }
        }).focus();
    }


    function submitLibrarySearch(){
            $('#resetLibraryFilter').removeAttr('disabled');
            document.querySelector(".quick-search").classList.replace("show", "hide");
            document.querySelector("#librarySearchEmpty").setAttribute("style", "display: none");

        var searchString = $("#librarySearch").val();
        var requestList = "";

        if(searchString.length == 0) {

        } else {
            $('ul.typeahead').css('display','none');
            <%if("true".equals(session["prepjoySite"])){%>
            $('#loading').show();
            <%}else{%>
            $('.loading-icon').removeClass('hidden');
            <%}%>

            for(var i=0;i<searchStringValues.length;i++){
                if(searchStringValues[i]==searchString){
                    var searchData = searchDataValues[i];
                    //callRemoteSearch = populateFromFilters(searchDataValues[i]);

                    if(searchData.hasOwnProperty("bookId")){
                        searchBookId= searchData.bookId;
                    }

                    break
                }
            }
            document.getElementById("show-library-books").innerHTML="";
            getInstituteBooksList(pageNo, instituteId, batchId, allBooksLoaded, "true", freeBooks, paidBooks, syllabusSelected, gradeSelected,testSeries);

        }
    }
<%}%>


    function submitSearchHeader(){
        var searchString = $(searchInputSelector).val();
        var requestList = "";

        if(searchString.length == 0) {

        } else {
            $('ul.typeahead').css('display','none');
            <%if("true".equals(session["prepjoySite"])){%>
            $('#loading').show();
            <%}else{%>
            $('.loading-icon').removeClass('hidden');
            <%}%>
            if (typeof submitSearchTop === 'function') {
                submitSearchTop();
                $('html, body').animate({scrollTop: $('.searching-book-store').offset().top - 77 }, 'slow');
            }else{
                if(searchStringValues!=null&&searchStringValues!=""){
                    //use the filters
                    for(var i=0;i<searchStringValues.length;i++){
                        if(searchStringValues[i]==searchString){
                            var searchData = searchDataValues[i];
                            //callRemoteSearch = populateFromFilters(searchDataValues[i]);

                            if(searchData.hasOwnProperty("publisherId")){
                                requestList += "&publisherId="+searchData.publisherId;
                            }
                            if(searchData.hasOwnProperty("level")){
                                requestList += "&level="+searchData.level;
                            }
                            if(searchData.hasOwnProperty("syllabus")){
                                requestList += "&syllabus="+searchData.syllabus;
                            }
                            if(searchData.hasOwnProperty("grade")){
                                requestList += "&grade="+searchData.grade;
                            }
                            if(searchData.hasOwnProperty("subject")){
                                requestList += "&subject="+searchData.subject;
                            }
                            break
                        }
                    }
                }
                <%if("true".equals(session["prepjoySite"])){%>
                    if (siteNamesPrep == 'prepjoycurrentaffairs'){
                        siteNamesPrep = 'currentaffairs'
                    }
                    window.location.href="/"+siteNamesPrep+"/eBooks?searchString="+encodeURIComponent(searchString)+requestList;
                <%}else if("true".equals(session["commonWhiteLabel"])){%>
                    window.location.href="/sp/<%= session["siteName"] %>/store?searchString="+encodeURIComponent(searchString)+requestList;
                <%}else{%>
                    window.location.href="/<%= session["entryController"] %>/store?searchString="+encodeURIComponent(searchString)+requestList;
                <%}%>
            }
        }
    }

    $(document).on("keypress", "#search-book-header", function(e) {
        if( e.which === 32 && this.value === '' ) {
            return false;
        } else if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearchHeader();
            }
        }
    });
    $(document).on("keypress", "#search-book-home", function(e) {
        if( e.which === 32 && this.value === '' ) {
            return false;
        } else if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearchHeader();
            }
        }
    });
    $(document).on("keypress", "#search-book-store", function(e) {
        if( e.which === 32 && this.value === '' ) {
            return false;
        } else if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitSearchHeader();
            }
        }
    });
    $(document).on("keypress", "#librarySearch", function(e) {
        if( e.which === 32 && this.value === '' ) {
            return false;
        } else if (e.which == 13) {
            if (e.keyCode == 13) {
                // to prevent submitting of the page
                e.preventDefault();
                submitLibrarySearch();
            }
        }
    });

    //ideally this should have been in the commonfooter.Since WS doesn't use common footer, adding it here.
    function genericValidateForm(formId){
        const form = document.getElementById(formId);
        const elements = form.elements;
        // Remove existing error messages
        const errorMessages = form.querySelectorAll(".error-message");
        errorMessages.forEach(function(errorMessage) {
            errorMessage.remove();
        });
        // Check each form element for validation
        for (let i = 0; i < elements.length; i++) {
            const element = elements[i];

            // Skip elements that are not required or are empty
            if (!element.required || element.value.trim() !== "") {
                continue;
            }

            // Show an error message for invalid elements
            const errorMessage = document.createElement("span");
            errorMessage.classList.add("error-message");
            errorMessage.textContent = "This is required";
            element.parentNode.insertBefore(errorMessage, element.nextSibling);
            element.focus();

            return false; // Stop further processing
        }
        return true
    }

</script>
