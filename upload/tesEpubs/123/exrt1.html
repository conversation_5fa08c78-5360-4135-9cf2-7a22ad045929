
<html lang="en">
<head>
    <meta charset="utf-8">
    <title>CHAPTER 1 Introduction</title>
    <link rel="stylesheet" type="text/css" href="epub.css" /></head><body><div class="chapter" id="s9789354791079.i382" title="CHAPTER 1 Introduction">
<a id="page1"></a>


<h1 class="head-title"><b><span class="label">CHAPTER 1</span> Introduction</b></h1>
<div class="sect1" id="s9789354791079.i387" title="">
<h1 class="title"><b><span class="label">1.1.</span> Prologue</b></h1>
<p>I still remember the day during my second year of study at a college in which the first lecture on prosody was taken up by a widely respected teacher. He quoted verses written by several eminent poets, including some poet laureates, with accents to put stress on some syllables but without mentioning the phrase ‘metrical structure’. We all enjoyed his recitations. In the second lecture (possibly the next day), he referred back to some of those verses, told us about the differences between them in terms of sequences of long and short syllables or syllables with stress and those with no stress and mentioned iambic, trochee, dactyl and anapaest as different metres in which verses are composed. Subsequent lectures on details, including some mnemonic verses, interested almost all of us. I had a friend studying at a different college and I gathered from him that their teacher had in the first lecture on prosody stated the different possible metric forms and provided illustrations from verses written by famous authors. These two teachers and a whole host of other teachers must have solved a decision problem—some knowingly, but most of them unconsciously—to choose one of the two alternatives, namely (a) mentioning the metric structures first and then providing illustrations and (b) considering verses written in different structures first and then mentioning the metric forms, referring to those illustrations. Most, if not all, teachers <a id="page2"></a>have solved this choice problem without any analysis but based on experience and intuition.</p>
<p>While exposing some topics in science, a decision is, implicitly or unconsciously, taken about whether to state general principles first, followed by examples, or to put forth examples first and to come up with general principles later, starting from the examples. It also remains true that the same teacher or exponent chooses one option on one occasion and the other option on a different occasion, depending on their perception of what would appeal most to the audience on each occasion.</p>
<p>Decisions can be—and they are quite often—situation-specific. Some simple and oft-used measures in statistics, for example, the standard deviation, can be explained as something as radius of gyration to a student with a physics or mathematics background, as the root mean square of current strength in an AC circuit to a student who knows about current electricity or just as the minimum root mean square deviation from some measure of central tendency to a student of statistics. The objective is to make the concept comprehended by the student group as easily as possible, and the exponent chooses one of these alternative ways to achieve this objective. The choice depends on the exponent’s assessment of the background of the majority of students. Some political leaders depend more on logical reasoning than on facts and figures to make their points, while others refer to such facts and figures with a penchant for convincing their audiences. There could be others who would use both strategies. And they choose the alternatives (strategies) beforehand. Sometimes, the same leader may choose different strategies to drive their point home to different audience types.</p>
</div>
<div class="sect1" id="s9789354791079.i388" title="">
<h1 class="title"><b><span class="label">1.2.</span> Decisions and Decision-making</b></h1>
<p>Decisions are made now—either instantaneously, on the basis of experience, intuition or alacrity and not involving any explicit analysis, or at the end of a due process of gathering and analysing pertinent information followed by the application of some logical or empirical principles—but are meant (in terms of implementation) for the future—immediate or near or remote. Exigencies may demand instant <a id="page3"></a>decisions, while planning requires well-worked out decisions. There could be a few exceptions where decisions are taken at the end of some implementation exercise to find out whether the objective(s) behind the implementation could be achieved or to what extent they could be achieved. (It may be rightfully argued that such an implementation exercise was preceded by a decision-making process.) Post-implementation decisions may also lead to identification of more effective and more efficient ways of implementation or even to scaling up or down the original objective(s). Such decisions are necessarily the outputs of due decision-making processes (DMPs), backed up by quantitative analysis and qualitative comprehension and support.</p>
<p>In some senses, decisions (preceding implementation) are like ‘designs’ for products or services. (One point of distinction may be borne in mind at this stage. Usually, many product/service units come out of the same design. However, even for repetitive actions of the same kind, decisions preceding these actions may change.) Just as ‘quality of design’, ‘quality of conformance to design requirements’ and ‘quality of performance’ are well-recognized facets of the quality of a product or a service, we can think of the ‘effectiveness of a decision’, ‘effectiveness of implementing the decision’ and ‘effectiveness of a decision on implementation in meeting the original objective(s)’ as three distinguishable entities. In fact, as in the case of a manufactured product or a service, we recognize the effectiveness of a decision to meet the original objective(s) only in terms of implementation being able to meet the objective(s). And in either case, we assume that during implementation there is no deviation from the ‘design’ or the ‘decision’. It is possible to accommodate a question regarding any such deviation during the post-implementation decision-making process.</p>
<p>Decision analysis, theory of social choice, operations research, management science and related subjects are expected to deal with formulation, analysis and solution of decision problems faced by individuals, groups, organizations, states and even international bodies. However, looking at the content of many other disciplines, we come across many decision problems—not possibly mentioned and treated as such—which have become integral components of discourses in those disciplines. A close look at the following sample of decision problems <a id="page4"></a>will be convincing to some extent. These problems may all appear to be statistical in nature and may be discussed incidentally or in the usual course in standard texts and reference books on statistics. However, they cover various concerns faced by academics and professionals. Each one of them deserves to be comprehended fully as a decision-making problem with all its implications.</p>
<p>Decisions usually precede actions; decisions can also follow actions and their outcomes. The first type of decision is more important with outcomes associated with the available alternatives or options (features or attributes appearing in criteria for assessment/selection of alternatives) either known with certainty or known in terms of a probability distribution or to be observed after some simulation experiments or (small-scale) trial runs of actual experiments. These features may be observable/measurable right now (at the time of decision-making) or in the future over a certain fixed or random period. In some real-life situations, decisions precede actions, which are followed by decisions, which again are followed by decisions and so on, till a stopping rule—as a part of the decision-making process—enjoins a final decision.</p>
<p>Decision-making constitutes the core of management. It is a critical component of strategic management where we engage ourselves in fixing goals and priorities, working out a roadmap to achieve the goals and allocate resources for the different tasks to be taken up. Decision-making involves a blend of logic and intuition. This can definitely borrow strength from decision theory, and this theory has been being enriched by recent developments in data science.</p>
<p>Decision-making has been an area of interest for philosophers and scientists, economists and sociologists, executives and managers and a whole host of other not-so-explicitly-mentioned individuals and groups. Decision theory (as distinct from decision-making as an exercise that does not necessarily follow as a logical corollary of decision theory) that is more or less context-free as well as subject-free is comprehended in terms of decision rules to be followed to arrive at a ‘good’ decision, the interpretation of the word ‘good’ being left for further elaboration and confirmation. What has become far more important, especially to executives and managers, is decision-making in practice <a id="page5"></a>by individuals and groups in specific situations, combining in intricate fashions, subjective intuitions and instincts, domain knowledge and experience with objective rules applied to relevant information that is available. It is worthwhile to mention that operations research, which has been the primary motive behind the development of many models, methods and techniques as well as algorithms and software used in making and implementing as well as evaluating decisions in complex man-machine-material-environment systems, was defined by Miller and Starr (1973) as ‘applied decision-making’.</p>
<p>It should be borne in mind that decision-making is not a monopoly of managers and executives (in the usual sense) only. Every one of us takes decisions all the time in our lives—to decide the course of studies to follow, to choose the profession or occupation to practise, to select the person to marry or remain single in life, to choose the place to live in, to visit the market every day or twice a week, to buy or rent a house or a car and so on. On some occasions, we take instant decisions, and on other occasions, we go on delaying a decision. In some cases, we collate all the relevant information before we reach a decision, while in some other situations, we simply use our ready wit to arrive at a decision. Of course, we calculate costs and benefits when we decide to invest in some properties or in some shares or stocks. Sometimes, we take decisions all by ourselves, and at other times, we consult somebody whom we trust as more knowledgeable or better informed to help us reach a good decision. Situations do exist when we find that decisions taken earlier have paid us well. At the same time, we also face situations when we regret such decisions.</p>
</div>
<div class="sect1" id="s9789354791079.i390" title="">
<h1 class="title"><b><span class="label">1.3.</span> Ubiquity of Decisions</b></h1>
<p>A very common decision taken by medical professionals in countries where the healthcare delivery system is not adequate, particularly to meet exigencies thrown up by endemic or epidemic diseases, is to choose between some relatively cheap, less time-consuming and less sophisticated diagnostic tests to start treatment in the case of a ‘positive’ indication or to wait till the result of a more time-consuming, costly and confirmatory test is available. Consequences depend on the <a id="page6"></a>available relative frequencies of false-negative and false-positive cases reported for the two tests and the response to delayed treatment of a truly ‘positive’ case. It may be difficult to make general statements about these. Similarly, the healthcare administration or the state government itself may face a crucial choice problem between expenses to upgrade quite urgently facilities and manpower to meet any adverse eventuality at a high cost and spending less on this account to enable release of funds to help the marginalized sections of the people to augment public health measures, including sanitation and personal hygiene.</p>
<p>In a study of customer satisfaction with the existing brand of a certain product, it is important to gather information from customers about their levels of satisfaction/dissatisfaction and the reasons thereof. A direct question like ‘are you satisfied with this product’ may not beget a truthful response. Using a randomized response technique is an unjustified overdose of theory, and one may try to extract some information from answers to questions like ‘how many friends and acquaintances do you have or do you recommend this brand for purchase?’ or ‘how many times have you purchased this brand in preference to others?’ or similar questions. Which strategy is likely to provide us with reliable information about a subjective realization like customer satisfaction is a hard nut to crack.</p>
<p>In a chemical or pharmaceutical experiment to process several basic chemicals mixed in certain proportions for a certain time under a certain temperature using a catalyst (to be determined/chosen) to produce a certain compound with some desirable properties, the problem in designing and analysing the concerned response surface experiment is how to treat more than one response (properties of the output), say, yield and concentration. Do we consider those as equally important and set targets for each or prioritize those and optimize the more important or primary response subject to some lower/upper limits for the remaining response variables?</p>
<p>In designing a sample survey with a pre-determined sample size, say <i>n</i> from a population of size <i>N</i>, how do we allocate the total sample across say <i>k</i> strata with sizes <i>N</i><sub>1</sub>, <i>N</i><sub>2</sub>, …, <i>N<sub>k</sub></i> so that sub-sample <a id="page7"></a>sizes from the strata satisfy the conditions that these are all integral? <i>N<sub>h</sub></i> &gt; <i>n<sub>h</sub></i> &gt; 0, ∑<i>n<sub>h</sub></i> = <i>n</i> and that the variance of the estimate of, say, the population mean is as small as possible.</p>
<p>For estimating an unknown population parameter, two estimators with some desirable properties already exist, and the need is to combine them to get a better estimate. Many options may exist, and the question is: How do we make a choice? Just to illustrate, we have the sample mean and the sample variance (with degrees of freedom as the divisor) as estimates of the Poisson parameter. How about a convex linear combination of the two or a suitably penalized sample mean as a better estimate? And how to find the penalty factor?</p>
<p>A manufacturer of some electronic devices has to demonstrate a specified reliability or probability that the device will continue to function satisfactorily for a stipulated period of time (reasonably large) and has to decide between two alternative test plans. They can put a random sample of units on test and find out how many survive till the stipulated time and, based on that number, estimate the reliability of the device. Alternatively, they can put a sample of units on test, record the time of failure for each item till a pre-specified test time and note the number of units surviving till the test time. Obviously, the test time is smaller than the specified life time. The first plan based on the number of failures (by a test time or by the mission-specified life time) will have cost consequences different from those involved in the second plan based on times to failure. Costs are in terms of the number of items completely used up, the number of items partly used up and still available for use, providing energy during the test to the units put on test, test time and computation of estimated reliability. A non-cost consequence of importance is the precision of the estimated reliability. The decision problem here is not just a binary choice between two test plans. Any test plan involves several decisions relating to sample size, test time and the like. Plans using censoring of different types call for more decisions.</p>
<p>In the context of poverty estimation, the national statistical system has to decide on a suitable measure or index of poverty, from among indicators such as the head count ratio, income gap ratio, Watt’s <a id="page8"></a>index, Sen’s index and several others. We should note that the decision should be related to the purpose or objective of using the measure to be finally chosen. And the purpose could be to estimate the total number of persons who should be provided with some resources or relief, irrespective of how poor they are. A different purpose could be to estimate the total amount of money required to lift the entire population below the poverty line to the non-poor level. A third objective could be to reduce inequality in the incomes of the poor and to ensure that none remain too poor. It is worthwhile to note that a related decision at a preceding stage is: How to determine the poverty line? Should it be based on disposable income or wealth in possession or productive assets in possession or consumption of necessities such as food, clothing, health and shelter? Should it take into account the distribution of income in the entire population?</p>
<p>There are several decision problems in the context of quality management which take recourse to general optimization methods and techniques, occasionally involving complicated objective functions and constraints, which are not directly considered as a distinct part of operations research or even of production management. Thus, the problems of designing a process control plan based on a Shewhart control chart or a cumulative sum control chart in terms of control limit factors or V-mask parameters, sample size and sampling frequency for various process parameters have been studied by quite a few investigators but are not recognized as contributors to decision analysis. Similar is the case with optimal design of a continuous sampling inspection plan characterized by the sampling interval and the length of a run of non-defective items. Even a binary decision to go for a control chart or a sampling inspection plan versus a scheme of complete inspection followed by necessary corrections may be of interest in short production runs as well as in slow production processes turning out critical units.</p>
<p>One decision problem faced by environmental management in state administration is, given an existing ecosystem like a large tract of marshy land not under cultivation or a large water body currently not in good shape or a patch of grassland adjoining a forest, which of the following decisions should be taken for implementation: <a id="page9"></a>(a) conserving the ecosystem as it is, implying indirectly a gradual deterioration in its visibility and appeal, (b) intervening in the ecosystem to conserve it in a better form, adding at the same time to its commercial value by way of enhanced attractiveness to tourists and nature lovers; such an intervention will usually take care of any adverse impact of the existing state of affairs and of intervention activities on the wider environment and (c) converting the ecosystem into a new entity for commercial purposes or for meeting the needs of infrastructure development or to augment recreation facilities. The latter two alternatives will involve costs to be incurred now that can be more or less directly estimated. But the benefits are only to accrue in the future and, hence, are uncertain in nature and magnitude. It is only imperative to treat expenses to be incurred now and benefits to arise in the future as two sets of criteria. For a decision to be made now, benefits have to be duly discounted to yield the expected present net value. Coming to the ‘cost’ aspect, there have been suggestions to realize a part of the amount to be spent on conservation or enhancement (of the beauty and utility) of an ecosystem by the beneficiary ‘public’. And we confront a social choice problem where members of society with diverse attitudes and perceptions about the underlying issues will have to decide on amounts ‘willing to pay’. A similar decision to be made by members of society, in case an existing ecosystem has to be changed for the sake of some infrastructure development, concerns the amount ‘willing to accept’ as compensation. The latter may be sidelined in several situations. In fact, environmental management with too many players or stakeholders with diverse interests involved has been a hot topic for multi-criteria decision-making (MCDM) in modern decision analysis.</p>
</div>
<div class="sect1" id="s9789354791079.i391" title="">
<h1 class="title"><b><span class="label">1.4.</span> Rationality and Bounded Rationality</b></h1>
<p>Decisions are taken, as they have to be taken, by all human beings, irrespective of their levels of knowledge and skill, their mental make-ups and interests. However, in the context of decision-making, it is generally assumed that every decision-maker is ‘rational’ and can make a sensible choice to satisfy their interests, and such a choice presumes quite a few attributes of the decision-maker.</p>
<p><a id="page10"></a>Rationality assumption has been a matter of philosophical discussions and debates for a long time. As mentioned in the <i>Stanford Encyclopedia of Philosophy</i>, Aristotle attributed rationality to human beings, though Bertrand Russell admitted to having failed to find any evidence to support Aristotle.</p>
<p>The assumption of rationality that seeks a cause behind every action and stresses decisions based on logic may, in some extreme situations, lead to inaction in the absence of any rational decision, and such inaction may invite unfortunate consequences. This has been illustrated by Buridan in a somewhat veiled attack on Laplace’s principle of equal ignorance (about the states of nature and thus about the outcomes) to justify equal probability. Buridan constructs a piquant situation in which a donkey stands exactly mid-way between two buckets of water, each full to the brim. The donkey is extremely thirsty but cannot decide which bucket to reach, since, speaking logically, it has no basis for preferring one bucket to the other. Indecision and inaction meant that the donkey died from dehydration. The lesson to be derived from this contrived example of Buridan’s ass is that even in such situations where seeking more information to build up a preference relation is ruled out, people make decisions based on intuition and emotion. In fact, such decisions have been dubbed as context-specific and cognitively biased by some economists and psychologists, though they have been supported as quite valid by others.</p>
<p>The concept of rationality in the context of human behaviour, as is reflected in decision-making by economic agents, was modified to that of ‘bounded rationality’ by Simon (1957), taking due account of limitations on the part of decision-makers in accessing relevant and useful information, possessing adequate analytical capability and accessing requisite computational resources. Such decision-makers or economic agents who may not be found to act in a manner in which the expected utility is a maximum are sometimes characterized as ‘cognitively limited’ agents. Without complete knowledge of all possible options and accurate ideas about their outcomes under each likely state of nature, most decision-makers are cognitively limited. Intricate environments within which decision-makers have to operate may also pose limitations.</p>
<p><a id="page11"></a>Bounded rationality—as distinct from classical ‘rationality’ assumed by economists earlier—offers an explanation for observed deviations in human behaviour from ‘perfect rationality’ or ‘global rationality’. Thus, whenever a decision-maker placed in a competitive situation does not necessarily choose an option that just maximizes their expected utility or gain, even at the cost of huge losses to the competitors, and not acting in a completely selfish manner, at the same time not ignoring their own gain, we may notice a departure from classical rationality. ‘Bounded rationality’ does accommodate such behaviour.</p>
<p>Satisficing as an alternative to optimizing is a strategy adopted by a decision-maker who aspires to achieve a given level of performance for the option chosen (in terms of its outcome or impact). This may be the minimally acceptable option, preferred especially in situations where a search for the globally optimal solution may be quite demanding on mathematical knowledge and computing resources in identifying all possible alternatives, examining their feasibility otherwise, assessing the outcome of each alternative considered and locating the globally optimal alternative(s). A threshold level for the objective function is initially decided, and once we arrive at an option for which the objective function reaches or exceeds this threshold, we are satisfied, and we choose this option to form our decision. The principle of satisficing has often been advocated in dealing with multiple criteria. Thus, for just two criteria in respect of which each option considered should be evaluated, we may first decide on which criterion is more important and fix a threshold level for performance in respect of the other criterion. Here, we can search for an optimal solution in respect of the first criterion, subject to satisficing in respect of the second criterion. The search can start with the first criterion only, and once we arrive at the optimal solution, we can check if it satisfies the threshold level for the other criterion.</p>
<p>Satisficing has been criticized because of its possibility of missing the true or global optimum. Given a specification of what will count as a good-enough outcome, satisficing replaces the optimization objective from the expected utility theory of selecting an undominated outcome with the objective of picking an option that meets your aspirations. Ignoring the procedural aspects of Simon’s original formulation of <a id="page12"></a>satisficing, if one has a fixed aspirational level for a given decision problem, then admissible choices from satisficing can be captured by the so-called <i>ϵ</i>-efficiency methods (Loridan, 1984; White, 1986).</p>
</div>
<div class="sect1" id="s9789354791079.i395" title="">
<h1 class="title"><b><span class="label">1.5.</span> Hierarchy of Decisions</b></h1>
<p>Levels and types of decision-making depend on many factors, which are as follows:</p>
<ul style="list-style-type: disc"><li>Status of the decision-maker—an individual or a collective body, responsibility held and authority enjoyed in relation to decision-based actions. The decision-maker is bound in some cases to consider just the given options, and in some others, they can generate or develop other alternatives.</li><li>The entity about which the decision is being made—a system, an operation, a method, an experiment, a survey, a finding from a study and so on. Some are concrete; others are abstract, and abstract entities are used to model concrete ones.</li><li>The environment in which the decision-maker is placed includes their access to pertinent resources, namely information bearing on the decision problem and required to solve it, as well as knowledge, experience and skills to process the information.</li><li>Nature (representing externalities that influence the consequences of any decision alternative) and the measure of the outcome(s) of the decision and the impact(s) thereof on the system under consideration as well as on related systems or entities. Outcomes could be immediate in some cases and remote in others, related to a point in time in some cases and spread over a period of time in others, exactly or approximately determinable in some cases and only predictable with uncertainty in others.</li></ul>
<p class="p-continued">Decisions can be categorized as strategic, operational and tactical depending on the status and constitution of the decision-maker, the length of the planning horizon to be kept in mind, the type of activities and their outcomes to be influenced and the impact(s) of the decision—when implemented—on the system within which the decision has been taken. From this point of view, a strategic decision usually <a id="page13"></a>relates to a long planning horizon of several years, sets out long-term goals and plans, is taken by people enjoying adequate authority and tasked with heavy responsibility, involves a lot of risk and uncertainty and needs thorough scanning and analysis of the external environment to collect and process pertinent information. Strategic decision-making lays down goals and objectives for tactical and operational decision-making. These decisions relate to problems like diversifying revenue streams or acquiring new businesses and their vertical integration with existing business verticals for a business organization. That way, strategic decisions influence many interest groups, and their outcomes have far-reaching consequences—beneficial or adverse—on the entire system and/or the environment within which it operates, even with international ramifications. A decision to link internal rivers flowing through a region or a country to derive full benefit from the total volume of water in these rivers during different seasons is a strategic decision that will have a continuing impact on the lives of people in the region in many ways. A decision that will entail a lot of expenditure and will occupy a considerable time period to be implemented is no doubt a strategic decision to be taken by the top functionaries in public life after a substantial amount of deliberations, consultations and computations. For an autonomous academic or a research organization, a decision, to open its programmes to international collaboration and funding or even hiring international faculty with an offer of attractive salaries and perks or introducing completely new areas of research or new courses for which resources, including manpower and laboratories, have to be substantially augmented, illustrates strategic decisions.</p>
<p>Tactical decisions are meant to translate strategic decisions into action plans in terms of decisions regarding acquisition/augmentation of technology, capacity, facilities and so on and their deployment. Senior and middle-level executives in business houses and bureaucrats in charge of implementation of national or state-level policies and projects are usually involved in developing tactical decisions for a medium-term horizon. The allocation of some national or state resources among several competing entrepreneurs interested in enriching the resource or, at a slightly lower level, the choice among alternative technologies by an entrepreneur for value addition to some such resource illustrates tactical decisions.</p>
<p><a id="page14"></a>Operational decisions are usually taken by frontline decision-makers who are required to implement various tactical decisions into practice. These are short-term decisions regarding actual deployment and management of resources for carrying out different operations. It may be pointed out that most operational decisions—decisions preceding and succeeding operations—relating to production planning, job scheduling, machine sequencing, inventorying, replacing equipment, transporting finished goods from plant to stores or dealers, fixing the number of service counters and a host of similar other decisions constitute the bulk of discussions in a text on operations research. Preparation of class routines, conduct of tests, organizing placement services, holding special lectures, seminars and similar programmes and so on are examples of operational decisions within an academic institution.</p>
<p>A decision relating to recruitment and deployment of workers to meet the needs of the technology adopted and other interests and obligations of the entrepreneur can be treated as a tactical decision or as an operational decision, depending on the scale of operations.</p>
<p>It is sometimes argued that the hierarchy mentioned above is not that important. The categorization is somewhat context-specific and arbitrary. A decision dubbed as tactical may well be regarded as an operational decision, and an operational decision, in some cases, may well qualify to be considered as a strategic decision. For a given manufacturing or service organization, strategic decisions regarding different business processes—core and support—can be definitely distinguished from operational and tactical decisions taken by its management. This is why decisions are sometimes classified as meta-level, macro-level and micro-level. And meta-level decisions even within an organization may be called strategic. However, national-level public decisions are undoubtedly strategic in nature.</p>
<p>Meta-level decision-making affecting the social, economic and political systems, and their processes in a country has become more important than ever before. This has national and even international implications. During the 100th anniversary of the small city of Flint in Michigan, USA (2014), an international conference addressed the <a id="page15"></a>vexing decision problem for economic planners: ‘Richer and more unequal or poorer and more equal—which is the better alternative?’ The controversy around carbon emissions to reduce energy consumption or to augment the existing supply of energy continues unabated. Meta-level decision problems are difficult to formulate, not to mention the evasive nature of the solution. In the first example, the two alternatives have to be comprehended and concretised in terms of several actions/operations as well as in terms of the consequences that will follow in the socio-economic arena, since they are both alternative objectives as well as alternative courses of action. Actual application requires many actions to be taken at different levels to be planned optimally, taking care to avoid risks of sub-optimization.</p>
</div>
<div class="sect1" id="s9789354791079.i397" title="">
<h1 class="title"><b><span class="label">1.6.</span> Group Decisions</b></h1>
<p>Groups of people, with varying intuitions and emotions, experiences and capabilities, preferences and indifferences, working in a group, a society or a business organization, have to take decisions on important issues. And within such a group, there could be some leaders and some expert advisors besides ordinary members. In such cases, several levels of decisions or decision-making situations have been pointed out by management scientists. These are as follows:</p>
<ol class="decimal"><li>The leader alone decides—a single decision-maker who might have been interacting with others over time but not seeking or using any input from any others for making the decision in the present context.</li><li>The leader decides with inputs from key stakeholders, who may represent a mix of external interests as well as people within the group. The processing and eventual use of such inputs is left to the single leader.</li><li>The leader builds a consensus with input from a subgroup, retaining the final say in the matter. The choice of the subgroup is also left to the leader.</li><li>The whole group votes on a decision (which may be suggested by any of the above mechanisms) before its acceptance or the decision is delegated to someone else for a final say.</li><li><a id="page16"></a>True consensus is built through discussions, consultations and compromises (whenever needed). This decision is the easiest to implement through wilful acceptance by all concerned.</li></ol>
<p class="p-continued">While such steps can be visualized within a well-knit organization with defined roles and responsibilities of members, they may be difficult to realize within a loosely knit organization or in a society.</p>
</div>
<div class="sect1" id="s9789354791079.i399" title="">
<h1 class="title"><b><span class="label">1.7.</span> Social Choice Decisions</b></h1>
<p>Important decisions are sometimes taken by groups or communities or societies, by noting and aggregating suitably preferences of individual members for different feasible (implementable) options or strategies, as well as their indifferences between some alternatives. The group could comprise just the husband and his wife willing to take a joint call on a particular issue at hand or the community of medical professionals interested in reaching a collective decision about the treatment protocol for a newly surfacing disease or residents of a city wanting to decide collectively on the best location for a recreation facility or a religious institution. Individual preferences and indifferences are not always explicit; alternatives may not be given or known in advance and may emerge during deliberations; members may not agree on the feasibility or otherwise of any alternative, and we may not have the advantage of applying some attractive optimization principles to arrive at a decision.</p>
<p>In a practical choice situation with a given set of alternatives S (based on a partial order among the alternatives), a decision-maker makes a personal assessment of what constitutes the best alternative when choosing one. Different decision-makers in the same situation and with the same set of alternatives may have different assessments of what constitutes the best alternative, and hence, their choices may differ. Each of them, however, may be able to justify the choice that was made. Even if the same choice was made by different decision-makers, their justifications could differ. Consider a subset <i>X</i> of the set of alternatives and let <i>C</i>(<i>X</i>) be the set of alternatives chosen from this subset. If we examine the subsets of alternatives chosen by the same <a id="page17"></a>decision-maker based on different subsets of <i>X</i>, <i>we can develop some ideas about</i> what may be called a choice function used by an individual decision-maker. It is quite interesting to note that the number of possible choice functions for a set of alternatives with <i>N</i> elements is as large as 2<sup>2</sup><i><sup>N</sup></i><sup>(</sup><i><sup>N</sup></i><sup> − 1)</sup>. With just three alternatives available to a decision-maker, the number of choice functions possible in theory is 4,096.</p>
<p>There have been several lines of development in social choice theory, the classical economists’ social choice, behavioural social choice and computational social choice. From axioms and game theory to ranking and ordering and incorporation of empirical evidence, a variety of tools have been used to offer an engaging literature on the subject that has grown since the days of Arrow, in both depth and diversity. In fact, Arrow’s impossibility theorem and subsequent attempts to overcome the impossibility can be regarded as the cornerstone of social choice theory in relation to decision theory.</p>
</div>
<div class="sect1" id="s9789354791079.i400" title="">
<h1 class="title"><b><span class="label">1.8.</span> Decision-making and Problem-solving</b></h1>
<p>Decisions have to be taken to solve a problem encountered right now, like those involved in fighting a fire that has erupted on the upper floors of a high-rise residential or commercial building. The goal is to minimize casualties or serious injuries to human lives or severe and irreparable damage to documents, records and material goods. Constraints could arise in terms of the absence of a ladder to reach the floor engulfed by fire or/and inadequacy of water pressure in the water storage nearby or/and non-availability of foam to be sprayed and so on. States of nature could be no one living in the building or the floor, people living in the building and the like. The options available to the fire-fighting team include dousing the flames coming out of the affected floor first, evacuating any humans likely to be trapped inside first, entering the building and switching off all electrical devices first and the like. The team has some experience of fighting such a fire several times in the past.</p>
<p>Decisions have to be taken, with due analysis and care, to solve problems likely to be faced in the future or continuing to plague the concerned people. Thus, planners have to decide on measures to reduce <a id="page18"></a>levels of poverty in the country through possible economic measures. Social and political considerations may appear as constraints on implementing measures likely to affect some interest groups, like corporate houses. Possible measures (decisions) could include the imposition of higher rates of direct taxes payable by the affluent sections of the people, putting curbs or higher entry taxes on luxury goods, improving the collection of indirect taxes from retail outlets, enhancing spending on corporate social responsibility, improving the productive efficiency of manufacturing and service enterprises, emphasis on labour-intensive but efficient service enterprises and the like. The goal is to achieve a visible decline in the head count ratio of poverty.</p>
<p>Decisions may not be necessarily linked up with problem-solving in the sense illustrated above but to help those in pursuit of knowledge in different disciplines become more effective and efficient in their efforts. Thus, good decisions are required to design an experiment, which is rather costly, to cut down on time and cost as well as on likely failures. We have to choose factors for inclusion as well as their levels, along with all experimental conditions, to come up with an expected response(s) with minimum cost and the shortest time. The same is the need for decisions at every stage of any investigation into a new phenomenon or a new aspect of an existing phenomenon.</p>
</div>
<div class="sect1" id="s9789354791079.i401" title="">
<h1 class="title"><b><span class="label">1.9.</span> Descriptive and Normative Decision Theory</b></h1>
<p>It may not be irrelevant to appreciate the difference between decision-making and decision theory—the latter providing or, better, meant to provide support to the former. Again, decision-making is focused on actions based on decisions and decision theory—as the very name implies—is concerned with concepts, methods and techniques that can be and are being used by decision-makers to arrive at effective decisions targeted at achieving some underlying goals and objectives.</p>
<p>Similarly, it is important to distinguish descriptive decision theory from normative decision theory. The former deals with discernible generalities behind how decisions are actually made by individuals and institutions, while the latter is concerned with principles and procedures which should be followed by decision-makers in reaching <a id="page19"></a>decisions, the way they should choose criteria to evaluate the outcomes of possible choices and the extent and nature of information they should seek for this purpose, as well as the type of analysis that should be carried out to decide on a choice and even to find out a means to assess the performance of a decision after its implementation. Descriptive decision theory is not just a dossier of decisions made purely subjectively, based on decision-makers’ emotions and intuitions. Revealed preferences among alternatives in respect of their outcomes, previous experience in dealing with decision situations or problems bearing some similarity to previous situations and corresponding decisions, any consultations or deliberations held, any logical argument spelt out, possible explanations of the way a final choice was made from the standpoint of behavioural sciences, explanations that can beget some support from economic theory and other features are codified to build up what may be justifiably called a theory. Also, the ‘theoretical’ content of normative decision theory hardly needs any elaboration at this stage. Suffice it to say that the present volume is primarily devoted to several chosen aspects of normative decision theory.</p>
<p>The Poliheuristic theory (PHT) of decision-making was developed in the early 1990s to bring together the cognitive and rationalist traditions in analysis of foreign policy decisions to take due cognizance of both the process (the how) and the outcome (the why) in foreign policy decision-making. PHT looks upon the cognitive and rationalist approaches to foreign policy analysis as complementary rather than competitive perspectives on how leaders make foreign policy decisions, integrating them into a unified framework and drawing strength from both. On the one hand, the advantages of the cognitive approach in providing more descriptively realistic accounts of how foreign policy decisions are made by leaders are enjoyed, and on the other, the ability of the rationalist models to provide deductive explanations of foreign policy choices is fully incorporated.</p>
<p>In fact, PHT models foreign policy as a two-stage process that combines a heuristics-based and an expected-utility-maximizing stage of decision-making. In the first stage, actors use a range of cognitive shortcuts that may run counter to rational choice assumptions and serve to simplify the decision problem by quickly eliminating <a id="page20"></a>unacceptable alternatives from the choice set. Decision-makers reject alternatives outright that score below a cut-off value on what they have identified as the most important dimensions of the decision, instead of establishing trade-offs among different dimensions or attributes of the available options. To illustrate, in foreign policymaking, domestic political loss associated with any alternative is accorded a high priority, and an alternative that is unacceptable on this crucial dimension is discarded. In the second stage, a more demanding and thorough screening of the remaining alternatives is undertaken to identify the alternative(s) that maximizes expected utility. While PHT can be applied outside the regime of foreign policy analysis, it has not found wide acceptance.</p>
<p>Growing attention is being paid by executives and professionals to ‘behavioural decision theory’, which should not be taken as synonymous with a theory concerned only with intuitive or cognitive decisions. In fact, exponents of this emerging theory accord due role to the decision-maker and to both cognitive and affective aspects of the process. Some even talk of rule-based as well as role-based decision-making as distinct processes, recognizing the fact that people engaged in certain professions, like doctors, do and should decide their actions in a given situation on the basis of instincts or intuition or experience without waiting for what may be appropriately called ‘rational’. Some authors on behavioural decision theory go to the extent of claiming descriptive, normative and prescriptive decision theory as three distinct constituents of behavioural decision theory, stretching it to include case-based decision theory (CBDT). There are others, of course, who do not subscribe to this all-pervasive exposition of the subject.</p>
</div>
<div class="sect1" id="s9789354791079.i402" title="">
<h1 class="title"><b><span class="label">1.10.</span> Case-based Decision Theory</b></h1>
<p>Gilboa and Schmeidler (1995) put forth a whole theory of reasoning or making choices or decision-making that departs from the classical set-up dominated by expected utility. It is well-known that the classical theory is primarily based on deductive logic and probabilistic inference, without an explicit recognition of inductive logic. Gilboa and <a id="page21"></a>Schmeidler argued that human decision-making takes into account behavioural data in terms of decisions or actions taken in similar decision problems recalled to the extent possible. This makes it possible to extend the scope of logical decision-making to situations where the classical framework in terms of states of nature, their probabilities of occurrence and utilities associated with possible alternative decisions (actions) cannot be used to comprehend and structure a decision problem. In fact, there exist problems where the states of nature are not given initially, nor can they be conveniently identified. Hence, all possible pay-offs for each alternative may not be known, and even in a state of nature initially incorporated, the pay-off may not be certainly known. However, such a decision problem might have been faced earlier by some decision-makers. The extent of similarity with those problems (that can be recalled by the present decision-maker) in terms of causes involved may be assessed by the present decision-maker, may be subjectively. The actions taken in those problems and the outcomes realized are better known. The concept of ‘expected utility’ may not be relevant. The set of problems with some similarity to the current problem, the set of actions taken to resolve those and the corresponding set of outcomes realized together constitute a ‘case’ in the language of Gilboa and Schmeidler, and with some axiomatization necessary to develop a theory, they have come up with what is known as CBDT, which has found interesting and useful applications in managerial decision-making.</p>
<p>CBDT cannot be regarded as a self-complete decision theory embracing general optimization problems besides choice problems. It was developed as an alternative to the Bayesian paradigm, which requires a prior probability measure over a large space containing states of nature, which are truth functions defined on all possible observations (and thus also on all possible observation-based rules). It avoids induction and does not suffer from any logical inconsistencies, since cases cannot be contradicted though rules can be.</p>
<p>As indicated above, a case here means a triplet of (problem, act result). Gilboa and Schmeidler refer to Hume (1748/1999) to justify the use of previously observed or narrated cases via analogies. Hume maintains that,</p><blockquote>
<p><a id="page22"></a>All arguments from experience are founded on the similarity which we discover among natural objects, and by which we are induced to expect effects similar to those which we have found to follow from such objects. From causes which appear similar, we expect similar effects. This is the sum of all our experimental conclusions.</p></blockquote>
<p class="p-continued">On this logic, we can solve decision problems by choosing acts which were successful in similar decision problems. It is clear that the focus in CBDT is on acts, rather than on decisions leading to these acts, and that acts chosen should be satisficing rather than ‘optimal’. It is also true that the subtle distinction between decisions and acts (to implement decisions) is not that important unless we accommodate some likely deviation during implementation. And we are facing decision problems where an objective function cannot be conceptualized conveniently for the purpose of optimization.</p>
<p>The two key concepts in CBDT are those of <i>similarity</i> between decision problems and the <i>desirability</i> of an outcome/result. Similarity could be understood in terms of the ‘nearest neighbour ‘technique, by possibly identifying key features of a decision problem. However, the most similar case which can be recalled by the decision-maker might have resulted in a major catastrophe or a very adverse result. Otherwise, besides the most similar case yielding a satisfactory result, some other acts in potentially similar cases might also have fared successfully and should be taken into account. Utility could be taken as the desirability measure for any result/outcome. We, of course, need to aggregate this utility over many cases to obtain an overall ranking of an alternative act.</p>
<p>In a formal statement of CBDT, we take the following as the given primitives:</p>
<ul class="no-marker"><li><i>P</i> : A set of decision problems <i>p</i></li><li><i>A</i> : A set of available acts <i>a</i></li><li><i>R</i> : A set of possible results or outcomes <i>r</i></li><li><i>M</i> : A subset of <i>P</i> which can be recalled by the decision-maker, called memory</li></ul>
<p class="p-continued">CBDT postulates the desirability of a result as its utility <i>u</i>(<i>r</i>).</p>
<p><a id="page23"></a>A decision-maker is characterized by a utility function that assigns real numbers to values of outcomes and a similarity function that assigns real numbers <i>s</i>(<i>p</i>, <i>q</i>) to pairs (<i>p</i>, <i>q</i>) of problems. Faced with a new problem <i>p</i>, the decision-maker chooses the act <i>a</i> that maximizes the overall utility defined as: <i>U</i>(<i>a</i>) = ∑s(<i>p</i>, <i>q</i>) <i>u</i>(<i>r</i>) summed overall (<i>q</i>, <i>a</i>, <i>r</i>) € <i>M</i>.</p>
<p>According to CBDT, choose the act that maximizes <i>U</i>.</p>
<p>The memory has been defined in such a manner that (a) no problem is encountered more than once (apparently dissimilar problems may be really similar to the decision-maker) and (b) memory is a set. The order in which cases appear in a memory is immaterial. If the description of a problem includes a time parameter, the memory set is as informative as a sequence of information.</p>
<p>Gilboa and Schmeidler suggested the following variants of the above procedure.</p>
<ul style="list-style-type: disc"><li><b>Averaged similarity:</b> Where for each act, the similarity coefficients are normalized to sum up to unity.</li><li><b>Act similarity:</b> In which the result of an act is considered along with the results of similar acts in the past. Similarity judgements of acts and of problems may depend on each other. Thus, the similarity function is defined in the space of problem–act pairs, and an act is evaluated by the sum of products of similarity of product pairs and the result of each act, taking into account the results of similar acts.</li><li><b>Memory-dependent similarity:</b> It allows the decision-maker to learn that some features of problems are more or less similar or that some attributes of a problem are more or less important, based on past experience. In such a case, the similarity function is likely to evolve with memory or the subset of cases recalled, an entity that may change over time.</li></ul>
<p class="p-continued">Usually, CBDT avoids rules. However, if the decision-maker takes into account all past cases, and these warrant the induction of a rule, the decision-maker would also act in accordance with this rule.</p>
<p><a id="page24"></a>In expected utility theory, as also in CBDT, acts are ranked by weighted sums of utilities. Of course, in the expected utility theory, outcomes and corresponding utilities against different states of nature for any act or option are, at least in some situations, ‘anticipated’. These are ‘actual’ outcomes and corresponding utilities achieved in the case of CBDT. However, several differences come up. In CBDT, each act is evaluated over a different set of cases. If acts a and b are such that <i>a</i> &lt; <i>b</i>, then the set of elements in <i>M</i> summed over to get <i>U</i>(a) is disjoint from the set used in <i>U</i>(<i>b</i>). In fact, the first set may even be empty for some acts. On the other hand, every act in the expected utility theory is evaluated at each stage.</p>
<p>CBDT results in conservative or uncertainty-averse behaviour. If each act <i>a</i>, €<i>A</i> ever results in an outcome <i>r</i><sub>0</sub>, the decision-maker will only try new acts until they find one that yields <i>u</i>(<i>r</i><sub>0</sub>) &gt; 0. Thereafter, they will try this act over and over again. They will be ‘satisfied’ with the reasonable act a and will not act to maximize their utility function. In some senses, this implies that the null point on the utility scale corresponds to their aspiration level. CBDT does not differentiate between ‘certain’ acts which lead to the same outcomes with certainty and ‘uncertain’ acts whose outcomes are not certainly known.</p>
<p>For operational purposes, CBDT requires a case library to be built up as a repository of cases along with the acts chosen to resolve the problems and the outcomes realized. The tasks involved in CBDT are (a) retrieval of the case that matches the current problem in terms of features or attributes to the best extent, (b) reuse of the previous case, namely the act, repaired if the proposed act does not solve the problem satisfactorily, (c) revise the act or solution evaluated for its appropriateness by comparing it with the confirmed target class and (d) retain the act finally chosen and the outcome realized in the library.</p>
<p>In its initial formulation, the generation of acts or possible solutions to the current problem was not a part of the case-based decision process; the authors themselves noted the importance of this activity and encouraged its adoption to enrich the case library.<a id="page25"></a></p>
</div>
<div class="sect1" id="s9789354791079.i405" title="">
<h1 class="title"><b><span class="label">1.11.</span> Decision Theory and Decision-making</b></h1>
<p>Decision theory is a composite body of knowledge derived from various disciplines, including philosophy, economics, mathematics, statistics and psychology, that has pushed the frontiers of research in political science and international relations, sociology, computer science and even cybernetics and cryptology. It integrates normative, descriptive and prescriptive aspects of how human decisions are made as well as how they should be made. Going by epistemological principles, decision theory embodies concepts, methods and techniques as well as tools like software to apply them in practice. The theory is growing fast and so are its applications in real life.</p>
<p>Decision-making is a task or an activity, not carried out aimlessly and without reference to a context. Decision-makers—called agents by some authors—are placed in some environments and are charged with the responsibility of achieving some goals or objectives, sometimes categorically and clearly spelt out and only vaguely or broadly indicated in some others, by making (or should we say, taking) decisions which are meant to be acted upon or implemented in the manner decided. If a decision-maker is needed to make a ‘good’ decision—as should be a natural requirement—a concomitant question awaiting an answer will be, ‘what do we mean by “good”?’ If ‘good’ is to imply a ‘good’ outcome, we have to wait for implementation or realization of the decision to find out if it was ‘good’ or not. If the implication is ‘rational’, the definition of rationality will dodge us. One may indirectly take it as ‘consistent’ and that again cannot be judged as a stand-alone decision. Should a cognitive or an ‘affective’ or a ‘calculating’ or an ‘intuitive ‘decision be considered as ‘good’? One may get back to the issue of a ‘good’ outcome. Also, to be noted is the fact that cognitive and affective needs are often in conflict. Of course, decision-making by way of solving general optimization problems will mostly steer clear of such issues, since the alternatives and their outcomes are linked in terms of functional relations and outcomes can be directly observed or computed, exactly or approximately, without a wait for implementation. However, there could be complex optimization problems where <a id="page26"></a>assessment of the outcome of any alternative may involve simulation of the underlying system or running an experiment, and that may take some time.</p>
<p>Implementation may or may not be a responsibility of those who decide. However, those who decide have to be aware of the needs of implementation and—in many situations—be involved in assessing the extent to which the goals and objectives can be achieved eventually. Implementation is a generic term and may imply simulation, computation, comparison and integration.</p>
<p>The decision–action pair is so natural that in several decision-making procedures, these terms have been used interchangeably. There are decision problems in the conceptual world where implementation of any possible alternative yielding some choice measure forms the basis of deciding an alternative as optimal or as satisfactory.</p>
<p>Theory provides an important input to someone carrying out a task; the knowledge and experience of the decision-maker along with their intuitive and imaginative capabilities bring in more inputs. Theory mandates the availability of some information bearing on the decision problem, along with the attendant resource requirements. The relative weights placed on theory and on these other inputs depend a lot on the decision-maker, the goal or objective and the context or environment.</p>
<p>Individuals who have taken path-breaking decisions in difficult situations have embodied their decision-making exercises with varying inputs from decision theory in terms of certain models for decision-making which definitely deserve our attention.</p>
<p>As the title of this book suggests, the focus here is on decision-making aided and strengthened by decision theory in all its domains and dimensions, at the same time emphasizing the role of human behaviour in all its diversity. However, the well-established facet of decision theory dealing with general optimization problems, which is rich in quantitative methods and provides rather little scope for human interference except in some marginal roles, has been accorded its due place in the book. In fact, many models, techniques and results <a id="page27"></a>developed in the context of general optimization theory have found useful applications in practice, in conjunction with several aspects of cognitive and even affective decision-making, to yield ‘good’ decisions. Decision-making, in that way, has derived strength from advances in several branches of knowledge, including mathematics, computer science, behavioural economics, logic and related subjects.</p>
</div>
</div></body></html>